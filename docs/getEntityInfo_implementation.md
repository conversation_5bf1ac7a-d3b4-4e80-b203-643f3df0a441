# getEntityInfo 方法实现说明

## 概述
`getEntityInfo` 方法用于根据统一社会信用代码（credit_no）和表单API名称（sheetApiName）获取实体信息。该方法实现了两步查询策略：
1. 首先查询数据库获取数据
2. 如果数据库中没有找到，则查询Redis缓存

## 实现细节

### 第一步：数据库查询
1. **验证信用代码**：检查传入的credit_no是否存在于实体表中
2. **构建查询SQL**：
   - 根据sheetApiName获取对应的表单信息
   - 获取用户角色可见的字段列表
   - 处理关联表的JOIN查询
3. **执行查询**：使用构建的SQL查询数据
4. **数据处理**：
   - 处理选项字段（单选/多选）的ID到名称转换
   - 处理级联字段的显示转换
   - 处理业务表选项字段的转换
5. **缓存结果**：将查询结果缓存到Redis，默认缓存30分钟

### 第二步：Redis缓存查询
1. **构建缓存键**：格式为 `entity_info:{credit_no}:{sheetApiName}`
2. **查询缓存**：从Redis中获取缓存的数据
3. **返回结果**：如果缓存中有数据，直接返回

## 新增的辅助方法

### processFieldTypes
处理字段类型转换，将数据库中的ID值转换为对应的显示值：
- 单选选项字段：ID → 选项名称
- 多选选项字段：ID列表 → 选项名称列表
- 单选业务表字段：ID → 业务表记录名称
- 多选业务表字段：ID列表 → 业务表记录名称列表
- 级联字段：ID链 → 级联选项名称链

### getOptionValueMap
获取选项值映射，用于选项字段的ID到名称转换。

### getSheetValueMap
获取业务表选项值映射，用于业务表选项字段的ID到名称转换。

### processDataConversion
执行具体的数据转换逻辑，处理各种字段类型的显示转换。

### 缓存管理方法
- `cacheEntityInfo`: 将数据缓存到Redis
- `getEntityInfoFromCache`: 从Redis获取缓存数据
- `clearEntityInfoCache`: 清除Redis缓存

## 缓存策略
- **缓存键格式**：`entity_info:{credit_no}:{sheetApiName}`
- **缓存时间**：30分钟（可配置）
- **缓存时机**：数据库查询成功后自动缓存
- **缓存清理**：提供手动清理方法

## 错误处理
- 统一社会信用代码不存在：返回"未找到相关数据"
- 表单API名称错误：返回"错误的业务表单"
- 数据转换异常：保持原值，不影响整体查询

## 性能优化
1. **缓存机制**：避免重复的数据库查询
2. **批量查询**：选项值和业务表数据使用批量查询
3. **异常处理**：数据转换异常不影响整体流程
4. **内存优化**：使用Stream API减少中间集合创建

## 使用示例

```java
UniversalQuery query = new UniversalQuery();
query.setCreditNo("91110000000000000X");
query.setSheetApiName("company_info");
query.setPage(1);
query.setPageSize(10);

AjaxResult result = universalService.getEntityInfo(query);
```

## 注意事项
1. 确保Redis服务正常运行
2. 统一社会信用代码必须在实体表中存在
3. 表单API名称必须正确且用户有访问权限
4. 缓存时间可根据业务需求调整
