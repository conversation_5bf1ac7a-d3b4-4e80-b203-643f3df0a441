package com.heju.file.api.feign.factory;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.result.R;
import com.heju.file.api.domain.SysFile;
import com.heju.file.api.feign.RemoteFileManageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.FallbackFactory;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 文件管理服务 降级处理
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RemoteFileManageFallbackFactory implements FallbackFactory<RemoteFileManageService> {

    @Override
    public RemoteFileManageService create(Throwable throwable) {
        log.error("文件管理服务调用失败:{}", throwable.getMessage());
        return new RemoteFileManageService() {
            @Override
            public R<Boolean> saveFileLog(SysFile file, String source) {
                return R.fail("存储文件记录失败:" + throwable.getMessage());
            }
        };
    }
}