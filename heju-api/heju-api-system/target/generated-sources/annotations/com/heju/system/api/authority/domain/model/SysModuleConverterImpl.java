package com.heju.system.api.authority.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.api.authority.domain.dto.SysModuleDto;
import com.heju.system.api.authority.domain.po.SysModulePo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-21T14:08:36+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysModuleConverterImpl implements SysModuleConverter {

    @Override
    public SysModuleDto mapperDto(SysModulePo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysModuleDto sysModuleDto = new SysModuleDto();

        sysModuleDto.setId( arg0.getId() );
        sysModuleDto.setSourceName( arg0.getSourceName() );
        sysModuleDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysModuleDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysModuleDto.setName( arg0.getName() );
        sysModuleDto.setStatus( arg0.getStatus() );
        sysModuleDto.setSort( arg0.getSort() );
        sysModuleDto.setRemark( arg0.getRemark() );
        sysModuleDto.setCreateBy( arg0.getCreateBy() );
        sysModuleDto.setCreateTime( arg0.getCreateTime() );
        sysModuleDto.setUpdateBy( arg0.getUpdateBy() );
        sysModuleDto.setUpdateTime( arg0.getUpdateTime() );
        sysModuleDto.setDelFlag( arg0.getDelFlag() );
        sysModuleDto.setCreateName( arg0.getCreateName() );
        sysModuleDto.setUpdateName( arg0.getUpdateName() );
        sysModuleDto.setIsCommon( arg0.getIsCommon() );
        sysModuleDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysModuleDto.setLogo( arg0.getLogo() );
        sysModuleDto.setPath( arg0.getPath() );
        sysModuleDto.setParamPath( arg0.getParamPath() );
        sysModuleDto.setType( arg0.getType() );
        sysModuleDto.setHideModule( arg0.getHideModule() );
        sysModuleDto.setIsDefault( arg0.getIsDefault() );

        return sysModuleDto;
    }

    @Override
    public List<SysModuleDto> mapperDto(Collection<SysModulePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysModuleDto> list = new ArrayList<SysModuleDto>( arg0.size() );
        for ( SysModulePo sysModulePo : arg0 ) {
            list.add( mapperDto( sysModulePo ) );
        }

        return list;
    }

    @Override
    public Page<SysModuleDto> mapperPageDto(Collection<SysModulePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysModuleDto> page = new Page<SysModuleDto>();
        for ( SysModulePo sysModulePo : arg0 ) {
            page.add( mapperDto( sysModulePo ) );
        }

        return page;
    }

    @Override
    public SysModulePo mapperPo(SysModuleDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysModulePo sysModulePo = new SysModulePo();

        sysModulePo.setId( arg0.getId() );
        sysModulePo.setSourceName( arg0.getSourceName() );
        sysModulePo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysModulePo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysModulePo.setName( arg0.getName() );
        sysModulePo.setStatus( arg0.getStatus() );
        sysModulePo.setSort( arg0.getSort() );
        sysModulePo.setRemark( arg0.getRemark() );
        sysModulePo.setCreateBy( arg0.getCreateBy() );
        sysModulePo.setCreateTime( arg0.getCreateTime() );
        sysModulePo.setUpdateBy( arg0.getUpdateBy() );
        sysModulePo.setUpdateTime( arg0.getUpdateTime() );
        sysModulePo.setDelFlag( arg0.getDelFlag() );
        sysModulePo.setCreateName( arg0.getCreateName() );
        sysModulePo.setUpdateName( arg0.getUpdateName() );
        sysModulePo.setIsCommon( arg0.getIsCommon() );
        sysModulePo.setEnterpriseId( arg0.getEnterpriseId() );
        sysModulePo.setLogo( arg0.getLogo() );
        sysModulePo.setPath( arg0.getPath() );
        sysModulePo.setParamPath( arg0.getParamPath() );
        sysModulePo.setType( arg0.getType() );
        sysModulePo.setHideModule( arg0.getHideModule() );
        sysModulePo.setIsDefault( arg0.getIsDefault() );

        return sysModulePo;
    }

    @Override
    public List<SysModulePo> mapperPo(Collection<SysModuleDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysModulePo> list = new ArrayList<SysModulePo>( arg0.size() );
        for ( SysModuleDto sysModuleDto : arg0 ) {
            list.add( mapperPo( sysModuleDto ) );
        }

        return list;
    }

    @Override
    public Page<SysModulePo> mapperPagePo(Collection<SysModuleDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysModulePo> page = new Page<SysModulePo>();
        for ( SysModuleDto sysModuleDto : arg0 ) {
            page.add( mapperPo( sysModuleDto ) );
        }

        return page;
    }
}
