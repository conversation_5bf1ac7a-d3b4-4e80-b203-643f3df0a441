package com.heju.system.api.dict.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.api.dict.domain.dto.SysDictTypeDto;
import com.heju.system.api.dict.domain.po.SysDictTypePo;
import com.heju.system.api.dict.domain.query.SysDictTypeQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 字典类型 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysDictTypeConverter extends BaseConverter<SysDictTypeQuery, SysDictTypeDto, SysDictTypePo> {
}
