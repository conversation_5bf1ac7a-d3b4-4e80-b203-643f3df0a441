package com.heju.system.api.authority.domain.model;

import com.heju.common.core.web.entity.model.TreeConverter;
import com.heju.system.api.authority.domain.dto.SysMenuDto;
import com.heju.system.api.authority.domain.po.SysMenuPo;
import com.heju.system.api.authority.domain.query.SysMenuQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 菜单 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysMenuConverter extends TreeConverter<SysMenuQuery, SysMenuDto, SysMenuPo> {
}
