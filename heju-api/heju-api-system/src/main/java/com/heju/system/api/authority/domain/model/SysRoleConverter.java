package com.heju.system.api.authority.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.api.authority.domain.dto.SysRoleDto;
import com.heju.system.api.authority.domain.po.SysRolePo;
import com.heju.system.api.authority.domain.query.SysRoleQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 角色 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysRoleConverter extends BaseConverter<SysRoleQuery, SysRoleDto, SysRolePo> {
}
