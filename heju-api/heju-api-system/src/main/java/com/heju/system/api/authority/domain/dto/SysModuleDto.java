package com.heju.system.api.authority.domain.dto;

import com.heju.common.core.annotation.Correlation;
import com.heju.common.core.constant.basic.OperateConstants;
import com.heju.system.api.authority.domain.po.SysModulePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

import static com.heju.system.api.authority.domain.merge.MergeGroup.MODULE_SysMenu_GROUP;

/**
 * 模块 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysModuleDto extends SysModulePo {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 菜单数据 */
    @Correlation(groupName = MODULE_SysMenu_GROUP, keyType = OperateConstants.SubKeyType.RECEIVE)
    private List<SysMenuDto> subList;
}
