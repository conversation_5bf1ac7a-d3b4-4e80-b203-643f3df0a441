package com.heju.system.api.annualReport.feign;

import com.heju.common.core.constant.basic.SecurityConstants;
import com.heju.common.core.constant.basic.ServiceConstants;
import com.heju.common.core.web.result.R;
import com.heju.system.api.annualReport.feign.factory.RemoteAnnualReportFallbackFactory;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;

/**
 * 年度报表服务
 *
 * <AUTHOR>
 */
@FeignClient(contextId = "remoteAnnualReportService", value = ServiceConstants.SYSTEM_SERVICE, fallbackFactory = RemoteAnnualReportFallbackFactory.class)
public interface RemoteAnnualReportService {
    @PostMapping("/report/check")
    R<Integer> check(@RequestHeader(SecurityConstants.ENTERPRISE_ID) Long enterpriseId,
                     @RequestHeader(SecurityConstants.SOURCE_NAME) String sourceName,
                     @RequestHeader(SecurityConstants.FROM_SOURCE) String source);
}
