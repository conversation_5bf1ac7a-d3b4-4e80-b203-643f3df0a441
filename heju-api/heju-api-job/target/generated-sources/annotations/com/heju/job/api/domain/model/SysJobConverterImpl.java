package com.heju.job.api.domain.model;

import com.github.pagehelper.Page;
import com.heju.job.api.domain.dto.SysJobDto;
import com.heju.job.api.domain.po.SysJobPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-21T14:09:28+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysJobConverterImpl implements SysJobConverter {

    @Override
    public SysJobDto mapperDto(SysJobPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysJobDto sysJobDto = new SysJobDto();

        sysJobDto.setId( arg0.getId() );
        sysJobDto.setSourceName( arg0.getSourceName() );
        sysJobDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysJobDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysJobDto.setSort( arg0.getSort() );
        sysJobDto.setRemark( arg0.getRemark() );
        sysJobDto.setCreateBy( arg0.getCreateBy() );
        sysJobDto.setCreateTime( arg0.getCreateTime() );
        sysJobDto.setUpdateBy( arg0.getUpdateBy() );
        sysJobDto.setUpdateTime( arg0.getUpdateTime() );
        sysJobDto.setDelFlag( arg0.getDelFlag() );
        sysJobDto.setCreateName( arg0.getCreateName() );
        sysJobDto.setUpdateName( arg0.getUpdateName() );
        sysJobDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysJobDto.setName( arg0.getName() );
        sysJobDto.setJobGroup( arg0.getJobGroup() );
        sysJobDto.setInvokeTarget( arg0.getInvokeTarget() );
        sysJobDto.setInvokeTenant( arg0.getInvokeTenant() );
        sysJobDto.setCronExpression( arg0.getCronExpression() );
        sysJobDto.setMisfirePolicy( arg0.getMisfirePolicy() );
        sysJobDto.setConcurrent( arg0.getConcurrent() );
        sysJobDto.setStatus( arg0.getStatus() );

        return sysJobDto;
    }

    @Override
    public List<SysJobDto> mapperDto(Collection<SysJobPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysJobDto> list = new ArrayList<SysJobDto>( arg0.size() );
        for ( SysJobPo sysJobPo : arg0 ) {
            list.add( mapperDto( sysJobPo ) );
        }

        return list;
    }

    @Override
    public Page<SysJobDto> mapperPageDto(Collection<SysJobPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysJobDto> page = new Page<SysJobDto>();
        for ( SysJobPo sysJobPo : arg0 ) {
            page.add( mapperDto( sysJobPo ) );
        }

        return page;
    }

    @Override
    public SysJobPo mapperPo(SysJobDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysJobPo sysJobPo = new SysJobPo();

        sysJobPo.setId( arg0.getId() );
        sysJobPo.setSourceName( arg0.getSourceName() );
        sysJobPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysJobPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysJobPo.setSort( arg0.getSort() );
        sysJobPo.setRemark( arg0.getRemark() );
        sysJobPo.setCreateBy( arg0.getCreateBy() );
        sysJobPo.setCreateTime( arg0.getCreateTime() );
        sysJobPo.setUpdateBy( arg0.getUpdateBy() );
        sysJobPo.setUpdateTime( arg0.getUpdateTime() );
        sysJobPo.setDelFlag( arg0.getDelFlag() );
        sysJobPo.setCreateName( arg0.getCreateName() );
        sysJobPo.setUpdateName( arg0.getUpdateName() );
        sysJobPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysJobPo.setName( arg0.getName() );
        sysJobPo.setJobGroup( arg0.getJobGroup() );
        sysJobPo.setInvokeTarget( arg0.getInvokeTarget() );
        sysJobPo.setInvokeTenant( arg0.getInvokeTenant() );
        sysJobPo.setCronExpression( arg0.getCronExpression() );
        sysJobPo.setMisfirePolicy( arg0.getMisfirePolicy() );
        sysJobPo.setConcurrent( arg0.getConcurrent() );
        sysJobPo.setStatus( arg0.getStatus() );

        return sysJobPo;
    }

    @Override
    public List<SysJobPo> mapperPo(Collection<SysJobDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysJobPo> list = new ArrayList<SysJobPo>( arg0.size() );
        for ( SysJobDto sysJobDto : arg0 ) {
            list.add( mapperPo( sysJobDto ) );
        }

        return list;
    }

    @Override
    public Page<SysJobPo> mapperPagePo(Collection<SysJobDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysJobPo> page = new Page<SysJobPo>();
        for ( SysJobDto sysJobDto : arg0 ) {
            page.add( mapperPo( sysJobDto ) );
        }

        return page;
    }
}
