10:16:45.671 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:16:46.886 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d7d046be-3ed5-4011-8741-452f241ef181_config-0
10:16:46.997 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 46 ms to scan 1 urls, producing 3 keys and 6 values 
10:16:47.054 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 4 keys and 9 values 
10:16:47.065 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 3 keys and 10 values 
10:16:47.079 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 5 values 
10:16:47.098 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 1 keys and 7 values 
10:16:47.108 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
10:16:47.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7d046be-3ed5-4011-8741-452f241ef181_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:16:47.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7d046be-3ed5-4011-8741-452f241ef181_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001dd813b71c0
10:16:47.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7d046be-3ed5-4011-8741-452f241ef181_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001dd813b73e0
10:16:47.116 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7d046be-3ed5-4011-8741-452f241ef181_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:16:47.117 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7d046be-3ed5-4011-8741-452f241ef181_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:16:47.129 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7d046be-3ed5-4011-8741-452f241ef181_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:16:48.413 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7d046be-3ed5-4011-8741-452f241ef181_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756001808079_127.0.0.1_11213
10:16:48.414 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7d046be-3ed5-4011-8741-452f241ef181_config-0] Notify connected event to listeners.
10:16:48.415 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7d046be-3ed5-4011-8741-452f241ef181_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:16:48.416 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d7d046be-3ed5-4011-8741-452f241ef181_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001dd814f8440
10:16:48.782 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:16:53.645 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:16:53.645 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:16:53.645 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:16:53.827 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:16:54.769 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:16:54.771 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:16:54.771 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:17:10.402 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:17:16.905 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3e3615ea-9478-4f85-9062-84d5d4d890fb
10:17:16.906 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e3615ea-9478-4f85-9062-84d5d4d890fb] RpcClient init label, labels = {module=naming, source=sdk}
10:17:16.909 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e3615ea-9478-4f85-9062-84d5d4d890fb] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:17:16.910 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e3615ea-9478-4f85-9062-84d5d4d890fb] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:17:16.911 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e3615ea-9478-4f85-9062-84d5d4d890fb] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:17:16.912 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e3615ea-9478-4f85-9062-84d5d4d890fb] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:17:17.044 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e3615ea-9478-4f85-9062-84d5d4d890fb] Success to connect to server [localhost:8848] on start up, connectionId = 1756001836926_127.0.0.1_11264
10:17:17.045 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e3615ea-9478-4f85-9062-84d5d4d890fb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:17:17.045 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e3615ea-9478-4f85-9062-84d5d4d890fb] Notify connected event to listeners.
10:17:17.045 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e3615ea-9478-4f85-9062-84d5d4d890fb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001dd814f8440
10:17:17.135 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:17:17.204 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:17:17.555 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 32.702 seconds (JVM running for 34.35)
10:17:17.589 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:17:17.591 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:17:17.592 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:17:17.644 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e3615ea-9478-4f85-9062-84d5d4d890fb] Receive server push request, request = NotifySubscriberRequest, requestId = 9
10:17:17.669 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e3615ea-9478-4f85-9062-84d5d4d890fb] Ack server push request, request = NotifySubscriberRequest, requestId = 9
10:17:18.065 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:20:54.502 [nacos-grpc-client-executor-52] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e3615ea-9478-4f85-9062-84d5d4d890fb] Receive server push request, request = NotifySubscriberRequest, requestId = 13
10:20:54.504 [nacos-grpc-client-executor-52] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e3615ea-9478-4f85-9062-84d5d4d890fb] Ack server push request, request = NotifySubscriberRequest, requestId = 13
10:20:55.816 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:20:55.817 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:20:56.139 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
10:20:56.139 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
18:54:36.298 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:54:36.301 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:54:36.642 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:54:36.642 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@307735dd[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:54:36.642 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756001836926_127.0.0.1_11264
18:54:36.645 [nacos-grpc-client-executor-6212] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756001836926_127.0.0.1_11264]Ignore complete event,isRunning:false,isAbandon=false
18:54:36.650 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5f726690[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 6213]
18:54:36.833 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:54:36.844 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
18:54:36.846 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
18:54:36.846 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
18:54:36.847 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
18:54:36.847 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:54:36.848 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:54:36.848 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
