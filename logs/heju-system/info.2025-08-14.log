09:17:36.345 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:17:37.613 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ad4426ce-f4da-4b13-bd75-26f01cff6c88_config-0
09:17:37.763 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 57 ms to scan 1 urls, producing 3 keys and 6 values 
09:17:37.844 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 4 keys and 9 values 
09:17:37.859 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 3 keys and 10 values 
09:17:37.884 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 18 ms to scan 1 urls, producing 1 keys and 5 values 
09:17:37.906 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 1 keys and 7 values 
09:17:37.946 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 35 ms to scan 1 urls, producing 2 keys and 8 values 
09:17:37.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad4426ce-f4da-4b13-bd75-26f01cff6c88_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:17:37.974 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad4426ce-f4da-4b13-bd75-26f01cff6c88_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001a1ba3b7d00
09:17:37.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad4426ce-f4da-4b13-bd75-26f01cff6c88_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001a1ba3b8000
09:17:37.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad4426ce-f4da-4b13-bd75-26f01cff6c88_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:17:37.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad4426ce-f4da-4b13-bd75-26f01cff6c88_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:17:38.007 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad4426ce-f4da-4b13-bd75-26f01cff6c88_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:17:39.755 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad4426ce-f4da-4b13-bd75-26f01cff6c88_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755134259308_127.0.0.1_14571
09:17:39.756 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad4426ce-f4da-4b13-bd75-26f01cff6c88_config-0] Notify connected event to listeners.
09:17:39.756 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad4426ce-f4da-4b13-bd75-26f01cff6c88_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:17:39.757 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad4426ce-f4da-4b13-bd75-26f01cff6c88_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001a1ba4f0228
09:17:40.332 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:17:48.773 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:17:48.774 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:17:48.775 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:17:49.149 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:17:50.504 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:17:50.506 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:17:50.508 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:18:04.739 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:18:07.929 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5e688dc8-05d2-4852-97a5-7397c4a5d980
09:18:07.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e688dc8-05d2-4852-97a5-7397c4a5d980] RpcClient init label, labels = {module=naming, source=sdk}
09:18:07.932 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e688dc8-05d2-4852-97a5-7397c4a5d980] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:18:07.932 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e688dc8-05d2-4852-97a5-7397c4a5d980] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:18:07.933 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e688dc8-05d2-4852-97a5-7397c4a5d980] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:18:07.933 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e688dc8-05d2-4852-97a5-7397c4a5d980] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:18:08.067 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e688dc8-05d2-4852-97a5-7397c4a5d980] Success to connect to server [localhost:8848] on start up, connectionId = 1755134287939_127.0.0.1_14736
09:18:08.068 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e688dc8-05d2-4852-97a5-7397c4a5d980] Notify connected event to listeners.
09:18:08.068 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e688dc8-05d2-4852-97a5-7397c4a5d980] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:08.068 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e688dc8-05d2-4852-97a5-7397c4a5d980] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001a1ba4f0228
09:18:08.151 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:18:08.195 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
09:18:08.339 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 32.909 seconds (JVM running for 36.45)
09:18:08.357 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:18:08.358 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:18:08.358 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:18:08.643 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e688dc8-05d2-4852-97a5-7397c4a5d980] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:18:08.658 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e688dc8-05d2-4852-97a5-7397c4a5d980] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:19:40.710 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:19:47.793 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e688dc8-05d2-4852-97a5-7397c4a5d980] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:19:47.795 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e688dc8-05d2-4852-97a5-7397c4a5d980] Ack server push request, request = NotifySubscriberRequest, requestId = 13
09:19:49.162 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:19:49.162 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:19:49.331 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:19:49.332 [http-nio-9600-exec-7] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
11:39:40.579 [nacos-grpc-client-executor-1712] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e688dc8-05d2-4852-97a5-7397c4a5d980] Receive server push request, request = NotifySubscriberRequest, requestId = 15
11:39:40.605 [nacos-grpc-client-executor-1712] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e688dc8-05d2-4852-97a5-7397c4a5d980] Ack server push request, request = NotifySubscriberRequest, requestId = 15
11:40:12.019 [nacos-grpc-client-executor-1719] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e688dc8-05d2-4852-97a5-7397c4a5d980] Receive server push request, request = NotifySubscriberRequest, requestId = 19
11:40:12.035 [nacos-grpc-client-executor-1719] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e688dc8-05d2-4852-97a5-7397c4a5d980] Ack server push request, request = NotifySubscriberRequest, requestId = 19
11:43:43.834 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:43:43.845 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:43:44.185 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:43:44.187 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@732abaa2[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:43:44.187 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755134287939_127.0.0.1_14736
11:43:44.191 [nacos-grpc-client-executor-1754] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755134287939_127.0.0.1_14736]Ignore complete event,isRunning:false,isAbandon=false
11:43:44.196 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@22bda3b3[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1755]
11:43:44.407 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:43:44.407 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
11:43:44.420 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
11:43:44.420 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:43:44.420 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:43:44.420 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:43:44.423 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:43:44.423 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:43:54.778 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:43:55.583 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 402753a3-8b90-4cda-9757-19edddd5050b_config-0
11:43:55.657 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 34 ms to scan 1 urls, producing 3 keys and 6 values 
11:43:55.693 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
11:43:55.703 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
11:43:55.712 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
11:43:55.721 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
11:43:55.733 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
11:43:55.735 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [402753a3-8b90-4cda-9757-19edddd5050b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:43:55.735 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [402753a3-8b90-4cda-9757-19edddd5050b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000017fce39e480
11:43:55.736 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [402753a3-8b90-4cda-9757-19edddd5050b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000017fce39e6a0
11:43:55.737 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [402753a3-8b90-4cda-9757-19edddd5050b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:43:55.737 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [402753a3-8b90-4cda-9757-19edddd5050b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:43:55.743 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [402753a3-8b90-4cda-9757-19edddd5050b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:43:56.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [402753a3-8b90-4cda-9757-19edddd5050b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755143036525_127.0.0.1_11261
11:43:56.756 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [402753a3-8b90-4cda-9757-19edddd5050b_config-0] Notify connected event to listeners.
11:43:56.757 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [402753a3-8b90-4cda-9757-19edddd5050b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:43:56.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [402753a3-8b90-4cda-9757-19edddd5050b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000017fce518228
11:43:56.953 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:44:00.960 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:44:00.960 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:44:00.960 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:44:01.129 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:44:01.858 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:44:01.861 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:44:01.862 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:44:10.075 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:44:13.319 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 66c256b7-c07b-41a1-a8f0-05c17c03a501
11:44:13.319 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66c256b7-c07b-41a1-a8f0-05c17c03a501] RpcClient init label, labels = {module=naming, source=sdk}
11:44:13.322 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66c256b7-c07b-41a1-a8f0-05c17c03a501] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:44:13.322 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66c256b7-c07b-41a1-a8f0-05c17c03a501] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:44:13.323 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66c256b7-c07b-41a1-a8f0-05c17c03a501] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:44:13.323 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66c256b7-c07b-41a1-a8f0-05c17c03a501] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:44:13.442 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66c256b7-c07b-41a1-a8f0-05c17c03a501] Success to connect to server [localhost:8848] on start up, connectionId = 1755143053330_127.0.0.1_11343
11:44:13.443 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66c256b7-c07b-41a1-a8f0-05c17c03a501] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:44:13.443 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66c256b7-c07b-41a1-a8f0-05c17c03a501] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000017fce518228
11:44:13.443 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66c256b7-c07b-41a1-a8f0-05c17c03a501] Notify connected event to listeners.
11:44:13.509 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:44:13.549 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
11:44:13.667 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.478 seconds (JVM running for 20.447)
11:44:13.681 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:44:13.681 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:44:13.682 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:44:13.873 [RMI TCP Connection(2)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:44:14.108 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66c256b7-c07b-41a1-a8f0-05c17c03a501] Receive server push request, request = NotifySubscriberRequest, requestId = 24
11:44:14.151 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66c256b7-c07b-41a1-a8f0-05c17c03a501] Ack server push request, request = NotifySubscriberRequest, requestId = 24
11:45:12.557 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
11:45:12.557 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:45:12.557 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:45:12.567 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
11:45:12.588 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
11:45:12.590 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:20:53.498 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:20:53.506 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:20:53.833 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:20:53.833 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5880d7cb[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:20:53.835 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755143053330_127.0.0.1_11343
12:20:53.836 [nacos-grpc-client-executor-400] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755143053330_127.0.0.1_11343]Ignore complete event,isRunning:false,isAbandon=false
12:20:53.836 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@715fa41a[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 400]
12:20:54.008 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:20:54.008 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
12:20:54.013 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
12:20:54.013 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:20:54.015 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:20:54.015 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:21:00.110 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:21:01.005 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c4b2b92e-88f6-4e4c-8865-548c52dac96e_config-0
12:21:01.077 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 35 ms to scan 1 urls, producing 3 keys and 6 values 
12:21:01.123 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
12:21:01.134 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
12:21:01.151 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 5 values 
12:21:01.161 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
12:21:01.170 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
12:21:01.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4b2b92e-88f6-4e4c-8865-548c52dac96e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:21:01.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4b2b92e-88f6-4e4c-8865-548c52dac96e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001bcd439e8d8
12:21:01.174 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4b2b92e-88f6-4e4c-8865-548c52dac96e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001bcd439eaf8
12:21:01.174 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4b2b92e-88f6-4e4c-8865-548c52dac96e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:21:01.176 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4b2b92e-88f6-4e4c-8865-548c52dac96e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:21:01.187 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4b2b92e-88f6-4e4c-8865-548c52dac96e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:21:02.224 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4b2b92e-88f6-4e4c-8865-548c52dac96e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755145261998_127.0.0.1_4382
12:21:02.226 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4b2b92e-88f6-4e4c-8865-548c52dac96e_config-0] Notify connected event to listeners.
12:21:02.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4b2b92e-88f6-4e4c-8865-548c52dac96e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:21:02.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4b2b92e-88f6-4e4c-8865-548c52dac96e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001bcd4518668
12:21:02.417 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:21:06.358 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
12:21:06.358 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:21:06.358 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:21:06.565 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:21:07.459 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:21:07.461 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:21:07.462 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:21:15.829 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:21:19.078 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cd94d1d3-5636-4b6a-b9f3-7da7d400061c
12:21:19.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cd94d1d3-5636-4b6a-b9f3-7da7d400061c] RpcClient init label, labels = {module=naming, source=sdk}
12:21:19.082 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cd94d1d3-5636-4b6a-b9f3-7da7d400061c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:21:19.082 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cd94d1d3-5636-4b6a-b9f3-7da7d400061c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:21:19.083 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cd94d1d3-5636-4b6a-b9f3-7da7d400061c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:21:19.084 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cd94d1d3-5636-4b6a-b9f3-7da7d400061c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:21:19.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cd94d1d3-5636-4b6a-b9f3-7da7d400061c] Success to connect to server [localhost:8848] on start up, connectionId = 1755145279095_127.0.0.1_4473
12:21:19.218 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cd94d1d3-5636-4b6a-b9f3-7da7d400061c] Notify connected event to listeners.
12:21:19.218 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cd94d1d3-5636-4b6a-b9f3-7da7d400061c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:21:19.218 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cd94d1d3-5636-4b6a-b9f3-7da7d400061c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001bcd4518668
12:21:19.316 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
12:21:19.360 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
12:21:19.500 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.013 seconds (JVM running for 21.134)
12:21:19.532 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
12:21:19.533 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
12:21:19.533 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
12:21:19.789 [RMI TCP Connection(4)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:21:19.866 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cd94d1d3-5636-4b6a-b9f3-7da7d400061c] Receive server push request, request = NotifySubscriberRequest, requestId = 29
12:21:19.892 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cd94d1d3-5636-4b6a-b9f3-7da7d400061c] Ack server push request, request = NotifySubscriberRequest, requestId = 29
12:21:37.806 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
12:21:37.806 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
12:21:37.806 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:21:37.811 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
12:21:37.827 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
12:21:37.827 [http-nio-9600-exec-6] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:28:27.725 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:28:27.725 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:28:28.079 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:28:28.079 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2b280a2b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:28:28.081 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755145279095_127.0.0.1_4473
12:28:28.083 [nacos-grpc-client-executor-95] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755145279095_127.0.0.1_4473]Ignore complete event,isRunning:false,isAbandon=false
12:28:28.087 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@47cb8948[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 96]
12:28:28.275 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:28:28.275 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
12:28:28.280 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
12:28:28.280 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:28:28.282 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:28:28.282 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:28:38.855 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:28:39.899 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5b7f8c9a-e9af-45c8-87ee-7b9352b22460_config-0
12:28:39.976 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 39 ms to scan 1 urls, producing 3 keys and 6 values 
12:28:40.024 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
12:28:40.032 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
12:28:40.054 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 1 keys and 5 values 
12:28:40.064 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
12:28:40.080 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
12:28:40.086 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b7f8c9a-e9af-45c8-87ee-7b9352b22460_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:28:40.086 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b7f8c9a-e9af-45c8-87ee-7b9352b22460_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001bc4439eaf8
12:28:40.086 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b7f8c9a-e9af-45c8-87ee-7b9352b22460_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001bc4439ed18
12:28:40.086 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b7f8c9a-e9af-45c8-87ee-7b9352b22460_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:28:40.088 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b7f8c9a-e9af-45c8-87ee-7b9352b22460_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:28:40.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b7f8c9a-e9af-45c8-87ee-7b9352b22460_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:28:41.138 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b7f8c9a-e9af-45c8-87ee-7b9352b22460_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755145720911_127.0.0.1_5819
12:28:41.140 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b7f8c9a-e9af-45c8-87ee-7b9352b22460_config-0] Notify connected event to listeners.
12:28:41.140 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b7f8c9a-e9af-45c8-87ee-7b9352b22460_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:28:41.142 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b7f8c9a-e9af-45c8-87ee-7b9352b22460_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001bc44518ad8
12:28:41.324 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:28:46.133 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
12:28:46.134 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:28:46.134 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:28:46.528 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:28:47.906 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:28:47.906 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:28:47.906 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:28:57.376 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:29:00.661 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 94c79e5c-c3a8-4631-8bd9-eb7ebd5f3a0f
12:29:00.661 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94c79e5c-c3a8-4631-8bd9-eb7ebd5f3a0f] RpcClient init label, labels = {module=naming, source=sdk}
12:29:00.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94c79e5c-c3a8-4631-8bd9-eb7ebd5f3a0f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:29:00.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94c79e5c-c3a8-4631-8bd9-eb7ebd5f3a0f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:29:00.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94c79e5c-c3a8-4631-8bd9-eb7ebd5f3a0f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:29:00.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94c79e5c-c3a8-4631-8bd9-eb7ebd5f3a0f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:29:00.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94c79e5c-c3a8-4631-8bd9-eb7ebd5f3a0f] Success to connect to server [localhost:8848] on start up, connectionId = 1755145740664_127.0.0.1_5887
12:29:00.789 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94c79e5c-c3a8-4631-8bd9-eb7ebd5f3a0f] Notify connected event to listeners.
12:29:00.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94c79e5c-c3a8-4631-8bd9-eb7ebd5f3a0f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:29:00.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94c79e5c-c3a8-4631-8bd9-eb7ebd5f3a0f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001bc44518ad8
12:29:00.852 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
12:29:00.891 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
12:29:01.032 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 23.041 seconds (JVM running for 24.258)
12:29:01.048 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
12:29:01.049 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
12:29:01.049 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
12:29:01.355 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94c79e5c-c3a8-4631-8bd9-eb7ebd5f3a0f] Receive server push request, request = NotifySubscriberRequest, requestId = 34
12:29:01.365 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [94c79e5c-c3a8-4631-8bd9-eb7ebd5f3a0f] Ack server push request, request = NotifySubscriberRequest, requestId = 34
12:29:01.611 [RMI TCP Connection(3)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:38:09.277 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:38:09.287 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:38:09.639 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:38:09.639 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7a933662[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:38:09.639 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755145740664_127.0.0.1_5887
12:38:09.642 [nacos-grpc-client-executor-120] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755145740664_127.0.0.1_5887]Ignore complete event,isRunning:false,isAbandon=false
12:38:09.648 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5a5f486[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 121]
12:38:09.806 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:38:09.810 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:38:09.813 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:38:09.813 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:38:16.530 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:38:17.417 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a13054f5-66dc-46a3-b161-48424fa6e43e_config-0
12:38:17.506 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 39 ms to scan 1 urls, producing 3 keys and 6 values 
12:38:17.549 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
12:38:17.565 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 3 keys and 10 values 
12:38:17.581 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
12:38:17.597 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
12:38:17.612 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
12:38:17.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a13054f5-66dc-46a3-b161-48424fa6e43e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:38:17.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a13054f5-66dc-46a3-b161-48424fa6e43e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002588139eaf8
12:38:17.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a13054f5-66dc-46a3-b161-48424fa6e43e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002588139ed18
12:38:17.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a13054f5-66dc-46a3-b161-48424fa6e43e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:38:17.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a13054f5-66dc-46a3-b161-48424fa6e43e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:38:17.628 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a13054f5-66dc-46a3-b161-48424fa6e43e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:38:18.806 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a13054f5-66dc-46a3-b161-48424fa6e43e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755146298559_127.0.0.1_7366
12:38:18.806 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a13054f5-66dc-46a3-b161-48424fa6e43e_config-0] Notify connected event to listeners.
12:38:18.806 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a13054f5-66dc-46a3-b161-48424fa6e43e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:38:18.811 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a13054f5-66dc-46a3-b161-48424fa6e43e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000025881518ad8
12:38:18.983 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:38:23.318 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
12:38:23.318 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:38:23.320 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:38:23.512 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:38:24.434 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:38:24.436 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:38:24.436 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:38:33.123 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:38:36.194 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 37bc1f6b-faa4-401d-b0cd-9a06e08f092e
12:38:36.194 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37bc1f6b-faa4-401d-b0cd-9a06e08f092e] RpcClient init label, labels = {module=naming, source=sdk}
12:38:36.196 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37bc1f6b-faa4-401d-b0cd-9a06e08f092e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:38:36.196 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37bc1f6b-faa4-401d-b0cd-9a06e08f092e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:38:36.197 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37bc1f6b-faa4-401d-b0cd-9a06e08f092e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:38:36.197 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37bc1f6b-faa4-401d-b0cd-9a06e08f092e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:38:36.335 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37bc1f6b-faa4-401d-b0cd-9a06e08f092e] Success to connect to server [localhost:8848] on start up, connectionId = 1755146316206_127.0.0.1_7449
12:38:36.336 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37bc1f6b-faa4-401d-b0cd-9a06e08f092e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:38:36.336 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37bc1f6b-faa4-401d-b0cd-9a06e08f092e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000025881518ad8
12:38:36.336 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37bc1f6b-faa4-401d-b0cd-9a06e08f092e] Notify connected event to listeners.
12:38:36.410 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
12:38:36.446 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
12:38:36.580 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.637 seconds (JVM running for 21.719)
12:38:36.595 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
12:38:36.595 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
12:38:36.595 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
12:38:36.984 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37bc1f6b-faa4-401d-b0cd-9a06e08f092e] Receive server push request, request = NotifySubscriberRequest, requestId = 39
12:38:37.006 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37bc1f6b-faa4-401d-b0cd-9a06e08f092e] Ack server push request, request = NotifySubscriberRequest, requestId = 39
12:38:37.053 [RMI TCP Connection(7)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:38:58.303 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
12:38:58.304 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:38:58.304 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
12:38:58.308 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
12:38:58.320 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
12:38:58.321 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:07:00.211 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:07:00.212 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:07:00.574 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:07:00.574 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3ca372d2[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:07:00.574 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755146316206_127.0.0.1_7449
15:07:00.574 [nacos-grpc-client-executor-1789] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755146316206_127.0.0.1_7449]Ignore complete event,isRunning:false,isAbandon=false
15:07:00.574 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@61761dc5[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1790]
15:07:00.706 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:07:00.706 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
15:07:00.706 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
15:07:00.706 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:07:00.706 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:07:00.706 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:07:26.335 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:07:26.879 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7b271425-8dfa-47e5-9b08-e73654f6c0d1_config-0
15:07:26.935 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 3 keys and 6 values 
15:07:26.962 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
15:07:26.962 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
15:07:26.962 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
15:07:26.978 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
15:07:26.978 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
15:07:26.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b271425-8dfa-47e5-9b08-e73654f6c0d1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:07:26.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b271425-8dfa-47e5-9b08-e73654f6c0d1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000254393b8638
15:07:26.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b271425-8dfa-47e5-9b08-e73654f6c0d1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000254393b8858
15:07:26.993 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b271425-8dfa-47e5-9b08-e73654f6c0d1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:07:26.993 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b271425-8dfa-47e5-9b08-e73654f6c0d1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:07:26.996 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b271425-8dfa-47e5-9b08-e73654f6c0d1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:07:27.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b271425-8dfa-47e5-9b08-e73654f6c0d1_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755155247554_127.0.0.1_14213
15:07:27.754 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b271425-8dfa-47e5-9b08-e73654f6c0d1_config-0] Notify connected event to listeners.
15:07:27.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b271425-8dfa-47e5-9b08-e73654f6c0d1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:07:27.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b271425-8dfa-47e5-9b08-e73654f6c0d1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000254394f0228
15:07:27.882 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:07:30.554 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:07:30.555 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:07:30.555 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:07:30.677 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:07:31.268 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:07:31.268 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:07:31.268 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:07:36.428 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:07:38.419 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fa3e7281-677c-4e27-a07e-8903587c5e3b
15:07:38.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa3e7281-677c-4e27-a07e-8903587c5e3b] RpcClient init label, labels = {module=naming, source=sdk}
15:07:38.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa3e7281-677c-4e27-a07e-8903587c5e3b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:07:38.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa3e7281-677c-4e27-a07e-8903587c5e3b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:07:38.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa3e7281-677c-4e27-a07e-8903587c5e3b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:07:38.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa3e7281-677c-4e27-a07e-8903587c5e3b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:07:38.550 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa3e7281-677c-4e27-a07e-8903587c5e3b] Success to connect to server [localhost:8848] on start up, connectionId = 1755155258439_127.0.0.1_14230
15:07:38.550 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa3e7281-677c-4e27-a07e-8903587c5e3b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:07:38.550 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa3e7281-677c-4e27-a07e-8903587c5e3b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000254394f0228
15:07:38.550 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa3e7281-677c-4e27-a07e-8903587c5e3b] Notify connected event to listeners.
15:07:38.591 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:07:38.612 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
15:07:38.698 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.807 seconds (JVM running for 14.657)
15:07:38.714 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:07:38.714 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:07:38.714 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:07:39.145 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa3e7281-677c-4e27-a07e-8903587c5e3b] Receive server push request, request = NotifySubscriberRequest, requestId = 44
15:07:39.161 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa3e7281-677c-4e27-a07e-8903587c5e3b] Ack server push request, request = NotifySubscriberRequest, requestId = 44
15:12:01.734 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:12:02.820 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:12:02.820 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:45:31.478 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:45:31.478 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:45:31.822 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:45:31.822 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2c248a73[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:45:31.822 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755155258439_127.0.0.1_14230
16:45:31.822 [nacos-grpc-client-executor-814] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755155258439_127.0.0.1_14230]Ignore complete event,isRunning:false,isAbandon=false
16:45:31.822 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@27e57255[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 815]
16:45:32.019 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:45:32.019 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:45:32.019 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:45:32.019 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:45:32.033 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:45:32.033 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:45:43.153 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:45:44.182 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 32425abd-665a-43bb-9325-0be2ec8c13d7_config-0
16:45:44.259 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 47 ms to scan 1 urls, producing 3 keys and 6 values 
16:45:44.313 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
16:45:44.327 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 3 keys and 10 values 
16:45:44.337 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
16:45:44.344 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
16:45:44.356 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
16:45:44.358 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32425abd-665a-43bb-9325-0be2ec8c13d7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:45:44.358 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32425abd-665a-43bb-9325-0be2ec8c13d7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000012bd63b6d38
16:45:44.358 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32425abd-665a-43bb-9325-0be2ec8c13d7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000012bd63b6f58
16:45:44.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32425abd-665a-43bb-9325-0be2ec8c13d7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:45:44.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32425abd-665a-43bb-9325-0be2ec8c13d7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:45:44.370 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32425abd-665a-43bb-9325-0be2ec8c13d7_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:45:45.406 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32425abd-665a-43bb-9325-0be2ec8c13d7_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755161145168_127.0.0.1_9206
16:45:45.406 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32425abd-665a-43bb-9325-0be2ec8c13d7_config-0] Notify connected event to listeners.
16:45:45.407 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32425abd-665a-43bb-9325-0be2ec8c13d7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:45:45.407 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32425abd-665a-43bb-9325-0be2ec8c13d7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000012bd64f0ad8
16:45:45.520 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:45:48.660 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:45:48.660 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:45:48.660 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:45:48.798 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:45:49.415 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:45:49.415 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:45:49.415 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:45:55.644 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:45:58.312 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 426374b2-4daa-4dc4-b701-3842e8a34743
16:45:58.312 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [426374b2-4daa-4dc4-b701-3842e8a34743] RpcClient init label, labels = {module=naming, source=sdk}
16:45:58.312 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [426374b2-4daa-4dc4-b701-3842e8a34743] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:45:58.312 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [426374b2-4daa-4dc4-b701-3842e8a34743] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:45:58.312 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [426374b2-4daa-4dc4-b701-3842e8a34743] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:45:58.316 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [426374b2-4daa-4dc4-b701-3842e8a34743] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:45:58.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [426374b2-4daa-4dc4-b701-3842e8a34743] Success to connect to server [localhost:8848] on start up, connectionId = 1755161158323_127.0.0.1_9238
16:45:58.453 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [426374b2-4daa-4dc4-b701-3842e8a34743] Notify connected event to listeners.
16:45:58.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [426374b2-4daa-4dc4-b701-3842e8a34743] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:45:58.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [426374b2-4daa-4dc4-b701-3842e8a34743] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000012bd64f0ad8
16:45:58.499 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:45:58.525 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
16:45:58.627 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 16.407 seconds (JVM running for 18.239)
16:45:58.639 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:45:58.639 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:45:58.639 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:45:59.048 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [426374b2-4daa-4dc4-b701-3842e8a34743] Receive server push request, request = NotifySubscriberRequest, requestId = 49
16:45:59.062 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [426374b2-4daa-4dc4-b701-3842e8a34743] Ack server push request, request = NotifySubscriberRequest, requestId = 49
16:46:03.786 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:46:04.950 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:46:04.950 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:51:34.076 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:51:34.078 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:51:34.402 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:51:34.402 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7865e19b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:51:34.402 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755161158323_127.0.0.1_9238
16:51:34.404 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755161158323_127.0.0.1_9238]Ignore complete event,isRunning:false,isAbandon=false
16:51:34.406 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@51fbdc47[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 25]
16:51:34.530 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:51:34.530 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:51:34.530 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:51:34.546 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:51:34.546 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:51:34.546 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:51:40.322 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:51:40.924 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3773990c-84cc-4b42-843a-5fa96b959fe6_config-0
16:51:40.974 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
16:51:41.005 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
16:51:41.012 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
16:51:41.020 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
16:51:41.027 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
16:51:41.035 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
16:51:41.038 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3773990c-84cc-4b42-843a-5fa96b959fe6_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:51:41.038 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3773990c-84cc-4b42-843a-5fa96b959fe6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001f38c3ceaf8
16:51:41.039 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3773990c-84cc-4b42-843a-5fa96b959fe6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001f38c3ced18
16:51:41.039 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3773990c-84cc-4b42-843a-5fa96b959fe6_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:51:41.039 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3773990c-84cc-4b42-843a-5fa96b959fe6_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:51:41.046 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3773990c-84cc-4b42-843a-5fa96b959fe6_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:51:41.778 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3773990c-84cc-4b42-843a-5fa96b959fe6_config-0] Success to connect to server [localhost:8848] on start up, connectionId = ********01595_127.0.0.1_9681
16:51:41.778 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3773990c-84cc-4b42-843a-5fa96b959fe6_config-0] Notify connected event to listeners.
16:51:41.779 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3773990c-84cc-4b42-843a-5fa96b959fe6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:51:41.779 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3773990c-84cc-4b42-843a-5fa96b959fe6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f38c508668
16:51:41.864 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:51:44.465 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:51:44.465 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:51:44.465 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:51:44.589 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:51:45.207 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:51:45.208 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:51:45.208 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:51:51.054 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:51:54.392 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 822aef82-2be7-4b95-9026-c84d2797bbd5
16:51:54.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [822aef82-2be7-4b95-9026-c84d2797bbd5] RpcClient init label, labels = {module=naming, source=sdk}
16:51:54.394 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [822aef82-2be7-4b95-9026-c84d2797bbd5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:51:54.394 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [822aef82-2be7-4b95-9026-c84d2797bbd5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:51:54.395 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [822aef82-2be7-4b95-9026-c84d2797bbd5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:51:54.395 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [822aef82-2be7-4b95-9026-c84d2797bbd5] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:51:54.526 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [822aef82-2be7-4b95-9026-c84d2797bbd5] Success to connect to server [localhost:8848] on start up, connectionId = ********14404_127.0.0.1_9710
16:51:54.526 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [822aef82-2be7-4b95-9026-c84d2797bbd5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:51:54.526 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [822aef82-2be7-4b95-9026-c84d2797bbd5] Notify connected event to listeners.
16:51:54.526 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [822aef82-2be7-4b95-9026-c84d2797bbd5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f38c508668
16:51:54.581 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:51:54.612 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
16:51:54.743 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 14.942 seconds (JVM running for 15.81)
16:51:54.759 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:51:54.759 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:51:54.760 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:51:55.068 [RMI TCP Connection(5)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:51:55.070 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [822aef82-2be7-4b95-9026-c84d2797bbd5] Receive server push request, request = NotifySubscriberRequest, requestId = 54
16:51:55.089 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [822aef82-2be7-4b95-9026-c84d2797bbd5] Ack server push request, request = NotifySubscriberRequest, requestId = 54
16:52:14.772 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:52:14.772 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:52:14.775 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
16:52:14.776 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
16:52:14.778 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:52:14.786 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:52:14.786 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:52:14.787 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
16:52:14.788 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
16:52:14.788 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:00:50.146 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:00:50.146 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:00:50.484 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:00:50.484 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@58bde85c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:00:50.484 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection ********14404_127.0.0.1_9710
20:00:50.486 [nacos-grpc-client-executor-2221] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [********14404_127.0.0.1_9710]Ignore complete event,isRunning:false,isAbandon=false
20:00:50.498 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@11d47a27[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2222]
20:00:50.661 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:00:50.661 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
20:00:50.661 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
20:00:50.661 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:00:50.661 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:00:50.661 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:01:20.996 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:01:21.773 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of df5c68b6-3c79-4de4-b0a7-01e10c99306b_config-0
20:01:21.840 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 34 ms to scan 1 urls, producing 3 keys and 6 values 
20:01:21.875 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
20:01:21.881 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
20:01:21.894 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
20:01:21.904 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
20:01:21.914 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
20:01:21.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df5c68b6-3c79-4de4-b0a7-01e10c99306b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:01:21.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df5c68b6-3c79-4de4-b0a7-01e10c99306b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002a53d3b7b00
20:01:21.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df5c68b6-3c79-4de4-b0a7-01e10c99306b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000002a53d3b7d20
20:01:21.920 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df5c68b6-3c79-4de4-b0a7-01e10c99306b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:01:21.920 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df5c68b6-3c79-4de4-b0a7-01e10c99306b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:01:21.920 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df5c68b6-3c79-4de4-b0a7-01e10c99306b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:01:22.815 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df5c68b6-3c79-4de4-b0a7-01e10c99306b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755172882606_127.0.0.1_11942
20:01:22.815 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df5c68b6-3c79-4de4-b0a7-01e10c99306b_config-0] Notify connected event to listeners.
20:01:22.815 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df5c68b6-3c79-4de4-b0a7-01e10c99306b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:01:22.815 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df5c68b6-3c79-4de4-b0a7-01e10c99306b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002a53d4efb88
20:01:23.024 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:01:26.492 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
20:01:26.492 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:01:26.492 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
20:01:26.652 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:01:27.252 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
20:01:27.252 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
20:01:27.252 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
20:01:34.185 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
20:01:37.219 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5e3cb445-a0f1-40ab-9626-be9c62e4ab7a
20:01:37.219 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3cb445-a0f1-40ab-9626-be9c62e4ab7a] RpcClient init label, labels = {module=naming, source=sdk}
20:01:37.219 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3cb445-a0f1-40ab-9626-be9c62e4ab7a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
20:01:37.219 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3cb445-a0f1-40ab-9626-be9c62e4ab7a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
20:01:37.219 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3cb445-a0f1-40ab-9626-be9c62e4ab7a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
20:01:37.219 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3cb445-a0f1-40ab-9626-be9c62e4ab7a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:01:37.345 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3cb445-a0f1-40ab-9626-be9c62e4ab7a] Success to connect to server [localhost:8848] on start up, connectionId = 1755172897230_127.0.0.1_11967
20:01:37.346 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3cb445-a0f1-40ab-9626-be9c62e4ab7a] Notify connected event to listeners.
20:01:37.346 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3cb445-a0f1-40ab-9626-be9c62e4ab7a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:01:37.346 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3cb445-a0f1-40ab-9626-be9c62e4ab7a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002a53d4efb88
20:01:37.385 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
20:01:37.424 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
20:01:37.529 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 17.003 seconds (JVM running for 18.943)
20:01:37.532 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
20:01:37.532 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
20:01:37.532 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
20:01:37.901 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3cb445-a0f1-40ab-9626-be9c62e4ab7a] Receive server push request, request = NotifySubscriberRequest, requestId = 59
20:01:37.913 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3cb445-a0f1-40ab-9626-be9c62e4ab7a] Ack server push request, request = NotifySubscriberRequest, requestId = 59
20:02:44.122 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:02:45.861 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3cb445-a0f1-40ab-9626-be9c62e4ab7a] Receive server push request, request = NotifySubscriberRequest, requestId = 62
20:02:45.861 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3cb445-a0f1-40ab-9626-be9c62e4ab7a] Ack server push request, request = NotifySubscriberRequest, requestId = 62
20:02:47.212 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
20:02:47.218 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:16:46.939 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5e3cb445-a0f1-40ab-9626-be9c62e4ab7a] Server check success, currentServer is localhost:8848 
20:30:12.932 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:30:12.936 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:30:13.274 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:30:13.274 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@676e3061[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:30:13.274 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755172897230_127.0.0.1_11967
20:30:13.276 [nacos-grpc-client-executor-195] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755172897230_127.0.0.1_11967]Ignore complete event,isRunning:false,isAbandon=false
20:30:13.277 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@ea47da5[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 196]
20:30:13.434 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:30:13.437 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
20:30:13.449 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
20:30:13.449 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:30:13.451 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:30:13.451 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
