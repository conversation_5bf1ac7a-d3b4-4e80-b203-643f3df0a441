13:11:39.707 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:11:41.072 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8c3fde52-3804-43d7-8a99-29e966104a25_config-0
13:11:41.199 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 48 ms to scan 1 urls, producing 3 keys and 6 values 
13:11:41.268 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 4 keys and 9 values 
13:11:41.281 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 3 keys and 10 values 
13:11:41.295 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 5 values 
13:11:41.309 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 7 values 
13:11:41.325 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
13:11:41.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c3fde52-3804-43d7-8a99-29e966104a25_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:11:41.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c3fde52-3804-43d7-8a99-29e966104a25_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002663a39e690
13:11:41.331 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c3fde52-3804-43d7-8a99-29e966104a25_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002663a39e8b0
13:11:41.331 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c3fde52-3804-43d7-8a99-29e966104a25_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:11:41.332 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c3fde52-3804-43d7-8a99-29e966104a25_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:11:41.346 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c3fde52-3804-43d7-8a99-29e966104a25_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:11:42.568 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c3fde52-3804-43d7-8a99-29e966104a25_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754889102285_127.0.0.1_13784
13:11:42.569 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c3fde52-3804-43d7-8a99-29e966104a25_config-0] Notify connected event to listeners.
13:11:42.569 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c3fde52-3804-43d7-8a99-29e966104a25_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:11:42.570 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8c3fde52-3804-43d7-8a99-29e966104a25_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002663a518228
13:11:42.720 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:11:47.713 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:11:47.714 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:11:47.714 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:11:47.885 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:11:48.583 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:11:48.584 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:11:48.584 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:11:57.739 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:12:03.601 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-180c-4df5-9f92-2321d668c0c0
13:12:03.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-180c-4df5-9f92-2321d668c0c0] RpcClient init label, labels = {module=naming, source=sdk}
13:12:03.605 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-180c-4df5-9f92-2321d668c0c0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:12:03.605 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-180c-4df5-9f92-2321d668c0c0] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:12:03.607 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-180c-4df5-9f92-2321d668c0c0] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:12:03.607 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-180c-4df5-9f92-2321d668c0c0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:12:03.851 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-180c-4df5-9f92-2321d668c0c0] Success to connect to server [localhost:8848] on start up, connectionId = 1754889123618_127.0.0.1_13863
13:12:03.852 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-180c-4df5-9f92-2321d668c0c0] Notify connected event to listeners.
13:12:03.854 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-180c-4df5-9f92-2321d668c0c0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:12:03.854 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-180c-4df5-9f92-2321d668c0c0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002663a518228
13:12:04.135 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:12:04.252 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
13:12:04.591 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-180c-4df5-9f92-2321d668c0c0] Receive server push request, request = NotifySubscriberRequest, requestId = 8
13:12:04.630 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-180c-4df5-9f92-2321d668c0c0] Ack server push request, request = NotifySubscriberRequest, requestId = 8
13:12:04.804 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 26.376 seconds (JVM running for 28.928)
13:12:04.849 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:12:04.851 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:12:04.853 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:12:09.394 [RMI TCP Connection(1)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:19:43.798 [nacos-grpc-client-executor-101] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-180c-4df5-9f92-2321d668c0c0] Receive server push request, request = NotifySubscriberRequest, requestId = 12
13:19:43.802 [nacos-grpc-client-executor-101] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-180c-4df5-9f92-2321d668c0c0] Ack server push request, request = NotifySubscriberRequest, requestId = 12
13:19:45.703 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
13:19:45.703 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
13:19:45.703 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
13:19:45.708 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:19:45.708 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-5} inited
13:19:45.714 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
13:19:45.720 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
13:19:45.720 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
13:19:45.720 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
13:19:45.725 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
13:19:45.725 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
13:19:45.725 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-5} closing ...
13:19:45.728 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-5} closed
13:19:45.728 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
13:21:48.581 [http-nio-9600-exec-8] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-6} inited
13:21:48.581 [http-nio-9600-exec-8] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:24:14.396 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:24:14.400 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:24:14.733 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:24:14.733 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@16e432b8[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:24:14.733 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754889123618_127.0.0.1_13863
13:24:14.736 [nacos-grpc-client-executor-155] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754889123618_127.0.0.1_13863]Ignore complete event,isRunning:false,isAbandon=false
13:24:14.738 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@9bc710f[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 156]
13:24:14.880 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:24:14.880 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:24:14.880 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:24:14.880 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-6} closing ...
13:24:14.880 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-6} closed
13:24:14.880 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:24:14.880 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:24:14.880 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:24:20.308 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:24:21.442 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of dc30b73c-cf15-4e20-b7b8-10ccab6fdf08_config-0
13:24:21.655 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 77 ms to scan 1 urls, producing 3 keys and 6 values 
13:24:21.783 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 4 keys and 9 values 
13:24:21.804 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 3 keys and 10 values 
13:24:21.822 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 5 values 
13:24:21.846 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 1 keys and 7 values 
13:24:21.872 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 2 keys and 8 values 
13:24:21.876 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc30b73c-cf15-4e20-b7b8-10ccab6fdf08_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:24:21.878 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc30b73c-cf15-4e20-b7b8-10ccab6fdf08_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000018c5c39eaf8
13:24:21.882 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc30b73c-cf15-4e20-b7b8-10ccab6fdf08_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000018c5c39ed18
13:24:21.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc30b73c-cf15-4e20-b7b8-10ccab6fdf08_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:24:21.885 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc30b73c-cf15-4e20-b7b8-10ccab6fdf08_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:24:21.901 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc30b73c-cf15-4e20-b7b8-10ccab6fdf08_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:24:23.505 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc30b73c-cf15-4e20-b7b8-10ccab6fdf08_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754889863126_127.0.0.1_3257
13:24:23.505 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc30b73c-cf15-4e20-b7b8-10ccab6fdf08_config-0] Notify connected event to listeners.
13:24:23.506 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc30b73c-cf15-4e20-b7b8-10ccab6fdf08_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:24:23.507 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc30b73c-cf15-4e20-b7b8-10ccab6fdf08_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000018c5c518ad8
13:24:23.693 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:24:27.955 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:24:27.955 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:24:27.956 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:24:28.104 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:24:28.655 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:24:28.656 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:24:28.656 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:24:35.064 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:24:37.757 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 16c7597e-6afc-4eca-8230-b87f54e1a035
13:24:37.758 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [16c7597e-6afc-4eca-8230-b87f54e1a035] RpcClient init label, labels = {module=naming, source=sdk}
13:24:37.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [16c7597e-6afc-4eca-8230-b87f54e1a035] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:24:37.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [16c7597e-6afc-4eca-8230-b87f54e1a035] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:24:37.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [16c7597e-6afc-4eca-8230-b87f54e1a035] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:24:37.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [16c7597e-6afc-4eca-8230-b87f54e1a035] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:24:37.887 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [16c7597e-6afc-4eca-8230-b87f54e1a035] Success to connect to server [localhost:8848] on start up, connectionId = 1754889877769_127.0.0.1_3336
13:24:37.888 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [16c7597e-6afc-4eca-8230-b87f54e1a035] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:24:37.888 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [16c7597e-6afc-4eca-8230-b87f54e1a035] Notify connected event to listeners.
13:24:37.888 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [16c7597e-6afc-4eca-8230-b87f54e1a035] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000018c5c518ad8
13:24:37.933 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:24:37.956 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
13:24:38.064 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 18.373 seconds (JVM running for 19.877)
13:24:38.076 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:24:38.077 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:24:38.077 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:24:38.461 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [16c7597e-6afc-4eca-8230-b87f54e1a035] Receive server push request, request = NotifySubscriberRequest, requestId = 22
13:24:38.476 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [16c7597e-6afc-4eca-8230-b87f54e1a035] Ack server push request, request = NotifySubscriberRequest, requestId = 22
13:24:38.639 [RMI TCP Connection(10)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:24:44.157 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:24:44.157 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:38:19.819 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:38:19.819 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:38:20.146 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:38:20.146 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@f9a83a4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:38:20.146 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754889877769_127.0.0.1_3336
13:38:20.146 [nacos-grpc-client-executor-175] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754889877769_127.0.0.1_3336]Ignore complete event,isRunning:false,isAbandon=false
13:38:20.146 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3011c142[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 176]
13:38:20.302 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:38:20.312 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:38:20.328 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:38:20.328 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:38:20.330 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:38:20.330 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:38:27.083 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:38:27.705 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1546b80c-a04a-45a0-9abb-6f02a1a2a8fe_config-0
13:38:27.759 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 
13:38:27.786 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
13:38:27.793 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
13:38:27.803 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
13:38:27.810 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
13:38:27.820 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
13:38:27.823 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1546b80c-a04a-45a0-9abb-6f02a1a2a8fe_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:38:27.823 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1546b80c-a04a-45a0-9abb-6f02a1a2a8fe_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000019d2e39e8d8
13:38:27.824 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1546b80c-a04a-45a0-9abb-6f02a1a2a8fe_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000019d2e39eaf8
13:38:27.824 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1546b80c-a04a-45a0-9abb-6f02a1a2a8fe_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:38:27.825 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1546b80c-a04a-45a0-9abb-6f02a1a2a8fe_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:38:27.832 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1546b80c-a04a-45a0-9abb-6f02a1a2a8fe_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:38:28.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1546b80c-a04a-45a0-9abb-6f02a1a2a8fe_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754890708560_127.0.0.1_6712
13:38:28.788 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1546b80c-a04a-45a0-9abb-6f02a1a2a8fe_config-0] Notify connected event to listeners.
13:38:28.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1546b80c-a04a-45a0-9abb-6f02a1a2a8fe_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:38:28.790 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1546b80c-a04a-45a0-9abb-6f02a1a2a8fe_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000019d2e518668
13:38:28.988 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:38:33.743 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:38:33.746 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:38:33.747 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:38:34.033 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:38:35.123 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:38:35.124 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:38:35.125 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:38:43.579 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:38:46.463 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ead36315-a958-4017-b7d0-5807d95e71de
13:38:46.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ead36315-a958-4017-b7d0-5807d95e71de] RpcClient init label, labels = {module=naming, source=sdk}
13:38:46.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ead36315-a958-4017-b7d0-5807d95e71de] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:38:46.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ead36315-a958-4017-b7d0-5807d95e71de] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:38:46.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ead36315-a958-4017-b7d0-5807d95e71de] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:38:46.466 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ead36315-a958-4017-b7d0-5807d95e71de] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:38:46.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ead36315-a958-4017-b7d0-5807d95e71de] Success to connect to server [localhost:8848] on start up, connectionId = 1754890726474_127.0.0.1_6778
13:38:46.594 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ead36315-a958-4017-b7d0-5807d95e71de] Notify connected event to listeners.
13:38:46.594 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ead36315-a958-4017-b7d0-5807d95e71de] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:38:46.594 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ead36315-a958-4017-b7d0-5807d95e71de] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000019d2e518668
13:38:46.639 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:38:46.664 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
13:38:46.769 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.318 seconds (JVM running for 21.452)
13:38:46.782 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:38:46.782 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:38:46.783 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:38:47.184 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ead36315-a958-4017-b7d0-5807d95e71de] Receive server push request, request = NotifySubscriberRequest, requestId = 29
13:38:47.200 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ead36315-a958-4017-b7d0-5807d95e71de] Ack server push request, request = NotifySubscriberRequest, requestId = 29
13:38:47.245 [RMI TCP Connection(13)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:39:11.090 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:39:11.091 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:44:36.071 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:44:36.071 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:44:36.386 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:44:36.386 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6389ed7e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:44:36.386 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754890726474_127.0.0.1_6778
13:44:36.386 [nacos-grpc-client-executor-80] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754890726474_127.0.0.1_6778]Ignore complete event,isRunning:false,isAbandon=false
13:44:36.392 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@788e1f08[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 81]
13:44:36.536 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:44:36.536 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:44:36.552 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:44:36.552 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:44:36.552 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:44:36.552 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:44:39.632 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:44:40.642 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ffbb56a6-d795-43cd-8347-7517b80f0d51_config-0
13:44:40.731 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 3 keys and 6 values 
13:44:40.789 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
13:44:40.804 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
13:44:40.820 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 5 values 
13:44:40.835 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
13:44:40.852 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
13:44:40.856 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffbb56a6-d795-43cd-8347-7517b80f0d51_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:44:40.857 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffbb56a6-d795-43cd-8347-7517b80f0d51_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000019c8139e480
13:44:40.858 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffbb56a6-d795-43cd-8347-7517b80f0d51_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000019c8139e6a0
13:44:40.859 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffbb56a6-d795-43cd-8347-7517b80f0d51_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:44:40.860 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffbb56a6-d795-43cd-8347-7517b80f0d51_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:44:40.872 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffbb56a6-d795-43cd-8347-7517b80f0d51_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:44:42.482 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffbb56a6-d795-43cd-8347-7517b80f0d51_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754891082251_127.0.0.1_7636
13:44:42.482 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffbb56a6-d795-43cd-8347-7517b80f0d51_config-0] Notify connected event to listeners.
13:44:42.483 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffbb56a6-d795-43cd-8347-7517b80f0d51_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:44:42.483 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffbb56a6-d795-43cd-8347-7517b80f0d51_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000019c81518228
13:44:42.573 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:44:45.167 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:44:45.168 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:44:45.168 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:44:45.281 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:44:46.215 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:44:46.216 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:44:46.216 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:44:51.501 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:44:53.772 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-9e45-406f-8810-c6d2ecffe80a
13:44:53.772 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9e45-406f-8810-c6d2ecffe80a] RpcClient init label, labels = {module=naming, source=sdk}
13:44:53.773 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9e45-406f-8810-c6d2ecffe80a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:44:53.773 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9e45-406f-8810-c6d2ecffe80a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:44:53.774 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9e45-406f-8810-c6d2ecffe80a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:44:53.774 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9e45-406f-8810-c6d2ecffe80a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:44:53.892 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9e45-406f-8810-c6d2ecffe80a] Success to connect to server [localhost:8848] on start up, connectionId = 1754891093782_127.0.0.1_7674
13:44:53.892 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9e45-406f-8810-c6d2ecffe80a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:44:53.892 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9e45-406f-8810-c6d2ecffe80a] Notify connected event to listeners.
13:44:53.892 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9e45-406f-8810-c6d2ecffe80a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000019c81518228
13:44:53.932 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:44:53.953 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
13:44:54.050 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.184 seconds (JVM running for 16.196)
13:44:54.063 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:44:54.064 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:44:54.064 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:44:54.225 [RMI TCP Connection(12)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:44:54.461 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9e45-406f-8810-c6d2ecffe80a] Receive server push request, request = NotifySubscriberRequest, requestId = 35
13:44:54.481 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9e45-406f-8810-c6d2ecffe80a] Ack server push request, request = NotifySubscriberRequest, requestId = 35
13:45:24.308 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:45:24.308 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:46:00.842 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffbb56a6-d795-43cd-8347-7517b80f0d51_config-0] Server healthy check fail, currentConnection = 1754891082251_127.0.0.1_7636
13:46:00.842 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9e45-406f-8810-c6d2ecffe80a] Server healthy check fail, currentConnection = 1754891093782_127.0.0.1_7674
13:46:00.843 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffbb56a6-d795-43cd-8347-7517b80f0d51_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
13:46:00.843 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9e45-406f-8810-c6d2ecffe80a] Try to reconnect to a new server, server is  not appointed, will choose a random server.
13:46:00.961 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9e45-406f-8810-c6d2ecffe80a] Success to connect a server [localhost:8848], connectionId = 1754891160853_127.0.0.1_8021
13:46:00.962 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9e45-406f-8810-c6d2ecffe80a] Abandon prev connection, server is localhost:8848, connectionId is 1754891093782_127.0.0.1_7674
13:46:00.962 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffbb56a6-d795-43cd-8347-7517b80f0d51_config-0] Success to connect a server [localhost:8848], connectionId = 1754891160853_127.0.0.1_8022
13:46:00.962 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754891093782_127.0.0.1_7674
13:46:00.962 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffbb56a6-d795-43cd-8347-7517b80f0d51_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1754891082251_127.0.0.1_7636
13:46:00.962 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754891082251_127.0.0.1_7636
13:46:00.965 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754891093782_127.0.0.1_7674]Ignore complete event,isRunning:false,isAbandon=true
13:46:00.965 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754891082251_127.0.0.1_7636]Ignore complete event,isRunning:false,isAbandon=true
13:46:00.967 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffbb56a6-d795-43cd-8347-7517b80f0d51_config-0] Notify disconnected event to listeners
13:46:00.968 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9e45-406f-8810-c6d2ecffe80a] Notify disconnected event to listeners
13:46:00.968 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ffbb56a6-d795-43cd-8347-7517b80f0d51_config-0] Notify connected event to listeners.
13:46:00.969 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9e45-406f-8810-c6d2ecffe80a] Notify connected event to listeners.
13:46:25.379 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9e45-406f-8810-c6d2ecffe80a] Receive server push request, request = NotifySubscriberRequest, requestId = 42
13:46:25.379 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-9e45-406f-8810-c6d2ecffe80a] Ack server push request, request = NotifySubscriberRequest, requestId = 42
13:49:35.515 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:49:35.515 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:49:35.864 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:49:35.864 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@686bcc54[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:49:35.864 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754891160853_127.0.0.1_8021
13:49:35.864 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6fc5b234[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 69]
13:49:36.034 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:49:36.035 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:49:36.044 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:49:36.044 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:49:36.051 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:49:36.051 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:49:42.823 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:49:43.403 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-ec90-4edd-8c04-632d490845d8_config-0
13:49:43.459 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
13:49:43.488 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
13:49:43.494 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
13:49:43.501 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
13:49:43.508 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
13:49:43.516 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
13:49:43.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ec90-4edd-8c04-632d490845d8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:49:43.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ec90-4edd-8c04-632d490845d8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001ad3b39ed38
13:49:43.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ec90-4edd-8c04-632d490845d8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001ad3b39ef58
13:49:43.520 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ec90-4edd-8c04-632d490845d8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:49:43.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ec90-4edd-8c04-632d490845d8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:49:43.527 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ec90-4edd-8c04-632d490845d8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:49:44.236 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ec90-4edd-8c04-632d490845d8_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754891384054_127.0.0.1_8893
13:49:44.237 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ec90-4edd-8c04-632d490845d8_config-0] Notify connected event to listeners.
13:49:44.237 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ec90-4edd-8c04-632d490845d8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:49:44.238 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ec90-4edd-8c04-632d490845d8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001ad3b518668
13:49:44.324 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:49:46.898 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:49:46.898 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:49:46.899 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:49:47.038 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:49:48.117 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:49:48.119 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:49:48.119 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:50:01.682 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:50:04.828 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6fee0a9f-efe2-4c2c-ae7e-9d75871d555d
13:50:04.829 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fee0a9f-efe2-4c2c-ae7e-9d75871d555d] RpcClient init label, labels = {module=naming, source=sdk}
13:50:04.831 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fee0a9f-efe2-4c2c-ae7e-9d75871d555d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:50:04.831 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fee0a9f-efe2-4c2c-ae7e-9d75871d555d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:50:04.832 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fee0a9f-efe2-4c2c-ae7e-9d75871d555d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:50:04.832 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fee0a9f-efe2-4c2c-ae7e-9d75871d555d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:50:04.954 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fee0a9f-efe2-4c2c-ae7e-9d75871d555d] Success to connect to server [localhost:8848] on start up, connectionId = 1754891404843_127.0.0.1_8974
13:50:04.954 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fee0a9f-efe2-4c2c-ae7e-9d75871d555d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:50:04.954 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fee0a9f-efe2-4c2c-ae7e-9d75871d555d] Notify connected event to listeners.
13:50:04.954 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fee0a9f-efe2-4c2c-ae7e-9d75871d555d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001ad3b518668
13:50:05.013 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:50:05.042 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
13:50:05.168 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 22.887 seconds (JVM running for 23.94)
13:50:05.181 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:50:05.182 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:50:05.182 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:50:05.360 [RMI TCP Connection(13)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:50:05.483 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fee0a9f-efe2-4c2c-ae7e-9d75871d555d] Receive server push request, request = NotifySubscriberRequest, requestId = 50
13:50:05.499 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fee0a9f-efe2-4c2c-ae7e-9d75871d555d] Ack server push request, request = NotifySubscriberRequest, requestId = 50
13:54:29.511 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:54:29.511 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:55:13.652 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:55:13.654 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:55:13.961 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:55:13.961 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@584e85a9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:55:13.961 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754891404843_127.0.0.1_8974
13:55:13.963 [nacos-grpc-client-executor-73] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754891404843_127.0.0.1_8974]Ignore complete event,isRunning:false,isAbandon=false
13:55:13.963 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1f56bbbd[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 74]
13:55:14.101 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:55:14.103 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:55:14.108 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:55:14.108 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:55:14.108 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:55:14.108 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:55:18.312 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:55:18.866 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5fdf0988-2df8-43ea-b498-60aaee1c0d6d_config-0
13:55:18.913 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
13:55:18.940 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
13:55:18.947 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
13:55:18.956 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
13:55:18.961 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
13:55:18.969 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
13:55:18.971 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fdf0988-2df8-43ea-b498-60aaee1c0d6d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:55:18.971 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fdf0988-2df8-43ea-b498-60aaee1c0d6d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001c7013cdd70
13:55:18.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fdf0988-2df8-43ea-b498-60aaee1c0d6d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001c7013cdf90
13:55:18.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fdf0988-2df8-43ea-b498-60aaee1c0d6d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:55:18.974 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fdf0988-2df8-43ea-b498-60aaee1c0d6d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:55:18.982 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fdf0988-2df8-43ea-b498-60aaee1c0d6d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:55:19.667 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fdf0988-2df8-43ea-b498-60aaee1c0d6d_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754891719481_127.0.0.1_9978
13:55:19.668 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fdf0988-2df8-43ea-b498-60aaee1c0d6d_config-0] Notify connected event to listeners.
13:55:19.668 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fdf0988-2df8-43ea-b498-60aaee1c0d6d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:55:19.669 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fdf0988-2df8-43ea-b498-60aaee1c0d6d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001c701508228
13:55:19.756 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:55:22.264 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:55:22.264 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:55:22.264 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:55:22.389 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:55:23.339 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:55:23.341 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:55:23.341 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:55:28.798 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:55:31.345 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ff4828e2-406a-4f6b-b5d7-46ac33935f0c
13:55:31.346 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff4828e2-406a-4f6b-b5d7-46ac33935f0c] RpcClient init label, labels = {module=naming, source=sdk}
13:55:31.347 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff4828e2-406a-4f6b-b5d7-46ac33935f0c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:55:31.348 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff4828e2-406a-4f6b-b5d7-46ac33935f0c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:55:31.348 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff4828e2-406a-4f6b-b5d7-46ac33935f0c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:55:31.348 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff4828e2-406a-4f6b-b5d7-46ac33935f0c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:55:31.478 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff4828e2-406a-4f6b-b5d7-46ac33935f0c] Success to connect to server [localhost:8848] on start up, connectionId = 1754891731357_127.0.0.1_10028
13:55:31.479 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff4828e2-406a-4f6b-b5d7-46ac33935f0c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:55:31.479 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff4828e2-406a-4f6b-b5d7-46ac33935f0c] Notify connected event to listeners.
13:55:31.479 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff4828e2-406a-4f6b-b5d7-46ac33935f0c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001c701508228
13:55:31.522 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:55:31.553 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
13:55:31.655 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.835 seconds (JVM running for 14.699)
13:55:31.668 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:55:31.668 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:55:31.669 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:55:31.948 [RMI TCP Connection(2)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:55:32.084 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff4828e2-406a-4f6b-b5d7-46ac33935f0c] Receive server push request, request = NotifySubscriberRequest, requestId = 56
13:55:32.098 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff4828e2-406a-4f6b-b5d7-46ac33935f0c] Ack server push request, request = NotifySubscriberRequest, requestId = 56
13:55:38.898 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:55:38.898 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:57:04.310 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:57:04.310 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:57:04.634 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:57:04.634 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@f233eae[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:57:04.634 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754891731357_127.0.0.1_10028
13:57:04.634 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754891731357_127.0.0.1_10028]Ignore complete event,isRunning:false,isAbandon=false
13:57:04.637 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@784c4a50[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 30]
13:57:04.761 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:57:04.761 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:57:04.761 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:57:04.761 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:57:04.761 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:57:04.761 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:57:09.106 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:57:09.681 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c1282b8b-1848-4c41-ba60-b543958b5e2a_config-0
13:57:09.736 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
13:57:09.764 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
13:57:09.771 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
13:57:09.777 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
13:57:09.783 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
13:57:09.791 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
13:57:09.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1282b8b-1848-4c41-ba60-b543958b5e2a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:57:09.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1282b8b-1848-4c41-ba60-b543958b5e2a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001a9a439e480
13:57:09.794 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1282b8b-1848-4c41-ba60-b543958b5e2a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001a9a439e6a0
13:57:09.794 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1282b8b-1848-4c41-ba60-b543958b5e2a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:57:09.796 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1282b8b-1848-4c41-ba60-b543958b5e2a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:57:09.804 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1282b8b-1848-4c41-ba60-b543958b5e2a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:57:10.687 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1282b8b-1848-4c41-ba60-b543958b5e2a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754891830500_127.0.0.1_10421
13:57:10.687 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1282b8b-1848-4c41-ba60-b543958b5e2a_config-0] Notify connected event to listeners.
13:57:10.688 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1282b8b-1848-4c41-ba60-b543958b5e2a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:57:10.688 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1282b8b-1848-4c41-ba60-b543958b5e2a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001a9a4518228
13:57:10.844 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:57:13.740 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:57:13.741 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:57:13.741 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:57:13.856 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:57:14.676 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:57:14.677 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:57:14.677 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:57:20.199 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:57:23.474 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 07781c5a-e772-42b5-84d8-2b9529bac8d4
13:57:23.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07781c5a-e772-42b5-84d8-2b9529bac8d4] RpcClient init label, labels = {module=naming, source=sdk}
13:57:23.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07781c5a-e772-42b5-84d8-2b9529bac8d4] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:57:23.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07781c5a-e772-42b5-84d8-2b9529bac8d4] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:57:23.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07781c5a-e772-42b5-84d8-2b9529bac8d4] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:57:23.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07781c5a-e772-42b5-84d8-2b9529bac8d4] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:57:23.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07781c5a-e772-42b5-84d8-2b9529bac8d4] Success to connect to server [localhost:8848] on start up, connectionId = 1754891843487_127.0.0.1_10496
13:57:23.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07781c5a-e772-42b5-84d8-2b9529bac8d4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:57:23.616 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07781c5a-e772-42b5-84d8-2b9529bac8d4] Notify connected event to listeners.
13:57:23.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07781c5a-e772-42b5-84d8-2b9529bac8d4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001a9a4518228
13:57:23.662 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:57:23.677 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
13:57:23.808 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.231 seconds (JVM running for 16.368)
13:57:23.821 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:57:23.821 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:57:23.821 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:57:24.178 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07781c5a-e772-42b5-84d8-2b9529bac8d4] Receive server push request, request = NotifySubscriberRequest, requestId = 64
13:57:24.186 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [07781c5a-e772-42b5-84d8-2b9529bac8d4] Ack server push request, request = NotifySubscriberRequest, requestId = 64
13:57:24.232 [RMI TCP Connection(5)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:57:29.525 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:57:29.525 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:04:54.176 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:04:54.176 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:04:54.511 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:04:54.511 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2153a7e6[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:04:54.511 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754891843487_127.0.0.1_10496
14:04:54.511 [nacos-grpc-client-executor-59] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754891843487_127.0.0.1_10496]Ignore complete event,isRunning:false,isAbandon=false
14:04:54.511 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@20ad583a[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 60]
14:04:54.668 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:04:54.673 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:04:54.676 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:04:54.676 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:04:54.676 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:04:54.676 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:05:00.092 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:05:00.670 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f2ca8009-a43d-405a-8a86-6be9689f0e4e_config-0
14:05:00.725 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 
14:05:00.752 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
14:05:00.759 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
14:05:00.766 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
14:05:00.772 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
14:05:00.780 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
14:05:00.782 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2ca8009-a43d-405a-8a86-6be9689f0e4e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:05:00.783 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2ca8009-a43d-405a-8a86-6be9689f0e4e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000243b63ceaf8
14:05:00.783 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2ca8009-a43d-405a-8a86-6be9689f0e4e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000243b63ced18
14:05:00.783 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2ca8009-a43d-405a-8a86-6be9689f0e4e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:05:00.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2ca8009-a43d-405a-8a86-6be9689f0e4e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:05:00.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2ca8009-a43d-405a-8a86-6be9689f0e4e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:05:01.483 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2ca8009-a43d-405a-8a86-6be9689f0e4e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754892301304_127.0.0.1_11815
14:05:01.483 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2ca8009-a43d-405a-8a86-6be9689f0e4e_config-0] Notify connected event to listeners.
14:05:01.483 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2ca8009-a43d-405a-8a86-6be9689f0e4e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:05:01.484 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2ca8009-a43d-405a-8a86-6be9689f0e4e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000243b6508668
14:05:01.572 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:05:05.289 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:05:05.289 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:05:05.289 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:05:05.444 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:05:06.014 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:05:06.014 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:05:06.014 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:05:12.201 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:05:14.943 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d19c1dc8-bc8c-4466-b1c8-96185316df46
14:05:14.943 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d19c1dc8-bc8c-4466-b1c8-96185316df46] RpcClient init label, labels = {module=naming, source=sdk}
14:05:14.947 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d19c1dc8-bc8c-4466-b1c8-96185316df46] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:05:14.947 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d19c1dc8-bc8c-4466-b1c8-96185316df46] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:05:14.947 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d19c1dc8-bc8c-4466-b1c8-96185316df46] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:05:14.947 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d19c1dc8-bc8c-4466-b1c8-96185316df46] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:05:15.072 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d19c1dc8-bc8c-4466-b1c8-96185316df46] Success to connect to server [localhost:8848] on start up, connectionId = 1754892314954_127.0.0.1_11874
14:05:15.072 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d19c1dc8-bc8c-4466-b1c8-96185316df46] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:05:15.072 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d19c1dc8-bc8c-4466-b1c8-96185316df46] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000243b6508668
14:05:15.073 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d19c1dc8-bc8c-4466-b1c8-96185316df46] Notify connected event to listeners.
14:05:15.122 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:05:15.141 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
14:05:15.249 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.735 seconds (JVM running for 16.571)
14:05:15.260 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:05:15.269 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:05:15.269 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:05:15.528 [RMI TCP Connection(8)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:05:15.616 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d19c1dc8-bc8c-4466-b1c8-96185316df46] Receive server push request, request = NotifySubscriberRequest, requestId = 68
14:05:15.642 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d19c1dc8-bc8c-4466-b1c8-96185316df46] Ack server push request, request = NotifySubscriberRequest, requestId = 68
14:05:25.879 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:05:25.879 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:55:17.441 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:55:17.457 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:55:17.796 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:55:17.796 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2a0ae480[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:55:17.796 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754892314954_127.0.0.1_11874
14:55:17.796 [nacos-grpc-client-executor-358] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754892314954_127.0.0.1_11874]Ignore complete event,isRunning:false,isAbandon=false
14:55:17.796 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7d1315b[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 359]
14:55:17.980 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:55:17.982 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:55:17.994 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:55:17.994 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:55:17.994 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:55:17.996 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:55:25.885 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:55:26.709 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 08375e58-d642-40b2-b01d-f891ae623a5e_config-0
14:55:26.795 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
14:55:26.837 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
14:55:26.848 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
14:55:26.858 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
14:55:26.868 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
14:55:26.877 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
14:55:26.880 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08375e58-d642-40b2-b01d-f891ae623a5e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:55:26.881 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08375e58-d642-40b2-b01d-f891ae623a5e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000236d339ed38
14:55:26.882 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08375e58-d642-40b2-b01d-f891ae623a5e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000236d339ef58
14:55:26.882 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08375e58-d642-40b2-b01d-f891ae623a5e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:55:26.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08375e58-d642-40b2-b01d-f891ae623a5e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:55:26.893 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08375e58-d642-40b2-b01d-f891ae623a5e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:55:28.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08375e58-d642-40b2-b01d-f891ae623a5e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754895327773_127.0.0.1_5344
14:55:28.012 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08375e58-d642-40b2-b01d-f891ae623a5e_config-0] Notify connected event to listeners.
14:55:28.013 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08375e58-d642-40b2-b01d-f891ae623a5e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:55:28.015 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08375e58-d642-40b2-b01d-f891ae623a5e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000236d3518ad8
14:55:28.245 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:55:33.362 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:55:33.363 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:55:33.363 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:55:33.548 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:55:34.670 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:55:34.672 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:55:34.673 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:55:43.618 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:55:46.976 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 26b70afc-036e-4c50-b76d-af6b4a8e36b5
14:55:46.976 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26b70afc-036e-4c50-b76d-af6b4a8e36b5] RpcClient init label, labels = {module=naming, source=sdk}
14:55:46.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26b70afc-036e-4c50-b76d-af6b4a8e36b5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:55:46.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26b70afc-036e-4c50-b76d-af6b4a8e36b5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:55:46.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26b70afc-036e-4c50-b76d-af6b4a8e36b5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:55:46.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26b70afc-036e-4c50-b76d-af6b4a8e36b5] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:55:47.111 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26b70afc-036e-4c50-b76d-af6b4a8e36b5] Success to connect to server [localhost:8848] on start up, connectionId = 1754895346989_127.0.0.1_5460
14:55:47.112 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26b70afc-036e-4c50-b76d-af6b4a8e36b5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:55:47.112 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26b70afc-036e-4c50-b76d-af6b4a8e36b5] Notify connected event to listeners.
14:55:47.112 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26b70afc-036e-4c50-b76d-af6b4a8e36b5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000236d3518ad8
14:55:47.181 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:55:47.216 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
14:55:47.363 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 22.128 seconds (JVM running for 23.306)
14:55:47.380 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:55:47.380 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:55:47.381 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:55:47.680 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26b70afc-036e-4c50-b76d-af6b4a8e36b5] Receive server push request, request = NotifySubscriberRequest, requestId = 75
14:55:47.700 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26b70afc-036e-4c50-b76d-af6b4a8e36b5] Ack server push request, request = NotifySubscriberRequest, requestId = 75
14:55:47.784 [RMI TCP Connection(17)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:56:01.173 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:56:01.174 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:41:24.421 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:41:24.426 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:41:24.755 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:41:24.755 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@41dbec83[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:41:24.755 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754895346989_127.0.0.1_5460
15:41:24.757 [nacos-grpc-client-executor-564] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754895346989_127.0.0.1_5460]Ignore complete event,isRunning:false,isAbandon=false
15:41:24.761 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3b690a80[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 565]
15:41:24.935 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:41:24.943 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:41:24.962 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:41:24.962 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:41:24.964 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:41:24.964 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:41:33.147 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:41:34.034 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ec44bbb8-ec79-4561-85fd-d9441418914b_config-0
15:41:34.111 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 3 keys and 6 values 
15:41:34.159 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
15:41:34.170 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
15:41:34.182 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
15:41:34.194 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
15:41:34.209 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
15:41:34.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec44bbb8-ec79-4561-85fd-d9441418914b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:41:34.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec44bbb8-ec79-4561-85fd-d9441418914b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000255cf39e480
15:41:34.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec44bbb8-ec79-4561-85fd-d9441418914b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000255cf39e6a0
15:41:34.213 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec44bbb8-ec79-4561-85fd-d9441418914b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:41:34.214 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec44bbb8-ec79-4561-85fd-d9441418914b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:41:34.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec44bbb8-ec79-4561-85fd-d9441418914b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:41:35.231 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec44bbb8-ec79-4561-85fd-d9441418914b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754898095024_127.0.0.1_11682
15:41:35.233 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec44bbb8-ec79-4561-85fd-d9441418914b_config-0] Notify connected event to listeners.
15:41:35.234 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec44bbb8-ec79-4561-85fd-d9441418914b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:41:35.235 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ec44bbb8-ec79-4561-85fd-d9441418914b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000255cf518228
15:41:35.408 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:41:41.400 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:41:41.401 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:41:41.402 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:41:41.822 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:41:46.047 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:41:46.050 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:41:46.050 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:42:09.246 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:42:16.447 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ae727636-c736-4627-ad60-2684857ae136
15:42:16.448 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae727636-c736-4627-ad60-2684857ae136] RpcClient init label, labels = {module=naming, source=sdk}
15:42:16.452 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae727636-c736-4627-ad60-2684857ae136] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:42:16.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae727636-c736-4627-ad60-2684857ae136] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:42:16.455 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae727636-c736-4627-ad60-2684857ae136] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:42:16.455 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae727636-c736-4627-ad60-2684857ae136] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:42:16.598 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae727636-c736-4627-ad60-2684857ae136] Success to connect to server [localhost:8848] on start up, connectionId = 1754898136474_127.0.0.1_11720
15:42:16.599 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae727636-c736-4627-ad60-2684857ae136] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:42:16.599 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae727636-c736-4627-ad60-2684857ae136] Notify connected event to listeners.
15:42:16.599 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae727636-c736-4627-ad60-2684857ae136] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000255cf518228
15:42:16.705 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:42:16.774 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
15:42:17.127 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 45.318 seconds (JVM running for 46.587)
15:42:17.163 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:42:17.165 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:42:17.167 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:42:17.231 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae727636-c736-4627-ad60-2684857ae136] Receive server push request, request = NotifySubscriberRequest, requestId = 84
15:42:17.255 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae727636-c736-4627-ad60-2684857ae136] Ack server push request, request = NotifySubscriberRequest, requestId = 84
15:42:17.315 [RMI TCP Connection(10)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:42:28.146 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:42:28.147 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:52:06.213 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:52:06.216 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:52:06.539 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:52:06.539 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@81923[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:52:06.539 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754898136474_127.0.0.1_11720
15:52:06.541 [nacos-grpc-client-executor-127] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754898136474_127.0.0.1_11720]Ignore complete event,isRunning:false,isAbandon=false
15:52:06.543 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7619ded9[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 128]
15:52:06.688 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:52:06.688 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:52:06.709 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:52:06.709 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:52:06.711 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:52:06.711 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:52:13.027 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:52:13.646 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 558cad6a-43c8-4bd4-a3d9-7e717593176f_config-0
15:52:13.702 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 
15:52:13.734 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
15:52:13.740 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
15:52:13.749 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
15:52:13.763 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
15:52:13.772 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
15:52:13.774 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [558cad6a-43c8-4bd4-a3d9-7e717593176f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:52:13.775 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [558cad6a-43c8-4bd4-a3d9-7e717593176f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002481a39e480
15:52:13.775 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [558cad6a-43c8-4bd4-a3d9-7e717593176f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002481a39e6a0
15:52:13.775 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [558cad6a-43c8-4bd4-a3d9-7e717593176f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:52:13.776 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [558cad6a-43c8-4bd4-a3d9-7e717593176f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:52:13.782 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [558cad6a-43c8-4bd4-a3d9-7e717593176f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:14.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [558cad6a-43c8-4bd4-a3d9-7e717593176f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754898734317_127.0.0.1_13133
15:52:14.515 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [558cad6a-43c8-4bd4-a3d9-7e717593176f_config-0] Notify connected event to listeners.
15:52:14.515 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [558cad6a-43c8-4bd4-a3d9-7e717593176f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:52:14.516 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [558cad6a-43c8-4bd4-a3d9-7e717593176f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002481a518228
15:52:14.610 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:52:17.539 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:52:17.539 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:52:17.539 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:52:17.688 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:52:18.916 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:52:18.917 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:52:18.917 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:52:25.304 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:52:28.611 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 591efc1b-6a18-4e75-9f9a-184403e85cf5
15:52:28.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [591efc1b-6a18-4e75-9f9a-184403e85cf5] RpcClient init label, labels = {module=naming, source=sdk}
15:52:28.614 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [591efc1b-6a18-4e75-9f9a-184403e85cf5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:52:28.615 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [591efc1b-6a18-4e75-9f9a-184403e85cf5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:52:28.615 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [591efc1b-6a18-4e75-9f9a-184403e85cf5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:52:28.615 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [591efc1b-6a18-4e75-9f9a-184403e85cf5] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:52:28.736 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [591efc1b-6a18-4e75-9f9a-184403e85cf5] Success to connect to server [localhost:8848] on start up, connectionId = 1754898748624_127.0.0.1_13230
15:52:28.736 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [591efc1b-6a18-4e75-9f9a-184403e85cf5] Notify connected event to listeners.
15:52:28.736 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [591efc1b-6a18-4e75-9f9a-184403e85cf5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:52:28.736 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [591efc1b-6a18-4e75-9f9a-184403e85cf5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002481a518228
15:52:28.788 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:52:28.810 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
15:52:28.923 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 16.544 seconds (JVM running for 17.643)
15:52:28.935 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:52:28.937 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:52:28.937 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:52:29.108 [RMI TCP Connection(12)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:52:29.263 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [591efc1b-6a18-4e75-9f9a-184403e85cf5] Receive server push request, request = NotifySubscriberRequest, requestId = 91
15:52:29.278 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [591efc1b-6a18-4e75-9f9a-184403e85cf5] Ack server push request, request = NotifySubscriberRequest, requestId = 91
15:52:36.553 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:52:36.553 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:29:38.345 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:29:38.350 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:29:38.702 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:29:38.702 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@57b7b226[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:29:38.702 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754898748624_127.0.0.1_13230
16:29:38.702 [nacos-grpc-client-executor-455] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754898748624_127.0.0.1_13230]Ignore complete event,isRunning:false,isAbandon=false
16:29:38.708 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4807c225[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 456]
16:29:38.901 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:29:38.907 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:29:38.923 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:29:38.923 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:29:38.927 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:29:38.929 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:29:46.300 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:29:46.919 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3890fe8c-9146-48f9-a6cb-3b5fd33b7a02_config-0
16:29:46.978 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 3 keys and 6 values 
16:29:47.005 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
16:29:47.011 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
16:29:47.018 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
16:29:47.025 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
16:29:47.033 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
16:29:47.035 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3890fe8c-9146-48f9-a6cb-3b5fd33b7a02_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:29:47.036 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3890fe8c-9146-48f9-a6cb-3b5fd33b7a02_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001fd6539eaf8
16:29:47.036 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3890fe8c-9146-48f9-a6cb-3b5fd33b7a02_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001fd6539ed18
16:29:47.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3890fe8c-9146-48f9-a6cb-3b5fd33b7a02_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:29:47.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3890fe8c-9146-48f9-a6cb-3b5fd33b7a02_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:29:47.043 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3890fe8c-9146-48f9-a6cb-3b5fd33b7a02_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:29:47.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3890fe8c-9146-48f9-a6cb-3b5fd33b7a02_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754900987600_127.0.0.1_8019
16:29:47.789 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3890fe8c-9146-48f9-a6cb-3b5fd33b7a02_config-0] Notify connected event to listeners.
16:29:47.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3890fe8c-9146-48f9-a6cb-3b5fd33b7a02_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:29:47.790 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3890fe8c-9146-48f9-a6cb-3b5fd33b7a02_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001fd65518ad8
16:29:47.877 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:29:50.406 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:29:50.407 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:29:50.407 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:29:50.514 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:29:52.729 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:29:52.731 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:29:52.731 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:29:58.352 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:30:01.481 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 657ec5b7-e5d4-4153-a0d4-0023ab33a8f1
16:30:01.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [657ec5b7-e5d4-4153-a0d4-0023ab33a8f1] RpcClient init label, labels = {module=naming, source=sdk}
16:30:01.483 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [657ec5b7-e5d4-4153-a0d4-0023ab33a8f1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:30:01.484 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [657ec5b7-e5d4-4153-a0d4-0023ab33a8f1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:30:01.484 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [657ec5b7-e5d4-4153-a0d4-0023ab33a8f1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:30:01.485 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [657ec5b7-e5d4-4153-a0d4-0023ab33a8f1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:30:01.615 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [657ec5b7-e5d4-4153-a0d4-0023ab33a8f1] Success to connect to server [localhost:8848] on start up, connectionId = 1754901001494_127.0.0.1_8072
16:30:01.618 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [657ec5b7-e5d4-4153-a0d4-0023ab33a8f1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:30:01.619 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [657ec5b7-e5d4-4153-a0d4-0023ab33a8f1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001fd65518ad8
16:30:01.619 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [657ec5b7-e5d4-4153-a0d4-0023ab33a8f1] Notify connected event to listeners.
16:30:01.668 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:30:01.703 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
16:30:01.848 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 16.12 seconds (JVM running for 17.175)
16:30:01.865 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:30:01.865 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:30:01.866 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:30:02.149 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [657ec5b7-e5d4-4153-a0d4-0023ab33a8f1] Receive server push request, request = NotifySubscriberRequest, requestId = 98
16:30:02.164 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [657ec5b7-e5d4-4153-a0d4-0023ab33a8f1] Ack server push request, request = NotifySubscriberRequest, requestId = 98
16:30:02.374 [RMI TCP Connection(3)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:30:25.133 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:30:25.133 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:34:30.252 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:34:30.256 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:34:30.568 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:34:30.568 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2c0d94a8[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:34:30.568 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754901001494_127.0.0.1_8072
16:34:30.568 [nacos-grpc-client-executor-65] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754901001494_127.0.0.1_8072]Ignore complete event,isRunning:false,isAbandon=false
16:34:30.576 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5c466b35[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 66]
16:34:30.757 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:34:30.761 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:34:30.779 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:34:30.781 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:34:30.783 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:34:30.785 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:34:43.782 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:34:44.885 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of da990fb3-f8f8-4b37-a5cd-71f49ae59e73_config-0
16:34:44.979 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 50 ms to scan 1 urls, producing 3 keys and 6 values 
16:34:45.018 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
16:34:45.028 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
16:34:45.040 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 5 values 
16:34:45.050 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
16:34:45.051 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
16:34:45.051 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da990fb3-f8f8-4b37-a5cd-71f49ae59e73_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:34:45.051 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da990fb3-f8f8-4b37-a5cd-71f49ae59e73_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000276bc3b6690
16:34:45.051 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da990fb3-f8f8-4b37-a5cd-71f49ae59e73_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000276bc3b68b0
16:34:45.066 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da990fb3-f8f8-4b37-a5cd-71f49ae59e73_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:34:45.067 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da990fb3-f8f8-4b37-a5cd-71f49ae59e73_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:34:45.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da990fb3-f8f8-4b37-a5cd-71f49ae59e73_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:34:46.051 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da990fb3-f8f8-4b37-a5cd-71f49ae59e73_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754901285830_127.0.0.1_9174
16:34:46.051 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da990fb3-f8f8-4b37-a5cd-71f49ae59e73_config-0] Notify connected event to listeners.
16:34:46.051 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da990fb3-f8f8-4b37-a5cd-71f49ae59e73_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:34:46.051 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da990fb3-f8f8-4b37-a5cd-71f49ae59e73_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000276bc4f0228
16:34:46.151 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:34:49.357 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:34:49.357 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:34:49.357 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:34:49.515 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:34:50.191 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:34:50.191 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:34:50.191 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:34:56.790 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:34:59.634 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 569d795c-df65-4b43-88db-e58bb72d826b
16:34:59.634 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [569d795c-df65-4b43-88db-e58bb72d826b] RpcClient init label, labels = {module=naming, source=sdk}
16:34:59.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [569d795c-df65-4b43-88db-e58bb72d826b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:34:59.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [569d795c-df65-4b43-88db-e58bb72d826b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:34:59.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [569d795c-df65-4b43-88db-e58bb72d826b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:34:59.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [569d795c-df65-4b43-88db-e58bb72d826b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:34:59.765 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [569d795c-df65-4b43-88db-e58bb72d826b] Success to connect to server [localhost:8848] on start up, connectionId = 1754901299639_127.0.0.1_9226
16:34:59.766 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [569d795c-df65-4b43-88db-e58bb72d826b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:34:59.766 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [569d795c-df65-4b43-88db-e58bb72d826b] Notify connected event to listeners.
16:34:59.766 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [569d795c-df65-4b43-88db-e58bb72d826b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000276bc4f0228
16:34:59.811 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:34:59.835 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
16:34:59.938 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 17.184 seconds (JVM running for 19.084)
16:34:59.961 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:34:59.961 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:34:59.961 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:35:00.328 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [569d795c-df65-4b43-88db-e58bb72d826b] Receive server push request, request = NotifySubscriberRequest, requestId = 105
16:35:00.347 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [569d795c-df65-4b43-88db-e58bb72d826b] Ack server push request, request = NotifySubscriberRequest, requestId = 105
16:35:12.058 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:35:14.001 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:35:14.001 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:46:25.289 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:46:25.292 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:46:25.618 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:46:25.618 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5bd02e7c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:46:25.618 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754901299639_127.0.0.1_9226
16:46:25.620 [nacos-grpc-client-executor-75] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754901299639_127.0.0.1_9226]Ignore complete event,isRunning:false,isAbandon=false
16:46:25.623 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4746d424[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 76]
16:46:25.772 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:46:25.774 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:46:25.780 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:46:25.780 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:46:25.782 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:46:25.782 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:46:33.012 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:46:33.634 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a678b772-fdcb-4429-8ffd-b8cfa31be482_config-0
16:46:33.682 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 3 keys and 6 values 
16:46:33.708 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
16:46:33.715 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
16:46:33.723 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
16:46:33.730 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
16:46:33.740 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
16:46:33.743 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a678b772-fdcb-4429-8ffd-b8cfa31be482_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:46:33.744 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a678b772-fdcb-4429-8ffd-b8cfa31be482_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000296bc39dd70
16:46:33.744 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a678b772-fdcb-4429-8ffd-b8cfa31be482_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000296bc39df90
16:46:33.744 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a678b772-fdcb-4429-8ffd-b8cfa31be482_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:46:33.745 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a678b772-fdcb-4429-8ffd-b8cfa31be482_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:46:33.752 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a678b772-fdcb-4429-8ffd-b8cfa31be482_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:46:34.524 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a678b772-fdcb-4429-8ffd-b8cfa31be482_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754901994330_127.0.0.1_11467
16:46:34.525 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a678b772-fdcb-4429-8ffd-b8cfa31be482_config-0] Notify connected event to listeners.
16:46:34.525 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a678b772-fdcb-4429-8ffd-b8cfa31be482_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:46:34.526 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a678b772-fdcb-4429-8ffd-b8cfa31be482_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000296bc518228
16:46:34.602 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:46:37.953 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:46:37.954 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:46:37.954 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:46:38.181 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:46:39.387 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:46:39.389 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:46:39.390 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:46:48.114 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:46:50.926 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f156e8c7-8cfe-47ec-b07d-fef8dcda8171
16:46:50.926 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f156e8c7-8cfe-47ec-b07d-fef8dcda8171] RpcClient init label, labels = {module=naming, source=sdk}
16:46:50.928 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f156e8c7-8cfe-47ec-b07d-fef8dcda8171] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:46:50.928 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f156e8c7-8cfe-47ec-b07d-fef8dcda8171] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:46:50.928 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f156e8c7-8cfe-47ec-b07d-fef8dcda8171] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:46:50.928 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f156e8c7-8cfe-47ec-b07d-fef8dcda8171] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:46:51.060 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f156e8c7-8cfe-47ec-b07d-fef8dcda8171] Success to connect to server [localhost:8848] on start up, connectionId = 1754902010937_127.0.0.1_11557
16:46:51.060 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f156e8c7-8cfe-47ec-b07d-fef8dcda8171] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:46:51.060 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f156e8c7-8cfe-47ec-b07d-fef8dcda8171] Notify connected event to listeners.
16:46:51.060 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f156e8c7-8cfe-47ec-b07d-fef8dcda8171] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000296bc518228
16:46:51.108 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:46:51.134 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
16:46:51.242 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 18.819 seconds (JVM running for 19.916)
16:46:51.256 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:46:51.256 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:46:51.257 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:46:51.680 [RMI TCP Connection(8)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:46:51.685 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f156e8c7-8cfe-47ec-b07d-fef8dcda8171] Receive server push request, request = NotifySubscriberRequest, requestId = 113
16:46:51.704 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f156e8c7-8cfe-47ec-b07d-fef8dcda8171] Ack server push request, request = NotifySubscriberRequest, requestId = 113
16:47:28.720 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:47:28.720 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:01:06.263 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f156e8c7-8cfe-47ec-b07d-fef8dcda8171] Server check success, currentServer is localhost:8848 
17:01:56.635 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a678b772-fdcb-4429-8ffd-b8cfa31be482_config-0] Server healthy check fail, currentConnection = 1754901994330_127.0.0.1_11467
17:01:56.636 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a678b772-fdcb-4429-8ffd-b8cfa31be482_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:06:55.446 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a678b772-fdcb-4429-8ffd-b8cfa31be482_config-0] Success to connect a server [localhost:8848], connectionId = 1754902916643_127.0.0.1_14217
17:06:55.446 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a678b772-fdcb-4429-8ffd-b8cfa31be482_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1754901994330_127.0.0.1_11467
17:06:55.446 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754901994330_127.0.0.1_11467
17:06:55.450 [nacos-grpc-client-executor-157] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754901994330_127.0.0.1_11467]Ignore complete event,isRunning:false,isAbandon=true
17:06:55.467 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a678b772-fdcb-4429-8ffd-b8cfa31be482_config-0] Notify disconnected event to listeners
17:06:55.468 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a678b772-fdcb-4429-8ffd-b8cfa31be482_config-0] Notify connected event to listeners.
17:07:41.910 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a678b772-fdcb-4429-8ffd-b8cfa31be482_config-0] Server healthy check fail, currentConnection = 1754902916643_127.0.0.1_14217
17:07:41.911 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a678b772-fdcb-4429-8ffd-b8cfa31be482_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:10:32.939 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f156e8c7-8cfe-47ec-b07d-fef8dcda8171] Server check success, currentServer is localhost:8848 
17:10:33.066 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a678b772-fdcb-4429-8ffd-b8cfa31be482_config-0] Success to connect a server [localhost:8848], connectionId = 1754903432944_127.0.0.1_1934
17:10:33.066 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a678b772-fdcb-4429-8ffd-b8cfa31be482_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1754902916643_127.0.0.1_14217
17:10:33.066 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754902916643_127.0.0.1_14217
17:10:33.067 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a678b772-fdcb-4429-8ffd-b8cfa31be482_config-0] Notify disconnected event to listeners
17:10:33.067 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a678b772-fdcb-4429-8ffd-b8cfa31be482_config-0] Notify connected event to listeners.
17:12:04.394 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a678b772-fdcb-4429-8ffd-b8cfa31be482_config-0] Server check success, currentServer is localhost:8848 
17:32:01.960 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f156e8c7-8cfe-47ec-b07d-fef8dcda8171] Server healthy check fail, currentConnection = 1754902010937_127.0.0.1_11557
17:32:01.960 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f156e8c7-8cfe-47ec-b07d-fef8dcda8171] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:32:02.096 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f156e8c7-8cfe-47ec-b07d-fef8dcda8171] Success to connect a server [localhost:8848], connectionId = 1754904721973_127.0.0.1_5456
17:32:02.096 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f156e8c7-8cfe-47ec-b07d-fef8dcda8171] Abandon prev connection, server is localhost:8848, connectionId is 1754902010937_127.0.0.1_11557
17:32:02.096 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754902010937_127.0.0.1_11557
17:32:02.097 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f156e8c7-8cfe-47ec-b07d-fef8dcda8171] Notify disconnected event to listeners
17:32:02.098 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f156e8c7-8cfe-47ec-b07d-fef8dcda8171] Notify connected event to listeners.
17:32:05.550 [nacos-grpc-client-executor-176] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f156e8c7-8cfe-47ec-b07d-fef8dcda8171] Receive server push request, request = NotifySubscriberRequest, requestId = 119
17:32:05.550 [nacos-grpc-client-executor-176] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f156e8c7-8cfe-47ec-b07d-fef8dcda8171] Ack server push request, request = NotifySubscriberRequest, requestId = 119
17:42:03.329 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:42:03.332 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:42:03.664 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:42:03.664 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1a7d486d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:42:03.664 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754904721973_127.0.0.1_5456
17:42:03.664 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@46fbe422[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 298]
17:42:03.797 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:42:03.797 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:42:03.797 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:42:03.797 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:42:03.797 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:42:03.797 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:44:09.910 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:44:10.536 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 763ef92e-8391-4e91-bf02-305bba6ad685_config-0
17:44:10.594 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 3 keys and 6 values 
17:44:10.614 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 4 keys and 9 values 
17:44:10.629 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
17:44:10.637 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
17:44:10.644 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
17:44:10.648 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 2 ms to scan 1 urls, producing 2 keys and 8 values 
17:44:10.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [763ef92e-8391-4e91-bf02-305bba6ad685_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:44:10.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [763ef92e-8391-4e91-bf02-305bba6ad685_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000201d23b8b08
17:44:10.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [763ef92e-8391-4e91-bf02-305bba6ad685_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000201d23b8d28
17:44:10.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [763ef92e-8391-4e91-bf02-305bba6ad685_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:44:10.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [763ef92e-8391-4e91-bf02-305bba6ad685_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:44:10.660 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [763ef92e-8391-4e91-bf02-305bba6ad685_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:44:11.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [763ef92e-8391-4e91-bf02-305bba6ad685_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754905451135_127.0.0.1_7718
17:44:11.329 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [763ef92e-8391-4e91-bf02-305bba6ad685_config-0] Notify connected event to listeners.
17:44:11.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [763ef92e-8391-4e91-bf02-305bba6ad685_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:44:11.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [763ef92e-8391-4e91-bf02-305bba6ad685_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000201d24f0ad8
17:44:11.458 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:44:14.404 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:44:14.404 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:44:14.405 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:44:14.520 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:44:15.116 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:44:15.118 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:44:15.120 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:44:21.511 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:44:23.918 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d9f4a97e-36da-485b-a01f-be4f9d8972e6
17:44:23.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9f4a97e-36da-485b-a01f-be4f9d8972e6] RpcClient init label, labels = {module=naming, source=sdk}
17:44:23.923 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9f4a97e-36da-485b-a01f-be4f9d8972e6] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:44:23.923 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9f4a97e-36da-485b-a01f-be4f9d8972e6] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:44:23.924 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9f4a97e-36da-485b-a01f-be4f9d8972e6] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:44:23.924 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9f4a97e-36da-485b-a01f-be4f9d8972e6] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:44:24.064 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9f4a97e-36da-485b-a01f-be4f9d8972e6] Success to connect to server [localhost:8848] on start up, connectionId = 1754905463934_127.0.0.1_7741
17:44:24.064 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9f4a97e-36da-485b-a01f-be4f9d8972e6] Notify connected event to listeners.
17:44:24.064 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9f4a97e-36da-485b-a01f-be4f9d8972e6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:44:24.065 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9f4a97e-36da-485b-a01f-be4f9d8972e6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000201d24f0ad8
17:44:24.117 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:44:24.148 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
17:44:24.232 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 14.77 seconds (JVM running for 16.59)
17:44:24.241 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:44:24.246 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:44:24.246 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:44:24.645 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9f4a97e-36da-485b-a01f-be4f9d8972e6] Receive server push request, request = NotifySubscriberRequest, requestId = 126
17:44:24.660 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9f4a97e-36da-485b-a01f-be4f9d8972e6] Ack server push request, request = NotifySubscriberRequest, requestId = 126
17:51:47.400 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:51:48.602 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:51:48.602 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:54:21.627 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9f4a97e-36da-485b-a01f-be4f9d8972e6] Server healthy check fail, currentConnection = 1754905463934_127.0.0.1_7741
17:54:22.124 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9f4a97e-36da-485b-a01f-be4f9d8972e6] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:54:22.700 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9f4a97e-36da-485b-a01f-be4f9d8972e6] Success to connect a server [localhost:8848], connectionId = 1754906062586_127.0.0.1_9173
17:54:22.700 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9f4a97e-36da-485b-a01f-be4f9d8972e6] Abandon prev connection, server is localhost:8848, connectionId is 1754905463934_127.0.0.1_7741
17:54:22.700 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754905463934_127.0.0.1_7741
17:54:22.701 [nacos-grpc-client-executor-106] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754905463934_127.0.0.1_7741]Ignore complete event,isRunning:false,isAbandon=true
17:54:22.703 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9f4a97e-36da-485b-a01f-be4f9d8972e6] Notify disconnected event to listeners
17:54:22.704 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9f4a97e-36da-485b-a01f-be4f9d8972e6] Notify connected event to listeners.
17:54:26.978 [nacos-grpc-client-executor-109] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9f4a97e-36da-485b-a01f-be4f9d8972e6] Receive server push request, request = NotifySubscriberRequest, requestId = 134
17:54:26.982 [nacos-grpc-client-executor-109] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d9f4a97e-36da-485b-a01f-be4f9d8972e6] Ack server push request, request = NotifySubscriberRequest, requestId = 134
18:11:21.546 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:11:21.549 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:11:21.882 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:11:21.883 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@14ebd424[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:11:21.883 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754906062586_127.0.0.1_9173
18:11:21.883 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@********[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 223]
18:11:22.020 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:11:22.021 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
18:11:22.031 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
18:11:22.031 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:11:22.032 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:11:22.032 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
18:11:36.179 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:11:37.456 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5ad2cab3-9050-4573-92f0-48c4283f2488_config-0
18:11:37.601 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 65 ms to scan 1 urls, producing 3 keys and 6 values 
18:11:37.676 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 4 keys and 9 values 
18:11:37.694 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 3 keys and 10 values 
18:11:37.711 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
18:11:37.728 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
18:11:37.753 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 2 keys and 8 values 
18:11:37.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ad2cab3-9050-4573-92f0-48c4283f2488_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:11:37.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ad2cab3-9050-4573-92f0-48c4283f2488_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002800139eaf8
18:11:37.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ad2cab3-9050-4573-92f0-48c4283f2488_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002800139ed18
18:11:37.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ad2cab3-9050-4573-92f0-48c4283f2488_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:11:37.763 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ad2cab3-9050-4573-92f0-48c4283f2488_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:11:37.782 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ad2cab3-9050-4573-92f0-48c4283f2488_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:11:39.434 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ad2cab3-9050-4573-92f0-48c4283f2488_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754907099165_127.0.0.1_11847
18:11:39.435 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ad2cab3-9050-4573-92f0-48c4283f2488_config-0] Notify connected event to listeners.
18:11:39.436 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ad2cab3-9050-4573-92f0-48c4283f2488_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:11:39.436 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ad2cab3-9050-4573-92f0-48c4283f2488_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000028001518668
18:11:39.764 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:11:50.022 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
18:11:50.025 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:11:50.027 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
18:11:50.797 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:11:52.014 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
18:11:52.016 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
18:11:52.017 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
18:12:07.082 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:12:11.340 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6c7fed7e-91f2-4e17-a80d-352cdefed142
18:12:11.340 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c7fed7e-91f2-4e17-a80d-352cdefed142] RpcClient init label, labels = {module=naming, source=sdk}
18:12:11.342 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c7fed7e-91f2-4e17-a80d-352cdefed142] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:12:11.342 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c7fed7e-91f2-4e17-a80d-352cdefed142] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:12:11.343 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c7fed7e-91f2-4e17-a80d-352cdefed142] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:12:11.344 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c7fed7e-91f2-4e17-a80d-352cdefed142] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:12:11.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c7fed7e-91f2-4e17-a80d-352cdefed142] Success to connect to server [localhost:8848] on start up, connectionId = 1754907131349_127.0.0.1_11994
18:12:11.477 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c7fed7e-91f2-4e17-a80d-352cdefed142] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:12:11.477 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c7fed7e-91f2-4e17-a80d-352cdefed142] Notify connected event to listeners.
18:12:11.478 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c7fed7e-91f2-4e17-a80d-352cdefed142] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000028001518668
18:12:11.535 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
18:12:11.573 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
18:12:11.785 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 36.522 seconds (JVM running for 38.071)
18:12:11.804 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
18:12:11.805 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
18:12:11.806 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
18:12:11.954 [RMI TCP Connection(24)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:12:12.110 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c7fed7e-91f2-4e17-a80d-352cdefed142] Receive server push request, request = NotifySubscriberRequest, requestId = 140
18:12:12.126 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c7fed7e-91f2-4e17-a80d-352cdefed142] Ack server push request, request = NotifySubscriberRequest, requestId = 140
18:20:06.645 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
18:20:06.645 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:26:49.174 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:26:49.177 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:26:49.515 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:26:49.515 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6cbad179[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:26:49.516 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754907131349_127.0.0.1_11994
18:26:49.518 [nacos-grpc-client-executor-186] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754907131349_127.0.0.1_11994]Ignore complete event,isRunning:false,isAbandon=false
18:26:49.520 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@257639f4[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 187]
18:26:49.678 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:26:49.682 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
18:26:49.689 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
18:26:49.690 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:26:49.692 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:26:49.693 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
18:27:03.479 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:27:05.462 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 12c176cb-88ec-415e-8865-a3a3bf128672_config-0
18:27:05.702 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 125 ms to scan 1 urls, producing 3 keys and 6 values 
18:27:05.829 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 33 ms to scan 1 urls, producing 4 keys and 9 values 
18:27:05.859 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 10 values 
18:27:05.883 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 5 values 
18:27:05.906 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 1 keys and 7 values 
18:27:05.930 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 2 keys and 8 values 
18:27:05.936 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12c176cb-88ec-415e-8865-a3a3bf128672_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:27:05.937 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12c176cb-88ec-415e-8865-a3a3bf128672_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001ffc63b71c0
18:27:05.938 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12c176cb-88ec-415e-8865-a3a3bf128672_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001ffc63b73e0
18:27:05.939 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12c176cb-88ec-415e-8865-a3a3bf128672_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:27:05.941 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12c176cb-88ec-415e-8865-a3a3bf128672_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:27:05.965 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12c176cb-88ec-415e-8865-a3a3bf128672_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:27:07.660 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12c176cb-88ec-415e-8865-a3a3bf128672_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754908027357_127.0.0.1_14748
18:27:07.662 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12c176cb-88ec-415e-8865-a3a3bf128672_config-0] Notify connected event to listeners.
18:27:07.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12c176cb-88ec-415e-8865-a3a3bf128672_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:27:07.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [12c176cb-88ec-415e-8865-a3a3bf128672_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001ffc64f0fb0
18:27:07.837 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:27:12.184 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
18:27:12.184 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:27:12.184 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
18:27:12.357 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:27:13.203 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
18:27:13.204 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
18:27:13.204 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
18:27:22.435 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:27:28.846 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 499950d7-8f4f-4d68-8c09-f066a8a59588
18:27:28.847 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [499950d7-8f4f-4d68-8c09-f066a8a59588] RpcClient init label, labels = {module=naming, source=sdk}
18:27:28.849 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [499950d7-8f4f-4d68-8c09-f066a8a59588] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:27:28.850 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [499950d7-8f4f-4d68-8c09-f066a8a59588] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:27:28.850 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [499950d7-8f4f-4d68-8c09-f066a8a59588] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:27:28.851 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [499950d7-8f4f-4d68-8c09-f066a8a59588] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:27:28.981 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [499950d7-8f4f-4d68-8c09-f066a8a59588] Success to connect to server [localhost:8848] on start up, connectionId = 1754908048863_127.0.0.1_14811
18:27:28.982 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [499950d7-8f4f-4d68-8c09-f066a8a59588] Notify connected event to listeners.
18:27:28.982 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [499950d7-8f4f-4d68-8c09-f066a8a59588] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:27:28.983 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [499950d7-8f4f-4d68-8c09-f066a8a59588] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001ffc64f0fb0
18:27:29.047 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
18:27:29.096 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
18:27:29.454 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 27.709 seconds (JVM running for 30.346)
18:27:29.506 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
18:27:29.507 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
18:27:29.508 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
18:27:29.628 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [499950d7-8f4f-4d68-8c09-f066a8a59588] Receive server push request, request = NotifySubscriberRequest, requestId = 148
18:27:29.679 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [499950d7-8f4f-4d68-8c09-f066a8a59588] Ack server push request, request = NotifySubscriberRequest, requestId = 148
18:27:58.031 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:27:59.230 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
18:27:59.230 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:31:12.611 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:31:12.611 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:31:12.945 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:31:12.945 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@27a60da3[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:31:12.945 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754908048863_127.0.0.1_14811
18:31:12.947 [nacos-grpc-client-executor-53] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754908048863_127.0.0.1_14811]Ignore complete event,isRunning:false,isAbandon=false
18:31:12.948 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@391b5796[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 54]
18:31:13.106 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:31:13.109 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
18:31:13.111 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
18:31:13.111 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:31:13.111 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:31:13.111 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
