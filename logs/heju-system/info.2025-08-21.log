09:01:07.376 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:01:07.381 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:01:07.726 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:01:07.727 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5b5311ed[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:01:07.736 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755688972474_127.0.0.1_5630
09:01:07.770 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6b1cd298[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 3103]
09:01:07.765 [nacos-grpc-client-executor-3103] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755688972474_127.0.0.1_5630]Ignore complete event,isRunning:false,isAbandon=false
09:01:08.030 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:01:08.031 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
09:01:08.032 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
09:01:08.032 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:01:08.032 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:01:08.033 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:32:05.283 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:32:06.152 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5005ef13-283e-4783-a78d-b0c6fd87100f_config-0
09:32:06.292 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 3 keys and 6 values 
09:32:06.345 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 4 keys and 9 values 
09:32:06.361 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
09:32:06.420 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 35 ms to scan 1 urls, producing 1 keys and 5 values 
09:32:06.423 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 7 values 
09:32:06.452 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
09:32:06.459 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5005ef13-283e-4783-a78d-b0c6fd87100f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:32:06.460 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5005ef13-283e-4783-a78d-b0c6fd87100f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001c31f39bda8
09:32:06.460 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5005ef13-283e-4783-a78d-b0c6fd87100f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001c31f39bfc8
09:32:06.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5005ef13-283e-4783-a78d-b0c6fd87100f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:32:06.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5005ef13-283e-4783-a78d-b0c6fd87100f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:32:06.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5005ef13-283e-4783-a78d-b0c6fd87100f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:32:07.750 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5005ef13-283e-4783-a78d-b0c6fd87100f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755739927527_127.0.0.1_13229
09:32:07.750 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5005ef13-283e-4783-a78d-b0c6fd87100f_config-0] Notify connected event to listeners.
09:32:07.750 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5005ef13-283e-4783-a78d-b0c6fd87100f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:32:07.750 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5005ef13-283e-4783-a78d-b0c6fd87100f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001c31f513ff8
09:32:07.851 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:32:11.427 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:32:11.428 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:32:11.428 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:32:11.555 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:32:12.287 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:32:12.302 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:32:12.302 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:32:20.125 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:32:22.475 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2d57d93e-f411-4604-81b8-7fca06cddc8a
09:32:22.475 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d57d93e-f411-4604-81b8-7fca06cddc8a] RpcClient init label, labels = {module=naming, source=sdk}
09:32:22.475 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d57d93e-f411-4604-81b8-7fca06cddc8a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:32:22.475 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d57d93e-f411-4604-81b8-7fca06cddc8a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:32:22.475 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d57d93e-f411-4604-81b8-7fca06cddc8a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:32:22.475 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d57d93e-f411-4604-81b8-7fca06cddc8a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:32:22.615 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d57d93e-f411-4604-81b8-7fca06cddc8a] Success to connect to server [localhost:8848] on start up, connectionId = 1755739942492_127.0.0.1_13251
09:32:22.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d57d93e-f411-4604-81b8-7fca06cddc8a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:32:22.616 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d57d93e-f411-4604-81b8-7fca06cddc8a] Notify connected event to listeners.
09:32:22.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d57d93e-f411-4604-81b8-7fca06cddc8a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001c31f513ff8
09:32:22.645 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:32:22.674 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:32:22.779 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 18.161 seconds (JVM running for 24.394)
09:32:22.797 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:32:22.797 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:32:22.807 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:32:23.200 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d57d93e-f411-4604-81b8-7fca06cddc8a] Receive server push request, request = NotifySubscriberRequest, requestId = 96
09:32:23.219 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d57d93e-f411-4604-81b8-7fca06cddc8a] Ack server push request, request = NotifySubscriberRequest, requestId = 96
09:46:25.131 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:46:26.169 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:46:26.169 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:00:35.821 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:00:35.834 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:00:36.158 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:00:36.158 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@a2f00a3[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:00:36.158 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755739942492_127.0.0.1_13251
11:00:36.158 [nacos-grpc-client-executor-1068] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755739942492_127.0.0.1_13251]Ignore complete event,isRunning:false,isAbandon=false
11:00:36.158 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5cf6b206[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 1069]
11:00:36.326 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:00:36.332 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:00:36.339 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:00:36.339 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:00:36.345 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:00:36.345 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:00:41.487 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:00:41.996 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9e94d422-07ad-4148-bad8-e444807371f6_config-0
11:00:42.047 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 3 keys and 6 values 
11:00:42.077 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
11:00:42.083 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
11:00:42.089 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
11:00:42.095 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
11:00:42.100 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
11:00:42.102 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e94d422-07ad-4148-bad8-e444807371f6_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:00:42.102 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e94d422-07ad-4148-bad8-e444807371f6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000022f823be480
11:00:42.102 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e94d422-07ad-4148-bad8-e444807371f6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000022f823be6a0
11:00:42.103 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e94d422-07ad-4148-bad8-e444807371f6_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:00:42.103 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e94d422-07ad-4148-bad8-e444807371f6_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:00:42.110 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e94d422-07ad-4148-bad8-e444807371f6_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:00:42.775 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e94d422-07ad-4148-bad8-e444807371f6_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755745242594_127.0.0.1_6267
11:00:42.776 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e94d422-07ad-4148-bad8-e444807371f6_config-0] Notify connected event to listeners.
11:00:42.776 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e94d422-07ad-4148-bad8-e444807371f6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:00:42.776 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e94d422-07ad-4148-bad8-e444807371f6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000022f824f8228
11:00:42.849 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:00:45.150 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:00:45.150 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:00:45.151 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:00:45.255 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:00:45.795 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:00:45.796 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:00:45.796 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:00:51.279 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:00:53.318 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 122d9073-0d05-4094-935b-58df7bba659a
11:00:53.319 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [122d9073-0d05-4094-935b-58df7bba659a] RpcClient init label, labels = {module=naming, source=sdk}
11:00:53.320 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [122d9073-0d05-4094-935b-58df7bba659a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:00:53.320 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [122d9073-0d05-4094-935b-58df7bba659a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:00:53.320 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [122d9073-0d05-4094-935b-58df7bba659a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:00:53.320 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [122d9073-0d05-4094-935b-58df7bba659a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:00:53.450 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [122d9073-0d05-4094-935b-58df7bba659a] Success to connect to server [localhost:8848] on start up, connectionId = 1755745253328_127.0.0.1_6292
11:00:53.450 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [122d9073-0d05-4094-935b-58df7bba659a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:00:53.450 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [122d9073-0d05-4094-935b-58df7bba659a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000022f824f8228
11:00:53.450 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [122d9073-0d05-4094-935b-58df7bba659a] Notify connected event to listeners.
11:00:53.488 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:00:53.511 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:00:53.593 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.55 seconds (JVM running for 13.37)
11:00:53.605 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:00:53.606 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:00:53.606 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:00:53.792 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:00:54.020 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [122d9073-0d05-4094-935b-58df7bba659a] Receive server push request, request = NotifySubscriberRequest, requestId = 106
11:00:54.036 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [122d9073-0d05-4094-935b-58df7bba659a] Ack server push request, request = NotifySubscriberRequest, requestId = 106
11:01:06.522 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
11:01:06.522 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:01:06.543 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:01:06.543 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
11:01:06.548 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
11:01:06.563 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
11:01:06.564 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:01:06.565 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:01:06.567 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:01:06.567 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:33:38.889 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:33:38.889 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:33:39.224 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:33:39.224 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@62ca7886[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:33:39.224 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755745253328_127.0.0.1_6292
11:33:39.227 [nacos-grpc-client-executor-404] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755745253328_127.0.0.1_6292]Ignore complete event,isRunning:false,isAbandon=false
11:33:39.233 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@********[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 405]
11:33:39.384 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:33:39.384 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
11:33:39.384 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
11:33:39.384 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:33:39.384 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:33:39.384 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:34:06.246 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:34:06.855 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-dd6e-46b7-a31b-bd167c105006_config-0
11:34:06.914 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 3 keys and 6 values 
11:34:06.943 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
11:34:06.949 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
11:34:06.956 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
11:34:06.963 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
11:34:06.970 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
11:34:06.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-dd6e-46b7-a31b-bd167c105006_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:34:06.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-dd6e-46b7-a31b-bd167c105006_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002dcd13b68d8
11:34:06.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-dd6e-46b7-a31b-bd167c105006_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000002dcd13b6af8
11:34:06.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-dd6e-46b7-a31b-bd167c105006_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:34:06.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-dd6e-46b7-a31b-bd167c105006_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:34:06.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-dd6e-46b7-a31b-bd167c105006_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:34:07.651 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-dd6e-46b7-a31b-bd167c105006_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755747247464_127.0.0.1_8726
11:34:07.651 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-dd6e-46b7-a31b-bd167c105006_config-0] Notify connected event to listeners.
11:34:07.651 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-dd6e-46b7-a31b-bd167c105006_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:34:07.652 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-dd6e-46b7-a31b-bd167c105006_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002dcd14f0668
11:34:07.782 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:34:10.171 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:34:10.172 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:34:10.172 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:34:10.283 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:34:10.815 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:34:10.817 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:34:10.817 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:34:22.518 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:34:26.678 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6fb8fbc7-24bc-4e8f-99d7-87d4b305eb26
11:34:26.679 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb8fbc7-24bc-4e8f-99d7-87d4b305eb26] RpcClient init label, labels = {module=naming, source=sdk}
11:34:26.681 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb8fbc7-24bc-4e8f-99d7-87d4b305eb26] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:34:26.681 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb8fbc7-24bc-4e8f-99d7-87d4b305eb26] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:34:26.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb8fbc7-24bc-4e8f-99d7-87d4b305eb26] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:34:26.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb8fbc7-24bc-4e8f-99d7-87d4b305eb26] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:34:26.814 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb8fbc7-24bc-4e8f-99d7-87d4b305eb26] Success to connect to server [localhost:8848] on start up, connectionId = 1755747266691_127.0.0.1_8792
11:34:26.814 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb8fbc7-24bc-4e8f-99d7-87d4b305eb26] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:34:26.814 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb8fbc7-24bc-4e8f-99d7-87d4b305eb26] Notify connected event to listeners.
11:34:26.814 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb8fbc7-24bc-4e8f-99d7-87d4b305eb26] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002dcd14f0668
11:34:26.878 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:34:26.921 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:34:27.381 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb8fbc7-24bc-4e8f-99d7-87d4b305eb26] Receive server push request, request = NotifySubscriberRequest, requestId = 110
11:34:27.402 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb8fbc7-24bc-4e8f-99d7-87d4b305eb26] Ack server push request, request = NotifySubscriberRequest, requestId = 110
11:34:27.451 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 21.628 seconds (JVM running for 23.309)
11:34:27.477 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:34:27.478 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:34:27.479 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:41:14.045 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:41:15.138 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:41:15.139 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:48:43.062 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:48:43.063 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:48:43.394 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:48:43.395 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@8143f82[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:48:43.395 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755747266691_127.0.0.1_8792
11:48:43.397 [nacos-grpc-client-executor-183] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755747266691_127.0.0.1_8792]Ignore complete event,isRunning:false,isAbandon=false
11:48:43.398 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6d81b21d[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 184]
11:48:43.527 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:48:43.529 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:48:43.534 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:48:43.534 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:48:43.535 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:48:43.535 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:48:49.128 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:48:49.700 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 37c55d84-37f6-45a9-9af6-7cd0927370a1_config-0
11:48:49.754 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 
11:48:49.782 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
11:48:49.788 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
11:48:49.795 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
11:48:49.801 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
11:48:49.810 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
11:48:49.811 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37c55d84-37f6-45a9-9af6-7cd0927370a1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:48:49.812 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37c55d84-37f6-45a9-9af6-7cd0927370a1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001be013c0b08
11:48:49.812 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37c55d84-37f6-45a9-9af6-7cd0927370a1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001be013c0d28
11:48:49.812 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37c55d84-37f6-45a9-9af6-7cd0927370a1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:48:49.813 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37c55d84-37f6-45a9-9af6-7cd0927370a1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:48:49.819 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37c55d84-37f6-45a9-9af6-7cd0927370a1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:48:50.512 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37c55d84-37f6-45a9-9af6-7cd0927370a1_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755748130330_127.0.0.1_10338
11:48:50.513 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37c55d84-37f6-45a9-9af6-7cd0927370a1_config-0] Notify connected event to listeners.
11:48:50.513 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37c55d84-37f6-45a9-9af6-7cd0927370a1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:48:50.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37c55d84-37f6-45a9-9af6-7cd0927370a1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001be014fa958
11:48:50.592 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:48:53.055 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:48:53.057 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:48:53.057 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:48:53.164 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:48:53.694 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:48:53.695 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:48:53.696 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:49:03.589 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:49:08.370 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d
11:49:08.370 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] RpcClient init label, labels = {module=naming, source=sdk}
11:49:08.373 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:49:08.374 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:49:08.374 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:49:08.375 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:49:08.517 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] Success to connect to server [localhost:8848] on start up, connectionId = 1755748148388_127.0.0.1_10361
11:49:08.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:49:08.518 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] Notify connected event to listeners.
11:49:08.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001be014fa958
11:49:08.602 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:49:08.649 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:49:08.919 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.273 seconds (JVM running for 21.176)
11:49:08.949 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:49:08.950 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:49:08.951 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:49:09.076 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] Receive server push request, request = NotifySubscriberRequest, requestId = 118
11:49:09.092 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] Ack server push request, request = NotifySubscriberRequest, requestId = 118
11:49:09.432 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:52:09.790 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:52:09.790 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:52:09.790 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
11:52:09.806 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:52:09.812 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:52:09.812 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:14:19.078 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] Server healthy check fail, currentConnection = 1755748148388_127.0.0.1_10361
12:14:19.079 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] Try to reconnect to a new server, server is  not appointed, will choose a random server.
12:14:30.674 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] Success to connect a server [localhost:8848], connectionId = 1755749659092_127.0.0.1_12382
12:14:30.675 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] Abandon prev connection, server is localhost:8848, connectionId is 1755748148388_127.0.0.1_10361
12:14:30.676 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755748148388_127.0.0.1_10361
12:14:30.705 [nacos-grpc-client-executor-308] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755748148388_127.0.0.1_10361]Ignore complete event,isRunning:false,isAbandon=true
12:15:09.043 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] Notify disconnected event to listeners
12:15:09.043 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] Notify connected event to listeners.
12:15:09.591 [nacos-grpc-client-executor-312] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] Receive server push request, request = NotifySubscriberRequest, requestId = 122
12:15:09.591 [nacos-grpc-client-executor-312] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] Ack server push request, request = NotifySubscriberRequest, requestId = 122
12:32:08.634 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] Server healthy check fail, currentConnection = 1755749659092_127.0.0.1_12382
12:32:08.635 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] Try to reconnect to a new server, server is  not appointed, will choose a random server.
12:32:08.752 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] Success to connect a server [localhost:8848], connectionId = 1755750728646_127.0.0.1_13702
12:32:08.752 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] Abandon prev connection, server is localhost:8848, connectionId is 1755749659092_127.0.0.1_12382
12:32:08.752 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755749659092_127.0.0.1_12382
12:32:08.752 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] Notify disconnected event to listeners
12:32:08.752 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] Notify connected event to listeners.
12:36:21.637 [nacos-grpc-client-executor-517] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] Receive server push request, request = NotifySubscriberRequest, requestId = 129
12:36:21.645 [nacos-grpc-client-executor-517] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cf8d6ac7-3da4-4cbb-b1ba-98ec271eb50d] Ack server push request, request = NotifySubscriberRequest, requestId = 129
12:36:25.482 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:36:25.482 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:36:25.818 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:36:25.818 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@69fb5c77[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:36:25.818 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755750728646_127.0.0.1_13702
12:36:25.820 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@48e6e07[Running, pool size = 10, active threads = 0, queued tasks = 0, completed tasks = 521]
12:36:25.999 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:36:25.999 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
12:36:26.001 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
12:36:26.001 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:36:26.001 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:36:26.001 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:36:30.529 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:36:31.075 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ce7b234e-ec68-473b-9f10-1d498eeec32a_config-0
12:36:31.122 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 3 keys and 6 values 
12:36:31.148 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
12:36:31.157 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
12:36:31.165 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
12:36:31.172 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
12:36:31.179 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
12:36:31.181 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ce7b234e-ec68-473b-9f10-1d498eeec32a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:36:31.181 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ce7b234e-ec68-473b-9f10-1d498eeec32a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001e28a3bdd70
12:36:31.182 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ce7b234e-ec68-473b-9f10-1d498eeec32a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001e28a3bdf90
12:36:31.182 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ce7b234e-ec68-473b-9f10-1d498eeec32a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:36:31.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ce7b234e-ec68-473b-9f10-1d498eeec32a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:36:31.188 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ce7b234e-ec68-473b-9f10-1d498eeec32a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:36:31.880 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ce7b234e-ec68-473b-9f10-1d498eeec32a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755750991695_127.0.0.1_13999
12:36:31.880 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ce7b234e-ec68-473b-9f10-1d498eeec32a_config-0] Notify connected event to listeners.
12:36:31.881 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ce7b234e-ec68-473b-9f10-1d498eeec32a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:36:31.882 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ce7b234e-ec68-473b-9f10-1d498eeec32a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001e28a4f7cb0
12:36:31.958 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:36:34.354 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
12:36:34.354 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:36:34.354 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:36:34.473 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:36:34.980 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:36:34.981 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:36:34.981 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:36:39.943 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:36:41.950 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6f387bf1-486f-484c-8e2d-77e6e99ca031
12:36:41.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f387bf1-486f-484c-8e2d-77e6e99ca031] RpcClient init label, labels = {module=naming, source=sdk}
12:36:41.951 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f387bf1-486f-484c-8e2d-77e6e99ca031] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:36:41.951 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f387bf1-486f-484c-8e2d-77e6e99ca031] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:36:41.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f387bf1-486f-484c-8e2d-77e6e99ca031] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:36:41.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f387bf1-486f-484c-8e2d-77e6e99ca031] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:36:42.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f387bf1-486f-484c-8e2d-77e6e99ca031] Success to connect to server [localhost:8848] on start up, connectionId = 1755751001958_127.0.0.1_14011
12:36:42.079 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f387bf1-486f-484c-8e2d-77e6e99ca031] Notify connected event to listeners.
12:36:42.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f387bf1-486f-484c-8e2d-77e6e99ca031] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:36:42.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f387bf1-486f-484c-8e2d-77e6e99ca031] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001e28a4f7cb0
12:36:42.119 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
12:36:42.140 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
12:36:42.221 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.165 seconds (JVM running for 12.997)
12:36:42.232 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
12:36:42.232 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
12:36:42.233 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
12:36:42.696 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f387bf1-486f-484c-8e2d-77e6e99ca031] Receive server push request, request = NotifySubscriberRequest, requestId = 137
12:36:42.711 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f387bf1-486f-484c-8e2d-77e6e99ca031] Ack server push request, request = NotifySubscriberRequest, requestId = 137
12:36:42.748 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:37:04.292 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
12:37:04.292 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:37:04.304 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
12:37:04.305 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
12:37:04.306 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
12:37:04.311 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
12:37:04.311 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:37:04.311 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
12:37:04.313 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
12:37:04.313 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:07:45.775 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:07:45.775 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:07:46.105 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:07:46.105 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6ce72c94[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:07:46.105 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755751001958_127.0.0.1_14011
14:07:46.107 [nacos-grpc-client-executor-1072] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755751001958_127.0.0.1_14011]Ignore complete event,isRunning:false,isAbandon=false
14:07:46.110 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3ece845d[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1073]
14:07:46.269 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:07:46.269 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:07:46.269 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:07:46.269 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:07:46.269 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:07:46.269 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:10:16.268 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:10:17.199 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7eadbbe7-d18e-4908-afae-2e7a6b7303a0_config-0
14:10:17.273 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 3 keys and 6 values 
14:10:17.311 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
14:10:17.321 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
14:10:17.329 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
14:10:17.339 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
14:10:17.352 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
14:10:17.357 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7eadbbe7-d18e-4908-afae-2e7a6b7303a0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:10:17.358 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7eadbbe7-d18e-4908-afae-2e7a6b7303a0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000028d0939daf0
14:10:17.359 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7eadbbe7-d18e-4908-afae-2e7a6b7303a0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000028d0939dd10
14:10:17.359 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7eadbbe7-d18e-4908-afae-2e7a6b7303a0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:10:17.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7eadbbe7-d18e-4908-afae-2e7a6b7303a0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:10:17.368 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7eadbbe7-d18e-4908-afae-2e7a6b7303a0_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:18.446 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7eadbbe7-d18e-4908-afae-2e7a6b7303a0_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755756618191_127.0.0.1_6183
14:10:18.447 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7eadbbe7-d18e-4908-afae-2e7a6b7303a0_config-0] Notify connected event to listeners.
14:10:18.447 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7eadbbe7-d18e-4908-afae-2e7a6b7303a0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:10:18.448 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7eadbbe7-d18e-4908-afae-2e7a6b7303a0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000028d09517b88
14:10:18.621 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:10:29.020 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:10:29.021 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:10:29.022 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:10:29.532 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:10:31.656 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:10:31.660 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:10:31.660 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:10:47.283 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:10:49.964 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4b9c18c3-e421-40b9-8cc1-cafaedd066c7
14:10:49.964 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b9c18c3-e421-40b9-8cc1-cafaedd066c7] RpcClient init label, labels = {module=naming, source=sdk}
14:10:49.967 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b9c18c3-e421-40b9-8cc1-cafaedd066c7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:10:49.967 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b9c18c3-e421-40b9-8cc1-cafaedd066c7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:10:49.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b9c18c3-e421-40b9-8cc1-cafaedd066c7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:10:49.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b9c18c3-e421-40b9-8cc1-cafaedd066c7] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:50.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b9c18c3-e421-40b9-8cc1-cafaedd066c7] Success to connect to server [localhost:8848] on start up, connectionId = 1755756649973_127.0.0.1_6263
14:10:50.098 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b9c18c3-e421-40b9-8cc1-cafaedd066c7] Notify connected event to listeners.
14:10:50.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b9c18c3-e421-40b9-8cc1-cafaedd066c7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:10:50.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b9c18c3-e421-40b9-8cc1-cafaedd066c7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000028d09517b88
14:10:50.141 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:10:50.165 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:10:50.262 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 34.628 seconds (JVM running for 35.646)
14:10:50.278 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:10:50.279 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:10:50.279 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:10:50.629 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:10:50.682 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b9c18c3-e421-40b9-8cc1-cafaedd066c7] Receive server push request, request = NotifySubscriberRequest, requestId = 144
14:10:50.699 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b9c18c3-e421-40b9-8cc1-cafaedd066c7] Ack server push request, request = NotifySubscriberRequest, requestId = 144
14:10:56.961 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:10:56.961 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:10:57.092 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
14:10:57.094 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:10:57.100 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:10:57.100 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:17:58.702 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:17:58.717 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:17:59.044 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:17:59.044 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4d3156b9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:17:59.044 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755756649973_127.0.0.1_6263
14:17:59.046 [nacos-grpc-client-executor-98] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755756649973_127.0.0.1_6263]Ignore complete event,isRunning:false,isAbandon=false
14:17:59.046 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@********[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 99]
14:17:59.193 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:17:59.193 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
14:17:59.195 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
14:17:59.200 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:17:59.203 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:17:59.203 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:18:04.178 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:18:04.705 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b563962d-abf7-437f-9e7c-8f1a10860166_config-0
14:18:04.757 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
14:18:04.785 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
14:18:04.792 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
14:18:04.800 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
14:18:04.806 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
14:18:04.812 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
14:18:04.814 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b563962d-abf7-437f-9e7c-8f1a10860166_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:18:04.814 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b563962d-abf7-437f-9e7c-8f1a10860166_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002a3e83bdd00
14:18:04.815 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b563962d-abf7-437f-9e7c-8f1a10860166_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002a3e83bdf20
14:18:04.816 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b563962d-abf7-437f-9e7c-8f1a10860166_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:18:04.816 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b563962d-abf7-437f-9e7c-8f1a10860166_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:18:04.822 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b563962d-abf7-437f-9e7c-8f1a10860166_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:18:05.494 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b563962d-abf7-437f-9e7c-8f1a10860166_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755757085314_127.0.0.1_7335
14:18:05.494 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b563962d-abf7-437f-9e7c-8f1a10860166_config-0] Notify connected event to listeners.
14:18:05.495 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b563962d-abf7-437f-9e7c-8f1a10860166_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:18:05.495 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b563962d-abf7-437f-9e7c-8f1a10860166_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002a3e84f7b78
14:18:05.571 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:18:07.934 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:18:07.934 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:18:07.935 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:18:08.042 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:18:08.563 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:18:08.564 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:18:08.564 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:18:13.527 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:18:15.487 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of be3b5b26-66ae-4b05-ab5f-c12d8d2ce336
14:18:15.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be3b5b26-66ae-4b05-ab5f-c12d8d2ce336] RpcClient init label, labels = {module=naming, source=sdk}
14:18:15.488 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be3b5b26-66ae-4b05-ab5f-c12d8d2ce336] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:18:15.488 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be3b5b26-66ae-4b05-ab5f-c12d8d2ce336] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:18:15.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be3b5b26-66ae-4b05-ab5f-c12d8d2ce336] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:18:15.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be3b5b26-66ae-4b05-ab5f-c12d8d2ce336] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:18:15.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be3b5b26-66ae-4b05-ab5f-c12d8d2ce336] Success to connect to server [localhost:8848] on start up, connectionId = 1755757095496_127.0.0.1_7346
14:18:15.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be3b5b26-66ae-4b05-ab5f-c12d8d2ce336] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:18:15.616 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be3b5b26-66ae-4b05-ab5f-c12d8d2ce336] Notify connected event to listeners.
14:18:15.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be3b5b26-66ae-4b05-ab5f-c12d8d2ce336] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002a3e84f7b78
14:18:15.657 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:18:15.678 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:18:15.759 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.052 seconds (JVM running for 12.861)
14:18:15.768 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:18:15.769 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:18:15.770 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:18:15.893 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:18:16.230 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be3b5b26-66ae-4b05-ab5f-c12d8d2ce336] Receive server push request, request = NotifySubscriberRequest, requestId = 150
14:18:16.243 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be3b5b26-66ae-4b05-ab5f-c12d8d2ce336] Ack server push request, request = NotifySubscriberRequest, requestId = 150
14:18:45.974 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:18:45.975 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:18:45.975 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
14:18:45.978 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:18:45.984 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:18:45.984 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:18:46.202 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
14:18:46.202 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
14:18:46.204 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
14:18:46.204 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:36:16.245 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:36:16.260 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:36:16.585 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:36:16.585 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1af3f66a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:36:16.585 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755757095496_127.0.0.1_7346
14:36:16.588 [nacos-grpc-client-executor-229] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755757095496_127.0.0.1_7346]Ignore complete event,isRunning:false,isAbandon=false
14:36:16.591 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3ef711d2[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 230]
14:36:16.760 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:36:16.760 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
14:36:16.760 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
14:36:16.760 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:36:16.760 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:36:16.760 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:36:21.439 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:36:21.989 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-d73c-4530-b99b-46fe06dd248b_config-0
14:36:22.034 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 3 keys and 6 values 
14:36:22.060 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
14:36:22.066 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
14:36:22.072 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
14:36:22.078 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
14:36:22.086 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
14:36:22.088 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-d73c-4530-b99b-46fe06dd248b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:36:22.088 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-d73c-4530-b99b-46fe06dd248b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000020c013bdd70
14:36:22.088 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-d73c-4530-b99b-46fe06dd248b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000020c013bdf90
14:36:22.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-d73c-4530-b99b-46fe06dd248b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:36:22.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-d73c-4530-b99b-46fe06dd248b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:36:22.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-d73c-4530-b99b-46fe06dd248b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:36:22.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-d73c-4530-b99b-46fe06dd248b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755758182572_127.0.0.1_8629
14:36:22.755 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-d73c-4530-b99b-46fe06dd248b_config-0] Notify connected event to listeners.
14:36:22.756 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-d73c-4530-b99b-46fe06dd248b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:36:22.757 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-d73c-4530-b99b-46fe06dd248b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020c014f7cb0
14:36:22.833 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:36:25.228 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:36:25.228 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:36:25.229 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:36:25.337 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:36:25.943 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:36:25.944 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:36:25.944 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:36:31.246 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:36:33.791 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8ebe2489-fd5b-4453-99f7-4636aeb66f7b
14:36:33.791 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ebe2489-fd5b-4453-99f7-4636aeb66f7b] RpcClient init label, labels = {module=naming, source=sdk}
14:36:33.792 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ebe2489-fd5b-4453-99f7-4636aeb66f7b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:36:33.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ebe2489-fd5b-4453-99f7-4636aeb66f7b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:36:33.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ebe2489-fd5b-4453-99f7-4636aeb66f7b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:36:33.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ebe2489-fd5b-4453-99f7-4636aeb66f7b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:36:33.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ebe2489-fd5b-4453-99f7-4636aeb66f7b] Success to connect to server [localhost:8848] on start up, connectionId = 1755758193801_127.0.0.1_8659
14:36:33.915 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ebe2489-fd5b-4453-99f7-4636aeb66f7b] Notify connected event to listeners.
14:36:33.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ebe2489-fd5b-4453-99f7-4636aeb66f7b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:36:33.916 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ebe2489-fd5b-4453-99f7-4636aeb66f7b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020c014f7cb0
14:36:33.956 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:36:33.978 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:36:34.080 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.095 seconds (JVM running for 13.942)
14:36:34.092 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:36:34.093 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:36:34.093 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:36:34.250 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:36:34.545 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ebe2489-fd5b-4453-99f7-4636aeb66f7b] Receive server push request, request = NotifySubscriberRequest, requestId = 157
14:36:34.564 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8ebe2489-fd5b-4453-99f7-4636aeb66f7b] Ack server push request, request = NotifySubscriberRequest, requestId = 157
14:37:17.553 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:37:17.554 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:21:02.997 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:21:03.001 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:21:03.333 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:21:03.333 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6cd265[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:21:03.333 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755758193801_127.0.0.1_8659
16:21:03.337 [nacos-grpc-client-executor-1271] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755758193801_127.0.0.1_8659]Ignore complete event,isRunning:false,isAbandon=false
16:21:03.337 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5e6716a7[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 1271]
16:21:03.535 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:21:03.542 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:21:03.560 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:21:03.560 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:21:03.563 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:21:03.564 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:21:18.656 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:21:19.484 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0a70206a-99d1-44c4-aaab-9aa62d2d3249_config-0
16:21:19.561 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 33 ms to scan 1 urls, producing 3 keys and 6 values 
16:21:19.608 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
16:21:19.620 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
16:21:19.628 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
16:21:19.638 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
16:21:19.644 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
16:21:19.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a70206a-99d1-44c4-aaab-9aa62d2d3249_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:21:19.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a70206a-99d1-44c4-aaab-9aa62d2d3249_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002055b39e8d8
16:21:19.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a70206a-99d1-44c4-aaab-9aa62d2d3249_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002055b39eaf8
16:21:19.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a70206a-99d1-44c4-aaab-9aa62d2d3249_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:21:19.655 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a70206a-99d1-44c4-aaab-9aa62d2d3249_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:21:19.655 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a70206a-99d1-44c4-aaab-9aa62d2d3249_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:21:20.596 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a70206a-99d1-44c4-aaab-9aa62d2d3249_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755764480389_127.0.0.1_4269
16:21:20.596 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a70206a-99d1-44c4-aaab-9aa62d2d3249_config-0] Notify connected event to listeners.
16:21:20.596 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a70206a-99d1-44c4-aaab-9aa62d2d3249_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:21:20.596 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a70206a-99d1-44c4-aaab-9aa62d2d3249_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002055b518668
16:21:20.706 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:21:26.726 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:21:26.726 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:21:26.726 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:21:26.885 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:21:27.542 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:21:27.543 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:21:27.543 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:21:43.944 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:21:48.344 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-885e-49f2-bc44-76e5f1a353e5
16:21:48.344 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-885e-49f2-bc44-76e5f1a353e5] RpcClient init label, labels = {module=naming, source=sdk}
16:21:48.346 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-885e-49f2-bc44-76e5f1a353e5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:21:48.346 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-885e-49f2-bc44-76e5f1a353e5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:21:48.346 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-885e-49f2-bc44-76e5f1a353e5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:21:48.347 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-885e-49f2-bc44-76e5f1a353e5] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:21:48.467 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-885e-49f2-bc44-76e5f1a353e5] Success to connect to server [localhost:8848] on start up, connectionId = 1755764508355_127.0.0.1_4342
16:21:48.467 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-885e-49f2-bc44-76e5f1a353e5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:21:48.467 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-885e-49f2-bc44-76e5f1a353e5] Notify connected event to listeners.
16:21:48.467 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-885e-49f2-bc44-76e5f1a353e5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002055b518668
16:21:48.509 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:21:48.539 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
16:21:48.641 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 30.657 seconds (JVM running for 32.021)
16:21:48.670 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:21:48.671 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:21:48.671 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:21:49.010 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-885e-49f2-bc44-76e5f1a353e5] Receive server push request, request = NotifySubscriberRequest, requestId = 161
16:21:49.026 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-885e-49f2-bc44-76e5f1a353e5] Ack server push request, request = NotifySubscriberRequest, requestId = 161
16:21:49.183 [RMI TCP Connection(28)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:21:55.239 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
16:21:55.239 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:21:55.239 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:21:55.242 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
16:21:55.243 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
16:21:55.252 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
16:21:55.253 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:21:55.254 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:21:55.256 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:21:55.256 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:25:20.206 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:25:20.206 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:25:20.561 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:25:20.561 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@42185e44[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:25:20.561 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755764508355_127.0.0.1_4342
16:25:20.565 [nacos-grpc-client-executor-53] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755764508355_127.0.0.1_4342]Ignore complete event,isRunning:false,isAbandon=false
16:25:20.567 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7f088d54[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 54]
16:25:20.710 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:25:20.710 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
16:25:20.717 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
16:25:20.717 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:25:20.718 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:25:20.718 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:25:32.547 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:25:34.248 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a69740d7-98d3-44b0-ae14-dc9510965a22_config-0
16:25:34.529 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 159 ms to scan 1 urls, producing 3 keys and 6 values 
16:25:34.625 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 4 keys and 9 values 
16:25:34.644 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 3 keys and 10 values 
16:25:34.678 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 5 values 
16:25:34.702 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 1 keys and 7 values 
16:25:34.721 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
16:25:34.726 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a69740d7-98d3-44b0-ae14-dc9510965a22_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:25:34.726 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a69740d7-98d3-44b0-ae14-dc9510965a22_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000299ae3b6f80
16:25:34.726 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a69740d7-98d3-44b0-ae14-dc9510965a22_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000299ae3b71a0
16:25:34.730 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a69740d7-98d3-44b0-ae14-dc9510965a22_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:25:34.730 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a69740d7-98d3-44b0-ae14-dc9510965a22_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:25:34.749 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a69740d7-98d3-44b0-ae14-dc9510965a22_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:25:36.398 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a69740d7-98d3-44b0-ae14-dc9510965a22_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755764736083_127.0.0.1_4838
16:25:36.399 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a69740d7-98d3-44b0-ae14-dc9510965a22_config-0] Notify connected event to listeners.
16:25:36.402 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a69740d7-98d3-44b0-ae14-dc9510965a22_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:25:36.405 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a69740d7-98d3-44b0-ae14-dc9510965a22_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000299ae4f0fb0
16:25:36.638 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:25:41.007 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:25:41.007 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:25:41.007 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:25:41.149 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:25:41.792 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:25:41.792 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:25:41.792 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:25:48.170 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:25:51.049 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bef23f0a-21fd-4179-8d0a-e6c51851921c
16:25:51.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bef23f0a-21fd-4179-8d0a-e6c51851921c] RpcClient init label, labels = {module=naming, source=sdk}
16:25:51.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bef23f0a-21fd-4179-8d0a-e6c51851921c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:25:51.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bef23f0a-21fd-4179-8d0a-e6c51851921c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:25:51.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bef23f0a-21fd-4179-8d0a-e6c51851921c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:25:51.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bef23f0a-21fd-4179-8d0a-e6c51851921c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:25:51.174 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bef23f0a-21fd-4179-8d0a-e6c51851921c] Success to connect to server [localhost:8848] on start up, connectionId = 1755764751058_127.0.0.1_4870
16:25:51.174 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bef23f0a-21fd-4179-8d0a-e6c51851921c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:25:51.174 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bef23f0a-21fd-4179-8d0a-e6c51851921c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000299ae4f0fb0
16:25:51.174 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bef23f0a-21fd-4179-8d0a-e6c51851921c] Notify connected event to listeners.
16:25:51.220 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:25:51.239 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
16:25:51.334 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.197 seconds (JVM running for 23.441)
16:25:51.361 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:25:51.361 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:25:51.361 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:25:51.766 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bef23f0a-21fd-4179-8d0a-e6c51851921c] Receive server push request, request = NotifySubscriberRequest, requestId = 166
16:25:51.787 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bef23f0a-21fd-4179-8d0a-e6c51851921c] Ack server push request, request = NotifySubscriberRequest, requestId = 166
16:26:01.266 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:26:02.514 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:26:02.514 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:26:02.514 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
16:26:02.514 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:26:02.529 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:26:02.529 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:26:02.550 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
16:26:02.550 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
16:26:02.551 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
16:26:02.551 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:14:45.278 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:14:45.278 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:14:45.614 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:14:45.614 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@43e36c9d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:14:45.614 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755764751058_127.0.0.1_4870
17:14:45.618 [nacos-grpc-client-executor-472] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755764751058_127.0.0.1_4870]Ignore complete event,isRunning:false,isAbandon=false
17:14:45.629 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@42b11348[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 473]
17:14:45.820 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:14:45.822 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
17:14:45.824 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
17:14:45.824 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:14:45.826 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:14:45.826 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:14:52.673 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:14:53.262 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3180c633-27b8-410b-afa5-825b7b31aacd_config-0
17:14:53.317 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
17:14:53.343 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 4 keys and 9 values 
17:14:53.359 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 3 keys and 10 values 
17:14:53.359 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
17:14:53.380 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 1 keys and 7 values 
17:14:53.384 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
17:14:53.397 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3180c633-27b8-410b-afa5-825b7b31aacd_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:14:53.397 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3180c633-27b8-410b-afa5-825b7b31aacd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000017b0239fd00
17:14:53.398 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3180c633-27b8-410b-afa5-825b7b31aacd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000017b023a0000
17:14:53.398 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3180c633-27b8-410b-afa5-825b7b31aacd_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:14:53.398 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3180c633-27b8-410b-afa5-825b7b31aacd_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:14:53.406 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3180c633-27b8-410b-afa5-825b7b31aacd_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:14:55.213 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3180c633-27b8-410b-afa5-825b7b31aacd_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755767694750_127.0.0.1_10153
17:14:55.214 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3180c633-27b8-410b-afa5-825b7b31aacd_config-0] Notify connected event to listeners.
17:14:55.215 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3180c633-27b8-410b-afa5-825b7b31aacd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:14:55.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3180c633-27b8-410b-afa5-825b7b31aacd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000017b0251a0a0
17:14:55.536 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:15:06.044 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:15:06.045 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:15:06.045 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:15:06.286 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:15:07.090 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:15:07.093 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:15:07.093 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:15:21.462 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:15:24.336 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3850eeea-337d-492d-a756-7d56cb2ed848
17:15:24.336 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3850eeea-337d-492d-a756-7d56cb2ed848] RpcClient init label, labels = {module=naming, source=sdk}
17:15:24.336 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3850eeea-337d-492d-a756-7d56cb2ed848] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:15:24.336 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3850eeea-337d-492d-a756-7d56cb2ed848] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:15:24.346 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3850eeea-337d-492d-a756-7d56cb2ed848] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:15:24.346 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3850eeea-337d-492d-a756-7d56cb2ed848] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:15:24.479 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3850eeea-337d-492d-a756-7d56cb2ed848] Success to connect to server [localhost:8848] on start up, connectionId = 1755767724354_127.0.0.1_10234
17:15:24.479 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3850eeea-337d-492d-a756-7d56cb2ed848] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:15:24.479 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3850eeea-337d-492d-a756-7d56cb2ed848] Notify connected event to listeners.
17:15:24.479 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3850eeea-337d-492d-a756-7d56cb2ed848] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000017b0251a0a0
17:15:24.524 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:15:24.552 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:15:24.667 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 32.553 seconds (JVM running for 33.518)
17:15:24.682 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:15:24.682 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:15:24.682 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:15:24.984 [RMI TCP Connection(23)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:15:24.994 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3850eeea-337d-492d-a756-7d56cb2ed848] Receive server push request, request = NotifySubscriberRequest, requestId = 172
17:15:25.003 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3850eeea-337d-492d-a756-7d56cb2ed848] Ack server push request, request = NotifySubscriberRequest, requestId = 172
17:15:26.291 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
17:15:26.291 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:15:26.298 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:15:26.302 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
17:15:26.313 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
17:15:26.313 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:35:15.466 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:35:15.466 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:35:15.814 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:35:15.814 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4f621812[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:35:15.814 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755767724354_127.0.0.1_10234
17:35:15.814 [nacos-grpc-client-executor-255] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755767724354_127.0.0.1_10234]Ignore complete event,isRunning:false,isAbandon=false
17:35:15.824 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@404aab52[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 256]
17:35:16.003 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:35:16.003 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:35:16.012 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:35:16.012 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:35:16.012 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:35:16.012 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:35:21.842 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:35:22.484 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bfe9a8ae-a210-4674-9ee9-5ce0d39dbf78_config-0
17:35:22.544 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
17:35:22.567 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 4 keys and 9 values 
17:35:22.581 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 3 keys and 10 values 
17:35:22.581 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
17:35:22.597 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
17:35:22.606 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
17:35:22.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bfe9a8ae-a210-4674-9ee9-5ce0d39dbf78_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:35:22.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bfe9a8ae-a210-4674-9ee9-5ce0d39dbf78_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000028c113ce480
17:35:22.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bfe9a8ae-a210-4674-9ee9-5ce0d39dbf78_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000028c113ce6a0
17:35:22.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bfe9a8ae-a210-4674-9ee9-5ce0d39dbf78_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:35:22.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bfe9a8ae-a210-4674-9ee9-5ce0d39dbf78_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:35:22.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bfe9a8ae-a210-4674-9ee9-5ce0d39dbf78_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:35:23.543 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bfe9a8ae-a210-4674-9ee9-5ce0d39dbf78_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755768923254_127.0.0.1_12039
17:35:23.545 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bfe9a8ae-a210-4674-9ee9-5ce0d39dbf78_config-0] Notify connected event to listeners.
17:35:23.545 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bfe9a8ae-a210-4674-9ee9-5ce0d39dbf78_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:35:23.545 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bfe9a8ae-a210-4674-9ee9-5ce0d39dbf78_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000028c11508668
17:35:23.751 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:35:26.517 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:35:26.517 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:35:26.517 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:35:26.638 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:35:27.091 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:35:27.091 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:35:27.091 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:35:32.360 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:35:34.861 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 66c42497-a1a0-4bf1-8d65-b0b1e6d1f65b
17:35:34.861 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66c42497-a1a0-4bf1-8d65-b0b1e6d1f65b] RpcClient init label, labels = {module=naming, source=sdk}
17:35:34.863 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66c42497-a1a0-4bf1-8d65-b0b1e6d1f65b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:35:34.863 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66c42497-a1a0-4bf1-8d65-b0b1e6d1f65b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:35:34.863 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66c42497-a1a0-4bf1-8d65-b0b1e6d1f65b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:35:34.863 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66c42497-a1a0-4bf1-8d65-b0b1e6d1f65b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:35:34.982 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66c42497-a1a0-4bf1-8d65-b0b1e6d1f65b] Success to connect to server [localhost:8848] on start up, connectionId = 1755768934871_127.0.0.1_12064
17:35:34.982 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66c42497-a1a0-4bf1-8d65-b0b1e6d1f65b] Notify connected event to listeners.
17:35:34.982 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66c42497-a1a0-4bf1-8d65-b0b1e6d1f65b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:35:34.982 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66c42497-a1a0-4bf1-8d65-b0b1e6d1f65b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000028c11508668
17:35:35.026 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:35:35.057 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:35:35.149 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.801 seconds (JVM running for 14.705)
17:35:35.161 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:35:35.162 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:35:35.162 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:35:35.592 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66c42497-a1a0-4bf1-8d65-b0b1e6d1f65b] Receive server push request, request = NotifySubscriberRequest, requestId = 176
17:35:35.608 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66c42497-a1a0-4bf1-8d65-b0b1e6d1f65b] Ack server push request, request = NotifySubscriberRequest, requestId = 176
17:35:35.655 [RMI TCP Connection(9)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:35:57.300 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:35:57.300 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:51:44.760 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:51:44.770 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:51:45.110 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:51:45.110 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5db354a3[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:51:45.111 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755768934871_127.0.0.1_12064
19:51:45.112 [nacos-grpc-client-executor-1652] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755768934871_127.0.0.1_12064]Ignore complete event,isRunning:false,isAbandon=false
19:51:45.123 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@54a5c3e2[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1653]
19:51:45.309 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:51:45.317 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:51:45.332 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:51:45.333 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:51:45.335 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:51:45.336 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
19:58:58.572 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:59:00.727 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9675220c-a394-41c1-9a12-85aaead8a971_config-0
19:59:01.005 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 161 ms to scan 1 urls, producing 3 keys and 6 values 
19:59:01.110 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
19:59:01.150 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
19:59:01.172 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 5 values 
19:59:01.192 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
19:59:01.206 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
19:59:01.211 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9675220c-a394-41c1-9a12-85aaead8a971_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:59:01.211 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9675220c-a394-41c1-9a12-85aaead8a971_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000020ce83b5d70
19:59:01.211 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9675220c-a394-41c1-9a12-85aaead8a971_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000020ce83b5f90
19:59:01.211 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9675220c-a394-41c1-9a12-85aaead8a971_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:59:01.211 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9675220c-a394-41c1-9a12-85aaead8a971_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:59:01.242 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9675220c-a394-41c1-9a12-85aaead8a971_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:59:02.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9675220c-a394-41c1-9a12-85aaead8a971_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755777542590_127.0.0.1_9233
19:59:02.919 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9675220c-a394-41c1-9a12-85aaead8a971_config-0] Notify connected event to listeners.
19:59:02.920 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9675220c-a394-41c1-9a12-85aaead8a971_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:59:02.921 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9675220c-a394-41c1-9a12-85aaead8a971_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000020ce84efb88
19:59:03.126 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:59:06.738 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
19:59:06.739 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:59:06.739 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:59:06.860 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:59:07.421 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
19:59:07.421 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
19:59:07.421 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
19:59:13.160 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:59:15.888 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5792a6e7-ea42-4132-b55a-31bf16c4e14b
19:59:15.893 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5792a6e7-ea42-4132-b55a-31bf16c4e14b] RpcClient init label, labels = {module=naming, source=sdk}
19:59:15.893 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5792a6e7-ea42-4132-b55a-31bf16c4e14b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:59:15.893 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5792a6e7-ea42-4132-b55a-31bf16c4e14b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:59:15.893 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5792a6e7-ea42-4132-b55a-31bf16c4e14b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:59:15.893 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5792a6e7-ea42-4132-b55a-31bf16c4e14b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:59:16.019 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5792a6e7-ea42-4132-b55a-31bf16c4e14b] Success to connect to server [localhost:8848] on start up, connectionId = 1755777555905_127.0.0.1_9243
19:59:16.019 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5792a6e7-ea42-4132-b55a-31bf16c4e14b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:59:16.019 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5792a6e7-ea42-4132-b55a-31bf16c4e14b] Notify connected event to listeners.
19:59:16.020 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5792a6e7-ea42-4132-b55a-31bf16c4e14b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000020ce84efb88
19:59:16.061 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
19:59:16.088 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
19:59:16.188 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 18.891 seconds (JVM running for 22.244)
19:59:16.197 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
19:59:16.197 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
19:59:16.197 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
19:59:16.635 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5792a6e7-ea42-4132-b55a-31bf16c4e14b] Receive server push request, request = NotifySubscriberRequest, requestId = 190
19:59:16.638 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5792a6e7-ea42-4132-b55a-31bf16c4e14b] Ack server push request, request = NotifySubscriberRequest, requestId = 190
19:59:47.412 [http-nio-9600-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:59:49.043 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5792a6e7-ea42-4132-b55a-31bf16c4e14b] Receive server push request, request = NotifySubscriberRequest, requestId = 192
19:59:49.043 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5792a6e7-ea42-4132-b55a-31bf16c4e14b] Ack server push request, request = NotifySubscriberRequest, requestId = 192
19:59:50.071 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
19:59:50.071 [http-nio-9600-exec-7] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:59:50.071 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
19:59:50.087 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
19:59:50.089 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:59:50.094 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:59:50.095 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:59:50.095 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
19:59:50.095 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
19:59:50.095 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:59:50.191 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-7} inited
19:59:50.191 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
19:59:50.192 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
19:59:50.192 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:59:50.196 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-5} inited
19:59:50.196 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-7} closing ...
19:59:50.197 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-7} closed
19:59:50.198 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:59:50.198 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-6} inited
19:59:50.198 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-5} closing ...
19:59:50.199 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-5} closed
19:59:50.200 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
