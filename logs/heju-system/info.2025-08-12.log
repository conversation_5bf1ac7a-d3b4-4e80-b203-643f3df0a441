09:33:30.096 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:33:31.895 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e6c25c30-5b66-4855-beea-59f403a83692_config-0
09:33:31.998 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 44 ms to scan 1 urls, producing 3 keys and 6 values 
09:33:32.048 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 4 keys and 9 values 
09:33:32.060 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:33:32.072 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:33:32.085 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 7 values 
09:33:32.095 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
09:33:32.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6c25c30-5b66-4855-beea-59f403a83692_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:33:32.100 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6c25c30-5b66-4855-beea-59f403a83692_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000025d543b6af8
09:33:32.100 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6c25c30-5b66-4855-beea-59f403a83692_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000025d543b6d18
09:33:32.101 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6c25c30-5b66-4855-beea-59f403a83692_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:33:32.102 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6c25c30-5b66-4855-beea-59f403a83692_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:33:32.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6c25c30-5b66-4855-beea-59f403a83692_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:33:33.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6c25c30-5b66-4855-beea-59f403a83692_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754962413198_127.0.0.1_12770
09:33:33.602 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6c25c30-5b66-4855-beea-59f403a83692_config-0] Notify connected event to listeners.
09:33:33.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6c25c30-5b66-4855-beea-59f403a83692_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:33:33.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6c25c30-5b66-4855-beea-59f403a83692_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000025d544f0668
09:33:34.016 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:33:41.860 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:33:41.860 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:33:41.860 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:33:42.229 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:33:43.736 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:33:43.752 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:33:43.752 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:33:52.219 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:33:55.378 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 037cdd85-5408-4260-b18f-e3f99e0c7169
09:33:55.378 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [037cdd85-5408-4260-b18f-e3f99e0c7169] RpcClient init label, labels = {module=naming, source=sdk}
09:33:55.380 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [037cdd85-5408-4260-b18f-e3f99e0c7169] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:33:55.380 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [037cdd85-5408-4260-b18f-e3f99e0c7169] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:33:55.380 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [037cdd85-5408-4260-b18f-e3f99e0c7169] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:33:55.382 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [037cdd85-5408-4260-b18f-e3f99e0c7169] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:33:55.520 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [037cdd85-5408-4260-b18f-e3f99e0c7169] Success to connect to server [localhost:8848] on start up, connectionId = 1754962435405_127.0.0.1_12844
09:33:55.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [037cdd85-5408-4260-b18f-e3f99e0c7169] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:33:55.521 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [037cdd85-5408-4260-b18f-e3f99e0c7169] Notify connected event to listeners.
09:33:55.522 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [037cdd85-5408-4260-b18f-e3f99e0c7169] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000025d544f0668
09:33:55.590 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:33:55.631 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
09:33:55.785 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 26.813 seconds (JVM running for 28.063)
09:33:55.803 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:33:55.804 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:33:55.804 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:33:56.055 [RMI TCP Connection(11)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:33:56.123 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [037cdd85-5408-4260-b18f-e3f99e0c7169] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:33:56.148 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [037cdd85-5408-4260-b18f-e3f99e0c7169] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:37:06.576 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:37:06.578 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:07:48.930 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:07:48.946 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:07:49.282 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:07:49.282 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2b25ecb9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:07:49.284 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754962435405_127.0.0.1_12844
12:07:49.286 [nacos-grpc-client-executor-1864] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754962435405_127.0.0.1_12844]Ignore complete event,isRunning:false,isAbandon=false
12:07:49.294 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@60a07c84[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1865]
12:07:49.458 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:07:49.470 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
12:07:49.481 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
12:07:49.481 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:07:49.481 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:07:49.481 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:08:17.831 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:08:18.899 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f695f096-80f6-44b4-a0ba-aa7089794cbf_config-0
12:08:18.993 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 40 ms to scan 1 urls, producing 3 keys and 6 values 
12:08:19.044 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
12:08:19.054 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
12:08:19.072 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
12:08:19.078 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 7 values 
12:08:19.107 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 29 ms to scan 1 urls, producing 2 keys and 8 values 
12:08:19.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f695f096-80f6-44b4-a0ba-aa7089794cbf_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:08:19.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f695f096-80f6-44b4-a0ba-aa7089794cbf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001f4203b8b08
12:08:19.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f695f096-80f6-44b4-a0ba-aa7089794cbf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001f4203b8d28
12:08:19.117 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f695f096-80f6-44b4-a0ba-aa7089794cbf_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:08:19.117 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f695f096-80f6-44b4-a0ba-aa7089794cbf_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:08:19.127 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f695f096-80f6-44b4-a0ba-aa7089794cbf_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:08:20.349 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f695f096-80f6-44b4-a0ba-aa7089794cbf_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754971700078_127.0.0.1_5320
12:08:20.349 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f695f096-80f6-44b4-a0ba-aa7089794cbf_config-0] Notify connected event to listeners.
12:08:20.349 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f695f096-80f6-44b4-a0ba-aa7089794cbf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:08:20.349 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f695f096-80f6-44b4-a0ba-aa7089794cbf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001f4204f0668
12:08:20.517 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:08:24.427 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
12:08:24.427 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:08:24.427 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:08:24.580 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:08:25.252 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:08:25.252 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:08:25.252 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:08:32.314 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:08:35.232 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cdfa884d-0ccb-4fb9-92da-8a97e8594241
12:08:35.232 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdfa884d-0ccb-4fb9-92da-8a97e8594241] RpcClient init label, labels = {module=naming, source=sdk}
12:08:35.232 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdfa884d-0ccb-4fb9-92da-8a97e8594241] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:08:35.232 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdfa884d-0ccb-4fb9-92da-8a97e8594241] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:08:35.232 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdfa884d-0ccb-4fb9-92da-8a97e8594241] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:08:35.232 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdfa884d-0ccb-4fb9-92da-8a97e8594241] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:08:35.365 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdfa884d-0ccb-4fb9-92da-8a97e8594241] Success to connect to server [localhost:8848] on start up, connectionId = 1754971715246_127.0.0.1_5370
12:08:35.365 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdfa884d-0ccb-4fb9-92da-8a97e8594241] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:08:35.365 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdfa884d-0ccb-4fb9-92da-8a97e8594241] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001f4204f0668
12:08:35.365 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdfa884d-0ccb-4fb9-92da-8a97e8594241] Notify connected event to listeners.
12:08:35.401 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
12:08:35.441 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
12:08:35.557 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 18.455 seconds (JVM running for 20.561)
12:08:35.557 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
12:08:35.573 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
12:08:35.573 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
12:08:35.912 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdfa884d-0ccb-4fb9-92da-8a97e8594241] Receive server push request, request = NotifySubscriberRequest, requestId = 17
12:08:35.920 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cdfa884d-0ccb-4fb9-92da-8a97e8594241] Ack server push request, request = NotifySubscriberRequest, requestId = 17
12:09:14.014 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:09:15.165 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
12:09:15.165 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:28:50.879 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:28:50.883 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:28:51.218 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:28:51.220 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@9d0c27[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:28:51.220 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754971715246_127.0.0.1_5370
13:28:51.222 [nacos-grpc-client-executor-972] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754971715246_127.0.0.1_5370]Ignore complete event,isRunning:false,isAbandon=false
13:28:51.225 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@56bc5b3b[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 973]
13:28:51.389 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:28:51.396 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:28:51.414 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:28:51.415 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:28:51.416 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:28:51.417 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:28:59.423 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:29:00.461 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bed6ac87-7f6a-49d6-a73d-3860b6be782b_config-0
13:29:00.554 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 40 ms to scan 1 urls, producing 3 keys and 6 values 
13:29:00.607 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
13:29:00.619 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
13:29:00.630 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
13:29:00.642 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
13:29:00.657 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
13:29:00.661 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bed6ac87-7f6a-49d6-a73d-3860b6be782b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:29:00.662 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bed6ac87-7f6a-49d6-a73d-3860b6be782b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000026dcf39eaf8
13:29:00.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bed6ac87-7f6a-49d6-a73d-3860b6be782b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000026dcf39ed18
13:29:00.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bed6ac87-7f6a-49d6-a73d-3860b6be782b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:29:00.664 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bed6ac87-7f6a-49d6-a73d-3860b6be782b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:29:00.674 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bed6ac87-7f6a-49d6-a73d-3860b6be782b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:29:01.724 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bed6ac87-7f6a-49d6-a73d-3860b6be782b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754976541496_127.0.0.1_4462
13:29:01.724 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bed6ac87-7f6a-49d6-a73d-3860b6be782b_config-0] Notify connected event to listeners.
13:29:01.725 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bed6ac87-7f6a-49d6-a73d-3860b6be782b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:29:01.726 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bed6ac87-7f6a-49d6-a73d-3860b6be782b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000026dcf518ad8
13:29:01.825 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:29:05.282 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:29:05.283 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:29:05.283 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:29:05.415 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:29:06.168 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:29:06.169 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:29:06.169 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:29:17.154 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:29:20.030 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f8f31ea4-b784-42f5-bf2f-aafaa36aee8f
13:29:20.031 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8f31ea4-b784-42f5-bf2f-aafaa36aee8f] RpcClient init label, labels = {module=naming, source=sdk}
13:29:20.032 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8f31ea4-b784-42f5-bf2f-aafaa36aee8f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:29:20.032 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8f31ea4-b784-42f5-bf2f-aafaa36aee8f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:29:20.033 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8f31ea4-b784-42f5-bf2f-aafaa36aee8f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:29:20.033 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8f31ea4-b784-42f5-bf2f-aafaa36aee8f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:29:20.168 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8f31ea4-b784-42f5-bf2f-aafaa36aee8f] Success to connect to server [localhost:8848] on start up, connectionId = 1754976560042_127.0.0.1_4487
13:29:20.169 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8f31ea4-b784-42f5-bf2f-aafaa36aee8f] Notify connected event to listeners.
13:29:20.169 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8f31ea4-b784-42f5-bf2f-aafaa36aee8f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:29:20.169 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8f31ea4-b784-42f5-bf2f-aafaa36aee8f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000026dcf518ad8
13:29:20.220 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:29:20.249 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
13:29:20.365 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 21.895 seconds (JVM running for 23.342)
13:29:20.378 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:29:20.378 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:29:20.379 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:29:20.732 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8f31ea4-b784-42f5-bf2f-aafaa36aee8f] Receive server push request, request = NotifySubscriberRequest, requestId = 21
13:29:20.748 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f8f31ea4-b784-42f5-bf2f-aafaa36aee8f] Ack server push request, request = NotifySubscriberRequest, requestId = 21
13:29:20.891 [RMI TCP Connection(12)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:29:30.555 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:29:30.555 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:54:08.162 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:54:08.162 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:54:08.507 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:54:08.507 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@17c55562[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:54:08.507 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754976560042_127.0.0.1_4487
13:54:08.507 [nacos-grpc-client-executor-309] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754976560042_127.0.0.1_4487]Ignore complete event,isRunning:false,isAbandon=false
13:54:08.516 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@75d7ea63[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 310]
13:54:08.695 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:54:08.699 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:54:08.712 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:54:08.712 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:54:08.715 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:54:08.718 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:54:16.627 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:54:17.855 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7d7ff15c-7d6c-4475-9d9b-8553bd4da68a_config-0
13:54:18.025 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 85 ms to scan 1 urls, producing 3 keys and 6 values 
13:54:18.146 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 40 ms to scan 1 urls, producing 4 keys and 9 values 
13:54:18.178 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 3 keys and 10 values 
13:54:18.197 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 5 values 
13:54:18.217 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
13:54:18.243 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 2 keys and 8 values 
13:54:18.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d7ff15c-7d6c-4475-9d9b-8553bd4da68a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:54:18.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d7ff15c-7d6c-4475-9d9b-8553bd4da68a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002625339dd70
13:54:18.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d7ff15c-7d6c-4475-9d9b-8553bd4da68a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002625339df90
13:54:18.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d7ff15c-7d6c-4475-9d9b-8553bd4da68a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:54:18.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d7ff15c-7d6c-4475-9d9b-8553bd4da68a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:54:18.275 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d7ff15c-7d6c-4475-9d9b-8553bd4da68a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:54:19.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d7ff15c-7d6c-4475-9d9b-8553bd4da68a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754978059526_127.0.0.1_8723
13:54:19.985 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d7ff15c-7d6c-4475-9d9b-8553bd4da68a_config-0] Notify connected event to listeners.
13:54:19.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d7ff15c-7d6c-4475-9d9b-8553bd4da68a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:54:19.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d7ff15c-7d6c-4475-9d9b-8553bd4da68a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000026253517b78
13:54:20.475 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:54:26.857 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:54:26.858 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:54:26.858 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:54:27.063 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:54:27.960 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:54:27.962 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:54:27.962 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:54:36.286 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:54:39.460 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d1741874-84c8-4f43-bd3e-65ac2c0fd165
13:54:39.460 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1741874-84c8-4f43-bd3e-65ac2c0fd165] RpcClient init label, labels = {module=naming, source=sdk}
13:54:39.462 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1741874-84c8-4f43-bd3e-65ac2c0fd165] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:54:39.462 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1741874-84c8-4f43-bd3e-65ac2c0fd165] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:54:39.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1741874-84c8-4f43-bd3e-65ac2c0fd165] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:54:39.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1741874-84c8-4f43-bd3e-65ac2c0fd165] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:54:39.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1741874-84c8-4f43-bd3e-65ac2c0fd165] Success to connect to server [localhost:8848] on start up, connectionId = 1754978079472_127.0.0.1_8798
13:54:39.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1741874-84c8-4f43-bd3e-65ac2c0fd165] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:54:39.601 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1741874-84c8-4f43-bd3e-65ac2c0fd165] Notify connected event to listeners.
13:54:39.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1741874-84c8-4f43-bd3e-65ac2c0fd165] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000026253517b78
13:54:39.647 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:54:39.678 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
13:54:39.798 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 24.171 seconds (JVM running for 25.708)
13:54:39.812 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:54:39.812 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:54:39.814 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:54:39.942 [RMI TCP Connection(12)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:54:40.203 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1741874-84c8-4f43-bd3e-65ac2c0fd165] Receive server push request, request = NotifySubscriberRequest, requestId = 27
13:54:40.221 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d1741874-84c8-4f43-bd3e-65ac2c0fd165] Ack server push request, request = NotifySubscriberRequest, requestId = 27
13:55:15.475 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:55:15.476 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:46:59.119 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:46:59.123 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:46:59.453 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:46:59.453 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4a567a90[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:46:59.453 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754978079472_127.0.0.1_8798
14:46:59.453 [nacos-grpc-client-executor-636] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754978079472_127.0.0.1_8798]Ignore complete event,isRunning:false,isAbandon=false
14:46:59.459 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7c5fa1ef[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 637]
14:46:59.636 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:46:59.636 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:46:59.654 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:46:59.654 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:46:59.660 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:46:59.660 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:47:10.740 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:47:11.845 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d73c24c4-2619-401c-909e-e58d17fbae5b_config-0
14:47:11.936 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
14:47:11.988 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
14:47:12.002 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
14:47:12.015 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
14:47:12.025 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
14:47:12.038 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
14:47:12.042 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d73c24c4-2619-401c-909e-e58d17fbae5b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:47:12.043 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d73c24c4-2619-401c-909e-e58d17fbae5b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000021ea539dd70
14:47:12.044 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d73c24c4-2619-401c-909e-e58d17fbae5b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000021ea539df90
14:47:12.044 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d73c24c4-2619-401c-909e-e58d17fbae5b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:47:12.046 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d73c24c4-2619-401c-909e-e58d17fbae5b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:47:12.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d73c24c4-2619-401c-909e-e58d17fbae5b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:47:13.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d73c24c4-2619-401c-909e-e58d17fbae5b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754981232883_127.0.0.1_4602
14:47:13.098 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d73c24c4-2619-401c-909e-e58d17fbae5b_config-0] Notify connected event to listeners.
14:47:13.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d73c24c4-2619-401c-909e-e58d17fbae5b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:47:13.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d73c24c4-2619-401c-909e-e58d17fbae5b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000021ea5517b88
14:47:13.210 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:47:19.138 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:47:19.140 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:47:19.140 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:47:19.471 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:47:20.627 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:47:20.630 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:47:20.631 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:47:30.183 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:47:35.636 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4536355e-5b0a-486f-b4ae-f6e3b5e905ca
14:47:35.636 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4536355e-5b0a-486f-b4ae-f6e3b5e905ca] RpcClient init label, labels = {module=naming, source=sdk}
14:47:35.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4536355e-5b0a-486f-b4ae-f6e3b5e905ca] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:47:35.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4536355e-5b0a-486f-b4ae-f6e3b5e905ca] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:47:35.640 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4536355e-5b0a-486f-b4ae-f6e3b5e905ca] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:47:35.641 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4536355e-5b0a-486f-b4ae-f6e3b5e905ca] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:47:35.777 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4536355e-5b0a-486f-b4ae-f6e3b5e905ca] Success to connect to server [localhost:8848] on start up, connectionId = 1754981255652_127.0.0.1_4717
14:47:35.777 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4536355e-5b0a-486f-b4ae-f6e3b5e905ca] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:47:35.777 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4536355e-5b0a-486f-b4ae-f6e3b5e905ca] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000021ea5517b88
14:47:35.777 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4536355e-5b0a-486f-b4ae-f6e3b5e905ca] Notify connected event to listeners.
14:47:35.878 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:47:35.912 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
14:47:36.073 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 26.32 seconds (JVM running for 28.278)
14:47:36.094 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:47:36.095 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:47:36.096 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:47:36.310 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4536355e-5b0a-486f-b4ae-f6e3b5e905ca] Receive server push request, request = NotifySubscriberRequest, requestId = 31
14:47:36.329 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4536355e-5b0a-486f-b4ae-f6e3b5e905ca] Ack server push request, request = NotifySubscriberRequest, requestId = 31
14:47:36.383 [RMI TCP Connection(23)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:47:40.671 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:47:40.671 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:52:10.855 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:52:10.863 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:52:11.182 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:52:11.182 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5f3c8867[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:52:11.182 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754981255652_127.0.0.1_4717
14:52:11.182 [nacos-grpc-client-executor-63] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754981255652_127.0.0.1_4717]Ignore complete event,isRunning:false,isAbandon=false
14:52:11.182 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1e140e14[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 64]
14:52:11.328 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:52:11.328 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:52:11.345 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:52:11.345 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:52:11.345 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:52:11.345 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:52:18.673 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:52:19.289 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4563cbfd-82e4-4f1c-8c9e-5c2a1b84ff32_config-0
14:52:19.342 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 3 keys and 6 values 
14:52:19.373 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
14:52:19.380 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
14:52:19.388 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
14:52:19.394 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
14:52:19.404 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
14:52:19.408 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4563cbfd-82e4-4f1c-8c9e-5c2a1b84ff32_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:52:19.408 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4563cbfd-82e4-4f1c-8c9e-5c2a1b84ff32_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000016d0139f1c0
14:52:19.408 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4563cbfd-82e4-4f1c-8c9e-5c2a1b84ff32_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000016d0139f3e0
14:52:19.408 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4563cbfd-82e4-4f1c-8c9e-5c2a1b84ff32_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:52:19.409 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4563cbfd-82e4-4f1c-8c9e-5c2a1b84ff32_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:52:19.416 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4563cbfd-82e4-4f1c-8c9e-5c2a1b84ff32_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:52:20.176 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4563cbfd-82e4-4f1c-8c9e-5c2a1b84ff32_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754981539980_127.0.0.1_5760
14:52:20.177 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4563cbfd-82e4-4f1c-8c9e-5c2a1b84ff32_config-0] Notify connected event to listeners.
14:52:20.177 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4563cbfd-82e4-4f1c-8c9e-5c2a1b84ff32_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:52:20.178 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4563cbfd-82e4-4f1c-8c9e-5c2a1b84ff32_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000016d01518668
14:52:20.280 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:52:23.029 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:52:23.030 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:52:23.030 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:52:23.173 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:52:23.904 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:52:23.905 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:52:23.905 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:52:31.143 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:52:37.846 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d70198d6-27c5-40b0-9993-510eb072a89d
14:52:37.847 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d70198d6-27c5-40b0-9993-510eb072a89d] RpcClient init label, labels = {module=naming, source=sdk}
14:52:37.851 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d70198d6-27c5-40b0-9993-510eb072a89d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:52:37.852 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d70198d6-27c5-40b0-9993-510eb072a89d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:52:37.854 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d70198d6-27c5-40b0-9993-510eb072a89d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:52:37.855 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d70198d6-27c5-40b0-9993-510eb072a89d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:52:37.996 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d70198d6-27c5-40b0-9993-510eb072a89d] Success to connect to server [localhost:8848] on start up, connectionId = 1754981557874_127.0.0.1_5835
14:52:37.997 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d70198d6-27c5-40b0-9993-510eb072a89d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:52:37.997 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d70198d6-27c5-40b0-9993-510eb072a89d] Notify connected event to listeners.
14:52:37.997 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d70198d6-27c5-40b0-9993-510eb072a89d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000016d01518668
14:52:38.103 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:52:38.169 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
14:52:38.561 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.519 seconds (JVM running for 21.635)
14:52:38.594 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:52:38.596 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:52:38.596 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:52:38.648 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d70198d6-27c5-40b0-9993-510eb072a89d] Receive server push request, request = NotifySubscriberRequest, requestId = 36
14:52:38.673 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d70198d6-27c5-40b0-9993-510eb072a89d] Ack server push request, request = NotifySubscriberRequest, requestId = 36
14:52:39.019 [RMI TCP Connection(11)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:52:45.543 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:52:45.543 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:52:58.224 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:52:58.238 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:52:58.582 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:52:58.582 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7c3c63ef[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:52:58.582 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754981557874_127.0.0.1_5835
14:52:58.587 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754981557874_127.0.0.1_5835]Ignore complete event,isRunning:false,isAbandon=false
14:52:58.592 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5b9b0bec[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 18]
14:52:58.765 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:52:58.774 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:52:58.789 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:52:58.789 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:52:58.789 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:52:58.794 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:55:54.538 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:55:56.345 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9bac74d6-d01e-4e3c-b6eb-e9de84d33d0c_config-0
14:55:56.459 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 54 ms to scan 1 urls, producing 3 keys and 6 values 
14:55:56.543 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 4 keys and 9 values 
14:55:56.563 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
14:55:56.591 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 1 keys and 5 values 
14:55:56.618 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 1 keys and 7 values 
14:55:56.662 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 2 keys and 8 values 
14:55:56.667 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bac74d6-d01e-4e3c-b6eb-e9de84d33d0c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:55:56.669 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bac74d6-d01e-4e3c-b6eb-e9de84d33d0c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001ff0539c060
14:55:56.670 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bac74d6-d01e-4e3c-b6eb-e9de84d33d0c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001ff0539c280
14:55:56.671 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bac74d6-d01e-4e3c-b6eb-e9de84d33d0c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:55:56.671 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bac74d6-d01e-4e3c-b6eb-e9de84d33d0c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:55:56.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bac74d6-d01e-4e3c-b6eb-e9de84d33d0c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:55:59.336 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bac74d6-d01e-4e3c-b6eb-e9de84d33d0c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754981758972_127.0.0.1_6612
14:55:59.337 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bac74d6-d01e-4e3c-b6eb-e9de84d33d0c_config-0] Notify connected event to listeners.
14:55:59.338 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bac74d6-d01e-4e3c-b6eb-e9de84d33d0c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:55:59.339 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9bac74d6-d01e-4e3c-b6eb-e9de84d33d0c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001ff05514480
14:55:59.569 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:56:05.850 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:56:05.850 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:56:05.850 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:56:06.140 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:56:07.145 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:56:07.148 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:56:07.148 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:56:13.814 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:56:16.562 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 08a66909-560d-4251-8827-28b8254f64d4
14:56:16.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08a66909-560d-4251-8827-28b8254f64d4] RpcClient init label, labels = {module=naming, source=sdk}
14:56:16.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08a66909-560d-4251-8827-28b8254f64d4] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:56:16.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08a66909-560d-4251-8827-28b8254f64d4] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:56:16.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08a66909-560d-4251-8827-28b8254f64d4] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:56:16.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08a66909-560d-4251-8827-28b8254f64d4] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:56:16.692 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08a66909-560d-4251-8827-28b8254f64d4] Success to connect to server [localhost:8848] on start up, connectionId = 1754981776578_127.0.0.1_6720
14:56:16.692 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08a66909-560d-4251-8827-28b8254f64d4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:56:16.692 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08a66909-560d-4251-8827-28b8254f64d4] Notify connected event to listeners.
14:56:16.692 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08a66909-560d-4251-8827-28b8254f64d4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001ff05514480
14:56:16.736 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:56:16.764 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
14:56:16.864 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 23.778 seconds (JVM running for 28.397)
14:56:16.877 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:56:16.877 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:56:16.880 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:56:17.236 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08a66909-560d-4251-8827-28b8254f64d4] Receive server push request, request = NotifySubscriberRequest, requestId = 45
14:56:17.252 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [08a66909-560d-4251-8827-28b8254f64d4] Ack server push request, request = NotifySubscriberRequest, requestId = 45
14:56:25.038 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:56:26.264 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:56:26.264 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:17:31.939 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:17:31.943 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:17:32.277 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:17:32.277 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@27de7acd[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:17:32.277 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754981776578_127.0.0.1_6720
15:17:32.277 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3808eee[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 223]
15:17:32.411 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:17:32.411 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:17:32.411 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:17:32.411 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:17:32.411 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:17:32.411 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:17:40.781 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:17:42.014 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b78fae47-41ee-49d6-be30-f2bc589d814f_config-0
15:17:42.126 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 56 ms to scan 1 urls, producing 3 keys and 6 values 
15:17:42.187 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
15:17:42.200 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
15:17:42.217 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 5 values 
15:17:42.233 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
15:17:42.246 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
15:17:42.250 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b78fae47-41ee-49d6-be30-f2bc589d814f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:17:42.251 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b78fae47-41ee-49d6-be30-f2bc589d814f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000023c093b68d8
15:17:42.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b78fae47-41ee-49d6-be30-f2bc589d814f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000023c093b6af8
15:17:42.253 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b78fae47-41ee-49d6-be30-f2bc589d814f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:17:42.253 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b78fae47-41ee-49d6-be30-f2bc589d814f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:17:42.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b78fae47-41ee-49d6-be30-f2bc589d814f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:17:43.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b78fae47-41ee-49d6-be30-f2bc589d814f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754983063256_127.0.0.1_10218
15:17:43.521 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b78fae47-41ee-49d6-be30-f2bc589d814f_config-0] Notify connected event to listeners.
15:17:43.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b78fae47-41ee-49d6-be30-f2bc589d814f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:17:43.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b78fae47-41ee-49d6-be30-f2bc589d814f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000023c094f0ad8
15:17:43.647 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:17:48.924 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:17:48.924 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:17:48.924 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:17:49.209 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:17:57.118 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:17:57.120 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:17:57.120 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:18:08.880 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:18:13.639 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a1ab3a55-aa72-4ca7-8eac-59c32ec7e251
15:18:13.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1ab3a55-aa72-4ca7-8eac-59c32ec7e251] RpcClient init label, labels = {module=naming, source=sdk}
15:18:13.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1ab3a55-aa72-4ca7-8eac-59c32ec7e251] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:18:13.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1ab3a55-aa72-4ca7-8eac-59c32ec7e251] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:18:13.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1ab3a55-aa72-4ca7-8eac-59c32ec7e251] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:18:13.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1ab3a55-aa72-4ca7-8eac-59c32ec7e251] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:18:13.779 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1ab3a55-aa72-4ca7-8eac-59c32ec7e251] Success to connect to server [localhost:8848] on start up, connectionId = 1754983093652_127.0.0.1_10358
15:18:13.780 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1ab3a55-aa72-4ca7-8eac-59c32ec7e251] Notify connected event to listeners.
15:18:13.781 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1ab3a55-aa72-4ca7-8eac-59c32ec7e251] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:18:13.781 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1ab3a55-aa72-4ca7-8eac-59c32ec7e251] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000023c094f0ad8
15:18:13.849 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:18:13.896 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
15:18:14.120 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 34.607 seconds (JVM running for 36.736)
15:18:14.152 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:18:14.152 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:18:14.152 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:18:14.327 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1ab3a55-aa72-4ca7-8eac-59c32ec7e251] Receive server push request, request = NotifySubscriberRequest, requestId = 50
15:18:14.343 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a1ab3a55-aa72-4ca7-8eac-59c32ec7e251] Ack server push request, request = NotifySubscriberRequest, requestId = 50
15:18:33.469 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:18:37.958 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:18:37.958 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:28:06.243 [lettuce-nioEventLoop-4-1] INFO  i.l.c.p.CommandHandler - [log,217] - null Unexpected exception during request: java.net.SocketException: Connection reset
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:259)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
15:28:06.389 [lettuce-eventExecutorLoop-1-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /***********00:6379
15:28:15.478 [lettuce-eventExecutorLoop-1-17] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
15:28:23.782 [lettuce-eventExecutorLoop-1-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
15:28:40.280 [lettuce-eventExecutorLoop-1-19] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
15:29:20.374 [lettuce-eventExecutorLoop-1-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
15:30:00.486 [lettuce-eventExecutorLoop-1-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
15:30:40.583 [lettuce-eventExecutorLoop-1-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
15:30:40.694 [lettuce-nioEventLoop-4-19] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to ***********00/<unresolved>:6379
19:00:32.149 [lettuce-nioEventLoop-4-19] INFO  i.l.c.p.CommandHandler - [log,217] - null Unexpected exception during request: java.net.SocketException: Connection reset
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:259)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
19:00:32.174 [lettuce-eventExecutorLoop-1-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /***********00:6379
19:00:43.275 [lettuce-eventExecutorLoop-1-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:00:53.874 [lettuce-eventExecutorLoop-1-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:01:04.979 [lettuce-eventExecutorLoop-1-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:01:17.072 [lettuce-eventExecutorLoop-1-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:01:31.277 [lettuce-eventExecutorLoop-1-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:01:49.571 [lettuce-eventExecutorLoop-1-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:02:16.085 [lettuce-eventExecutorLoop-1-21] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:02:56.178 [lettuce-eventExecutorLoop-1-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:03:36.272 [lettuce-eventExecutorLoop-1-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:04:16.378 [lettuce-eventExecutorLoop-1-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:04:56.478 [lettuce-eventExecutorLoop-1-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:05:36.572 [lettuce-eventExecutorLoop-1-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:06:16.675 [lettuce-eventExecutorLoop-1-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:06:17.709 [lettuce-nioEventLoop-4-18] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to ***********00/<unresolved>:6379
19:43:18.165 [lettuce-nioEventLoop-4-18] INFO  i.l.c.p.CommandHandler - [log,217] - null Unexpected exception during request: java.net.SocketException: Connection reset
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:259)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
19:43:18.171 [lettuce-eventExecutorLoop-1-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /***********00:6379
19:43:28.475 [lettuce-eventExecutorLoop-1-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:43:38.572 [lettuce-eventExecutorLoop-1-19] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:43:48.672 [lettuce-eventExecutorLoop-1-20] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:43:58.777 [lettuce-eventExecutorLoop-1-21] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:44:08.973 [lettuce-eventExecutorLoop-1-22] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:44:19.275 [lettuce-eventExecutorLoop-1-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:44:29.877 [lettuce-eventExecutorLoop-1-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:44:40.973 [lettuce-eventExecutorLoop-1-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:44:53.076 [lettuce-eventExecutorLoop-1-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:45:07.278 [lettuce-eventExecutorLoop-1-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:45:25.573 [lettuce-eventExecutorLoop-1-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:45:51.972 [lettuce-eventExecutorLoop-1-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:46:32.071 [lettuce-eventExecutorLoop-1-22] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:47:12.173 [lettuce-eventExecutorLoop-1-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:47:52.273 [lettuce-eventExecutorLoop-1-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:48:32.372 [lettuce-eventExecutorLoop-1-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:49:12.476 [lettuce-eventExecutorLoop-1-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:49:52.572 [lettuce-eventExecutorLoop-1-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:50:32.671 [lettuce-eventExecutorLoop-1-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:51:12.772 [lettuce-eventExecutorLoop-1-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:51:52.880 [lettuce-eventExecutorLoop-1-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:52:32.976 [lettuce-eventExecutorLoop-1-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:53:13.083 [lettuce-eventExecutorLoop-1-20] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:53:53.181 [lettuce-eventExecutorLoop-1-22] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:54:33.277 [lettuce-eventExecutorLoop-1-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:55:13.372 [lettuce-eventExecutorLoop-1-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:55:53.472 [lettuce-eventExecutorLoop-1-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:56:33.576 [lettuce-eventExecutorLoop-1-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:57:13.671 [lettuce-eventExecutorLoop-1-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:57:53.777 [lettuce-eventExecutorLoop-1-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:58:33.871 [lettuce-eventExecutorLoop-1-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:59:13.976 [lettuce-eventExecutorLoop-1-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:59:54.091 [lettuce-eventExecutorLoop-1-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:00:34.171 [lettuce-eventExecutorLoop-1-20] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:01:14.274 [lettuce-eventExecutorLoop-1-22] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:01:54.382 [lettuce-eventExecutorLoop-1-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:02:34.480 [lettuce-eventExecutorLoop-1-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:03:14.575 [lettuce-eventExecutorLoop-1-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:03:54.671 [lettuce-eventExecutorLoop-1-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:04:34.772 [lettuce-eventExecutorLoop-1-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:05:14.875 [lettuce-eventExecutorLoop-1-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:05:54.977 [lettuce-eventExecutorLoop-1-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:06:35.072 [lettuce-eventExecutorLoop-1-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:07:15.172 [lettuce-eventExecutorLoop-1-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:07:55.276 [lettuce-eventExecutorLoop-1-20] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:08:35.371 [lettuce-eventExecutorLoop-1-22] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:09:15.472 [lettuce-eventExecutorLoop-1-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:09:55.577 [lettuce-eventExecutorLoop-1-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:10:35.672 [lettuce-eventExecutorLoop-1-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:11:15.777 [lettuce-eventExecutorLoop-1-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:11:55.877 [lettuce-eventExecutorLoop-1-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:12:35.971 [lettuce-eventExecutorLoop-1-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:13:16.071 [lettuce-eventExecutorLoop-1-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:13:56.183 [lettuce-eventExecutorLoop-1-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:14:36.278 [lettuce-eventExecutorLoop-1-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:15:16.386 [lettuce-eventExecutorLoop-1-20] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:15:56.481 [lettuce-eventExecutorLoop-1-22] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:16:36.571 [lettuce-eventExecutorLoop-1-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:17:16.672 [lettuce-eventExecutorLoop-1-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:17:56.772 [lettuce-eventExecutorLoop-1-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:18:36.877 [lettuce-eventExecutorLoop-1-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:19:16.976 [lettuce-eventExecutorLoop-1-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:19:57.073 [lettuce-eventExecutorLoop-1-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:20:37.172 [lettuce-eventExecutorLoop-1-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:21:17.272 [lettuce-eventExecutorLoop-1-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:21:57.376 [lettuce-eventExecutorLoop-1-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:22:37.477 [lettuce-eventExecutorLoop-1-20] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:23:17.571 [lettuce-eventExecutorLoop-1-22] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:23:57.672 [lettuce-eventExecutorLoop-1-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:24:37.783 [lettuce-eventExecutorLoop-1-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:25:17.873 [lettuce-eventExecutorLoop-1-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:25:57.977 [lettuce-eventExecutorLoop-1-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:26:38.074 [lettuce-eventExecutorLoop-1-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:27:18.172 [lettuce-eventExecutorLoop-1-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:27:58.272 [lettuce-eventExecutorLoop-1-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:28:38.379 [lettuce-eventExecutorLoop-1-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:29:18.472 [lettuce-eventExecutorLoop-1-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:29:58.573 [lettuce-eventExecutorLoop-1-20] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:30:38.672 [lettuce-eventExecutorLoop-1-22] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:31:18.777 [lettuce-eventExecutorLoop-1-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:31:58.885 [lettuce-eventExecutorLoop-1-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:32:38.977 [lettuce-eventExecutorLoop-1-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:33:19.073 [lettuce-eventExecutorLoop-1-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:33:59.182 [lettuce-eventExecutorLoop-1-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:34:39.275 [lettuce-eventExecutorLoop-1-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:35:11.316 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:35:11.316 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:35:11.666 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:35:11.667 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@581ff401[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:35:11.667 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754983093652_127.0.0.1_10358
20:35:11.671 [nacos-grpc-client-executor-3808] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754983093652_127.0.0.1_10358]Ignore complete event,isRunning:false,isAbandon=false
20:35:11.682 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3df4601f[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3809]
20:35:11.904 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:35:11.908 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
20:35:11.925 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
20:35:11.926 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:35:11.928 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:35:11.928 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
