10:40:51.863 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:40:54.490 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2e2515f0-023a-458c-b532-a6dc6632f276_config-0
10:40:54.776 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 117 ms to scan 1 urls, producing 3 keys and 6 values 
10:40:54.961 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 31 ms to scan 1 urls, producing 4 keys and 9 values 
10:40:54.990 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 23 ms to scan 1 urls, producing 3 keys and 10 values 
10:40:55.020 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 19 ms to scan 1 urls, producing 1 keys and 5 values 
10:40:55.069 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 24 ms to scan 1 urls, producing 1 keys and 7 values 
10:40:55.096 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 2 keys and 8 values 
10:40:55.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e2515f0-023a-458c-b532-a6dc6632f276_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:40:55.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e2515f0-023a-458c-b532-a6dc6632f276_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001fa013b9288
10:40:55.110 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e2515f0-023a-458c-b532-a6dc6632f276_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001fa013b94a8
10:40:55.110 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e2515f0-023a-458c-b532-a6dc6632f276_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:40:55.114 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e2515f0-023a-458c-b532-a6dc6632f276_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:40:55.141 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e2515f0-023a-458c-b532-a6dc6632f276_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:41:00.052 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e2515f0-023a-458c-b532-a6dc6632f276_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754793659544_127.0.0.1_4712
10:41:00.052 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e2515f0-023a-458c-b532-a6dc6632f276_config-0] Notify connected event to listeners.
10:41:00.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e2515f0-023a-458c-b532-a6dc6632f276_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:41:00.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e2515f0-023a-458c-b532-a6dc6632f276_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001fa014f0fb0
10:41:00.511 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:41:14.464 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:41:14.466 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:41:14.468 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:41:14.913 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:41:16.883 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:41:16.884 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:41:16.886 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:41:39.068 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:41:48.491 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7348a060-9306-42c1-b1dd-2054dbd8d0a1
10:41:48.491 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7348a060-9306-42c1-b1dd-2054dbd8d0a1] RpcClient init label, labels = {module=naming, source=sdk}
10:41:48.497 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7348a060-9306-42c1-b1dd-2054dbd8d0a1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:41:48.497 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7348a060-9306-42c1-b1dd-2054dbd8d0a1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:41:48.497 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7348a060-9306-42c1-b1dd-2054dbd8d0a1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:41:48.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7348a060-9306-42c1-b1dd-2054dbd8d0a1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:41:48.633 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7348a060-9306-42c1-b1dd-2054dbd8d0a1] Success to connect to server [localhost:8848] on start up, connectionId = 1754793708510_127.0.0.1_5169
10:41:48.633 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7348a060-9306-42c1-b1dd-2054dbd8d0a1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:41:48.633 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7348a060-9306-42c1-b1dd-2054dbd8d0a1] Notify connected event to listeners.
10:41:48.633 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7348a060-9306-42c1-b1dd-2054dbd8d0a1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001fa014f0fb0
10:41:48.745 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:41:48.811 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:41:49.122 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 58.873 seconds (JVM running for 66.675)
10:41:49.151 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:41:49.151 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:41:49.160 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:41:49.195 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7348a060-9306-42c1-b1dd-2054dbd8d0a1] Receive server push request, request = NotifySubscriberRequest, requestId = 8
10:41:49.221 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7348a060-9306-42c1-b1dd-2054dbd8d0a1] Ack server push request, request = NotifySubscriberRequest, requestId = 8
10:45:07.379 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:45:15.172 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7348a060-9306-42c1-b1dd-2054dbd8d0a1] Receive server push request, request = NotifySubscriberRequest, requestId = 13
10:45:15.172 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7348a060-9306-42c1-b1dd-2054dbd8d0a1] Ack server push request, request = NotifySubscriberRequest, requestId = 13
10:45:16.929 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:45:16.929 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:45:17.427 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
10:45:17.429 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
14:08:20.126 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:08:20.130 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:08:20.515 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:08:20.515 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@47b9edd8[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:08:20.515 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754793708510_127.0.0.1_5169
14:08:20.533 [nacos-grpc-client-executor-2488] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754793708510_127.0.0.1_5169]Ignore complete event,isRunning:false,isAbandon=false
14:08:20.535 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2bdd1af7[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 2489]
14:08:20.841 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:08:20.849 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
14:08:20.865 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
14:08:20.865 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:08:20.867 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:08:20.867 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:08:20.869 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:08:20.870 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
