09:23:58.450 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:23:59.258 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6447a2aa-b29b-4d19-8721-36786a642457_config-0
09:23:59.374 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 47 ms to scan 1 urls, producing 3 keys and 6 values 
09:23:59.428 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 4 keys and 9 values 
09:23:59.438 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 3 keys and 10 values 
09:23:59.451 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:23:59.462 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:23:59.472 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
09:23:59.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6447a2aa-b29b-4d19-8721-36786a642457_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:23:59.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6447a2aa-b29b-4d19-8721-36786a642457_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000291c23b6f80
09:23:59.477 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6447a2aa-b29b-4d19-8721-36786a642457_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000291c23b71a0
09:23:59.477 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6447a2aa-b29b-4d19-8721-36786a642457_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:23:59.478 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6447a2aa-b29b-4d19-8721-36786a642457_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:23:59.488 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6447a2aa-b29b-4d19-8721-36786a642457_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:00.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6447a2aa-b29b-4d19-8721-36786a642457_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755566640688_127.0.0.1_14635
09:24:00.953 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6447a2aa-b29b-4d19-8721-36786a642457_config-0] Notify connected event to listeners.
09:24:00.953 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6447a2aa-b29b-4d19-8721-36786a642457_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:00.954 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6447a2aa-b29b-4d19-8721-36786a642457_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000291c24f0fb0
09:24:01.145 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:24:07.684 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:24:07.685 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:24:07.686 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:24:07.972 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:24:08.821 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:24:08.822 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:24:08.822 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:24:18.946 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:24:22.300 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-2b39-432c-a963-751f9044f55c
09:24:22.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-2b39-432c-a963-751f9044f55c] RpcClient init label, labels = {module=naming, source=sdk}
09:24:22.302 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-2b39-432c-a963-751f9044f55c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:24:22.302 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-2b39-432c-a963-751f9044f55c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:24:22.303 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-2b39-432c-a963-751f9044f55c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:24:22.303 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-2b39-432c-a963-751f9044f55c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:22.435 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-2b39-432c-a963-751f9044f55c] Success to connect to server [localhost:8848] on start up, connectionId = 1755566662314_127.0.0.1_14735
09:24:22.436 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-2b39-432c-a963-751f9044f55c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:22.436 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-2b39-432c-a963-751f9044f55c] Notify connected event to listeners.
09:24:22.436 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-2b39-432c-a963-751f9044f55c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000291c24f0fb0
09:24:22.521 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:24:22.561 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:24:22.729 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 25.188 seconds (JVM running for 28.82)
09:24:22.750 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:24:22.751 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:24:22.751 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:24:23.027 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-2b39-432c-a963-751f9044f55c] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:24:23.049 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-2b39-432c-a963-751f9044f55c] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:24:23.108 [RMI TCP Connection(22)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:25:16.245 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-2b39-432c-a963-751f9044f55c] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:25:16.246 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-2b39-432c-a963-751f9044f55c] Ack server push request, request = NotifySubscriberRequest, requestId = 13
09:25:16.861 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:25:16.862 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:25:17.031 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:25:17.032 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
10:15:14.887 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:15:14.891 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:15:15.212 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:15:15.214 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@61c364c9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:15:15.214 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755566662314_127.0.0.1_14735
10:15:15.216 [nacos-grpc-client-executor-628] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755566662314_127.0.0.1_14735]Ignore complete event,isRunning:false,isAbandon=false
10:15:15.221 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@297cde1c[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 629]
10:15:15.429 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:15:15.439 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
10:15:15.452 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
10:15:15.452 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:15:15.452 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:15:15.452 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:15:15.461 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:15:15.461 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:15:50.540 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:15:51.486 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0a3efdf5-1aef-4a38-9ad5-8527a63a3fff_config-0
10:15:51.556 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 3 keys and 6 values 
10:15:51.594 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
10:15:51.603 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
10:15:51.612 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
10:15:51.621 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
10:15:51.629 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
10:15:51.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a3efdf5-1aef-4a38-9ad5-8527a63a3fff_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:15:51.633 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a3efdf5-1aef-4a38-9ad5-8527a63a3fff_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002744439bda8
10:15:51.633 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a3efdf5-1aef-4a38-9ad5-8527a63a3fff_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000002744439bfc8
10:15:51.634 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a3efdf5-1aef-4a38-9ad5-8527a63a3fff_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:15:51.634 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a3efdf5-1aef-4a38-9ad5-8527a63a3fff_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:15:51.647 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a3efdf5-1aef-4a38-9ad5-8527a63a3fff_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:15:52.538 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a3efdf5-1aef-4a38-9ad5-8527a63a3fff_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755569752304_127.0.0.1_6560
10:15:52.540 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a3efdf5-1aef-4a38-9ad5-8527a63a3fff_config-0] Notify connected event to listeners.
10:15:52.540 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a3efdf5-1aef-4a38-9ad5-8527a63a3fff_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:15:52.541 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0a3efdf5-1aef-4a38-9ad5-8527a63a3fff_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000027444513ff8
10:15:52.812 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:15:57.333 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:15:57.334 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:15:57.334 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:15:57.515 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:15:58.312 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:15:58.313 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:15:58.313 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:16:14.417 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:16:20.925 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fc4905a5-ade2-40db-ac42-a923a25ede7e
10:16:20.926 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4905a5-ade2-40db-ac42-a923a25ede7e] RpcClient init label, labels = {module=naming, source=sdk}
10:16:20.930 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4905a5-ade2-40db-ac42-a923a25ede7e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:16:20.930 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4905a5-ade2-40db-ac42-a923a25ede7e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:16:20.931 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4905a5-ade2-40db-ac42-a923a25ede7e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:16:20.932 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4905a5-ade2-40db-ac42-a923a25ede7e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:16:21.075 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4905a5-ade2-40db-ac42-a923a25ede7e] Success to connect to server [localhost:8848] on start up, connectionId = 1755569780949_127.0.0.1_6611
10:16:21.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4905a5-ade2-40db-ac42-a923a25ede7e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:16:21.077 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4905a5-ade2-40db-ac42-a923a25ede7e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000027444513ff8
10:16:21.076 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4905a5-ade2-40db-ac42-a923a25ede7e] Notify connected event to listeners.
10:16:21.177 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:16:21.244 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:16:21.542 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 31.779 seconds (JVM running for 34.702)
10:16:21.574 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:16:21.575 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:16:21.577 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:16:21.674 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4905a5-ade2-40db-ac42-a923a25ede7e] Receive server push request, request = NotifySubscriberRequest, requestId = 20
10:16:21.696 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4905a5-ade2-40db-ac42-a923a25ede7e] Ack server push request, request = NotifySubscriberRequest, requestId = 20
10:16:52.214 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:16:53.978 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:16:53.979 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:16:53.981 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
10:16:53.982 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
10:16:53.986 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:16:53.995 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:16:53.996 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:16:53.996 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
10:16:53.998 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
10:16:53.998 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:44:25.606 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:44:25.610 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:44:25.953 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:44:25.953 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3ef75096[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:44:25.953 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755569780949_127.0.0.1_6611
10:44:25.956 [nacos-grpc-client-executor-347] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755569780949_127.0.0.1_6611]Ignore complete event,isRunning:false,isAbandon=false
10:44:25.960 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7a0da82[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 348]
10:44:26.143 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:44:26.145 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
10:44:26.149 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
10:44:26.149 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:44:26.151 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:44:26.151 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:44:31.921 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:44:32.661 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b9176ff2-5315-446e-8888-8ee4f90c36f1_config-0
10:44:32.742 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 36 ms to scan 1 urls, producing 3 keys and 6 values 
10:44:32.793 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
10:44:32.802 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
10:44:32.813 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
10:44:32.823 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
10:44:32.833 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
10:44:32.840 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9176ff2-5315-446e-8888-8ee4f90c36f1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:44:32.840 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9176ff2-5315-446e-8888-8ee4f90c36f1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000021bdc39e480
10:44:32.841 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9176ff2-5315-446e-8888-8ee4f90c36f1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000021bdc39e6a0
10:44:32.841 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9176ff2-5315-446e-8888-8ee4f90c36f1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:44:32.842 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9176ff2-5315-446e-8888-8ee4f90c36f1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:44:32.853 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9176ff2-5315-446e-8888-8ee4f90c36f1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:44:33.890 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9176ff2-5315-446e-8888-8ee4f90c36f1_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755571473645_127.0.0.1_9144
10:44:33.892 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9176ff2-5315-446e-8888-8ee4f90c36f1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:44:33.893 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9176ff2-5315-446e-8888-8ee4f90c36f1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000021bdc518228
10:44:33.893 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b9176ff2-5315-446e-8888-8ee4f90c36f1_config-0] Notify connected event to listeners.
10:44:34.070 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:44:38.072 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:44:38.073 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:44:38.073 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:44:38.294 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:44:39.114 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:44:39.116 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:44:39.117 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:44:47.518 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:44:50.765 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of aabe8d38-dd13-41c4-8987-7dd13355b761
10:44:50.765 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aabe8d38-dd13-41c4-8987-7dd13355b761] RpcClient init label, labels = {module=naming, source=sdk}
10:44:50.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aabe8d38-dd13-41c4-8987-7dd13355b761] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:44:50.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aabe8d38-dd13-41c4-8987-7dd13355b761] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:44:50.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aabe8d38-dd13-41c4-8987-7dd13355b761] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:44:50.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aabe8d38-dd13-41c4-8987-7dd13355b761] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:44:50.894 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aabe8d38-dd13-41c4-8987-7dd13355b761] Success to connect to server [localhost:8848] on start up, connectionId = 1755571490777_127.0.0.1_9191
10:44:50.895 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aabe8d38-dd13-41c4-8987-7dd13355b761] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:44:50.895 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aabe8d38-dd13-41c4-8987-7dd13355b761] Notify connected event to listeners.
10:44:50.895 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aabe8d38-dd13-41c4-8987-7dd13355b761] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000021bdc518228
10:44:50.982 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:44:51.020 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:44:51.170 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.826 seconds (JVM running for 20.781)
10:44:51.207 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:44:51.209 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:44:51.210 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:44:51.521 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aabe8d38-dd13-41c4-8987-7dd13355b761] Receive server push request, request = NotifySubscriberRequest, requestId = 25
10:44:51.541 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aabe8d38-dd13-41c4-8987-7dd13355b761] Ack server push request, request = NotifySubscriberRequest, requestId = 25
10:44:51.696 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:45:03.534 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:45:03.534 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:55:50.531 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:55:50.544 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:55:50.876 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:55:50.876 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7e24be4e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:55:50.876 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755571490777_127.0.0.1_9191
10:55:50.879 [nacos-grpc-client-executor-144] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755571490777_127.0.0.1_9191]Ignore complete event,isRunning:false,isAbandon=false
10:55:50.882 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3787c950[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 145]
10:55:51.051 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:55:51.057 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:55:51.067 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:55:51.072 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:55:51.072 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:55:51.072 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:55:57.214 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:55:58.070 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 69646d15-d419-419d-ad44-fefc5272d590_config-0
10:55:58.230 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 82 ms to scan 1 urls, producing 3 keys and 6 values 
10:55:58.285 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
10:55:58.301 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 3 keys and 10 values 
10:55:58.314 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
10:55:58.327 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
10:55:58.340 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
10:55:58.343 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69646d15-d419-419d-ad44-fefc5272d590_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:55:58.344 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69646d15-d419-419d-ad44-fefc5272d590_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001dcce39e8d8
10:55:58.345 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69646d15-d419-419d-ad44-fefc5272d590_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001dcce39eaf8
10:55:58.345 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69646d15-d419-419d-ad44-fefc5272d590_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:55:58.346 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69646d15-d419-419d-ad44-fefc5272d590_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:55:58.358 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69646d15-d419-419d-ad44-fefc5272d590_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:55:59.368 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69646d15-d419-419d-ad44-fefc5272d590_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755572159151_127.0.0.1_10550
10:55:59.370 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69646d15-d419-419d-ad44-fefc5272d590_config-0] Notify connected event to listeners.
10:55:59.370 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69646d15-d419-419d-ad44-fefc5272d590_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:55:59.373 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [69646d15-d419-419d-ad44-fefc5272d590_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001dcce518d48
10:55:59.559 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:56:03.681 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:56:03.682 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:56:03.682 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:56:03.886 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:56:05.286 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:56:05.287 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:56:05.287 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:56:13.330 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:56:16.452 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c2ef65ff-349a-4fde-b550-cb4a0b3bdd53
10:56:16.452 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2ef65ff-349a-4fde-b550-cb4a0b3bdd53] RpcClient init label, labels = {module=naming, source=sdk}
10:56:16.454 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2ef65ff-349a-4fde-b550-cb4a0b3bdd53] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:56:16.454 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2ef65ff-349a-4fde-b550-cb4a0b3bdd53] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:56:16.455 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2ef65ff-349a-4fde-b550-cb4a0b3bdd53] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:56:16.456 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2ef65ff-349a-4fde-b550-cb4a0b3bdd53] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:56:16.589 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2ef65ff-349a-4fde-b550-cb4a0b3bdd53] Success to connect to server [localhost:8848] on start up, connectionId = 1755572176469_127.0.0.1_10568
10:56:16.590 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2ef65ff-349a-4fde-b550-cb4a0b3bdd53] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:56:16.590 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2ef65ff-349a-4fde-b550-cb4a0b3bdd53] Notify connected event to listeners.
10:56:16.590 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2ef65ff-349a-4fde-b550-cb4a0b3bdd53] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001dcce518d48
10:56:16.670 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:56:16.718 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:56:16.853 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.249 seconds (JVM running for 21.328)
10:56:16.872 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:56:16.873 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:56:16.873 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:56:17.253 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2ef65ff-349a-4fde-b550-cb4a0b3bdd53] Receive server push request, request = NotifySubscriberRequest, requestId = 35
10:56:17.278 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c2ef65ff-349a-4fde-b550-cb4a0b3bdd53] Ack server push request, request = NotifySubscriberRequest, requestId = 35
10:56:17.482 [RMI TCP Connection(8)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:56:28.504 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
10:56:28.505 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:56:28.525 [http-nio-9600-exec-9] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
10:56:28.526 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:56:28.530 [http-nio-9600-exec-9] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
10:56:28.542 [http-nio-9600-exec-9] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
10:56:28.543 [http-nio-9600-exec-9] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:56:28.543 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
10:56:28.545 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
10:56:28.545 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:17:24.445 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:17:24.450 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:17:24.776 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:17:24.776 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@73b27629[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:17:24.776 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755572176469_127.0.0.1_10568
11:17:24.779 [nacos-grpc-client-executor-264] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755572176469_127.0.0.1_10568]Ignore complete event,isRunning:false,isAbandon=false
11:17:24.789 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@652df627[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 265]
11:17:25.008 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:17:25.008 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:17:25.016 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:17:25.016 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:17:25.016 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:17:25.016 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:17:30.889 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:17:31.787 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 60f55f95-61a7-4385-b207-1fb2083f3b7c_config-0
11:17:31.892 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 46 ms to scan 1 urls, producing 3 keys and 6 values 
11:17:31.939 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
11:17:31.950 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
11:17:31.962 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
11:17:31.975 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
11:17:31.995 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
11:17:32.000 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60f55f95-61a7-4385-b207-1fb2083f3b7c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:17:32.001 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60f55f95-61a7-4385-b207-1fb2083f3b7c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000023c4d39e480
11:17:32.002 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60f55f95-61a7-4385-b207-1fb2083f3b7c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000023c4d39e6a0
11:17:32.003 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60f55f95-61a7-4385-b207-1fb2083f3b7c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:17:32.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60f55f95-61a7-4385-b207-1fb2083f3b7c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:17:32.017 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60f55f95-61a7-4385-b207-1fb2083f3b7c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:17:33.174 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60f55f95-61a7-4385-b207-1fb2083f3b7c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755573452955_127.0.0.1_12019
11:17:33.176 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60f55f95-61a7-4385-b207-1fb2083f3b7c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:17:33.176 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60f55f95-61a7-4385-b207-1fb2083f3b7c_config-0] Notify connected event to listeners.
11:17:33.178 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60f55f95-61a7-4385-b207-1fb2083f3b7c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023c4d518228
11:17:33.349 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:17:37.833 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:17:37.833 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:17:37.834 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:17:38.051 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:17:39.043 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:17:39.045 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:17:39.045 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:17:51.148 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:17:54.192 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c05c7808-124e-4ec7-89df-2cc1437d5684
11:17:54.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c05c7808-124e-4ec7-89df-2cc1437d5684] RpcClient init label, labels = {module=naming, source=sdk}
11:17:54.195 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c05c7808-124e-4ec7-89df-2cc1437d5684] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:17:54.195 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c05c7808-124e-4ec7-89df-2cc1437d5684] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:17:54.196 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c05c7808-124e-4ec7-89df-2cc1437d5684] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:17:54.196 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c05c7808-124e-4ec7-89df-2cc1437d5684] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:17:54.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c05c7808-124e-4ec7-89df-2cc1437d5684] Success to connect to server [localhost:8848] on start up, connectionId = 1755573474207_127.0.0.1_12063
11:17:54.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c05c7808-124e-4ec7-89df-2cc1437d5684] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:17:54.330 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c05c7808-124e-4ec7-89df-2cc1437d5684] Notify connected event to listeners.
11:17:54.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c05c7808-124e-4ec7-89df-2cc1437d5684] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023c4d518228
11:17:54.420 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:17:54.468 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:17:54.711 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 24.447 seconds (JVM running for 25.593)
11:17:54.721 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:17:54.721 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:17:54.721 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:17:54.922 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c05c7808-124e-4ec7-89df-2cc1437d5684] Receive server push request, request = NotifySubscriberRequest, requestId = 41
11:17:54.947 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c05c7808-124e-4ec7-89df-2cc1437d5684] Ack server push request, request = NotifySubscriberRequest, requestId = 41
11:17:55.248 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:18:04.613 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:18:04.629 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:18:04.960 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:18:04.960 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3b0d852f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:18:04.960 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755573474207_127.0.0.1_12063
11:18:04.960 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755573474207_127.0.0.1_12063]Ignore complete event,isRunning:false,isAbandon=false
11:18:04.970 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4363dd3f[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 13]
11:18:05.112 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:18:05.131 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:18:05.132 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:18:05.132 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:18:10.244 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:18:11.114 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d8d89502-dc05-401a-bdc7-46d2f78011f9_config-0
11:18:11.184 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 3 keys and 6 values 
11:18:11.223 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
11:18:11.232 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
11:18:11.241 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
11:18:11.250 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
11:18:11.260 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
11:18:11.263 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8d89502-dc05-401a-bdc7-46d2f78011f9_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:18:11.263 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8d89502-dc05-401a-bdc7-46d2f78011f9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000025e8939f1c0
11:18:11.264 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8d89502-dc05-401a-bdc7-46d2f78011f9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000025e8939f3e0
11:18:11.264 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8d89502-dc05-401a-bdc7-46d2f78011f9_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:18:11.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8d89502-dc05-401a-bdc7-46d2f78011f9_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:18:11.274 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8d89502-dc05-401a-bdc7-46d2f78011f9_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:18:12.729 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8d89502-dc05-401a-bdc7-46d2f78011f9_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755573492495_127.0.0.1_12114
11:18:12.730 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8d89502-dc05-401a-bdc7-46d2f78011f9_config-0] Notify connected event to listeners.
11:18:12.730 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8d89502-dc05-401a-bdc7-46d2f78011f9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:18:12.731 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8d89502-dc05-401a-bdc7-46d2f78011f9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000025e89518fb0
11:18:12.890 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:18:16.746 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:18:16.747 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:18:16.747 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:18:16.956 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:18:17.753 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:18:17.754 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:18:17.754 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:18:25.829 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:18:28.872 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 513fed04-8e34-4b27-b77a-1af91ed7cacb
11:18:28.873 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [513fed04-8e34-4b27-b77a-1af91ed7cacb] RpcClient init label, labels = {module=naming, source=sdk}
11:18:28.876 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [513fed04-8e34-4b27-b77a-1af91ed7cacb] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:18:28.877 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [513fed04-8e34-4b27-b77a-1af91ed7cacb] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:18:28.877 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [513fed04-8e34-4b27-b77a-1af91ed7cacb] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:18:28.878 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [513fed04-8e34-4b27-b77a-1af91ed7cacb] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:18:28.998 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [513fed04-8e34-4b27-b77a-1af91ed7cacb] Success to connect to server [localhost:8848] on start up, connectionId = 1755573508887_127.0.0.1_12161
11:18:28.999 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [513fed04-8e34-4b27-b77a-1af91ed7cacb] Notify connected event to listeners.
11:18:28.999 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [513fed04-8e34-4b27-b77a-1af91ed7cacb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:18:29.000 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [513fed04-8e34-4b27-b77a-1af91ed7cacb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000025e89518fb0
11:18:29.079 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:18:29.117 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:18:29.255 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.649 seconds (JVM running for 20.58)
11:18:29.275 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:18:29.276 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:18:29.277 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:18:29.550 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [513fed04-8e34-4b27-b77a-1af91ed7cacb] Receive server push request, request = NotifySubscriberRequest, requestId = 47
11:18:29.575 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [513fed04-8e34-4b27-b77a-1af91ed7cacb] Ack server push request, request = NotifySubscriberRequest, requestId = 47
11:18:29.594 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:18:41.532 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:18:41.533 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:20:31.297 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:20:31.309 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:20:31.643 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:20:31.645 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@337c982c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:20:31.646 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755573508887_127.0.0.1_12161
11:20:31.650 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755573508887_127.0.0.1_12161]Ignore complete event,isRunning:false,isAbandon=false
11:20:31.656 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1851a3ea[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 39]
11:20:31.828 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:20:31.833 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:20:31.845 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:20:31.845 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:20:31.847 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:20:31.848 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:20:37.044 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:20:37.844 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3109bea6-3a15-4da4-8ef8-c45c93261644_config-0
11:20:37.934 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 36 ms to scan 1 urls, producing 3 keys and 6 values 
11:20:37.973 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
11:20:37.982 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
11:20:37.993 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
11:20:38.002 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
11:20:38.011 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
11:20:38.013 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3109bea6-3a15-4da4-8ef8-c45c93261644_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:20:38.014 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3109bea6-3a15-4da4-8ef8-c45c93261644_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000015a5239eaf8
11:20:38.014 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3109bea6-3a15-4da4-8ef8-c45c93261644_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000015a5239ed18
11:20:38.015 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3109bea6-3a15-4da4-8ef8-c45c93261644_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:20:38.016 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3109bea6-3a15-4da4-8ef8-c45c93261644_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:20:38.025 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3109bea6-3a15-4da4-8ef8-c45c93261644_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:20:39.034 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3109bea6-3a15-4da4-8ef8-c45c93261644_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755573638812_127.0.0.1_12359
11:20:39.036 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3109bea6-3a15-4da4-8ef8-c45c93261644_config-0] Notify connected event to listeners.
11:20:39.036 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3109bea6-3a15-4da4-8ef8-c45c93261644_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:20:39.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3109bea6-3a15-4da4-8ef8-c45c93261644_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000015a52518ad8
11:20:39.224 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:20:43.449 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:20:43.450 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:20:43.450 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:20:43.642 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:20:44.463 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:20:44.465 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:20:44.466 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:20:59.178 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:21:02.287 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bbafeb58-4c55-48da-bcc0-6640ed004a17
11:21:02.288 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbafeb58-4c55-48da-bcc0-6640ed004a17] RpcClient init label, labels = {module=naming, source=sdk}
11:21:02.290 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbafeb58-4c55-48da-bcc0-6640ed004a17] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:21:02.291 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbafeb58-4c55-48da-bcc0-6640ed004a17] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:21:02.291 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbafeb58-4c55-48da-bcc0-6640ed004a17] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:21:02.292 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbafeb58-4c55-48da-bcc0-6640ed004a17] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:21:02.413 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbafeb58-4c55-48da-bcc0-6640ed004a17] Success to connect to server [localhost:8848] on start up, connectionId = 1755573662300_127.0.0.1_12407
11:21:02.414 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbafeb58-4c55-48da-bcc0-6640ed004a17] Notify connected event to listeners.
11:21:02.414 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbafeb58-4c55-48da-bcc0-6640ed004a17] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:21:02.414 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbafeb58-4c55-48da-bcc0-6640ed004a17] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000015a52518ad8
11:21:02.485 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:21:02.521 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:21:02.675 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 26.313 seconds (JVM running for 27.3)
11:21:02.696 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:21:02.697 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:21:02.698 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:21:03.040 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbafeb58-4c55-48da-bcc0-6640ed004a17] Receive server push request, request = NotifySubscriberRequest, requestId = 54
11:21:03.059 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bbafeb58-4c55-48da-bcc0-6640ed004a17] Ack server push request, request = NotifySubscriberRequest, requestId = 54
11:21:03.112 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:21:20.814 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:21:20.814 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:23:13.661 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:23:13.666 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:23:14.009 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:23:14.009 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7c5d9ab4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:23:14.009 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755573662300_127.0.0.1_12407
11:23:14.013 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2fc86cbc[Running, pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 39]
11:23:14.016 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755573662300_127.0.0.1_12407]Ignore complete event,isRunning:false,isAbandon=false
11:23:14.185 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:23:14.191 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:23:14.201 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:23:14.201 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:23:14.203 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:23:14.203 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:23:19.525 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:23:20.321 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2ca936b3-c13e-44d4-8838-632a14179fb7_config-0
11:23:20.393 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 3 keys and 6 values 
11:23:20.439 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
11:23:20.448 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
11:23:20.457 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
11:23:20.466 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
11:23:20.478 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
11:23:20.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ca936b3-c13e-44d4-8838-632a14179fb7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:23:20.482 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ca936b3-c13e-44d4-8838-632a14179fb7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001e5d639e8d8
11:23:20.482 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ca936b3-c13e-44d4-8838-632a14179fb7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001e5d639eaf8
11:23:20.483 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ca936b3-c13e-44d4-8838-632a14179fb7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:23:20.484 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ca936b3-c13e-44d4-8838-632a14179fb7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:23:20.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ca936b3-c13e-44d4-8838-632a14179fb7_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:23:21.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ca936b3-c13e-44d4-8838-632a14179fb7_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755573801362_127.0.0.1_12593
11:23:21.606 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ca936b3-c13e-44d4-8838-632a14179fb7_config-0] Notify connected event to listeners.
11:23:21.607 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ca936b3-c13e-44d4-8838-632a14179fb7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:23:21.608 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ca936b3-c13e-44d4-8838-632a14179fb7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001e5d6518228
11:23:21.802 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:23:25.657 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:23:25.658 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:23:25.658 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:23:25.861 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:23:26.788 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:23:26.790 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:23:26.791 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:23:35.005 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:23:38.081 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a31336fc-31ed-4fed-92de-a3bc685cddcc
11:23:38.082 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a31336fc-31ed-4fed-92de-a3bc685cddcc] RpcClient init label, labels = {module=naming, source=sdk}
11:23:38.083 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a31336fc-31ed-4fed-92de-a3bc685cddcc] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:23:38.084 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a31336fc-31ed-4fed-92de-a3bc685cddcc] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:23:38.084 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a31336fc-31ed-4fed-92de-a3bc685cddcc] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:23:38.085 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a31336fc-31ed-4fed-92de-a3bc685cddcc] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:23:38.216 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a31336fc-31ed-4fed-92de-a3bc685cddcc] Success to connect to server [localhost:8848] on start up, connectionId = 1755573818094_127.0.0.1_12613
11:23:38.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a31336fc-31ed-4fed-92de-a3bc685cddcc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:23:38.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a31336fc-31ed-4fed-92de-a3bc685cddcc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001e5d6518228
11:23:38.217 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a31336fc-31ed-4fed-92de-a3bc685cddcc] Notify connected event to listeners.
11:23:38.297 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:23:38.350 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:23:38.520 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.612 seconds (JVM running for 20.696)
11:23:38.540 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:23:38.541 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:23:38.542 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:23:38.841 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a31336fc-31ed-4fed-92de-a3bc685cddcc] Receive server push request, request = NotifySubscriberRequest, requestId = 61
11:23:38.858 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a31336fc-31ed-4fed-92de-a3bc685cddcc] Ack server push request, request = NotifySubscriberRequest, requestId = 61
11:23:39.083 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:23:40.352 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:23:40.352 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
11:23:40.353 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:23:40.358 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:23:40.370 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:23:40.370 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:23:40.375 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
11:23:40.376 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
11:23:40.378 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
11:23:40.378 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:18:52.015 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:18:52.015 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:18:52.340 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:18:52.340 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@20b2bc3f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:18:52.340 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755573818094_127.0.0.1_12613
13:18:52.340 [nacos-grpc-client-executor-1394] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755573818094_127.0.0.1_12613]Ignore complete event,isRunning:false,isAbandon=false
13:18:52.348 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7dd6df01[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1395]
13:18:52.494 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:18:52.494 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
13:18:52.494 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
13:18:52.494 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:18:52.494 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:18:52.494 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:18:57.098 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:18:57.602 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a2dea199-0b22-4242-96cc-917495ed637c_config-0
13:18:57.653 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 3 keys and 6 values 
13:18:57.679 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
13:18:57.686 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
13:18:57.693 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
13:18:57.700 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
13:18:57.706 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
13:18:57.708 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2dea199-0b22-4242-96cc-917495ed637c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:18:57.709 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2dea199-0b22-4242-96cc-917495ed637c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001f08a3c0fc8
13:18:57.709 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2dea199-0b22-4242-96cc-917495ed637c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001f08a3c11e8
13:18:57.709 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2dea199-0b22-4242-96cc-917495ed637c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:18:57.710 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2dea199-0b22-4242-96cc-917495ed637c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:18:57.716 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2dea199-0b22-4242-96cc-917495ed637c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:18:58.412 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2dea199-0b22-4242-96cc-917495ed637c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755580738233_127.0.0.1_6660
13:18:58.413 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2dea199-0b22-4242-96cc-917495ed637c_config-0] Notify connected event to listeners.
13:18:58.413 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2dea199-0b22-4242-96cc-917495ed637c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:18:58.414 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2dea199-0b22-4242-96cc-917495ed637c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f08a4fb250
13:18:58.504 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:19:00.989 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:19:00.990 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:19:00.990 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:19:01.113 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:19:01.606 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:19:01.607 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:19:01.608 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:19:07.133 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:19:09.429 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7b28c5b5-5c21-46d1-9606-42089c10490c
13:19:09.429 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b28c5b5-5c21-46d1-9606-42089c10490c] RpcClient init label, labels = {module=naming, source=sdk}
13:19:09.430 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b28c5b5-5c21-46d1-9606-42089c10490c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:19:09.431 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b28c5b5-5c21-46d1-9606-42089c10490c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:19:09.431 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b28c5b5-5c21-46d1-9606-42089c10490c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:19:09.431 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b28c5b5-5c21-46d1-9606-42089c10490c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:19:09.557 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b28c5b5-5c21-46d1-9606-42089c10490c] Success to connect to server [localhost:8848] on start up, connectionId = 1755580749438_127.0.0.1_6676
13:19:09.557 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b28c5b5-5c21-46d1-9606-42089c10490c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:19:09.557 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b28c5b5-5c21-46d1-9606-42089c10490c] Notify connected event to listeners.
13:19:09.558 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b28c5b5-5c21-46d1-9606-42089c10490c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001f08a4fb250
13:19:09.600 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:19:09.622 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
13:19:09.704 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.073 seconds (JVM running for 13.948)
13:19:09.715 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:19:09.715 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:19:09.716 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:19:09.834 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:19:10.091 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b28c5b5-5c21-46d1-9606-42089c10490c] Receive server push request, request = NotifySubscriberRequest, requestId = 68
13:19:10.104 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7b28c5b5-5c21-46d1-9606-42089c10490c] Ack server push request, request = NotifySubscriberRequest, requestId = 68
13:19:31.107 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
13:19:31.107 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
13:19:31.107 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:19:31.107 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:19:31.110 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
13:19:31.115 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
13:19:31.116 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:19:31.117 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
13:19:31.118 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
13:19:31.118 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:42:51.081 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:42:51.083 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:42:51.423 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:42:51.425 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@26cd84ce[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:42:51.425 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755580749438_127.0.0.1_6676
16:42:51.427 [nacos-grpc-client-executor-2457] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755580749438_127.0.0.1_6676]Ignore complete event,isRunning:false,isAbandon=false
16:42:51.427 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1c2c9f4d[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2458]
16:42:51.562 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:42:51.562 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:42:51.562 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:42:51.562 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:42:51.562 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:42:51.562 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:42:58.269 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:42:59.097 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 66b78a04-ac11-4c30-ab58-bb835585c318_config-0
16:42:59.153 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
16:42:59.200 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
16:42:59.206 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
16:42:59.212 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
16:42:59.218 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
16:42:59.226 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
16:42:59.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66b78a04-ac11-4c30-ab58-bb835585c318_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:42:59.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66b78a04-ac11-4c30-ab58-bb835585c318_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001e40139e8d8
16:42:59.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66b78a04-ac11-4c30-ab58-bb835585c318_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001e40139eaf8
16:42:59.229 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66b78a04-ac11-4c30-ab58-bb835585c318_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:42:59.229 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66b78a04-ac11-4c30-ab58-bb835585c318_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:42:59.234 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66b78a04-ac11-4c30-ab58-bb835585c318_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:42:59.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66b78a04-ac11-4c30-ab58-bb835585c318_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755592979779_127.0.0.1_12081
16:42:59.974 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66b78a04-ac11-4c30-ab58-bb835585c318_config-0] Notify connected event to listeners.
16:42:59.974 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66b78a04-ac11-4c30-ab58-bb835585c318_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:42:59.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66b78a04-ac11-4c30-ab58-bb835585c318_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001e401518ad8
16:43:00.070 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:43:03.038 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:43:03.039 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:43:03.039 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:43:03.158 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:43:03.875 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:43:03.876 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:43:03.876 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:43:09.523 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:43:11.785 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ff50bb30-e1e7-402d-9e7d-a843fdef8ffc
16:43:11.785 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff50bb30-e1e7-402d-9e7d-a843fdef8ffc] RpcClient init label, labels = {module=naming, source=sdk}
16:43:11.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff50bb30-e1e7-402d-9e7d-a843fdef8ffc] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:43:11.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff50bb30-e1e7-402d-9e7d-a843fdef8ffc] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:43:11.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff50bb30-e1e7-402d-9e7d-a843fdef8ffc] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:43:11.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff50bb30-e1e7-402d-9e7d-a843fdef8ffc] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:43:11.917 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff50bb30-e1e7-402d-9e7d-a843fdef8ffc] Success to connect to server [localhost:8848] on start up, connectionId = 1755592991794_127.0.0.1_12096
16:43:11.917 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff50bb30-e1e7-402d-9e7d-a843fdef8ffc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:43:11.917 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff50bb30-e1e7-402d-9e7d-a843fdef8ffc] Notify connected event to listeners.
16:43:11.917 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff50bb30-e1e7-402d-9e7d-a843fdef8ffc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001e401518ad8
16:43:11.955 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:43:11.975 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
16:43:12.056 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 14.592 seconds (JVM running for 16.007)
16:43:12.068 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:43:12.069 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:43:12.069 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:43:12.476 [RMI TCP Connection(9)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:43:12.533 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff50bb30-e1e7-402d-9e7d-a843fdef8ffc] Receive server push request, request = NotifySubscriberRequest, requestId = 75
16:43:12.549 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ff50bb30-e1e7-402d-9e7d-a843fdef8ffc] Ack server push request, request = NotifySubscriberRequest, requestId = 75
16:43:56.690 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
16:43:56.690 [http-nio-9600-exec-7] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:43:56.690 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
16:43:56.691 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:43:56.693 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
16:43:56.699 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
16:43:56.699 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:43:56.700 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
16:43:56.700 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
16:43:56.701 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:46:32.171 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:46:32.171 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:46:32.506 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:46:32.508 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@10121b12[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:46:32.508 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755592991794_127.0.0.1_12096
16:46:32.510 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755592991794_127.0.0.1_12096]Ignore complete event,isRunning:false,isAbandon=false
16:46:32.512 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3eba63df[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 52]
16:46:32.718 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:46:32.719 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:46:32.723 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:46:32.723 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:46:32.728 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:46:32.728 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:46:44.628 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:46:45.643 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1adca7e5-d4bf-452a-9923-4b82cf5b4ea1_config-0
16:46:45.733 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 39 ms to scan 1 urls, producing 3 keys and 6 values 
16:46:45.787 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 4 keys and 9 values 
16:46:45.801 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
16:46:45.814 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
16:46:45.827 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
16:46:45.835 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
16:46:45.837 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1adca7e5-d4bf-452a-9923-4b82cf5b4ea1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:46:45.839 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1adca7e5-d4bf-452a-9923-4b82cf5b4ea1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001a3253b7410
16:46:45.839 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1adca7e5-d4bf-452a-9923-4b82cf5b4ea1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001a3253b7630
16:46:45.839 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1adca7e5-d4bf-452a-9923-4b82cf5b4ea1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:46:45.841 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1adca7e5-d4bf-452a-9923-4b82cf5b4ea1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:46:45.852 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1adca7e5-d4bf-452a-9923-4b82cf5b4ea1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:46:46.782 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1adca7e5-d4bf-452a-9923-4b82cf5b4ea1_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755593206561_127.0.0.1_12451
16:46:46.783 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1adca7e5-d4bf-452a-9923-4b82cf5b4ea1_config-0] Notify connected event to listeners.
16:46:46.783 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1adca7e5-d4bf-452a-9923-4b82cf5b4ea1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:46:46.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1adca7e5-d4bf-452a-9923-4b82cf5b4ea1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001a3254f1450
16:46:46.877 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:46:49.677 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:46:49.677 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:46:49.693 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:46:49.821 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:46:50.372 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:46:50.372 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:46:50.372 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:46:55.803 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:46:58.310 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 74fd0193-4a0f-4677-bc8a-f72b58ad4597
16:46:58.310 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74fd0193-4a0f-4677-bc8a-f72b58ad4597] RpcClient init label, labels = {module=naming, source=sdk}
16:46:58.310 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74fd0193-4a0f-4677-bc8a-f72b58ad4597] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:46:58.310 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74fd0193-4a0f-4677-bc8a-f72b58ad4597] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:46:58.310 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74fd0193-4a0f-4677-bc8a-f72b58ad4597] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:46:58.310 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74fd0193-4a0f-4677-bc8a-f72b58ad4597] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:46:58.437 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74fd0193-4a0f-4677-bc8a-f72b58ad4597] Success to connect to server [localhost:8848] on start up, connectionId = 1755593218310_127.0.0.1_12479
16:46:58.437 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74fd0193-4a0f-4677-bc8a-f72b58ad4597] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:46:58.437 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74fd0193-4a0f-4677-bc8a-f72b58ad4597] Notify connected event to listeners.
16:46:58.437 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74fd0193-4a0f-4677-bc8a-f72b58ad4597] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001a3254f1450
16:46:58.498 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:46:58.508 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
16:46:58.619 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.073 seconds (JVM running for 17.244)
16:46:58.633 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:46:58.633 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:46:58.634 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:46:59.026 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74fd0193-4a0f-4677-bc8a-f72b58ad4597] Receive server push request, request = NotifySubscriberRequest, requestId = 82
16:46:59.036 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74fd0193-4a0f-4677-bc8a-f72b58ad4597] Ack server push request, request = NotifySubscriberRequest, requestId = 82
16:47:06.109 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:47:07.139 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
16:47:07.139 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:47:07.140 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
16:47:07.140 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:47:07.143 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
16:47:07.146 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
16:47:07.146 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:47:07.148 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
16:47:07.149 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
16:47:07.149 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:48:03.411 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:48:03.416 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:48:03.761 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:48:03.762 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2971c0b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:48:03.762 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755593218310_127.0.0.1_12479
16:48:03.764 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755593218310_127.0.0.1_12479]Ignore complete event,isRunning:false,isAbandon=false
16:48:03.767 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@63803a9a[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 25]
16:48:03.934 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:48:03.934 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:48:03.936 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:48:03.937 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:48:03.938 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:48:03.938 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:48:15.370 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:48:16.480 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8de3309d-f52c-47f4-9a14-1ec9d49e1d95_config-0
16:48:16.568 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 34 ms to scan 1 urls, producing 3 keys and 6 values 
16:48:16.632 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
16:48:16.632 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
16:48:16.664 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
16:48:16.674 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
16:48:16.683 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
16:48:16.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8de3309d-f52c-47f4-9a14-1ec9d49e1d95_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:48:16.687 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8de3309d-f52c-47f4-9a14-1ec9d49e1d95_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000288483b68d8
16:48:16.687 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8de3309d-f52c-47f4-9a14-1ec9d49e1d95_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000288483b6af8
16:48:16.687 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8de3309d-f52c-47f4-9a14-1ec9d49e1d95_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:48:16.687 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8de3309d-f52c-47f4-9a14-1ec9d49e1d95_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:48:16.699 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8de3309d-f52c-47f4-9a14-1ec9d49e1d95_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:48:17.714 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8de3309d-f52c-47f4-9a14-1ec9d49e1d95_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755593297482_127.0.0.1_12622
16:48:17.714 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8de3309d-f52c-47f4-9a14-1ec9d49e1d95_config-0] Notify connected event to listeners.
16:48:17.714 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8de3309d-f52c-47f4-9a14-1ec9d49e1d95_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:48:17.714 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8de3309d-f52c-47f4-9a14-1ec9d49e1d95_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000288484f0668
16:48:17.815 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:48:20.574 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:48:20.574 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:48:20.584 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:48:20.701 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:48:21.353 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:48:21.353 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:48:21.353 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:48:26.747 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:48:29.174 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bcadc4e1-6b3c-434a-a18e-1a2faff70fcf
16:48:29.174 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcadc4e1-6b3c-434a-a18e-1a2faff70fcf] RpcClient init label, labels = {module=naming, source=sdk}
16:48:29.175 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcadc4e1-6b3c-434a-a18e-1a2faff70fcf] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:48:29.175 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcadc4e1-6b3c-434a-a18e-1a2faff70fcf] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:48:29.175 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcadc4e1-6b3c-434a-a18e-1a2faff70fcf] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:48:29.175 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcadc4e1-6b3c-434a-a18e-1a2faff70fcf] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:48:29.295 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcadc4e1-6b3c-434a-a18e-1a2faff70fcf] Success to connect to server [localhost:8848] on start up, connectionId = 1755593309182_127.0.0.1_12642
16:48:29.295 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcadc4e1-6b3c-434a-a18e-1a2faff70fcf] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:48:29.295 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcadc4e1-6b3c-434a-a18e-1a2faff70fcf] Notify connected event to listeners.
16:48:29.295 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcadc4e1-6b3c-434a-a18e-1a2faff70fcf] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000288484f0668
16:48:29.351 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:48:29.378 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
16:48:29.470 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.491 seconds (JVM running for 17.826)
16:48:29.483 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:48:29.483 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:48:29.483 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:48:29.893 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcadc4e1-6b3c-434a-a18e-1a2faff70fcf] Receive server push request, request = NotifySubscriberRequest, requestId = 89
16:48:29.916 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bcadc4e1-6b3c-434a-a18e-1a2faff70fcf] Ack server push request, request = NotifySubscriberRequest, requestId = 89
16:48:49.515 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:48:50.505 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:48:50.505 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:48:50.518 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
16:48:50.520 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
16:48:50.520 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:48:50.525 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:48:50.526 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:48:50.526 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
16:48:50.527 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
16:48:50.527 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:51:46.799 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:51:46.799 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:51:47.143 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:51:47.143 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@350f181a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:51:47.143 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755593309182_127.0.0.1_12642
16:51:47.145 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755593309182_127.0.0.1_12642]Ignore complete event,isRunning:false,isAbandon=false
16:51:47.148 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@68352f9f[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 50]
16:51:47.299 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:51:47.299 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
16:51:47.299 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
16:51:47.299 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:51:47.299 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:51:47.303 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:51:52.926 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:51:53.524 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d956f1b5-a975-42cf-83ae-f0132955dd1c_config-0
16:51:53.579 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
16:51:53.610 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
16:51:53.616 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
16:51:53.625 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
16:51:53.630 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 7 values 
16:51:53.638 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
16:51:53.640 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d956f1b5-a975-42cf-83ae-f0132955dd1c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:51:53.640 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d956f1b5-a975-42cf-83ae-f0132955dd1c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001764439f1c0
16:51:53.640 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d956f1b5-a975-42cf-83ae-f0132955dd1c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001764439f3e0
16:51:53.641 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d956f1b5-a975-42cf-83ae-f0132955dd1c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:51:53.641 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d956f1b5-a975-42cf-83ae-f0132955dd1c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:51:53.646 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d956f1b5-a975-42cf-83ae-f0132955dd1c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:51:54.445 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d956f1b5-a975-42cf-83ae-f0132955dd1c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755593514249_127.0.0.1_12977
16:51:54.445 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d956f1b5-a975-42cf-83ae-f0132955dd1c_config-0] Notify connected event to listeners.
16:51:54.446 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d956f1b5-a975-42cf-83ae-f0132955dd1c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:51:54.446 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d956f1b5-a975-42cf-83ae-f0132955dd1c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000017644518fb0
16:51:54.529 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:51:58.211 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:51:58.212 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:51:58.212 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:51:58.349 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:51:59.030 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:51:59.031 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:51:59.031 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:52:04.684 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:52:10.821 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8acdfdd1-c73e-49cd-b577-c15bc04800ec
16:52:10.822 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8acdfdd1-c73e-49cd-b577-c15bc04800ec] RpcClient init label, labels = {module=naming, source=sdk}
16:52:10.824 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8acdfdd1-c73e-49cd-b577-c15bc04800ec] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:52:10.825 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8acdfdd1-c73e-49cd-b577-c15bc04800ec] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:52:10.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8acdfdd1-c73e-49cd-b577-c15bc04800ec] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:52:10.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8acdfdd1-c73e-49cd-b577-c15bc04800ec] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:52:10.960 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8acdfdd1-c73e-49cd-b577-c15bc04800ec] Success to connect to server [localhost:8848] on start up, connectionId = 1755593530840_127.0.0.1_13048
16:52:10.960 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8acdfdd1-c73e-49cd-b577-c15bc04800ec] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:52:10.960 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8acdfdd1-c73e-49cd-b577-c15bc04800ec] Notify connected event to listeners.
16:52:10.960 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8acdfdd1-c73e-49cd-b577-c15bc04800ec] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000017644518fb0
16:52:11.021 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:52:11.067 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
16:52:11.245 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 18.871 seconds (JVM running for 19.992)
16:52:11.263 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:52:11.264 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:52:11.265 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:52:11.543 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8acdfdd1-c73e-49cd-b577-c15bc04800ec] Receive server push request, request = NotifySubscriberRequest, requestId = 97
16:52:11.558 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8acdfdd1-c73e-49cd-b577-c15bc04800ec] Ack server push request, request = NotifySubscriberRequest, requestId = 97
16:52:11.687 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:52:13.882 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:52:13.882 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:18:29.923 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:18:29.925 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:18:30.268 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:18:30.268 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@********[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:18:30.268 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755593530840_127.0.0.1_13048
18:18:30.271 [nacos-grpc-client-executor-1046] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755593530840_127.0.0.1_13048]Ignore complete event,isRunning:false,isAbandon=false
18:18:30.274 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1ea331e7[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 1047]
18:18:30.442 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:18:30.442 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
18:18:30.442 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
18:18:30.442 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:18:30.454 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:18:30.454 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
18:18:59.261 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:19:00.541 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b32f29c9-eeab-4e79-a07f-06ee3aea3e76_config-0
18:19:00.650 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 50 ms to scan 1 urls, producing 3 keys and 6 values 
18:19:00.712 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
18:19:00.727 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
18:19:00.743 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
18:19:00.743 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 7 values 
18:19:00.775 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
18:19:00.780 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b32f29c9-eeab-4e79-a07f-06ee3aea3e76_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:19:00.780 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b32f29c9-eeab-4e79-a07f-06ee3aea3e76_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001ff3f3b8200
18:19:00.780 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b32f29c9-eeab-4e79-a07f-06ee3aea3e76_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001ff3f3b8420
18:19:00.780 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b32f29c9-eeab-4e79-a07f-06ee3aea3e76_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:19:00.780 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b32f29c9-eeab-4e79-a07f-06ee3aea3e76_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:19:00.791 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b32f29c9-eeab-4e79-a07f-06ee3aea3e76_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:19:02.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b32f29c9-eeab-4e79-a07f-06ee3aea3e76_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755598741790_127.0.0.1_4922
18:19:02.048 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b32f29c9-eeab-4e79-a07f-06ee3aea3e76_config-0] Notify connected event to listeners.
18:19:02.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b32f29c9-eeab-4e79-a07f-06ee3aea3e76_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:19:02.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b32f29c9-eeab-4e79-a07f-06ee3aea3e76_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001ff3f4f0228
18:19:02.220 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:19:06.565 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
18:19:06.566 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:19:06.566 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
18:19:06.801 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:19:07.585 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
18:19:07.585 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
18:19:07.585 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
18:19:13.655 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:19:16.434 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c56a8602-5f3d-4ac8-9f54-0c304d2e94cb
18:19:16.434 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c56a8602-5f3d-4ac8-9f54-0c304d2e94cb] RpcClient init label, labels = {module=naming, source=sdk}
18:19:16.434 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c56a8602-5f3d-4ac8-9f54-0c304d2e94cb] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:19:16.434 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c56a8602-5f3d-4ac8-9f54-0c304d2e94cb] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:19:16.434 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c56a8602-5f3d-4ac8-9f54-0c304d2e94cb] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:19:16.434 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c56a8602-5f3d-4ac8-9f54-0c304d2e94cb] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:19:16.573 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c56a8602-5f3d-4ac8-9f54-0c304d2e94cb] Success to connect to server [localhost:8848] on start up, connectionId = 1755598756446_127.0.0.1_4947
18:19:16.573 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c56a8602-5f3d-4ac8-9f54-0c304d2e94cb] Notify connected event to listeners.
18:19:16.573 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c56a8602-5f3d-4ac8-9f54-0c304d2e94cb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:19:16.573 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c56a8602-5f3d-4ac8-9f54-0c304d2e94cb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001ff3f4f0228
18:19:16.614 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
18:19:16.634 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
18:19:16.731 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 18.247 seconds (JVM running for 20.658)
18:19:16.742 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
18:19:16.742 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
18:19:16.742 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
18:19:17.099 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c56a8602-5f3d-4ac8-9f54-0c304d2e94cb] Receive server push request, request = NotifySubscriberRequest, requestId = 108
18:19:17.114 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c56a8602-5f3d-4ac8-9f54-0c304d2e94cb] Ack server push request, request = NotifySubscriberRequest, requestId = 108
18:20:09.473 [http-nio-9600-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:20:10.533 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
18:20:10.534 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:20:10.535 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
18:20:10.535 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
18:20:10.539 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
18:20:10.544 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
18:20:10.544 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:20:10.545 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
18:20:10.546 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
18:20:10.546 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:25:14.378 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:25:14.380 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:25:14.706 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:25:14.706 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@37a44abc[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:25:14.706 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755598756446_127.0.0.1_4947
18:25:14.706 [nacos-grpc-client-executor-86] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755598756446_127.0.0.1_4947]Ignore complete event,isRunning:false,isAbandon=false
18:25:14.712 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5066fc40[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 87]
18:25:14.838 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:25:14.838 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
18:25:14.838 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
18:25:14.838 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:25:14.838 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:25:14.838 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
18:25:17.641 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:25:18.187 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 54614c23-2587-4d64-9e56-a755eb0bdff5_config-0
18:25:18.230 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 3 keys and 6 values 
18:25:18.270 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
18:25:18.288 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
18:25:18.295 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
18:25:18.304 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
18:25:18.310 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
18:25:18.312 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54614c23-2587-4d64-9e56-a755eb0bdff5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:25:18.312 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54614c23-2587-4d64-9e56-a755eb0bdff5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002080c3be480
18:25:18.312 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54614c23-2587-4d64-9e56-a755eb0bdff5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002080c3be6a0
18:25:18.312 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54614c23-2587-4d64-9e56-a755eb0bdff5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:25:18.312 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54614c23-2587-4d64-9e56-a755eb0bdff5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:25:18.320 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54614c23-2587-4d64-9e56-a755eb0bdff5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:25:19.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54614c23-2587-4d64-9e56-a755eb0bdff5_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755599118855_127.0.0.1_5383
18:25:19.037 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54614c23-2587-4d64-9e56-a755eb0bdff5_config-0] Notify connected event to listeners.
18:25:19.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54614c23-2587-4d64-9e56-a755eb0bdff5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:25:19.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54614c23-2587-4d64-9e56-a755eb0bdff5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002080c4f8228
18:25:19.110 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:25:21.647 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
18:25:21.647 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:25:21.647 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
18:25:21.765 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:25:22.300 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
18:25:22.300 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
18:25:22.300 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
18:25:31.993 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:25:36.633 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cbd570dc-29d8-4346-80a7-9a0241f4d9ad
18:25:36.633 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] RpcClient init label, labels = {module=naming, source=sdk}
18:25:36.633 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:25:36.633 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:25:36.633 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:25:36.633 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:25:36.766 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] Success to connect to server [localhost:8848] on start up, connectionId = 1755599136651_127.0.0.1_5405
18:25:36.766 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:25:36.766 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] Notify connected event to listeners.
18:25:36.766 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002080c4f8228
18:25:36.830 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
18:25:36.866 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
18:25:37.020 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.895 seconds (JVM running for 21.025)
18:25:37.034 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
18:25:37.034 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
18:25:37.034 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
18:25:37.289 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] Receive server push request, request = NotifySubscriberRequest, requestId = 116
18:25:37.304 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] Ack server push request, request = NotifySubscriberRequest, requestId = 116
18:25:37.655 [RMI TCP Connection(9)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:27:44.448 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] Receive server push request, request = NotifySubscriberRequest, requestId = 118
18:27:44.448 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] Ack server push request, request = NotifySubscriberRequest, requestId = 118
18:27:44.677 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
18:27:44.677 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
18:27:44.677 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-6} inited
18:27:44.677 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:27:44.679 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
18:27:44.679 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-7} inited
18:27:44.679 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-5} inited
18:27:44.679 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
18:27:44.685 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
18:27:44.685 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:27:44.685 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
18:27:44.685 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
18:27:44.685 [http-nio-9600-exec-7] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:27:44.686 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-5} closing ...
18:27:44.686 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-5} closed
18:27:44.686 [http-nio-9600-exec-6] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:27:44.687 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-7} closing ...
18:27:44.688 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-7} closed
18:27:44.688 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:27:44.689 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
18:27:44.690 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
18:27:44.690 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:33:36.329 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] Server check success, currentServer is localhost:8848 
18:33:53.750 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] Server healthy check fail, currentConnection = 1755599136651_127.0.0.1_5405
18:33:53.761 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] Try to reconnect to a new server, server is  not appointed, will choose a random server.
18:34:13.551 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] Success to connect a server [localhost:8848], connectionId = 1755599634448_127.0.0.1_5954
18:34:13.551 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] Abandon prev connection, server is localhost:8848, connectionId is 1755599136651_127.0.0.1_5405
18:34:13.557 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755599136651_127.0.0.1_5405
18:34:13.559 [nacos-grpc-client-executor-107] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755599136651_127.0.0.1_5405]Ignore complete event,isRunning:false,isAbandon=true
18:34:13.562 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] Notify disconnected event to listeners
18:34:13.564 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] Notify connected event to listeners.
18:35:17.368 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] Server check success, currentServer is localhost:8848 
18:35:17.376 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] Server check success, currentServer is localhost:8848 
18:35:21.424 [nacos-grpc-client-executor-117] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] Receive server push request, request = NotifySubscriberRequest, requestId = 123
18:35:21.425 [nacos-grpc-client-executor-117] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] Ack server push request, request = NotifySubscriberRequest, requestId = 123
18:35:21.431 [nacos-grpc-client-executor-118] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] Receive server push request, request = NotifySubscriberRequest, requestId = 128
18:35:21.438 [nacos-grpc-client-executor-118] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cbd570dc-29d8-4346-80a7-9a0241f4d9ad] Ack server push request, request = NotifySubscriberRequest, requestId = 128
18:39:58.109 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:39:58.111 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:39:58.430 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:39:58.432 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@762af0f4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:39:58.433 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755599634448_127.0.0.1_5954
18:39:58.435 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@522e6b0d[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 175]
18:39:58.559 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:39:58.559 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-6} closing ...
18:39:58.559 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-6} closed
18:39:58.559 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:39:58.559 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:39:58.559 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
18:40:03.456 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:40:04.002 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 24c03c75-d4b6-4842-899f-7a04b14184b2_config-0
18:40:04.055 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 
18:40:04.084 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
18:40:04.090 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
18:40:04.098 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
18:40:04.105 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
18:40:04.110 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
18:40:04.112 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24c03c75-d4b6-4842-899f-7a04b14184b2_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:40:04.112 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24c03c75-d4b6-4842-899f-7a04b14184b2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000264683beaf8
18:40:04.112 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24c03c75-d4b6-4842-899f-7a04b14184b2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000264683bed18
18:40:04.113 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24c03c75-d4b6-4842-899f-7a04b14184b2_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:40:04.113 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24c03c75-d4b6-4842-899f-7a04b14184b2_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:40:04.119 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24c03c75-d4b6-4842-899f-7a04b14184b2_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:40:04.849 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24c03c75-d4b6-4842-899f-7a04b14184b2_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755600004662_127.0.0.1_6365
18:40:04.850 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24c03c75-d4b6-4842-899f-7a04b14184b2_config-0] Notify connected event to listeners.
18:40:04.850 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24c03c75-d4b6-4842-899f-7a04b14184b2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:40:04.851 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [24c03c75-d4b6-4842-899f-7a04b14184b2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000264684f8ad8
18:40:04.946 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:40:07.538 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
18:40:07.539 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:40:07.539 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
18:40:07.671 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:40:08.236 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
18:40:08.237 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
18:40:08.238 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
18:40:13.700 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:40:15.901 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 921edb14-216a-47da-a950-e71408fcb711
18:40:15.902 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [921edb14-216a-47da-a950-e71408fcb711] RpcClient init label, labels = {module=naming, source=sdk}
18:40:15.903 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [921edb14-216a-47da-a950-e71408fcb711] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:40:15.903 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [921edb14-216a-47da-a950-e71408fcb711] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:40:15.903 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [921edb14-216a-47da-a950-e71408fcb711] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:40:15.903 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [921edb14-216a-47da-a950-e71408fcb711] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:40:16.023 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [921edb14-216a-47da-a950-e71408fcb711] Success to connect to server [localhost:8848] on start up, connectionId = 1755600015910_127.0.0.1_6386
18:40:16.023 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [921edb14-216a-47da-a950-e71408fcb711] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:40:16.023 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [921edb14-216a-47da-a950-e71408fcb711] Notify connected event to listeners.
18:40:16.023 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [921edb14-216a-47da-a950-e71408fcb711] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000264684f8ad8
18:40:16.061 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
18:40:16.081 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
18:40:16.180 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.238 seconds (JVM running for 14.076)
18:40:16.193 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
18:40:16.193 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
18:40:16.193 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
18:40:16.556 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [921edb14-216a-47da-a950-e71408fcb711] Receive server push request, request = NotifySubscriberRequest, requestId = 134
18:40:16.571 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [921edb14-216a-47da-a950-e71408fcb711] Ack server push request, request = NotifySubscriberRequest, requestId = 134
18:40:16.699 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:41:02.326 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
18:41:02.326 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
18:41:02.326 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:41:02.340 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
18:41:02.343 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
18:41:02.348 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
18:41:02.348 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:41:02.349 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
18:41:02.349 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
18:41:02.349 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:30:27.627 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:30:27.627 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:30:27.955 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:30:27.955 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@66ceb038[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:30:27.955 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755600015910_127.0.0.1_6386
19:30:27.955 [nacos-grpc-client-executor-614] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755600015910_127.0.0.1_6386]Ignore complete event,isRunning:false,isAbandon=false
19:30:27.962 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6ffc497[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 615]
19:30:28.101 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:30:28.101 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
19:30:28.101 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
19:30:28.101 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:30:28.101 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:30:28.101 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
19:30:32.980 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:30:33.533 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6fc7749f-f3a9-4d78-a75d-da948f689043_config-0
19:30:33.586 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 
19:30:33.614 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
19:30:33.621 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
19:30:33.628 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
19:30:33.635 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
19:30:33.641 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
19:30:33.643 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fc7749f-f3a9-4d78-a75d-da948f689043_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:30:33.643 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fc7749f-f3a9-4d78-a75d-da948f689043_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000216013be480
19:30:33.643 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fc7749f-f3a9-4d78-a75d-da948f689043_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000216013be6a0
19:30:33.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fc7749f-f3a9-4d78-a75d-da948f689043_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:30:33.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fc7749f-f3a9-4d78-a75d-da948f689043_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:30:33.649 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fc7749f-f3a9-4d78-a75d-da948f689043_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:30:34.331 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fc7749f-f3a9-4d78-a75d-da948f689043_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755603034155_127.0.0.1_10360
19:30:34.332 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fc7749f-f3a9-4d78-a75d-da948f689043_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:30:34.332 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fc7749f-f3a9-4d78-a75d-da948f689043_config-0] Notify connected event to listeners.
19:30:34.334 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fc7749f-f3a9-4d78-a75d-da948f689043_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000216014f8440
19:30:34.409 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:30:36.837 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
19:30:36.838 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:30:36.838 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:30:36.948 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:30:37.680 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
19:30:37.681 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
19:30:37.682 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
19:30:42.866 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:30:45.548 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ee816e6a-fd81-4d0b-8cb1-a5a5a811232d
19:30:45.549 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee816e6a-fd81-4d0b-8cb1-a5a5a811232d] RpcClient init label, labels = {module=naming, source=sdk}
19:30:45.551 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee816e6a-fd81-4d0b-8cb1-a5a5a811232d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:30:45.551 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee816e6a-fd81-4d0b-8cb1-a5a5a811232d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:30:45.552 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee816e6a-fd81-4d0b-8cb1-a5a5a811232d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:30:45.552 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee816e6a-fd81-4d0b-8cb1-a5a5a811232d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:30:45.677 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee816e6a-fd81-4d0b-8cb1-a5a5a811232d] Success to connect to server [localhost:8848] on start up, connectionId = 1755603045560_127.0.0.1_10384
19:30:45.677 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee816e6a-fd81-4d0b-8cb1-a5a5a811232d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:30:45.677 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee816e6a-fd81-4d0b-8cb1-a5a5a811232d] Notify connected event to listeners.
19:30:45.677 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee816e6a-fd81-4d0b-8cb1-a5a5a811232d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000216014f8440
19:30:45.723 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
19:30:45.746 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
19:30:45.849 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.336 seconds (JVM running for 14.183)
19:30:45.862 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
19:30:45.863 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
19:30:45.863 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
19:30:46.228 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee816e6a-fd81-4d0b-8cb1-a5a5a811232d] Receive server push request, request = NotifySubscriberRequest, requestId = 145
19:30:46.242 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee816e6a-fd81-4d0b-8cb1-a5a5a811232d] Ack server push request, request = NotifySubscriberRequest, requestId = 145
19:30:46.273 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:40:13.064 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
19:40:13.064 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
19:40:13.064 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:40:13.064 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
19:40:13.074 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
19:40:13.079 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
19:40:13.079 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:40:13.079 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
19:40:13.080 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
19:40:13.080 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:44:37.226 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:44:37.230 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:44:37.554 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:44:37.555 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@e305112[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:44:37.555 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755603045560_127.0.0.1_10384
19:44:37.557 [nacos-grpc-client-executor-181] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755603045560_127.0.0.1_10384]Ignore complete event,isRunning:false,isAbandon=false
19:44:37.558 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@34081fde[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 182]
19:44:37.690 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:44:37.691 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:44:37.691 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:44:37.692 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:44:37.692 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:44:37.692 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
19:44:42.455 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:44:42.989 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 58bdcf38-2019-4b6e-a9ce-105554b4f826_config-0
19:44:43.043 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
19:44:43.070 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
19:44:43.078 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
19:44:43.085 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
19:44:43.093 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
19:44:43.100 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
19:44:43.102 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bdcf38-2019-4b6e-a9ce-105554b4f826_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:44:43.103 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bdcf38-2019-4b6e-a9ce-105554b4f826_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002153b3bf1c0
19:44:43.103 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bdcf38-2019-4b6e-a9ce-105554b4f826_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002153b3bf3e0
19:44:43.103 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bdcf38-2019-4b6e-a9ce-105554b4f826_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:44:43.103 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bdcf38-2019-4b6e-a9ce-105554b4f826_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:44:43.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bdcf38-2019-4b6e-a9ce-105554b4f826_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:44:43.814 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bdcf38-2019-4b6e-a9ce-105554b4f826_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755603883631_127.0.0.1_11521
19:44:43.815 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bdcf38-2019-4b6e-a9ce-105554b4f826_config-0] Notify connected event to listeners.
19:44:43.816 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bdcf38-2019-4b6e-a9ce-105554b4f826_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:44:43.816 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bdcf38-2019-4b6e-a9ce-105554b4f826_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002153b4f8d48
19:44:43.895 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:44:46.326 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
19:44:46.327 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:44:46.327 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:44:46.438 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:44:47.124 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
19:44:47.125 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
19:44:47.126 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
19:44:53.304 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:44:58.158 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e951ddeb-32d4-4aa6-b670-624fc46b48de
19:44:58.158 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e951ddeb-32d4-4aa6-b670-624fc46b48de] RpcClient init label, labels = {module=naming, source=sdk}
19:44:58.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e951ddeb-32d4-4aa6-b670-624fc46b48de] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:44:58.161 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e951ddeb-32d4-4aa6-b670-624fc46b48de] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:44:58.161 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e951ddeb-32d4-4aa6-b670-624fc46b48de] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:44:58.162 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e951ddeb-32d4-4aa6-b670-624fc46b48de] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:44:58.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e951ddeb-32d4-4aa6-b670-624fc46b48de] Success to connect to server [localhost:8848] on start up, connectionId = 1755603898178_127.0.0.1_11561
19:44:58.308 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e951ddeb-32d4-4aa6-b670-624fc46b48de] Notify connected event to listeners.
19:44:58.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e951ddeb-32d4-4aa6-b670-624fc46b48de] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:44:58.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e951ddeb-32d4-4aa6-b670-624fc46b48de] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002153b4f8d48
19:44:58.403 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
19:44:58.470 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
19:44:58.687 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 16.707 seconds (JVM running for 17.624)
19:44:58.710 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
19:44:58.711 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
19:44:58.712 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
19:44:58.841 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e951ddeb-32d4-4aa6-b670-624fc46b48de] Receive server push request, request = NotifySubscriberRequest, requestId = 155
19:44:58.857 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e951ddeb-32d4-4aa6-b670-624fc46b48de] Ack server push request, request = NotifySubscriberRequest, requestId = 155
19:44:59.167 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:46:19.504 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
19:46:19.504 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:46:19.512 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
19:46:19.512 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
19:46:19.514 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
19:46:19.520 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
19:46:19.520 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:46:19.521 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
19:46:19.521 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
19:46:19.522 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:59:26.682 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:59:26.682 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:59:27.036 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:59:27.036 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4e34e4c7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:59:27.036 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755603898178_127.0.0.1_11561
19:59:27.036 [nacos-grpc-client-executor-187] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755603898178_127.0.0.1_11561]Ignore complete event,isRunning:false,isAbandon=false
19:59:27.042 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1528afd9[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 188]
19:59:27.185 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:59:27.185 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:59:27.197 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:59:27.197 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:59:27.197 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:59:27.197 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
19:59:32.314 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:59:32.886 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3e307270-c5c8-44b5-9193-5939ddb758bc_config-0
19:59:32.937 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 
19:59:32.969 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
19:59:32.976 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
19:59:32.983 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
19:59:32.990 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
19:59:32.998 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
19:59:33.001 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e307270-c5c8-44b5-9193-5939ddb758bc_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:59:33.001 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e307270-c5c8-44b5-9193-5939ddb758bc_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001fe283be8d8
19:59:33.002 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e307270-c5c8-44b5-9193-5939ddb758bc_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001fe283beaf8
19:59:33.002 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e307270-c5c8-44b5-9193-5939ddb758bc_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:59:33.002 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e307270-c5c8-44b5-9193-5939ddb758bc_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:59:33.008 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e307270-c5c8-44b5-9193-5939ddb758bc_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:59:33.709 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e307270-c5c8-44b5-9193-5939ddb758bc_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755604773526_127.0.0.1_12992
19:59:33.709 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e307270-c5c8-44b5-9193-5939ddb758bc_config-0] Notify connected event to listeners.
19:59:33.710 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e307270-c5c8-44b5-9193-5939ddb758bc_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:59:33.710 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e307270-c5c8-44b5-9193-5939ddb758bc_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001fe284f8668
19:59:33.782 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:59:36.188 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
19:59:36.189 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:59:36.189 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:59:36.299 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:59:36.753 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
19:59:36.755 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
19:59:36.755 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
19:59:42.175 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:59:44.957 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 09d488aa-af5b-43fb-a0bc-89f4cedc1564
19:59:44.957 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09d488aa-af5b-43fb-a0bc-89f4cedc1564] RpcClient init label, labels = {module=naming, source=sdk}
19:59:44.959 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09d488aa-af5b-43fb-a0bc-89f4cedc1564] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:59:44.959 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09d488aa-af5b-43fb-a0bc-89f4cedc1564] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:59:44.959 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09d488aa-af5b-43fb-a0bc-89f4cedc1564] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:59:44.959 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09d488aa-af5b-43fb-a0bc-89f4cedc1564] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:59:45.085 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09d488aa-af5b-43fb-a0bc-89f4cedc1564] Success to connect to server [localhost:8848] on start up, connectionId = 1755604784963_127.0.0.1_13005
19:59:45.085 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09d488aa-af5b-43fb-a0bc-89f4cedc1564] Notify connected event to listeners.
19:59:45.085 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09d488aa-af5b-43fb-a0bc-89f4cedc1564] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:59:45.085 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09d488aa-af5b-43fb-a0bc-89f4cedc1564] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001fe284f8668
19:59:45.117 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
19:59:45.134 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
19:59:45.223 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.417 seconds (JVM running for 14.274)
19:59:45.246 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
19:59:45.246 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
19:59:45.246 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
19:59:45.609 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09d488aa-af5b-43fb-a0bc-89f4cedc1564] Receive server push request, request = NotifySubscriberRequest, requestId = 162
19:59:45.628 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [09d488aa-af5b-43fb-a0bc-89f4cedc1564] Ack server push request, request = NotifySubscriberRequest, requestId = 162
19:59:45.666 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:01:53.048 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
20:01:53.048 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:12:47.312 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:12:47.321 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:12:47.652 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:12:47.652 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2687997f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:12:47.652 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755604784963_127.0.0.1_13005
20:12:47.652 [nacos-grpc-client-executor-136] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755604784963_127.0.0.1_13005]Ignore complete event,isRunning:false,isAbandon=false
20:12:47.658 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@32ba7c6d[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 137]
20:12:47.790 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:12:47.798 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
20:12:47.798 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
20:12:47.798 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:12:47.798 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:12:47.798 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:12:53.245 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:12:53.953 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 674f3d62-7d38-4b68-8f5b-5aca208ebd01_config-0
20:12:54.027 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 3 keys and 6 values 
20:12:54.055 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
20:12:54.062 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
20:12:54.069 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
20:12:54.075 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
20:12:54.085 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
20:12:54.088 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [674f3d62-7d38-4b68-8f5b-5aca208ebd01_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:12:54.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [674f3d62-7d38-4b68-8f5b-5aca208ebd01_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000012aa439e480
20:12:54.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [674f3d62-7d38-4b68-8f5b-5aca208ebd01_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000012aa439e6a0
20:12:54.090 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [674f3d62-7d38-4b68-8f5b-5aca208ebd01_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:12:54.091 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [674f3d62-7d38-4b68-8f5b-5aca208ebd01_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:12:54.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [674f3d62-7d38-4b68-8f5b-5aca208ebd01_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:12:54.888 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [674f3d62-7d38-4b68-8f5b-5aca208ebd01_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755605574707_127.0.0.1_14053
20:12:54.888 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [674f3d62-7d38-4b68-8f5b-5aca208ebd01_config-0] Notify connected event to listeners.
20:12:54.888 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [674f3d62-7d38-4b68-8f5b-5aca208ebd01_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:12:54.888 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [674f3d62-7d38-4b68-8f5b-5aca208ebd01_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000012aa4518228
20:12:54.972 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:12:57.481 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
20:12:57.482 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:12:57.482 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
20:12:57.615 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:12:58.177 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
20:12:58.178 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
20:12:58.178 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
20:13:04.048 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:13:04.051 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:13:04.056 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:13:04.056 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:13:04.057 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
20:15:30.658 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:15:32.329 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9eba8e6c-1570-404e-a2fb-e94898758090_config-0
20:15:32.465 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 70 ms to scan 1 urls, producing 3 keys and 6 values 
20:15:32.542 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 4 keys and 9 values 
20:15:32.546 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
20:15:32.568 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
20:15:32.585 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
20:15:32.623 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 2 keys and 8 values 
20:15:32.625 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9eba8e6c-1570-404e-a2fb-e94898758090_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:15:32.625 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9eba8e6c-1570-404e-a2fb-e94898758090_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001d5013b6d38
20:15:32.625 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9eba8e6c-1570-404e-a2fb-e94898758090_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001d5013b6f58
20:15:32.625 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9eba8e6c-1570-404e-a2fb-e94898758090_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:15:32.625 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9eba8e6c-1570-404e-a2fb-e94898758090_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:15:32.653 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9eba8e6c-1570-404e-a2fb-e94898758090_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:15:34.165 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9eba8e6c-1570-404e-a2fb-e94898758090_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755605733890_127.0.0.1_14252
20:15:34.165 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9eba8e6c-1570-404e-a2fb-e94898758090_config-0] Notify connected event to listeners.
20:15:34.165 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9eba8e6c-1570-404e-a2fb-e94898758090_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:15:34.165 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9eba8e6c-1570-404e-a2fb-e94898758090_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001d5014f0668
20:15:34.336 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:15:39.889 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
20:15:39.889 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:15:39.889 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
20:15:40.069 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:15:40.808 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
20:15:40.808 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
20:15:40.808 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
20:15:47.760 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
20:15:50.334 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 87cfaecb-d762-452b-b8fa-9308e50e81a7
20:15:50.334 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [87cfaecb-d762-452b-b8fa-9308e50e81a7] RpcClient init label, labels = {module=naming, source=sdk}
20:15:50.334 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [87cfaecb-d762-452b-b8fa-9308e50e81a7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
20:15:50.334 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [87cfaecb-d762-452b-b8fa-9308e50e81a7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
20:15:50.334 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [87cfaecb-d762-452b-b8fa-9308e50e81a7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
20:15:50.334 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [87cfaecb-d762-452b-b8fa-9308e50e81a7] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:15:50.462 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [87cfaecb-d762-452b-b8fa-9308e50e81a7] Success to connect to server [localhost:8848] on start up, connectionId = 1755605750334_127.0.0.1_14292
20:15:50.462 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [87cfaecb-d762-452b-b8fa-9308e50e81a7] Notify connected event to listeners.
20:15:50.462 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [87cfaecb-d762-452b-b8fa-9308e50e81a7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:15:50.462 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [87cfaecb-d762-452b-b8fa-9308e50e81a7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001d5014f0668
20:15:50.522 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
20:15:50.536 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
20:15:50.637 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 21.756 seconds (JVM running for 25.286)
20:15:50.662 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
20:15:50.662 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
20:15:50.663 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
20:15:50.989 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [87cfaecb-d762-452b-b8fa-9308e50e81a7] Receive server push request, request = NotifySubscriberRequest, requestId = 176
20:15:51.005 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [87cfaecb-d762-452b-b8fa-9308e50e81a7] Ack server push request, request = NotifySubscriberRequest, requestId = 176
20:15:57.012 [http-nio-9600-exec-8] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:15:58.115 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
20:15:58.115 [http-nio-9600-exec-7] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:15:58.120 [http-nio-9600-exec-10] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
20:15:58.132 [http-nio-9600-exec-10] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
20:15:58.139 [http-nio-9600-exec-10] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
20:15:58.139 [http-nio-9600-exec-10] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:21:04.921 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:21:04.921 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:21:05.252 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:21:05.252 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@698e7d12[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:21:05.252 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755605750334_127.0.0.1_14292
20:21:05.252 [nacos-grpc-client-executor-70] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755605750334_127.0.0.1_14292]Ignore complete event,isRunning:false,isAbandon=false
20:21:05.259 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@31cd28ac[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 71]
20:21:05.398 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:21:05.398 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
20:21:05.400 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
20:21:05.400 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:21:05.400 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:21:05.400 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:21:09.595 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:21:10.136 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e1983378-81c1-4f69-9b43-42e1aca1ec35_config-0
20:21:10.203 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 3 keys and 6 values 
20:21:10.230 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
20:21:10.236 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
20:21:10.241 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 3 ms to scan 1 urls, producing 1 keys and 5 values 
20:21:10.243 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 2 ms to scan 1 urls, producing 1 keys and 7 values 
20:21:10.243 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
20:21:10.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1983378-81c1-4f69-9b43-42e1aca1ec35_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:21:10.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1983378-81c1-4f69-9b43-42e1aca1ec35_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000021f013ce8d8
20:21:10.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1983378-81c1-4f69-9b43-42e1aca1ec35_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000021f013ceaf8
20:21:10.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1983378-81c1-4f69-9b43-42e1aca1ec35_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:21:10.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1983378-81c1-4f69-9b43-42e1aca1ec35_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:21:10.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1983378-81c1-4f69-9b43-42e1aca1ec35_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:21:10.937 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1983378-81c1-4f69-9b43-42e1aca1ec35_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755606070754_127.0.0.1_14749
20:21:10.937 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1983378-81c1-4f69-9b43-42e1aca1ec35_config-0] Notify connected event to listeners.
20:21:10.937 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1983378-81c1-4f69-9b43-42e1aca1ec35_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:21:10.937 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1983378-81c1-4f69-9b43-42e1aca1ec35_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000021f01508ad8
20:21:11.003 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:21:13.349 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
20:21:13.362 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:21:13.362 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
20:21:13.466 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:21:14.002 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
20:21:14.002 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
20:21:14.002 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
20:21:19.367 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
20:21:21.487 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8e93113a-8973-432d-a142-9007dd7fdf4c
20:21:21.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e93113a-8973-432d-a142-9007dd7fdf4c] RpcClient init label, labels = {module=naming, source=sdk}
20:21:21.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e93113a-8973-432d-a142-9007dd7fdf4c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
20:21:21.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e93113a-8973-432d-a142-9007dd7fdf4c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
20:21:21.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e93113a-8973-432d-a142-9007dd7fdf4c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
20:21:21.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e93113a-8973-432d-a142-9007dd7fdf4c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:21:21.622 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e93113a-8973-432d-a142-9007dd7fdf4c] Success to connect to server [localhost:8848] on start up, connectionId = 1755606081500_127.0.0.1_14767
20:21:21.622 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e93113a-8973-432d-a142-9007dd7fdf4c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:21:21.622 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e93113a-8973-432d-a142-9007dd7fdf4c] Notify connected event to listeners.
20:21:21.622 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e93113a-8973-432d-a142-9007dd7fdf4c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000021f01508ad8
20:21:21.667 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
20:21:21.689 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
20:21:21.780 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.668 seconds (JVM running for 13.511)
20:21:21.791 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
20:21:21.791 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
20:21:21.792 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
20:21:21.907 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:21:22.167 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e93113a-8973-432d-a142-9007dd7fdf4c] Receive server push request, request = NotifySubscriberRequest, requestId = 180
20:21:22.180 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e93113a-8973-432d-a142-9007dd7fdf4c] Ack server push request, request = NotifySubscriberRequest, requestId = 180
20:21:27.748 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
20:21:27.748 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:41:07.125 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:41:07.129 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:41:07.462 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:41:07.462 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1d3bb808[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:41:07.462 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755606081500_127.0.0.1_14767
20:41:07.464 [nacos-grpc-client-executor-253] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755606081500_127.0.0.1_14767]Ignore complete event,isRunning:false,isAbandon=false
20:41:07.467 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@********[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 254]
20:41:07.729 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:41:07.739 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
20:41:07.745 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
20:41:07.745 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:41:07.749 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:41:07.749 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
