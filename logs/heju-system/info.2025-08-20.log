09:28:17.725 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:28:18.609 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 215b9757-d2d6-4bca-8ec1-0b7d73a9cb0e_config-0
09:28:18.716 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 48 ms to scan 1 urls, producing 3 keys and 6 values 
09:28:18.777 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 4 keys and 9 values 
09:28:18.789 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 3 keys and 10 values 
09:28:18.801 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:28:18.813 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:28:18.823 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:28:18.827 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [215b9757-d2d6-4bca-8ec1-0b7d73a9cb0e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:28:18.828 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [215b9757-d2d6-4bca-8ec1-0b7d73a9cb0e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000028dd63b6480
09:28:18.828 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [215b9757-d2d6-4bca-8ec1-0b7d73a9cb0e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000028dd63b66a0
09:28:18.828 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [215b9757-d2d6-4bca-8ec1-0b7d73a9cb0e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:28:18.829 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [215b9757-d2d6-4bca-8ec1-0b7d73a9cb0e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:28:18.838 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [215b9757-d2d6-4bca-8ec1-0b7d73a9cb0e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:28:19.923 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [215b9757-d2d6-4bca-8ec1-0b7d73a9cb0e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755653299671_127.0.0.1_8708
09:28:19.924 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [215b9757-d2d6-4bca-8ec1-0b7d73a9cb0e_config-0] Notify connected event to listeners.
09:28:19.925 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [215b9757-d2d6-4bca-8ec1-0b7d73a9cb0e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:28:19.926 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [215b9757-d2d6-4bca-8ec1-0b7d73a9cb0e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000028dd64f0228
09:28:20.154 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:28:26.637 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:28:26.639 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:28:26.639 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:28:27.085 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:28:28.440 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:28:28.442 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:28:28.443 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:28:40.719 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:28:44.076 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9763d152-3438-4ca1-9ad3-2e8b56ae0ca9
09:28:44.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9763d152-3438-4ca1-9ad3-2e8b56ae0ca9] RpcClient init label, labels = {module=naming, source=sdk}
09:28:44.080 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9763d152-3438-4ca1-9ad3-2e8b56ae0ca9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:28:44.080 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9763d152-3438-4ca1-9ad3-2e8b56ae0ca9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:28:44.081 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9763d152-3438-4ca1-9ad3-2e8b56ae0ca9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:28:44.082 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9763d152-3438-4ca1-9ad3-2e8b56ae0ca9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:28:44.219 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9763d152-3438-4ca1-9ad3-2e8b56ae0ca9] Success to connect to server [localhost:8848] on start up, connectionId = 1755653324096_127.0.0.1_8792
09:28:44.220 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9763d152-3438-4ca1-9ad3-2e8b56ae0ca9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:28:44.220 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9763d152-3438-4ca1-9ad3-2e8b56ae0ca9] Notify connected event to listeners.
09:28:44.220 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9763d152-3438-4ca1-9ad3-2e8b56ae0ca9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000028dd64f0228
09:28:44.288 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:28:44.325 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:28:44.451 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 27.326 seconds (JVM running for 28.752)
09:28:44.478 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:28:44.479 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:28:44.480 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:28:44.814 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9763d152-3438-4ca1-9ad3-2e8b56ae0ca9] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:28:44.839 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9763d152-3438-4ca1-9ad3-2e8b56ae0ca9] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:29:07.359 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:29:10.926 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9763d152-3438-4ca1-9ad3-2e8b56ae0ca9] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:29:10.927 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9763d152-3438-4ca1-9ad3-2e8b56ae0ca9] Ack server push request, request = NotifySubscriberRequest, requestId = 12
09:29:11.566 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:29:11.567 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:29:11.801 [http-nio-9600-exec-8] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:29:11.802 [http-nio-9600-exec-8] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
10:52:06.808 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:52:06.821 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:52:07.162 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:52:07.162 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7cb4145[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:52:07.162 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755653324096_127.0.0.1_8792
10:52:07.168 [nacos-grpc-client-executor-1026] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755653324096_127.0.0.1_8792]Ignore complete event,isRunning:false,isAbandon=false
10:52:07.187 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@508ca15e[Running, pool size = 7, active threads = 0, queued tasks = 0, completed tasks = 1027]
10:52:07.653 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:52:07.676 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
10:52:07.717 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
10:52:07.718 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:52:07.723 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:52:07.725 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:52:07.727 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:52:07.729 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:52:14.943 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:52:15.767 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ecd9eddf-346f-420b-a19a-fda1597fd121_config-0
10:52:15.871 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 44 ms to scan 1 urls, producing 3 keys and 6 values 
10:52:15.924 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
10:52:15.937 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
10:52:15.949 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
10:52:15.961 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
10:52:15.974 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
10:52:15.980 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ecd9eddf-346f-420b-a19a-fda1597fd121_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:52:15.982 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ecd9eddf-346f-420b-a19a-fda1597fd121_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002442539eaf8
10:52:15.982 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ecd9eddf-346f-420b-a19a-fda1597fd121_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002442539ed18
10:52:15.983 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ecd9eddf-346f-420b-a19a-fda1597fd121_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:52:15.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ecd9eddf-346f-420b-a19a-fda1597fd121_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:52:15.999 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ecd9eddf-346f-420b-a19a-fda1597fd121_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:52:17.143 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ecd9eddf-346f-420b-a19a-fda1597fd121_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755658336921_127.0.0.1_2928
10:52:17.146 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ecd9eddf-346f-420b-a19a-fda1597fd121_config-0] Notify connected event to listeners.
10:52:17.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ecd9eddf-346f-420b-a19a-fda1597fd121_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:52:17.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ecd9eddf-346f-420b-a19a-fda1597fd121_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000024425518fb0
10:52:17.340 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:52:21.615 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:52:21.616 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:52:21.616 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:52:21.829 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:52:22.706 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:52:22.708 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:52:22.709 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:52:32.180 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:52:38.840 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9b3f5094-bfd0-47b7-9638-630db7c1cbfe
10:52:38.840 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b3f5094-bfd0-47b7-9638-630db7c1cbfe] RpcClient init label, labels = {module=naming, source=sdk}
10:52:38.846 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b3f5094-bfd0-47b7-9638-630db7c1cbfe] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:52:38.846 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b3f5094-bfd0-47b7-9638-630db7c1cbfe] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:52:38.846 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b3f5094-bfd0-47b7-9638-630db7c1cbfe] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:52:38.846 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b3f5094-bfd0-47b7-9638-630db7c1cbfe] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:52:38.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b3f5094-bfd0-47b7-9638-630db7c1cbfe] Success to connect to server [localhost:8848] on start up, connectionId = 1755658358864_127.0.0.1_2947
10:52:38.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b3f5094-bfd0-47b7-9638-630db7c1cbfe] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:52:38.992 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b3f5094-bfd0-47b7-9638-630db7c1cbfe] Notify connected event to listeners.
10:52:38.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b3f5094-bfd0-47b7-9638-630db7c1cbfe] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000024425518fb0
10:52:39.087 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:52:39.152 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:52:39.461 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 25.114 seconds (JVM running for 26.15)
10:52:39.512 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:52:39.516 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:52:39.516 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:52:39.582 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b3f5094-bfd0-47b7-9638-630db7c1cbfe] Receive server push request, request = NotifySubscriberRequest, requestId = 20
10:52:39.600 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b3f5094-bfd0-47b7-9638-630db7c1cbfe] Ack server push request, request = NotifySubscriberRequest, requestId = 20
10:52:39.723 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:52:58.499 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
10:52:58.499 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:52:58.499 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
10:52:58.499 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:52:58.509 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
10:52:58.523 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
10:52:58.523 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:52:58.524 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
10:52:58.526 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
10:52:58.527 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:17:38.670 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:17:38.678 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:17:39.009 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:17:39.011 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2d6aab11[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:17:39.011 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755658358864_127.0.0.1_2947
11:17:39.014 [nacos-grpc-client-executor-277] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755658358864_127.0.0.1_2947]Ignore complete event,isRunning:false,isAbandon=false
11:17:39.015 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@********[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 278]
11:17:39.274 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:17:39.274 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:17:39.274 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:17:39.274 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:17:39.282 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:17:39.283 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:17:47.252 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:17:48.031 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5179f2a4-bc69-493e-b390-ceccf7bfb2e5_config-0
11:17:48.091 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 3 keys and 6 values 
11:17:48.131 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
11:17:48.140 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
11:17:48.158 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 5 values 
11:17:48.167 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
11:17:48.174 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
11:17:48.176 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5179f2a4-bc69-493e-b390-ceccf7bfb2e5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:17:48.177 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5179f2a4-bc69-493e-b390-ceccf7bfb2e5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000234b239e8d8
11:17:48.177 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5179f2a4-bc69-493e-b390-ceccf7bfb2e5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000234b239eaf8
11:17:48.178 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5179f2a4-bc69-493e-b390-ceccf7bfb2e5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:17:48.179 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5179f2a4-bc69-493e-b390-ceccf7bfb2e5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:17:48.187 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5179f2a4-bc69-493e-b390-ceccf7bfb2e5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:17:49.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5179f2a4-bc69-493e-b390-ceccf7bfb2e5_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755659868976_127.0.0.1_4805
11:17:49.202 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5179f2a4-bc69-493e-b390-ceccf7bfb2e5_config-0] Notify connected event to listeners.
11:17:49.204 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5179f2a4-bc69-493e-b390-ceccf7bfb2e5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:17:49.205 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5179f2a4-bc69-493e-b390-ceccf7bfb2e5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000234b2518ad8
11:17:49.403 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:17:53.155 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:17:53.156 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:17:53.156 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:17:53.353 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:17:54.061 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:17:54.063 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:17:54.064 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:18:02.216 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:18:06.034 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f4a9045f-c3d4-423b-9fbd-b31eca5bd53d
11:18:06.035 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4a9045f-c3d4-423b-9fbd-b31eca5bd53d] RpcClient init label, labels = {module=naming, source=sdk}
11:18:06.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4a9045f-c3d4-423b-9fbd-b31eca5bd53d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:18:06.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4a9045f-c3d4-423b-9fbd-b31eca5bd53d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:18:06.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4a9045f-c3d4-423b-9fbd-b31eca5bd53d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:18:06.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4a9045f-c3d4-423b-9fbd-b31eca5bd53d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:18:06.162 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4a9045f-c3d4-423b-9fbd-b31eca5bd53d] Success to connect to server [localhost:8848] on start up, connectionId = 1755659886046_127.0.0.1_4826
11:18:06.163 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4a9045f-c3d4-423b-9fbd-b31eca5bd53d] Notify connected event to listeners.
11:18:06.164 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4a9045f-c3d4-423b-9fbd-b31eca5bd53d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:18:06.165 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4a9045f-c3d4-423b-9fbd-b31eca5bd53d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000234b2518ad8
11:18:06.232 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:18:06.280 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:18:06.410 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.769 seconds (JVM running for 20.928)
11:18:06.425 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:18:06.426 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:18:06.426 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:18:06.740 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4a9045f-c3d4-423b-9fbd-b31eca5bd53d] Receive server push request, request = NotifySubscriberRequest, requestId = 26
11:18:06.770 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4a9045f-c3d4-423b-9fbd-b31eca5bd53d] Ack server push request, request = NotifySubscriberRequest, requestId = 26
11:18:06.857 [RMI TCP Connection(9)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:18:23.974 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:18:23.974 [http-nio-9600-exec-7] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:19:13.919 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:19:13.931 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:19:14.259 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:19:14.259 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1c89b32e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:19:14.261 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755659886046_127.0.0.1_4826
11:19:14.265 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755659886046_127.0.0.1_4826]Ignore complete event,isRunning:false,isAbandon=false
11:19:14.266 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@bc592f2[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 24]
11:19:14.482 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:19:14.497 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:19:14.516 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:19:14.518 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:19:14.520 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:19:14.520 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:19:21.401 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:19:22.201 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9dde86a6-ef5f-40f2-a3b4-6d71c2347f29_config-0
11:19:22.282 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 35 ms to scan 1 urls, producing 3 keys and 6 values 
11:19:22.325 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
11:19:22.334 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
11:19:22.343 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
11:19:22.358 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
11:19:22.365 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
11:19:22.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dde86a6-ef5f-40f2-a3b4-6d71c2347f29_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:19:22.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dde86a6-ef5f-40f2-a3b4-6d71c2347f29_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002a8ce39e8d8
11:19:22.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dde86a6-ef5f-40f2-a3b4-6d71c2347f29_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002a8ce39eaf8
11:19:22.370 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dde86a6-ef5f-40f2-a3b4-6d71c2347f29_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:19:22.371 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dde86a6-ef5f-40f2-a3b4-6d71c2347f29_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:19:22.383 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dde86a6-ef5f-40f2-a3b4-6d71c2347f29_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:19:23.431 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dde86a6-ef5f-40f2-a3b4-6d71c2347f29_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755659963216_127.0.0.1_4974
11:19:23.432 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dde86a6-ef5f-40f2-a3b4-6d71c2347f29_config-0] Notify connected event to listeners.
11:19:23.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dde86a6-ef5f-40f2-a3b4-6d71c2347f29_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:19:23.434 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dde86a6-ef5f-40f2-a3b4-6d71c2347f29_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002a8ce518668
11:19:23.578 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:19:27.804 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:19:27.805 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:19:27.806 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:19:28.339 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:19:29.219 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:19:29.222 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:19:29.223 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:19:37.755 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:19:40.879 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1279a4c2-018d-46eb-a974-c20f322aed5c
11:19:40.880 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1279a4c2-018d-46eb-a974-c20f322aed5c] RpcClient init label, labels = {module=naming, source=sdk}
11:19:40.882 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1279a4c2-018d-46eb-a974-c20f322aed5c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:19:40.882 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1279a4c2-018d-46eb-a974-c20f322aed5c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:19:40.882 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1279a4c2-018d-46eb-a974-c20f322aed5c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:19:40.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1279a4c2-018d-46eb-a974-c20f322aed5c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:19:41.018 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1279a4c2-018d-46eb-a974-c20f322aed5c] Success to connect to server [localhost:8848] on start up, connectionId = 1755659980893_127.0.0.1_5020
11:19:41.019 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1279a4c2-018d-46eb-a974-c20f322aed5c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:19:41.019 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1279a4c2-018d-46eb-a974-c20f322aed5c] Notify connected event to listeners.
11:19:41.019 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1279a4c2-018d-46eb-a974-c20f322aed5c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002a8ce518668
11:19:41.105 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:19:41.154 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:19:41.317 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.547 seconds (JVM running for 21.511)
11:19:41.341 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:19:41.342 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:19:41.342 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:19:41.574 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1279a4c2-018d-46eb-a974-c20f322aed5c] Receive server push request, request = NotifySubscriberRequest, requestId = 33
11:19:41.596 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1279a4c2-018d-46eb-a974-c20f322aed5c] Ack server push request, request = NotifySubscriberRequest, requestId = 33
11:19:41.709 [RMI TCP Connection(13)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:19:49.113 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:19:49.114 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:21:03.510 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:21:03.531 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:21:03.880 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:21:03.880 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6881ef3d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:21:03.880 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755659980893_127.0.0.1_5020
11:21:03.880 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755659980893_127.0.0.1_5020]Ignore complete event,isRunning:false,isAbandon=false
11:21:03.880 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2b96a18b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 26]
11:21:04.017 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:21:04.017 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:21:04.037 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:21:04.037 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:21:04.037 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:21:04.037 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:21:11.796 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:21:12.597 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c748ad1e-3bd8-4447-a7d2-7b090d5e6302_config-0
11:21:12.678 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 36 ms to scan 1 urls, producing 3 keys and 6 values 
11:21:12.716 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
11:21:12.725 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
11:21:12.734 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
11:21:12.743 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
11:21:12.755 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
11:21:12.758 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c748ad1e-3bd8-4447-a7d2-7b090d5e6302_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:21:12.758 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c748ad1e-3bd8-4447-a7d2-7b090d5e6302_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000139c539e8d8
11:21:12.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c748ad1e-3bd8-4447-a7d2-7b090d5e6302_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000139c539eaf8
11:21:12.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c748ad1e-3bd8-4447-a7d2-7b090d5e6302_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:21:12.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c748ad1e-3bd8-4447-a7d2-7b090d5e6302_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:21:12.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c748ad1e-3bd8-4447-a7d2-7b090d5e6302_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:21:13.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c748ad1e-3bd8-4447-a7d2-7b090d5e6302_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755660073473_127.0.0.1_5159
11:21:13.687 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c748ad1e-3bd8-4447-a7d2-7b090d5e6302_config-0] Notify connected event to listeners.
11:21:13.688 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c748ad1e-3bd8-4447-a7d2-7b090d5e6302_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:21:13.689 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c748ad1e-3bd8-4447-a7d2-7b090d5e6302_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000139c5518668
11:21:13.849 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:21:17.753 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:21:17.754 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:21:17.754 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:21:17.973 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:21:18.634 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:21:18.635 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:21:18.635 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:21:26.494 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:21:29.532 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2bc43ec9-4ee3-4969-b220-57cead29e1c1
11:21:29.533 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bc43ec9-4ee3-4969-b220-57cead29e1c1] RpcClient init label, labels = {module=naming, source=sdk}
11:21:29.534 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bc43ec9-4ee3-4969-b220-57cead29e1c1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:21:29.534 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bc43ec9-4ee3-4969-b220-57cead29e1c1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:21:29.535 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bc43ec9-4ee3-4969-b220-57cead29e1c1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:21:29.535 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bc43ec9-4ee3-4969-b220-57cead29e1c1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:21:29.662 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bc43ec9-4ee3-4969-b220-57cead29e1c1] Success to connect to server [localhost:8848] on start up, connectionId = 1755660089544_127.0.0.1_5171
11:21:29.663 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bc43ec9-4ee3-4969-b220-57cead29e1c1] Notify connected event to listeners.
11:21:29.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bc43ec9-4ee3-4969-b220-57cead29e1c1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:21:29.664 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bc43ec9-4ee3-4969-b220-57cead29e1c1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000139c5518668
11:21:29.745 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:21:29.790 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:21:29.940 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 18.765 seconds (JVM running for 19.85)
11:21:29.958 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:21:29.959 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:21:29.959 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:21:30.289 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bc43ec9-4ee3-4969-b220-57cead29e1c1] Receive server push request, request = NotifySubscriberRequest, requestId = 42
11:21:30.313 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bc43ec9-4ee3-4969-b220-57cead29e1c1] Ack server push request, request = NotifySubscriberRequest, requestId = 42
11:21:30.382 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:21:38.229 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
11:21:38.230 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:30:36.490 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:30:36.501 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:30:36.814 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:30:36.814 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@d30afef[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:30:36.814 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755660089544_127.0.0.1_5171
13:30:36.814 [nacos-grpc-client-executor-1560] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755660089544_127.0.0.1_5171]Ignore complete event,isRunning:false,isAbandon=false
13:30:36.821 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@145ab77f[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1561]
13:30:36.955 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:30:36.955 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:30:36.971 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:30:36.972 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:30:36.972 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:30:36.972 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:31:04.750 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:31:05.350 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a7612c5b-4cfb-41a0-b486-928cf767069f_config-0
13:31:05.420 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 3 keys and 6 values 
13:31:05.456 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
13:31:05.463 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
13:31:05.470 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
13:31:05.478 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
13:31:05.487 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
13:31:05.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7612c5b-4cfb-41a0-b486-928cf767069f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:31:05.491 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7612c5b-4cfb-41a0-b486-928cf767069f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001f8a73b6480
13:31:05.491 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7612c5b-4cfb-41a0-b486-928cf767069f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001f8a73b66a0
13:31:05.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7612c5b-4cfb-41a0-b486-928cf767069f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:31:05.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7612c5b-4cfb-41a0-b486-928cf767069f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:31:05.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7612c5b-4cfb-41a0-b486-928cf767069f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:31:06.211 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7612c5b-4cfb-41a0-b486-928cf767069f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755667866017_127.0.0.1_7516
13:31:06.211 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7612c5b-4cfb-41a0-b486-928cf767069f_config-0] Notify connected event to listeners.
13:31:06.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7612c5b-4cfb-41a0-b486-928cf767069f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:31:06.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a7612c5b-4cfb-41a0-b486-928cf767069f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001f8a74f0228
13:31:06.348 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:31:08.914 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:31:08.915 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:31:08.915 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:31:09.029 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:31:09.472 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:31:09.474 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:31:09.474 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:31:21.059 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:31:25.524 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 44a18e89-1998-4e20-8781-865734d37233
13:31:25.525 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44a18e89-1998-4e20-8781-865734d37233] RpcClient init label, labels = {module=naming, source=sdk}
13:31:25.528 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44a18e89-1998-4e20-8781-865734d37233] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:31:25.528 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44a18e89-1998-4e20-8781-865734d37233] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:31:25.529 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44a18e89-1998-4e20-8781-865734d37233] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:31:25.530 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44a18e89-1998-4e20-8781-865734d37233] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:31:25.658 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44a18e89-1998-4e20-8781-865734d37233] Success to connect to server [localhost:8848] on start up, connectionId = 1755667885540_127.0.0.1_7584
13:31:25.659 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44a18e89-1998-4e20-8781-865734d37233] Notify connected event to listeners.
13:31:25.659 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44a18e89-1998-4e20-8781-865734d37233] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:31:25.660 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44a18e89-1998-4e20-8781-865734d37233] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001f8a74f0228
13:31:25.735 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:31:25.786 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
13:31:25.981 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 21.7 seconds (JVM running for 23.446)
13:31:26.005 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:31:26.007 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:31:26.008 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:31:26.295 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44a18e89-1998-4e20-8781-865734d37233] Receive server push request, request = NotifySubscriberRequest, requestId = 50
13:31:26.313 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [44a18e89-1998-4e20-8781-865734d37233] Ack server push request, request = NotifySubscriberRequest, requestId = 50
13:32:58.971 [http-nio-9600-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:32:59.971 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:32:59.971 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
13:32:59.971 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:32:59.972 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
13:32:59.975 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:32:59.980 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:32:59.980 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:32:59.980 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
13:32:59.981 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
13:32:59.981 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:33:41.849 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:33:41.861 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:33:42.187 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:33:42.187 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@36f41d0b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:33:42.187 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755667885540_127.0.0.1_7584
15:33:42.187 [nacos-grpc-client-executor-1481] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755667885540_127.0.0.1_7584]Ignore complete event,isRunning:false,isAbandon=false
15:33:42.187 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3c6061a9[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 1482]
15:33:42.321 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:33:42.321 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
15:33:42.321 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
15:33:42.321 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:33:42.321 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:33:42.321 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:33:47.421 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:33:47.971 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of db73aaa1-98fb-4e35-a24e-8a68f17d60f2_config-0
15:33:48.024 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 
15:33:48.052 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
15:33:48.060 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
15:33:48.067 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
15:33:48.074 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
15:33:48.080 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
15:33:48.082 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db73aaa1-98fb-4e35-a24e-8a68f17d60f2_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:33:48.082 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db73aaa1-98fb-4e35-a24e-8a68f17d60f2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000200113beaf8
15:33:48.082 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db73aaa1-98fb-4e35-a24e-8a68f17d60f2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000200113bed18
15:33:48.083 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db73aaa1-98fb-4e35-a24e-8a68f17d60f2_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:33:48.083 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db73aaa1-98fb-4e35-a24e-8a68f17d60f2_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:33:48.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db73aaa1-98fb-4e35-a24e-8a68f17d60f2_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:33:48.771 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db73aaa1-98fb-4e35-a24e-8a68f17d60f2_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755675228590_127.0.0.1_2827
15:33:48.771 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db73aaa1-98fb-4e35-a24e-8a68f17d60f2_config-0] Notify connected event to listeners.
15:33:48.773 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db73aaa1-98fb-4e35-a24e-8a68f17d60f2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:33:48.774 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db73aaa1-98fb-4e35-a24e-8a68f17d60f2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000200114f8668
15:33:48.867 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:33:51.487 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:33:51.487 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:33:51.488 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:33:51.594 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:33:52.132 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:33:52.134 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:33:52.134 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:33:57.345 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:33:59.358 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 37f4facc-8614-4d8e-bfc4-e73e8350bc4c
15:33:59.358 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37f4facc-8614-4d8e-bfc4-e73e8350bc4c] RpcClient init label, labels = {module=naming, source=sdk}
15:33:59.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37f4facc-8614-4d8e-bfc4-e73e8350bc4c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:33:59.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37f4facc-8614-4d8e-bfc4-e73e8350bc4c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:33:59.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37f4facc-8614-4d8e-bfc4-e73e8350bc4c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:33:59.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37f4facc-8614-4d8e-bfc4-e73e8350bc4c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:33:59.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37f4facc-8614-4d8e-bfc4-e73e8350bc4c] Success to connect to server [localhost:8848] on start up, connectionId = 1755675239368_127.0.0.1_2842
15:33:59.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37f4facc-8614-4d8e-bfc4-e73e8350bc4c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:33:59.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37f4facc-8614-4d8e-bfc4-e73e8350bc4c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000200114f8668
15:33:59.481 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37f4facc-8614-4d8e-bfc4-e73e8350bc4c] Notify connected event to listeners.
15:33:59.524 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:33:59.546 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:33:59.633 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.695 seconds (JVM running for 13.561)
15:33:59.644 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:33:59.644 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:33:59.645 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:34:00.041 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37f4facc-8614-4d8e-bfc4-e73e8350bc4c] Receive server push request, request = NotifySubscriberRequest, requestId = 59
15:34:00.056 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [37f4facc-8614-4d8e-bfc4-e73e8350bc4c] Ack server push request, request = NotifySubscriberRequest, requestId = 59
15:34:00.161 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:35:26.160 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:35:26.160 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:37:17.046 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:37:17.046 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:37:17.380 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:37:17.383 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6172f958[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:37:17.383 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755675239368_127.0.0.1_2842
15:37:17.383 [nacos-grpc-client-executor-53] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755675239368_127.0.0.1_2842]Ignore complete event,isRunning:false,isAbandon=false
15:37:17.383 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4319ff4f[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 54]
15:37:17.557 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:37:17.570 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:37:17.576 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:37:17.576 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:37:17.576 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:37:17.576 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:37:21.658 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:37:22.178 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c869974c-7c2a-430d-af02-96c9f5dc61cc_config-0
15:37:22.230 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 6 values 
15:37:22.250 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
15:37:22.260 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
15:37:22.266 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
15:37:22.266 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 7 values 
15:37:22.280 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
15:37:22.282 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c869974c-7c2a-430d-af02-96c9f5dc61cc_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:37:22.282 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c869974c-7c2a-430d-af02-96c9f5dc61cc_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000234013be480
15:37:22.282 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c869974c-7c2a-430d-af02-96c9f5dc61cc_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000234013be6a0
15:37:22.282 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c869974c-7c2a-430d-af02-96c9f5dc61cc_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:37:22.284 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c869974c-7c2a-430d-af02-96c9f5dc61cc_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:37:22.289 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c869974c-7c2a-430d-af02-96c9f5dc61cc_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:37:22.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c869974c-7c2a-430d-af02-96c9f5dc61cc_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755675442764_127.0.0.1_3106
15:37:22.950 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c869974c-7c2a-430d-af02-96c9f5dc61cc_config-0] Notify connected event to listeners.
15:37:22.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c869974c-7c2a-430d-af02-96c9f5dc61cc_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:37:22.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c869974c-7c2a-430d-af02-96c9f5dc61cc_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000234014f8440
15:37:23.038 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:37:25.352 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:37:25.352 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:37:25.352 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:37:25.450 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:37:26.038 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:37:26.038 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:37:26.038 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:37:30.915 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:37:32.949 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of *************-4859-bf6e-8093b2469b53
15:37:32.949 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [*************-4859-bf6e-8093b2469b53] RpcClient init label, labels = {module=naming, source=sdk}
15:37:32.949 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [*************-4859-bf6e-8093b2469b53] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:37:32.949 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [*************-4859-bf6e-8093b2469b53] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:37:32.949 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [*************-4859-bf6e-8093b2469b53] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:37:32.949 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [*************-4859-bf6e-8093b2469b53] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:37:33.068 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [*************-4859-bf6e-8093b2469b53] Success to connect to server [localhost:8848] on start up, connectionId = 1755675452958_127.0.0.1_3125
15:37:33.068 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [*************-4859-bf6e-8093b2469b53] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:37:33.068 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [*************-4859-bf6e-8093b2469b53] Notify connected event to listeners.
15:37:33.068 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [*************-4859-bf6e-8093b2469b53] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000234014f8440
15:37:33.092 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:37:33.121 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:37:33.203 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.007 seconds (JVM running for 12.788)
15:37:33.204 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:37:33.204 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:37:33.204 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:37:33.525 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:37:33.599 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [*************-4859-bf6e-8093b2469b53] Receive server push request, request = NotifySubscriberRequest, requestId = 69
15:37:33.615 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [*************-4859-bf6e-8093b2469b53] Ack server push request, request = NotifySubscriberRequest, requestId = 69
15:37:42.820 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:37:42.820 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:37:42.820 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
15:37:42.832 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:37:42.838 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:37:42.838 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:05:29.226 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:05:29.228 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:05:29.554 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:05:29.554 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@579b9c79[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:05:29.554 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755675452958_127.0.0.1_3125
16:05:29.555 [nacos-grpc-client-executor-348] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755675452958_127.0.0.1_3125]Ignore complete event,isRunning:false,isAbandon=false
16:05:29.555 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@723ffc20[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 349]
16:05:29.695 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:05:29.695 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
16:05:29.695 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
16:05:29.695 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:05:29.695 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:05:29.695 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:05:35.721 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:05:36.260 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1aaaf1c5-dcc8-4d59-bc78-03f3f63e6ebd_config-0
16:05:36.313 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 
16:05:36.345 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
16:05:36.350 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
16:05:36.355 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
16:05:36.360 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
16:05:36.370 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
16:05:36.370 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1aaaf1c5-dcc8-4d59-bc78-03f3f63e6ebd_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:05:36.370 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1aaaf1c5-dcc8-4d59-bc78-03f3f63e6ebd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001b4463ce480
16:05:36.370 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1aaaf1c5-dcc8-4d59-bc78-03f3f63e6ebd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001b4463ce6a0
16:05:36.370 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1aaaf1c5-dcc8-4d59-bc78-03f3f63e6ebd_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:05:36.374 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1aaaf1c5-dcc8-4d59-bc78-03f3f63e6ebd_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:05:36.380 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1aaaf1c5-dcc8-4d59-bc78-03f3f63e6ebd_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:05:37.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1aaaf1c5-dcc8-4d59-bc78-03f3f63e6ebd_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755677136880_127.0.0.1_12952
16:05:37.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1aaaf1c5-dcc8-4d59-bc78-03f3f63e6ebd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:05:37.056 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1aaaf1c5-dcc8-4d59-bc78-03f3f63e6ebd_config-0] Notify connected event to listeners.
16:05:37.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1aaaf1c5-dcc8-4d59-bc78-03f3f63e6ebd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001b446508228
16:05:37.130 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:05:39.790 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:05:39.790 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:05:39.790 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:05:39.934 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:05:40.680 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:05:40.680 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:05:40.680 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:05:51.156 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:05:54.158 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f48cbf93-a930-4bf0-b2e2-494cf1684aa9
16:05:54.158 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f48cbf93-a930-4bf0-b2e2-494cf1684aa9] RpcClient init label, labels = {module=naming, source=sdk}
16:05:54.161 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f48cbf93-a930-4bf0-b2e2-494cf1684aa9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:05:54.162 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f48cbf93-a930-4bf0-b2e2-494cf1684aa9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:05:54.162 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f48cbf93-a930-4bf0-b2e2-494cf1684aa9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:05:54.162 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f48cbf93-a930-4bf0-b2e2-494cf1684aa9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:05:54.292 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f48cbf93-a930-4bf0-b2e2-494cf1684aa9] Success to connect to server [localhost:8848] on start up, connectionId = 1755677154170_127.0.0.1_12983
16:05:54.292 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f48cbf93-a930-4bf0-b2e2-494cf1684aa9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:05:54.292 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f48cbf93-a930-4bf0-b2e2-494cf1684aa9] Notify connected event to listeners.
16:05:54.292 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f48cbf93-a930-4bf0-b2e2-494cf1684aa9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001b446508228
16:05:54.343 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:05:54.373 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
16:05:54.491 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.235 seconds (JVM running for 20.13)
16:05:54.507 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:05:54.507 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:05:54.508 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:05:54.885 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f48cbf93-a930-4bf0-b2e2-494cf1684aa9] Receive server push request, request = NotifySubscriberRequest, requestId = 80
16:05:54.901 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f48cbf93-a930-4bf0-b2e2-494cf1684aa9] Ack server push request, request = NotifySubscriberRequest, requestId = 80
16:05:54.972 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:06:37.650 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:06:37.650 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:06:37.670 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
16:06:37.682 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:06:37.685 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:06:37.685 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:22:22.503 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:22:22.508 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:22:22.840 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:22:22.843 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@53f16226[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:22:22.843 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755677154170_127.0.0.1_12983
19:22:22.843 [nacos-grpc-client-executor-2370] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755677154170_127.0.0.1_12983]Ignore complete event,isRunning:false,isAbandon=false
19:22:22.843 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6aefd56c[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 2370]
19:22:23.024 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:22:23.034 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
19:22:23.034 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
19:22:23.034 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:22:23.034 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:22:23.034 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
19:22:29.526 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:22:30.391 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 33afd8ae-eff6-448d-a1e9-d6908c7517ed_config-0
19:22:30.474 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 40 ms to scan 1 urls, producing 3 keys and 6 values 
19:22:30.522 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
19:22:30.531 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
19:22:30.543 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
19:22:30.553 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
19:22:30.567 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
19:22:30.570 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [33afd8ae-eff6-448d-a1e9-d6908c7517ed_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:22:30.570 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [33afd8ae-eff6-448d-a1e9-d6908c7517ed_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001b0d23beaf8
19:22:30.570 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [33afd8ae-eff6-448d-a1e9-d6908c7517ed_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001b0d23bed18
19:22:30.571 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [33afd8ae-eff6-448d-a1e9-d6908c7517ed_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:22:30.572 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [33afd8ae-eff6-448d-a1e9-d6908c7517ed_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:22:30.580 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [33afd8ae-eff6-448d-a1e9-d6908c7517ed_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:22:31.695 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [33afd8ae-eff6-448d-a1e9-d6908c7517ed_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755688951464_127.0.0.1_5598
19:22:31.700 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [33afd8ae-eff6-448d-a1e9-d6908c7517ed_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:22:31.700 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [33afd8ae-eff6-448d-a1e9-d6908c7517ed_config-0] Notify connected event to listeners.
19:22:31.702 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [33afd8ae-eff6-448d-a1e9-d6908c7517ed_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001b0d24f8ad8
19:22:31.945 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:22:36.410 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
19:22:36.411 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:22:36.411 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:22:36.594 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:22:37.530 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
19:22:37.531 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
19:22:37.532 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
19:22:46.028 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:22:52.461 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 161f809c-9070-4f0c-8904-eeff58c050fe
19:22:52.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [161f809c-9070-4f0c-8904-eeff58c050fe] RpcClient init label, labels = {module=naming, source=sdk}
19:22:52.464 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [161f809c-9070-4f0c-8904-eeff58c050fe] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:22:52.464 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [161f809c-9070-4f0c-8904-eeff58c050fe] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:22:52.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [161f809c-9070-4f0c-8904-eeff58c050fe] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:22:52.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [161f809c-9070-4f0c-8904-eeff58c050fe] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:22:52.600 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [161f809c-9070-4f0c-8904-eeff58c050fe] Success to connect to server [localhost:8848] on start up, connectionId = 1755688972474_127.0.0.1_5630
19:22:52.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [161f809c-9070-4f0c-8904-eeff58c050fe] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:22:52.601 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [161f809c-9070-4f0c-8904-eeff58c050fe] Notify connected event to listeners.
19:22:52.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [161f809c-9070-4f0c-8904-eeff58c050fe] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001b0d24f8ad8
19:22:52.683 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
19:22:52.723 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
19:22:52.900 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 24.031 seconds (JVM running for 25.146)
19:22:52.929 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
19:22:52.929 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
19:22:52.930 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
19:22:52.996 [RMI TCP Connection(9)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:22:53.153 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [161f809c-9070-4f0c-8904-eeff58c050fe] Receive server push request, request = NotifySubscriberRequest, requestId = 87
19:22:53.169 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [161f809c-9070-4f0c-8904-eeff58c050fe] Ack server push request, request = NotifySubscriberRequest, requestId = 87
19:22:55.757 [nacos-grpc-client-executor-14] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [161f809c-9070-4f0c-8904-eeff58c050fe] Receive server push request, request = NotifySubscriberRequest, requestId = 90
19:22:55.758 [nacos-grpc-client-executor-14] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [161f809c-9070-4f0c-8904-eeff58c050fe] Ack server push request, request = NotifySubscriberRequest, requestId = 90
19:22:56.474 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
19:22:56.475 [http-nio-9600-exec-7] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:22:56.542 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-6} inited
19:22:56.544 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-5} inited
19:22:56.544 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
19:22:56.545 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-7} inited
19:22:56.546 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
19:22:56.555 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
19:22:56.555 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:22:56.556 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-6} closing ...
19:22:56.558 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-6} closed
19:22:56.558 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:22:56.559 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-7} closing ...
19:22:56.560 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-7} closed
19:22:56.561 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:22:56.562 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:22:56.564 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:22:56.564 [http-nio-9600-exec-6] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:22:56.840 [http-nio-9600-exec-9] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
19:22:56.854 [http-nio-9600-exec-9] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-5} closing ...
19:22:56.855 [http-nio-9600-exec-9] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-5} closed
19:22:56.855 [http-nio-9600-exec-9] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
