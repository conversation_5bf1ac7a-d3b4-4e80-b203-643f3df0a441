09:08:42.507 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:08:43.718 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 29a51a29-a719-4f4e-bc87-a37f708bbb29_config-0
09:08:43.847 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 53 ms to scan 1 urls, producing 3 keys and 6 values 
09:08:43.914 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 4 keys and 9 values 
09:08:43.929 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 3 keys and 10 values 
09:08:43.941 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:08:43.953 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 7 values 
09:08:43.964 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
09:08:43.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29a51a29-a719-4f4e-bc87-a37f708bbb29_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:08:43.969 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29a51a29-a719-4f4e-bc87-a37f708bbb29_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000023c653b71c0
09:08:43.970 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29a51a29-a719-4f4e-bc87-a37f708bbb29_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000023c653b73e0
09:08:43.970 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29a51a29-a719-4f4e-bc87-a37f708bbb29_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:08:43.971 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29a51a29-a719-4f4e-bc87-a37f708bbb29_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:08:43.981 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29a51a29-a719-4f4e-bc87-a37f708bbb29_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:08:45.806 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29a51a29-a719-4f4e-bc87-a37f708bbb29_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755479325418_127.0.0.1_2297
09:08:45.808 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29a51a29-a719-4f4e-bc87-a37f708bbb29_config-0] Notify connected event to listeners.
09:08:45.808 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29a51a29-a719-4f4e-bc87-a37f708bbb29_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:08:45.810 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [29a51a29-a719-4f4e-bc87-a37f708bbb29_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023c654f8668
09:08:46.388 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:08:50.745 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:08:50.747 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:08:50.747 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:08:50.927 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:08:51.708 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:08:51.710 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:08:51.711 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:09:04.014 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:09:12.821 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f4eda842-67cf-4c08-b127-42251dfd7a53
09:09:12.821 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4eda842-67cf-4c08-b127-42251dfd7a53] RpcClient init label, labels = {module=naming, source=sdk}
09:09:12.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4eda842-67cf-4c08-b127-42251dfd7a53] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:09:12.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4eda842-67cf-4c08-b127-42251dfd7a53] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:09:12.826 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4eda842-67cf-4c08-b127-42251dfd7a53] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:09:12.828 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4eda842-67cf-4c08-b127-42251dfd7a53] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:09:12.964 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4eda842-67cf-4c08-b127-42251dfd7a53] Success to connect to server [localhost:8848] on start up, connectionId = 1755479352844_127.0.0.1_2342
09:09:12.965 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4eda842-67cf-4c08-b127-42251dfd7a53] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:09:12.966 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4eda842-67cf-4c08-b127-42251dfd7a53] Notify connected event to listeners.
09:09:12.966 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4eda842-67cf-4c08-b127-42251dfd7a53] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023c654f8668
09:09:13.047 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:09:13.109 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:09:13.448 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 31.633 seconds (JVM running for 34.203)
09:09:13.496 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:09:13.499 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:09:13.501 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:09:13.519 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4eda842-67cf-4c08-b127-42251dfd7a53] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:09:13.543 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4eda842-67cf-4c08-b127-42251dfd7a53] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:09:13.866 [RMI TCP Connection(14)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:11:15.730 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4eda842-67cf-4c08-b127-42251dfd7a53] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:11:15.736 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4eda842-67cf-4c08-b127-42251dfd7a53] Ack server push request, request = NotifySubscriberRequest, requestId = 13
09:11:17.191 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:11:17.193 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:11:17.577 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:11:17.577 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
09:27:34.817 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:27:34.825 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:27:35.176 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:27:35.177 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@740b6f75[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:27:35.179 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755479352844_127.0.0.1_2342
09:27:35.184 [nacos-grpc-client-executor-237] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755479352844_127.0.0.1_2342]Ignore complete event,isRunning:false,isAbandon=false
09:27:35.189 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@********[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 238]
09:27:35.398 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:27:35.407 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
09:27:35.422 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
09:27:35.422 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
09:27:35.424 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
09:27:35.424 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:27:35.426 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:27:35.426 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:28:10.163 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:28:10.958 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 25862d84-42a9-46d4-b16e-d85586a55511_config-0
09:28:11.019 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 3 keys and 6 values 
09:28:11.051 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
09:28:11.060 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
09:28:11.067 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
09:28:11.076 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
09:28:11.086 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:28:11.088 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25862d84-42a9-46d4-b16e-d85586a55511_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:28:11.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25862d84-42a9-46d4-b16e-d85586a55511_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001c7da39af18
09:28:11.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25862d84-42a9-46d4-b16e-d85586a55511_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001c7da39b138
09:28:11.090 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25862d84-42a9-46d4-b16e-d85586a55511_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:28:11.090 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25862d84-42a9-46d4-b16e-d85586a55511_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:28:11.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25862d84-42a9-46d4-b16e-d85586a55511_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:28:11.906 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25862d84-42a9-46d4-b16e-d85586a55511_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755480491692_127.0.0.1_3843
09:28:11.907 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25862d84-42a9-46d4-b16e-d85586a55511_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:28:11.907 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25862d84-42a9-46d4-b16e-d85586a55511_config-0] Notify connected event to listeners.
09:28:11.908 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25862d84-42a9-46d4-b16e-d85586a55511_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001c7da512dd8
09:28:12.143 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:28:22.181 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:28:22.182 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:28:22.183 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:28:22.580 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:28:23.990 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:28:23.993 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:28:23.995 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:28:34.328 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:28:37.377 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a0c5d979-05ef-4cba-bb1a-61cad882e50e
09:28:37.378 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0c5d979-05ef-4cba-bb1a-61cad882e50e] RpcClient init label, labels = {module=naming, source=sdk}
09:28:37.382 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0c5d979-05ef-4cba-bb1a-61cad882e50e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:28:37.382 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0c5d979-05ef-4cba-bb1a-61cad882e50e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:28:37.383 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0c5d979-05ef-4cba-bb1a-61cad882e50e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:28:37.383 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0c5d979-05ef-4cba-bb1a-61cad882e50e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:28:37.552 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0c5d979-05ef-4cba-bb1a-61cad882e50e] Success to connect to server [localhost:8848] on start up, connectionId = 1755480517420_127.0.0.1_3887
09:28:37.553 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0c5d979-05ef-4cba-bb1a-61cad882e50e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:28:37.553 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0c5d979-05ef-4cba-bb1a-61cad882e50e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001c7da512dd8
09:28:37.553 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0c5d979-05ef-4cba-bb1a-61cad882e50e] Notify connected event to listeners.
09:28:37.644 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:28:37.687 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:28:37.805 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 28.389 seconds (JVM running for 31.252)
09:28:37.819 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:28:37.820 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:28:37.820 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:28:38.104 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0c5d979-05ef-4cba-bb1a-61cad882e50e] Receive server push request, request = NotifySubscriberRequest, requestId = 20
09:28:38.124 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0c5d979-05ef-4cba-bb1a-61cad882e50e] Ack server push request, request = NotifySubscriberRequest, requestId = 20
09:29:18.281 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:29:20.163 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:29:20.163 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:06:46.712 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:06:46.716 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:06:47.057 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:06:47.057 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@54f1e034[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:06:47.057 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755480517420_127.0.0.1_3887
10:06:47.061 [nacos-grpc-client-executor-430] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755480517420_127.0.0.1_3887]Ignore complete event,isRunning:false,isAbandon=false
10:06:47.061 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@55efeb88[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 430]
10:06:47.226 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:06:47.229 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:06:47.236 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:06:47.236 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:06:47.238 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:06:47.238 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:06:55.566 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:06:56.389 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ed32f8e6-373d-487a-b56d-ba780a641b80_config-0
10:06:56.465 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 34 ms to scan 1 urls, producing 3 keys and 6 values 
10:06:56.513 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
10:06:56.524 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
10:06:56.534 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
10:06:56.543 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
10:06:56.565 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 2 keys and 8 values 
10:06:56.570 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed32f8e6-373d-487a-b56d-ba780a641b80_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:06:56.571 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed32f8e6-373d-487a-b56d-ba780a641b80_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000013d8139f1c0
10:06:56.572 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed32f8e6-373d-487a-b56d-ba780a641b80_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000013d8139f3e0
10:06:56.572 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed32f8e6-373d-487a-b56d-ba780a641b80_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:06:56.573 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed32f8e6-373d-487a-b56d-ba780a641b80_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:06:56.585 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed32f8e6-373d-487a-b56d-ba780a641b80_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:06:57.662 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed32f8e6-373d-487a-b56d-ba780a641b80_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755482817429_127.0.0.1_6442
10:06:57.664 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed32f8e6-373d-487a-b56d-ba780a641b80_config-0] Notify connected event to listeners.
10:06:57.665 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed32f8e6-373d-487a-b56d-ba780a641b80_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:06:57.666 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed32f8e6-373d-487a-b56d-ba780a641b80_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000013d81518fb0
10:06:57.838 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:07:01.845 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:07:01.845 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:07:01.845 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:07:02.022 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:07:02.790 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:07:02.792 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:07:02.793 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:07:11.253 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:07:14.541 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 923a642a-2a08-4370-be97-8a6b930b969e
10:07:14.541 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [923a642a-2a08-4370-be97-8a6b930b969e] RpcClient init label, labels = {module=naming, source=sdk}
10:07:14.543 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [923a642a-2a08-4370-be97-8a6b930b969e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:07:14.544 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [923a642a-2a08-4370-be97-8a6b930b969e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:07:14.544 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [923a642a-2a08-4370-be97-8a6b930b969e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:07:14.544 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [923a642a-2a08-4370-be97-8a6b930b969e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:07:14.668 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [923a642a-2a08-4370-be97-8a6b930b969e] Success to connect to server [localhost:8848] on start up, connectionId = 1755482834554_127.0.0.1_6494
10:07:14.669 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [923a642a-2a08-4370-be97-8a6b930b969e] Notify connected event to listeners.
10:07:14.669 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [923a642a-2a08-4370-be97-8a6b930b969e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:07:14.669 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [923a642a-2a08-4370-be97-8a6b930b969e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000013d81518fb0
10:07:14.757 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:07:14.804 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:07:14.979 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.955 seconds (JVM running for 21.081)
10:07:14.998 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:07:15.000 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:07:15.001 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:07:15.282 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [923a642a-2a08-4370-be97-8a6b930b969e] Receive server push request, request = NotifySubscriberRequest, requestId = 25
10:07:15.305 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [923a642a-2a08-4370-be97-8a6b930b969e] Ack server push request, request = NotifySubscriberRequest, requestId = 25
10:07:15.348 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:07:27.882 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
10:07:27.883 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:07:27.884 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:07:27.886 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
10:07:27.891 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
10:07:27.911 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
10:07:27.912 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:07:27.913 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:07:27.917 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:07:27.917 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:13:14.094 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:13:14.112 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:13:14.426 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:13:14.426 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@71caab8c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:13:14.427 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755482834554_127.0.0.1_6494
10:13:14.429 [nacos-grpc-client-executor-86] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755482834554_127.0.0.1_6494]Ignore complete event,isRunning:false,isAbandon=false
10:13:14.436 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@200c9178[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 87]
10:13:14.613 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:13:14.614 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
10:13:14.616 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
10:13:14.616 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:13:14.618 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:13:14.618 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:13:19.088 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:13:19.898 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1df10dfb-dbd5-450a-b174-1d9080397326_config-0
10:13:19.971 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 3 keys and 6 values 
10:13:20.009 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
10:13:20.020 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
10:13:20.029 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
10:13:20.037 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
10:13:20.045 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
10:13:20.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1df10dfb-dbd5-450a-b174-1d9080397326_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:13:20.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1df10dfb-dbd5-450a-b174-1d9080397326_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001c6233b71c0
10:13:20.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1df10dfb-dbd5-450a-b174-1d9080397326_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001c6233b73e0
10:13:20.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1df10dfb-dbd5-450a-b174-1d9080397326_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:13:20.050 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1df10dfb-dbd5-450a-b174-1d9080397326_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:13:20.061 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1df10dfb-dbd5-450a-b174-1d9080397326_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:13:21.078 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1df10dfb-dbd5-450a-b174-1d9080397326_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755483200821_127.0.0.1_6810
10:13:21.078 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1df10dfb-dbd5-450a-b174-1d9080397326_config-0] Notify connected event to listeners.
10:13:21.078 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1df10dfb-dbd5-450a-b174-1d9080397326_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:13:21.082 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1df10dfb-dbd5-450a-b174-1d9080397326_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001c6234f0fb0
10:13:21.259 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:13:25.073 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:13:25.074 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:13:25.074 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:13:25.276 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:13:26.061 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:13:26.064 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:13:26.064 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:13:33.794 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:13:36.793 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 028a5bf7-8f07-4e12-99c5-b47e830d2ff7
10:13:36.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [028a5bf7-8f07-4e12-99c5-b47e830d2ff7] RpcClient init label, labels = {module=naming, source=sdk}
10:13:36.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [028a5bf7-8f07-4e12-99c5-b47e830d2ff7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:13:36.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [028a5bf7-8f07-4e12-99c5-b47e830d2ff7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:13:36.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [028a5bf7-8f07-4e12-99c5-b47e830d2ff7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:13:36.798 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [028a5bf7-8f07-4e12-99c5-b47e830d2ff7] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:13:36.931 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [028a5bf7-8f07-4e12-99c5-b47e830d2ff7] Success to connect to server [localhost:8848] on start up, connectionId = 1755483216801_127.0.0.1_6835
10:13:36.935 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [028a5bf7-8f07-4e12-99c5-b47e830d2ff7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:13:36.935 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [028a5bf7-8f07-4e12-99c5-b47e830d2ff7] Notify connected event to listeners.
10:13:36.935 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [028a5bf7-8f07-4e12-99c5-b47e830d2ff7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001c6234f0fb0
10:13:37.026 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:13:37.073 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:13:37.209 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 18.89 seconds (JVM running for 20.763)
10:13:37.230 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:13:37.240 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:13:37.241 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:13:37.514 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [028a5bf7-8f07-4e12-99c5-b47e830d2ff7] Receive server push request, request = NotifySubscriberRequest, requestId = 35
10:13:37.530 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [028a5bf7-8f07-4e12-99c5-b47e830d2ff7] Ack server push request, request = NotifySubscriberRequest, requestId = 35
10:13:57.425 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:14:05.526 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:14:05.526 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:55:24.840 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:55:24.848 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:55:25.182 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:55:25.185 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@49afff42[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:55:25.187 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755483216801_127.0.0.1_6835
10:55:25.191 [nacos-grpc-client-executor-508] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755483216801_127.0.0.1_6835]Ignore complete event,isRunning:false,isAbandon=false
10:55:25.194 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@56e30aaa[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 509]
10:55:25.422 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:55:25.426 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:55:25.442 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:55:25.442 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:55:25.444 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:55:25.444 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:55:33.535 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:55:34.324 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b8ae3364-fe82-4f98-a798-08774d543563_config-0
10:55:34.393 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 33 ms to scan 1 urls, producing 3 keys and 6 values 
10:55:34.437 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
10:55:34.449 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
10:55:34.460 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
10:55:34.471 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
10:55:34.486 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
10:55:34.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8ae3364-fe82-4f98-a798-08774d543563_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:55:34.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8ae3364-fe82-4f98-a798-08774d543563_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001e4b339eaf8
10:55:34.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8ae3364-fe82-4f98-a798-08774d543563_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001e4b339ed18
10:55:34.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8ae3364-fe82-4f98-a798-08774d543563_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:55:34.491 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8ae3364-fe82-4f98-a798-08774d543563_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:55:34.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8ae3364-fe82-4f98-a798-08774d543563_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:55:35.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8ae3364-fe82-4f98-a798-08774d543563_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755485735263_127.0.0.1_9317
10:55:35.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8ae3364-fe82-4f98-a798-08774d543563_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:55:35.490 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8ae3364-fe82-4f98-a798-08774d543563_config-0] Notify connected event to listeners.
10:55:35.491 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8ae3364-fe82-4f98-a798-08774d543563_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001e4b3518668
10:55:35.648 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:55:39.378 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:55:39.378 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:55:39.379 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:55:39.558 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:55:40.347 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:55:40.348 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:55:40.348 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:55:48.726 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:55:51.868 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fda01f15-a0a6-4dba-a0ec-df3d9d9d94df
10:55:51.869 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fda01f15-a0a6-4dba-a0ec-df3d9d9d94df] RpcClient init label, labels = {module=naming, source=sdk}
10:55:51.870 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fda01f15-a0a6-4dba-a0ec-df3d9d9d94df] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:55:51.870 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fda01f15-a0a6-4dba-a0ec-df3d9d9d94df] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:55:51.871 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fda01f15-a0a6-4dba-a0ec-df3d9d9d94df] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:55:51.871 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fda01f15-a0a6-4dba-a0ec-df3d9d9d94df] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:55:52.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fda01f15-a0a6-4dba-a0ec-df3d9d9d94df] Success to connect to server [localhost:8848] on start up, connectionId = 1755485751879_127.0.0.1_9329
10:55:52.005 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fda01f15-a0a6-4dba-a0ec-df3d9d9d94df] Notify connected event to listeners.
10:55:52.005 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fda01f15-a0a6-4dba-a0ec-df3d9d9d94df] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:55:52.006 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fda01f15-a0a6-4dba-a0ec-df3d9d9d94df] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001e4b3518668
10:55:52.088 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:55:52.131 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
10:55:52.273 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 19.365 seconds (JVM running for 20.484)
10:55:52.294 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:55:52.294 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:55:52.295 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:55:52.656 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fda01f15-a0a6-4dba-a0ec-df3d9d9d94df] Receive server push request, request = NotifySubscriberRequest, requestId = 39
10:55:52.677 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fda01f15-a0a6-4dba-a0ec-df3d9d9d94df] Ack server push request, request = NotifySubscriberRequest, requestId = 39
10:55:52.725 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:56:27.921 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:56:27.921 [http-nio-9600-exec-6] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:00:52.675 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:00:52.680 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:00:53.017 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:00:53.018 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@66c6b0e4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:00:53.018 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755485751879_127.0.0.1_9329
18:00:53.020 [nacos-grpc-client-executor-5379] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755485751879_127.0.0.1_9329]Ignore complete event,isRunning:false,isAbandon=false
18:00:53.027 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@56f7e97f[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 5380]
18:00:53.197 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:00:53.203 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
18:00:53.206 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
18:00:53.206 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:00:53.207 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:00:53.208 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
