09:11:55.852 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:11:56.973 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e763579b-c95c-4bd5-97ae-7f1b434c9fab_config-0
09:11:57.065 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 29 ms to scan 1 urls, producing 3 keys and 6 values 
09:11:57.132 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 4 keys and 9 values 
09:11:57.132 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 0 ms to scan 1 urls, producing 3 keys and 10 values 
09:11:57.164 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 16 ms to scan 1 urls, producing 1 keys and 5 values 
09:11:57.180 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 1 keys and 7 values 
09:11:57.196 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
09:11:57.196 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e763579b-c95c-4bd5-97ae-7f1b434c9fab_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:11:57.196 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e763579b-c95c-4bd5-97ae-7f1b434c9fab_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002284139fb00
09:11:57.196 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e763579b-c95c-4bd5-97ae-7f1b434c9fab_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002284139fd20
09:11:57.196 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e763579b-c95c-4bd5-97ae-7f1b434c9fab_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:11:57.196 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e763579b-c95c-4bd5-97ae-7f1b434c9fab_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:11:57.211 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e763579b-c95c-4bd5-97ae-7f1b434c9fab_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:11:58.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e763579b-c95c-4bd5-97ae-7f1b434c9fab_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755220318250_127.0.0.1_2141
09:11:58.487 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e763579b-c95c-4bd5-97ae-7f1b434c9fab_config-0] Notify connected event to listeners.
09:11:58.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e763579b-c95c-4bd5-97ae-7f1b434c9fab_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:11:58.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e763579b-c95c-4bd5-97ae-7f1b434c9fab_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000022841519f88
09:11:58.707 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:12:04.682 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:12:04.682 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:12:04.682 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:12:04.961 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:12:06.586 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:12:06.588 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:12:06.588 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:12:18.245 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:12:21.637 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7821b881-c411-4a82-98f5-82e759fdbaeb
09:12:21.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7821b881-c411-4a82-98f5-82e759fdbaeb] RpcClient init label, labels = {module=naming, source=sdk}
09:12:21.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7821b881-c411-4a82-98f5-82e759fdbaeb] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:12:21.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7821b881-c411-4a82-98f5-82e759fdbaeb] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:12:21.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7821b881-c411-4a82-98f5-82e759fdbaeb] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:12:21.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7821b881-c411-4a82-98f5-82e759fdbaeb] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:12:21.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7821b881-c411-4a82-98f5-82e759fdbaeb] Success to connect to server [localhost:8848] on start up, connectionId = 1755220341651_127.0.0.1_2175
09:12:21.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7821b881-c411-4a82-98f5-82e759fdbaeb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:12:21.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7821b881-c411-4a82-98f5-82e759fdbaeb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000022841519f88
09:12:21.768 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7821b881-c411-4a82-98f5-82e759fdbaeb] Notify connected event to listeners.
09:12:21.864 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:12:21.904 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
09:12:22.035 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 27.393 seconds (JVM running for 29.313)
09:12:22.050 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:12:22.051 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:12:22.053 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:12:22.278 [RMI TCP Connection(3)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:12:22.384 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7821b881-c411-4a82-98f5-82e759fdbaeb] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:12:22.407 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7821b881-c411-4a82-98f5-82e759fdbaeb] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:29:01.397 [nacos-grpc-client-executor-214] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7821b881-c411-4a82-98f5-82e759fdbaeb] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:29:01.399 [nacos-grpc-client-executor-214] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7821b881-c411-4a82-98f5-82e759fdbaeb] Ack server push request, request = NotifySubscriberRequest, requestId = 13
09:29:01.515 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:29:01.515 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:29:01.670 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:29:01.670 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
10:18:39.901 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:18:39.914 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:18:40.259 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:18:40.265 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3ae3fd0e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:18:40.265 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755220341651_127.0.0.1_2175
10:18:40.273 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7deaaf7[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 817]
10:18:40.285 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7821b881-c411-4a82-98f5-82e759fdbaeb] Notify disconnected event to listeners
10:18:40.840 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:18:40.847 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
10:18:40.876 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
10:18:40.876 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:18:40.876 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:18:40.876 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:18:40.876 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:18:40.876 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:19:17.879 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:19:18.710 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of dae13cea-aa21-452d-87d4-5b8d801e4718_config-0
10:19:18.801 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 40 ms to scan 1 urls, producing 3 keys and 6 values 
10:19:18.839 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
10:19:18.848 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
10:19:18.864 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
10:19:18.879 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
10:19:18.879 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
10:19:18.893 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dae13cea-aa21-452d-87d4-5b8d801e4718_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:19:18.893 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dae13cea-aa21-452d-87d4-5b8d801e4718_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000269653b9e68
10:19:18.893 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dae13cea-aa21-452d-87d4-5b8d801e4718_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000269653ba088
10:19:18.895 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dae13cea-aa21-452d-87d4-5b8d801e4718_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:19:18.896 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dae13cea-aa21-452d-87d4-5b8d801e4718_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:19:18.906 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dae13cea-aa21-452d-87d4-5b8d801e4718_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:19:19.964 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dae13cea-aa21-452d-87d4-5b8d801e4718_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755224359703_127.0.0.1_10686
10:19:19.964 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dae13cea-aa21-452d-87d4-5b8d801e4718_config-0] Notify connected event to listeners.
10:19:19.964 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dae13cea-aa21-452d-87d4-5b8d801e4718_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:19:19.969 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dae13cea-aa21-452d-87d4-5b8d801e4718_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000269654f20a0
10:19:20.232 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:19:29.309 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:19:29.312 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:19:29.313 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:19:29.998 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:19:31.767 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:19:31.770 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:19:31.770 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:19:51.562 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:20:01.751 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e3ba67af-3818-420e-ad29-25e8395053bf
10:20:01.752 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3ba67af-3818-420e-ad29-25e8395053bf] RpcClient init label, labels = {module=naming, source=sdk}
10:20:01.757 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3ba67af-3818-420e-ad29-25e8395053bf] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:20:01.758 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3ba67af-3818-420e-ad29-25e8395053bf] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:20:01.758 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3ba67af-3818-420e-ad29-25e8395053bf] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:20:01.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3ba67af-3818-420e-ad29-25e8395053bf] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:20:01.898 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3ba67af-3818-420e-ad29-25e8395053bf] Success to connect to server [localhost:8848] on start up, connectionId = 1755224401773_127.0.0.1_10815
10:20:01.899 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3ba67af-3818-420e-ad29-25e8395053bf] Notify connected event to listeners.
10:20:01.899 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3ba67af-3818-420e-ad29-25e8395053bf] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:20:01.900 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3ba67af-3818-420e-ad29-25e8395053bf] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000269654f20a0
10:20:02.001 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:20:02.074 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
10:20:02.466 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 45.397 seconds (JVM running for 48.108)
10:20:02.502 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:20:02.504 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:20:02.505 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:20:02.536 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3ba67af-3818-420e-ad29-25e8395053bf] Receive server push request, request = NotifySubscriberRequest, requestId = 20
10:20:02.557 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e3ba67af-3818-420e-ad29-25e8395053bf] Ack server push request, request = NotifySubscriberRequest, requestId = 20
10:20:31.126 [http-nio-9600-exec-3] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:20:32.935 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
10:20:32.935 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
10:20:32.936 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:20:32.936 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:20:32.940 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
10:20:32.950 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
10:20:32.950 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:20:32.950 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:20:32.952 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:20:32.952 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:38:39.241 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:38:39.259 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:38:39.598 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:38:39.598 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@143fe15e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:38:39.598 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755224401773_127.0.0.1_10815
13:38:39.598 [nacos-grpc-client-executor-2394] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755224401773_127.0.0.1_10815]Ignore complete event,isRunning:false,isAbandon=false
13:38:39.604 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@402adcfd[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2395]
13:38:39.760 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:38:39.760 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
13:38:39.760 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
13:38:39.760 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:38:39.760 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:38:39.760 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:38:45.678 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:38:46.228 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ab78f29e-1bb3-499c-9ca0-96ccc09e8201_config-0
13:38:46.282 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
13:38:46.309 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
13:38:46.316 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
13:38:46.322 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
13:38:46.328 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
13:38:46.333 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
13:38:46.335 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab78f29e-1bb3-499c-9ca0-96ccc09e8201_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:38:46.336 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab78f29e-1bb3-499c-9ca0-96ccc09e8201_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001b71a3c0b08
13:38:46.336 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab78f29e-1bb3-499c-9ca0-96ccc09e8201_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001b71a3c0d28
13:38:46.337 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab78f29e-1bb3-499c-9ca0-96ccc09e8201_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:38:46.337 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab78f29e-1bb3-499c-9ca0-96ccc09e8201_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:38:46.343 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab78f29e-1bb3-499c-9ca0-96ccc09e8201_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:38:47.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab78f29e-1bb3-499c-9ca0-96ccc09e8201_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755236326888_127.0.0.1_8151
13:38:47.076 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab78f29e-1bb3-499c-9ca0-96ccc09e8201_config-0] Notify connected event to listeners.
13:38:47.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab78f29e-1bb3-499c-9ca0-96ccc09e8201_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:38:47.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab78f29e-1bb3-499c-9ca0-96ccc09e8201_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001b71a4fa958
13:38:47.142 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:38:49.576 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:38:49.576 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:38:49.576 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:38:49.701 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:38:50.272 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:38:50.273 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:38:50.273 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:38:59.394 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:39:03.323 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4e151315-54ed-4221-ac39-6d70753591bd
13:39:03.323 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e151315-54ed-4221-ac39-6d70753591bd] RpcClient init label, labels = {module=naming, source=sdk}
13:39:03.325 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e151315-54ed-4221-ac39-6d70753591bd] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:39:03.325 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e151315-54ed-4221-ac39-6d70753591bd] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:39:03.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e151315-54ed-4221-ac39-6d70753591bd] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:39:03.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e151315-54ed-4221-ac39-6d70753591bd] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:39:03.466 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e151315-54ed-4221-ac39-6d70753591bd] Success to connect to server [localhost:8848] on start up, connectionId = 1755236343337_127.0.0.1_8280
13:39:03.466 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e151315-54ed-4221-ac39-6d70753591bd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:39:03.466 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e151315-54ed-4221-ac39-6d70753591bd] Notify connected event to listeners.
13:39:03.466 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e151315-54ed-4221-ac39-6d70753591bd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001b71a4fa958
13:39:03.529 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:39:03.561 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
13:39:03.738 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 18.534 seconds (JVM running for 19.438)
13:39:03.766 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:39:03.766 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:39:03.766 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:39:03.998 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e151315-54ed-4221-ac39-6d70753591bd] Receive server push request, request = NotifySubscriberRequest, requestId = 25
13:39:04.011 [RMI TCP Connection(4)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:39:04.013 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4e151315-54ed-4221-ac39-6d70753591bd] Ack server push request, request = NotifySubscriberRequest, requestId = 25
13:39:14.128 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:39:14.128 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:39:14.128 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
13:39:14.132 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:39:14.134 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
13:39:14.139 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:39:14.139 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:39:14.139 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
13:39:14.140 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
13:39:14.140 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:33:24.513 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:33:24.529 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:33:24.864 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:33:24.864 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4fc44586[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:33:24.864 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755236343337_127.0.0.1_8280
15:33:24.864 [nacos-grpc-client-executor-1189] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755236343337_127.0.0.1_8280]Ignore complete event,isRunning:false,isAbandon=false
15:33:24.869 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1aefc2bc[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1190]
15:33:25.015 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:33:25.015 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
15:33:25.015 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
15:33:25.015 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:33:25.015 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:33:25.033 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:33:52.505 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:33:53.126 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9dfc003d-587a-4f2d-84f1-f1f8a7951280_config-0
15:33:53.193 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 34 ms to scan 1 urls, producing 3 keys and 6 values 
15:33:53.218 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
15:33:53.218 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
15:33:53.240 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
15:33:53.246 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 7 values 
15:33:53.250 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 2 ms to scan 1 urls, producing 2 keys and 8 values 
15:33:53.250 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dfc003d-587a-4f2d-84f1-f1f8a7951280_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:33:53.250 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dfc003d-587a-4f2d-84f1-f1f8a7951280_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000266113b8b08
15:33:53.250 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dfc003d-587a-4f2d-84f1-f1f8a7951280_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000266113b8d28
15:33:53.250 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dfc003d-587a-4f2d-84f1-f1f8a7951280_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:33:53.250 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dfc003d-587a-4f2d-84f1-f1f8a7951280_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:33:53.263 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dfc003d-587a-4f2d-84f1-f1f8a7951280_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:33:53.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dfc003d-587a-4f2d-84f1-f1f8a7951280_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755243233762_127.0.0.1_13566
15:33:53.963 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dfc003d-587a-4f2d-84f1-f1f8a7951280_config-0] Notify connected event to listeners.
15:33:53.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dfc003d-587a-4f2d-84f1-f1f8a7951280_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:33:53.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dfc003d-587a-4f2d-84f1-f1f8a7951280_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000266114f0d48
15:33:54.093 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:33:56.687 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:33:56.687 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:33:56.688 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:33:56.799 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:33:57.379 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:33:57.379 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:33:57.379 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:34:02.562 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:34:04.686 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fc4080d3-349b-48c5-88de-01f07b32aa00
15:34:04.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4080d3-349b-48c5-88de-01f07b32aa00] RpcClient init label, labels = {module=naming, source=sdk}
15:34:04.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4080d3-349b-48c5-88de-01f07b32aa00] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:34:04.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4080d3-349b-48c5-88de-01f07b32aa00] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:34:04.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4080d3-349b-48c5-88de-01f07b32aa00] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:34:04.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4080d3-349b-48c5-88de-01f07b32aa00] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:34:04.809 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4080d3-349b-48c5-88de-01f07b32aa00] Success to connect to server [localhost:8848] on start up, connectionId = 1755243244696_127.0.0.1_13592
15:34:04.809 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4080d3-349b-48c5-88de-01f07b32aa00] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:34:04.809 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4080d3-349b-48c5-88de-01f07b32aa00] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000266114f0d48
15:34:04.809 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4080d3-349b-48c5-88de-01f07b32aa00] Notify connected event to listeners.
15:34:04.846 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:34:04.875 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
15:34:04.979 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.932 seconds (JVM running for 14.814)
15:34:04.990 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:34:04.990 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:34:04.990 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:34:05.346 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4080d3-349b-48c5-88de-01f07b32aa00] Receive server push request, request = NotifySubscriberRequest, requestId = 32
15:34:05.354 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4080d3-349b-48c5-88de-01f07b32aa00] Ack server push request, request = NotifySubscriberRequest, requestId = 32
15:34:36.848 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:34:37.896 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
15:34:37.896 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
15:34:37.896 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:34:37.901 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
15:34:37.901 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:34:37.909 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
15:34:37.909 [http-nio-9600-exec-6] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:34:37.910 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
15:34:37.910 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
15:34:37.910 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:41:45.686 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dfc003d-587a-4f2d-84f1-f1f8a7951280_config-0] Server healthy check fail, currentConnection = 1755243233762_127.0.0.1_13566
15:41:45.686 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4080d3-349b-48c5-88de-01f07b32aa00] Server healthy check fail, currentConnection = 1755243244696_127.0.0.1_13592
15:41:46.206 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dfc003d-587a-4f2d-84f1-f1f8a7951280_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:41:46.207 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4080d3-349b-48c5-88de-01f07b32aa00] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:41:47.044 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4080d3-349b-48c5-88de-01f07b32aa00] Success to connect a server [localhost:8848], connectionId = 1755243706932_127.0.0.1_14714
15:41:47.044 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4080d3-349b-48c5-88de-01f07b32aa00] Abandon prev connection, server is localhost:8848, connectionId is 1755243244696_127.0.0.1_13592
15:41:47.044 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755243244696_127.0.0.1_13592
15:41:47.044 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dfc003d-587a-4f2d-84f1-f1f8a7951280_config-0] Success to connect a server [localhost:8848], connectionId = 1755243706932_127.0.0.1_14715
15:41:47.044 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dfc003d-587a-4f2d-84f1-f1f8a7951280_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1755243233762_127.0.0.1_13566
15:41:47.044 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755243233762_127.0.0.1_13566
15:41:47.047 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4080d3-349b-48c5-88de-01f07b32aa00] Notify disconnected event to listeners
15:41:47.046 [nacos-grpc-client-executor-61] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755243244696_127.0.0.1_13592]Ignore complete event,isRunning:false,isAbandon=true
15:41:47.047 [nacos-grpc-client-executor-69] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755243233762_127.0.0.1_13566]Ignore complete event,isRunning:false,isAbandon=true
15:41:47.048 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4080d3-349b-48c5-88de-01f07b32aa00] Notify connected event to listeners.
15:41:47.048 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dfc003d-587a-4f2d-84f1-f1f8a7951280_config-0] Notify disconnected event to listeners
15:41:47.049 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dfc003d-587a-4f2d-84f1-f1f8a7951280_config-0] Notify connected event to listeners.
15:41:50.933 [nacos-grpc-client-executor-64] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4080d3-349b-48c5-88de-01f07b32aa00] Receive server push request, request = NotifySubscriberRequest, requestId = 42
15:41:50.933 [nacos-grpc-client-executor-64] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc4080d3-349b-48c5-88de-01f07b32aa00] Ack server push request, request = NotifySubscriberRequest, requestId = 42
15:53:42.630 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:53:42.633 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:53:42.963 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:53:42.965 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4dcb73cf[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:53:42.965 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755243706932_127.0.0.1_14714
15:53:42.965 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@624edbac[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 195]
15:53:43.098 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:53:43.098 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:53:43.098 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:53:43.098 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:53:43.098 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:53:43.098 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:53:49.095 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:53:49.668 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f6723a10-3268-4b73-bdf1-b6f06e3ffaab_config-0
15:53:49.722 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 6 values 
15:53:49.752 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
15:53:49.758 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
15:53:49.764 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
15:53:49.769 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 7 values 
15:53:49.775 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
15:53:49.776 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f6723a10-3268-4b73-bdf1-b6f06e3ffaab_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:53:49.776 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f6723a10-3268-4b73-bdf1-b6f06e3ffaab_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000232dd39e8d8
15:53:49.776 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f6723a10-3268-4b73-bdf1-b6f06e3ffaab_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000232dd39eaf8
15:53:49.778 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f6723a10-3268-4b73-bdf1-b6f06e3ffaab_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:53:49.778 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f6723a10-3268-4b73-bdf1-b6f06e3ffaab_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:53:49.785 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f6723a10-3268-4b73-bdf1-b6f06e3ffaab_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:53:50.484 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f6723a10-3268-4b73-bdf1-b6f06e3ffaab_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755244430297_127.0.0.1_3043
15:53:50.485 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f6723a10-3268-4b73-bdf1-b6f06e3ffaab_config-0] Notify connected event to listeners.
15:53:50.485 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f6723a10-3268-4b73-bdf1-b6f06e3ffaab_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:53:50.485 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f6723a10-3268-4b73-bdf1-b6f06e3ffaab_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000232dd518668
15:53:50.567 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:53:52.982 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:53:52.983 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:53:52.983 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:53:53.097 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:53:53.646 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:53:53.647 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:53:53.647 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:53:58.695 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:54:00.738 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of dccc3de9-2ea0-4022-89f7-31faa5545dd4
15:54:00.739 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dccc3de9-2ea0-4022-89f7-31faa5545dd4] RpcClient init label, labels = {module=naming, source=sdk}
15:54:00.741 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dccc3de9-2ea0-4022-89f7-31faa5545dd4] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:54:00.741 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dccc3de9-2ea0-4022-89f7-31faa5545dd4] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:54:00.741 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dccc3de9-2ea0-4022-89f7-31faa5545dd4] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:54:00.741 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dccc3de9-2ea0-4022-89f7-31faa5545dd4] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:54:00.858 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dccc3de9-2ea0-4022-89f7-31faa5545dd4] Success to connect to server [localhost:8848] on start up, connectionId = 1755244440748_127.0.0.1_3100
15:54:00.858 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dccc3de9-2ea0-4022-89f7-31faa5545dd4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:54:00.858 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dccc3de9-2ea0-4022-89f7-31faa5545dd4] Notify connected event to listeners.
15:54:00.858 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dccc3de9-2ea0-4022-89f7-31faa5545dd4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000232dd518668
15:54:00.896 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:54:00.915 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
15:54:01.004 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.457 seconds (JVM running for 13.461)
15:54:01.015 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:54:01.016 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:54:01.016 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:54:01.136 [RMI TCP Connection(2)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:54:01.407 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dccc3de9-2ea0-4022-89f7-31faa5545dd4] Receive server push request, request = NotifySubscriberRequest, requestId = 47
15:54:01.424 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dccc3de9-2ea0-4022-89f7-31faa5545dd4] Ack server push request, request = NotifySubscriberRequest, requestId = 47
15:54:47.436 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:54:47.436 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:06:39.707 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:06:39.710 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:06:40.039 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:06:40.039 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@772ad48[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:06:40.039 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755244440748_127.0.0.1_3100
16:06:40.039 [nacos-grpc-client-executor-161] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755244440748_127.0.0.1_3100]Ignore complete event,isRunning:false,isAbandon=false
16:06:40.039 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@17c4d644[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 162]
16:06:40.209 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:06:40.214 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:06:40.229 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:06:40.229 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:06:40.232 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:06:40.234 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:06:54.171 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:06:55.816 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 49ed44c4-ff16-4ff5-993b-95b04731823b_config-0
16:06:55.964 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 72 ms to scan 1 urls, producing 3 keys and 6 values 
16:06:56.074 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 4 keys and 9 values 
16:06:56.094 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 3 keys and 10 values 
16:06:56.117 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 1 keys and 5 values 
16:06:56.141 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 1 keys and 7 values 
16:06:56.166 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 2 keys and 8 values 
16:06:56.170 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ed44c4-ff16-4ff5-993b-95b04731823b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:06:56.171 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ed44c4-ff16-4ff5-993b-95b04731823b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000018c113b6af8
16:06:56.172 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ed44c4-ff16-4ff5-993b-95b04731823b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000018c113b6d18
16:06:56.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ed44c4-ff16-4ff5-993b-95b04731823b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:06:56.174 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ed44c4-ff16-4ff5-993b-95b04731823b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:06:56.192 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ed44c4-ff16-4ff5-993b-95b04731823b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:06:58.405 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ed44c4-ff16-4ff5-993b-95b04731823b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755245218067_127.0.0.1_5459
16:06:58.407 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ed44c4-ff16-4ff5-993b-95b04731823b_config-0] Notify connected event to listeners.
16:06:58.407 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ed44c4-ff16-4ff5-993b-95b04731823b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:06:58.408 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [49ed44c4-ff16-4ff5-993b-95b04731823b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000018c114f0668
16:06:58.570 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:07:01.792 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:07:01.792 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:07:01.792 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:07:01.909 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:07:02.599 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:07:02.599 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:07:02.599 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:07:08.109 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:07:10.593 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2691b292-7765-4d30-8de7-543ab3871484
16:07:10.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2691b292-7765-4d30-8de7-543ab3871484] RpcClient init label, labels = {module=naming, source=sdk}
16:07:10.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2691b292-7765-4d30-8de7-543ab3871484] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:07:10.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2691b292-7765-4d30-8de7-543ab3871484] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:07:10.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2691b292-7765-4d30-8de7-543ab3871484] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:07:10.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2691b292-7765-4d30-8de7-543ab3871484] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:07:10.726 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2691b292-7765-4d30-8de7-543ab3871484] Success to connect to server [localhost:8848] on start up, connectionId = 1755245230609_127.0.0.1_5505
16:07:10.726 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2691b292-7765-4d30-8de7-543ab3871484] Notify connected event to listeners.
16:07:10.726 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2691b292-7765-4d30-8de7-543ab3871484] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:07:10.726 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2691b292-7765-4d30-8de7-543ab3871484] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000018c114f0668
16:07:10.767 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:07:10.793 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
16:07:10.893 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 18.29 seconds (JVM running for 21.213)
16:07:10.909 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:07:10.919 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:07:10.919 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:07:11.299 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2691b292-7765-4d30-8de7-543ab3871484] Receive server push request, request = NotifySubscriberRequest, requestId = 53
16:07:11.310 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2691b292-7765-4d30-8de7-543ab3871484] Ack server push request, request = NotifySubscriberRequest, requestId = 53
16:07:11.646 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:07:12.861 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:07:12.861 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:43:44.820 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:43:44.826 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:43:45.164 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:43:45.164 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@70129aaa[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:43:45.164 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755245230609_127.0.0.1_5505
16:43:45.164 [nacos-grpc-client-executor-448] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755245230609_127.0.0.1_5505]Ignore complete event,isRunning:false,isAbandon=false
16:43:45.170 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@22f529f7[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 449]
16:43:45.342 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:43:45.358 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:43:45.369 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:43:45.369 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:43:45.369 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:43:45.369 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:43:56.438 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:43:58.934 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 177553a6-8a50-406d-8566-524bd73c75d9_config-0
16:43:59.148 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 95 ms to scan 1 urls, producing 3 keys and 6 values 
16:43:59.258 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 4 keys and 9 values 
16:43:59.274 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
16:43:59.307 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 1 keys and 5 values 
16:43:59.338 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 1 keys and 7 values 
16:43:59.369 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 2 keys and 8 values 
16:43:59.377 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [177553a6-8a50-406d-8566-524bd73c75d9_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:43:59.378 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [177553a6-8a50-406d-8566-524bd73c75d9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000019b613b71c0
16:43:59.379 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [177553a6-8a50-406d-8566-524bd73c75d9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000019b613b73e0
16:43:59.380 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [177553a6-8a50-406d-8566-524bd73c75d9_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:43:59.382 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [177553a6-8a50-406d-8566-524bd73c75d9_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:43:59.410 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [177553a6-8a50-406d-8566-524bd73c75d9_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:44:00.871 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [177553a6-8a50-406d-8566-524bd73c75d9_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755247440590_127.0.0.1_8945
16:44:00.871 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [177553a6-8a50-406d-8566-524bd73c75d9_config-0] Notify connected event to listeners.
16:44:00.871 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [177553a6-8a50-406d-8566-524bd73c75d9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:44:00.873 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [177553a6-8a50-406d-8566-524bd73c75d9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000019b614f0fb0
16:44:01.009 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:44:04.287 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:44:04.287 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:44:04.287 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:44:04.433 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:44:05.013 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:44:05.013 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:44:05.013 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:44:11.302 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:44:14.656 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c1700c5a-6fdd-479b-926d-2bc7a3b63f76
16:44:14.656 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1700c5a-6fdd-479b-926d-2bc7a3b63f76] RpcClient init label, labels = {module=naming, source=sdk}
16:44:14.668 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1700c5a-6fdd-479b-926d-2bc7a3b63f76] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:44:14.668 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1700c5a-6fdd-479b-926d-2bc7a3b63f76] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:44:14.668 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1700c5a-6fdd-479b-926d-2bc7a3b63f76] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:44:14.668 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1700c5a-6fdd-479b-926d-2bc7a3b63f76] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:44:14.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1700c5a-6fdd-479b-926d-2bc7a3b63f76] Success to connect to server [localhost:8848] on start up, connectionId = 1755247454670_127.0.0.1_8960
16:44:14.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1700c5a-6fdd-479b-926d-2bc7a3b63f76] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:44:14.789 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1700c5a-6fdd-479b-926d-2bc7a3b63f76] Notify connected event to listeners.
16:44:14.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1700c5a-6fdd-479b-926d-2bc7a3b63f76] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000019b614f0fb0
16:44:14.837 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:44:14.870 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
16:44:15.003 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.15 seconds (JVM running for 23.155)
16:44:15.019 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:44:15.019 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:44:15.019 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:44:15.418 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1700c5a-6fdd-479b-926d-2bc7a3b63f76] Receive server push request, request = NotifySubscriberRequest, requestId = 62
16:44:15.426 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c1700c5a-6fdd-479b-926d-2bc7a3b63f76] Ack server push request, request = NotifySubscriberRequest, requestId = 62
16:45:30.475 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:45:31.606 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
16:45:31.606 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:45:31.615 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
16:45:31.616 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
16:45:31.625 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:45:31.625 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
16:45:31.626 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:45:31.626 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
16:45:31.627 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
16:45:31.627 [http-nio-9600-exec-7] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:49:51.322 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:49:51.327 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:49:51.640 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:49:51.640 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5ad98875[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:49:51.640 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755247454670_127.0.0.1_8960
16:49:51.640 [nacos-grpc-client-executor-78] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755247454670_127.0.0.1_8960]Ignore complete event,isRunning:false,isAbandon=false
16:49:51.647 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@24fa4242[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 79]
16:49:51.789 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:49:51.789 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:49:51.789 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:49:51.789 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:49:51.797 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:49:51.797 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:50:05.698 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:50:07.668 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5c99f288-bf3e-4b23-a732-bea277e99570_config-0
16:50:07.850 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 87 ms to scan 1 urls, producing 3 keys and 6 values 
16:50:07.946 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 4 keys and 9 values 
16:50:07.971 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 3 keys and 10 values 
16:50:07.998 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 1 keys and 5 values 
16:50:08.028 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 1 keys and 7 values 
16:50:08.045 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
16:50:08.059 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c99f288-bf3e-4b23-a732-bea277e99570_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:50:08.061 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c99f288-bf3e-4b23-a732-bea277e99570_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000280013b71c0
16:50:08.063 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c99f288-bf3e-4b23-a732-bea277e99570_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000280013b73e0
16:50:08.063 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c99f288-bf3e-4b23-a732-bea277e99570_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:50:08.063 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c99f288-bf3e-4b23-a732-bea277e99570_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:50:08.092 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c99f288-bf3e-4b23-a732-bea277e99570_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:50:09.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c99f288-bf3e-4b23-a732-bea277e99570_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755247809573_127.0.0.1_9438
16:50:09.906 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c99f288-bf3e-4b23-a732-bea277e99570_config-0] Notify connected event to listeners.
16:50:09.907 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c99f288-bf3e-4b23-a732-bea277e99570_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:50:09.907 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c99f288-bf3e-4b23-a732-bea277e99570_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000280014f0fb0
16:50:10.156 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:50:15.312 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:50:15.312 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:50:15.312 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:50:15.477 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:50:16.203 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:50:16.203 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:50:16.203 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:50:22.186 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:50:24.576 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 059c5454-9ada-4907-8f1b-a9a4d7913a41
16:50:24.576 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [059c5454-9ada-4907-8f1b-a9a4d7913a41] RpcClient init label, labels = {module=naming, source=sdk}
16:50:24.576 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [059c5454-9ada-4907-8f1b-a9a4d7913a41] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:50:24.576 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [059c5454-9ada-4907-8f1b-a9a4d7913a41] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:50:24.576 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [059c5454-9ada-4907-8f1b-a9a4d7913a41] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:50:24.576 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [059c5454-9ada-4907-8f1b-a9a4d7913a41] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:50:24.703 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [059c5454-9ada-4907-8f1b-a9a4d7913a41] Success to connect to server [localhost:8848] on start up, connectionId = 1755247824591_127.0.0.1_9451
16:50:24.703 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [059c5454-9ada-4907-8f1b-a9a4d7913a41] Notify connected event to listeners.
16:50:24.703 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [059c5454-9ada-4907-8f1b-a9a4d7913a41] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:50:24.703 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [059c5454-9ada-4907-8f1b-a9a4d7913a41] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000280014f0fb0
16:50:24.729 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:50:24.766 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
16:50:24.863 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.71 seconds (JVM running for 23.386)
16:50:24.869 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:50:24.869 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:50:24.869 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:50:25.306 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [059c5454-9ada-4907-8f1b-a9a4d7913a41] Receive server push request, request = NotifySubscriberRequest, requestId = 70
16:50:25.323 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [059c5454-9ada-4907-8f1b-a9a4d7913a41] Ack server push request, request = NotifySubscriberRequest, requestId = 70
16:51:49.133 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:51:50.205 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
16:51:50.205 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:51:50.217 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:51:50.217 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
16:51:50.220 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
16:51:50.225 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
16:51:50.226 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:51:50.226 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:51:50.226 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:51:50.227 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:53:17.155 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:53:17.171 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:53:17.509 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:53:17.509 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@392cd8b7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:53:17.509 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755247824591_127.0.0.1_9451
16:53:17.515 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755247824591_127.0.0.1_9451]Ignore complete event,isRunning:false,isAbandon=false
16:53:17.518 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@403a11ee[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 47]
16:53:17.686 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:53:17.686 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
16:53:17.686 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
16:53:17.686 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:53:17.691 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:53:17.691 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:53:27.390 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:53:28.281 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 801de9ae-a48d-4950-9c81-9982a1a6a622_config-0
16:53:28.358 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 3 keys and 6 values 
16:53:28.398 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
16:53:28.409 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
16:53:28.418 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
16:53:28.428 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
16:53:28.439 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
16:53:28.442 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [801de9ae-a48d-4950-9c81-9982a1a6a622_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:53:28.442 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [801de9ae-a48d-4950-9c81-9982a1a6a622_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001fa9139dd70
16:53:28.444 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [801de9ae-a48d-4950-9c81-9982a1a6a622_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001fa9139df90
16:53:28.444 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [801de9ae-a48d-4950-9c81-9982a1a6a622_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:53:28.445 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [801de9ae-a48d-4950-9c81-9982a1a6a622_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:53:28.452 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [801de9ae-a48d-4950-9c81-9982a1a6a622_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:53:29.326 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [801de9ae-a48d-4950-9c81-9982a1a6a622_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755248009125_127.0.0.1_9714
16:53:29.326 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [801de9ae-a48d-4950-9c81-9982a1a6a622_config-0] Notify connected event to listeners.
16:53:29.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [801de9ae-a48d-4950-9c81-9982a1a6a622_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:53:29.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [801de9ae-a48d-4950-9c81-9982a1a6a622_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001fa91518228
16:53:29.421 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:53:32.201 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:53:32.201 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:53:32.202 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:53:32.335 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:53:32.905 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:53:32.907 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:53:32.907 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:53:38.566 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:53:41.000 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 27bce261-06f4-4f1b-bd20-6946d6349f76
16:53:41.000 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27bce261-06f4-4f1b-bd20-6946d6349f76] RpcClient init label, labels = {module=naming, source=sdk}
16:53:41.002 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27bce261-06f4-4f1b-bd20-6946d6349f76] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:53:41.002 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27bce261-06f4-4f1b-bd20-6946d6349f76] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:53:41.002 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27bce261-06f4-4f1b-bd20-6946d6349f76] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:53:41.002 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27bce261-06f4-4f1b-bd20-6946d6349f76] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:53:41.120 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27bce261-06f4-4f1b-bd20-6946d6349f76] Success to connect to server [localhost:8848] on start up, connectionId = 1755248021010_127.0.0.1_9728
16:53:41.120 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27bce261-06f4-4f1b-bd20-6946d6349f76] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:53:41.120 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27bce261-06f4-4f1b-bd20-6946d6349f76] Notify connected event to listeners.
16:53:41.121 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27bce261-06f4-4f1b-bd20-6946d6349f76] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001fa91518228
16:53:41.160 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:53:41.184 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
16:53:41.285 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 14.744 seconds (JVM running for 16.132)
16:53:41.296 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:53:41.297 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:53:41.297 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:53:41.471 [RMI TCP Connection(4)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:53:41.658 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27bce261-06f4-4f1b-bd20-6946d6349f76] Receive server push request, request = NotifySubscriberRequest, requestId = 75
16:53:41.674 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27bce261-06f4-4f1b-bd20-6946d6349f76] Ack server push request, request = NotifySubscriberRequest, requestId = 75
16:54:24.055 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
16:54:24.055 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:54:24.063 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:54:24.063 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
16:54:24.065 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
16:54:24.070 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
16:54:24.070 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:54:24.071 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:54:24.071 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:54:24.071 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:03:15.477 [nacos-grpc-client-executor-131] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [801de9ae-a48d-4950-9c81-9982a1a6a622_config-0] Receive server push request, request = ConfigChangeNotifyRequest, requestId = 78
17:03:15.477 [nacos-grpc-client-executor-131] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [801de9ae-a48d-4950-9c81-9982a1a6a622_config-0] Ack server push request, request = ConfigChangeNotifyRequest, requestId = 78
17:03:24.273 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:03:24.273 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:03:24.606 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:03:24.606 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5680b848[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:03:24.606 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755248021010_127.0.0.1_9728
17:03:24.611 [nacos-grpc-client-executor-130] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755248021010_127.0.0.1_9728]Ignore complete event,isRunning:false,isAbandon=false
17:03:24.611 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@108e27ec[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 130]
17:03:24.766 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:03:24.768 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
17:03:24.770 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
17:03:24.770 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:03:24.773 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:03:24.773 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:03:28.432 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:03:29.040 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3e687735-84cf-47ab-8bfc-e0bdbc51e5df_config-0
17:03:29.087 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 3 keys and 6 values 
17:03:29.114 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
17:03:29.120 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
17:03:29.127 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
17:03:29.133 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
17:03:29.140 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
17:03:29.143 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e687735-84cf-47ab-8bfc-e0bdbc51e5df_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:03:29.143 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e687735-84cf-47ab-8bfc-e0bdbc51e5df_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002c18a3cdd00
17:03:29.144 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e687735-84cf-47ab-8bfc-e0bdbc51e5df_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002c18a3cdf20
17:03:29.144 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e687735-84cf-47ab-8bfc-e0bdbc51e5df_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:03:29.144 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e687735-84cf-47ab-8bfc-e0bdbc51e5df_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:03:29.150 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e687735-84cf-47ab-8bfc-e0bdbc51e5df_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:03:29.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e687735-84cf-47ab-8bfc-e0bdbc51e5df_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755248609658_127.0.0.1_10557
17:03:29.844 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e687735-84cf-47ab-8bfc-e0bdbc51e5df_config-0] Notify connected event to listeners.
17:03:29.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e687735-84cf-47ab-8bfc-e0bdbc51e5df_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:03:29.845 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e687735-84cf-47ab-8bfc-e0bdbc51e5df_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002c18a507cb0
17:03:29.936 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:03:32.471 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:03:32.472 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:03:32.472 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:03:32.594 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:03:33.101 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:03:33.102 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:03:33.102 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:03:38.198 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:03:40.254 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2b61c442-0063-4a19-9bb0-944636af4e37
17:03:40.256 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2b61c442-0063-4a19-9bb0-944636af4e37] RpcClient init label, labels = {module=naming, source=sdk}
17:03:40.257 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2b61c442-0063-4a19-9bb0-944636af4e37] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:03:40.257 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2b61c442-0063-4a19-9bb0-944636af4e37] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:03:40.257 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2b61c442-0063-4a19-9bb0-944636af4e37] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:03:40.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2b61c442-0063-4a19-9bb0-944636af4e37] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:03:40.372 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2b61c442-0063-4a19-9bb0-944636af4e37] Success to connect to server [localhost:8848] on start up, connectionId = 1755248620264_127.0.0.1_10575
17:03:40.372 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2b61c442-0063-4a19-9bb0-944636af4e37] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:03:40.372 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2b61c442-0063-4a19-9bb0-944636af4e37] Notify connected event to listeners.
17:03:40.372 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2b61c442-0063-4a19-9bb0-944636af4e37] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002c18a507cb0
17:03:40.411 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:03:40.431 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
17:03:40.518 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.806 seconds (JVM running for 14.249)
17:03:40.529 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:03:40.530 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:03:40.530 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:03:40.924 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2b61c442-0063-4a19-9bb0-944636af4e37] Receive server push request, request = NotifySubscriberRequest, requestId = 84
17:03:40.939 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2b61c442-0063-4a19-9bb0-944636af4e37] Ack server push request, request = NotifySubscriberRequest, requestId = 84
17:03:41.052 [RMI TCP Connection(6)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:04:53.370 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
17:04:53.370 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:04:53.370 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
17:04:53.384 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:04:53.385 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
17:04:53.391 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
17:04:53.391 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:04:53.392 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
17:04:53.392 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
17:04:53.392 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:29:19.429 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:29:19.429 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:29:19.772 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:29:19.772 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1266100[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:29:19.772 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755248620264_127.0.0.1_10575
17:29:19.772 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6a6066c6[Running, pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 254]
17:29:19.772 [nacos-grpc-client-executor-254] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755248620264_127.0.0.1_10575]Ignore complete event,isRunning:false,isAbandon=false
17:29:19.921 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:29:19.921 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:29:19.921 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:29:19.921 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:29:19.921 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:29:19.921 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
