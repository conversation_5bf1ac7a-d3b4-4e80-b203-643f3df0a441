14:06:31.460 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:06:34.264 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9d6611b3-b5cf-42ef-9ead-cff0d8a255fb_config-0
14:06:34.536 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 110 ms to scan 1 urls, producing 3 keys and 6 values 
14:06:34.685 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 38 ms to scan 1 urls, producing 4 keys and 9 values 
14:06:34.710 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 23 ms to scan 1 urls, producing 3 keys and 10 values 
14:06:34.736 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 1 keys and 5 values 
14:06:34.767 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 19 ms to scan 1 urls, producing 1 keys and 7 values 
14:06:34.797 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 2 keys and 8 values 
14:06:34.804 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d6611b3-b5cf-42ef-9ead-cff0d8a255fb_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:06:34.805 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d6611b3-b5cf-42ef-9ead-cff0d8a255fb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000022f723b6af8
14:06:34.806 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d6611b3-b5cf-42ef-9ead-cff0d8a255fb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000022f723b6d18
14:06:34.808 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d6611b3-b5cf-42ef-9ead-cff0d8a255fb_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:06:34.809 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d6611b3-b5cf-42ef-9ead-cff0d8a255fb_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:06:34.831 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d6611b3-b5cf-42ef-9ead-cff0d8a255fb_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:06:36.964 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d6611b3-b5cf-42ef-9ead-cff0d8a255fb_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1757052396614_127.0.0.1_10206
14:06:36.964 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d6611b3-b5cf-42ef-9ead-cff0d8a255fb_config-0] Notify connected event to listeners.
14:06:36.965 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d6611b3-b5cf-42ef-9ead-cff0d8a255fb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:06:36.966 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d6611b3-b5cf-42ef-9ead-cff0d8a255fb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000022f724f0668
14:06:37.246 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:06:43.431 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:06:43.431 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:06:43.431 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:06:43.629 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:06:44.502 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:06:44.503 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:06:44.503 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:06:52.889 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:06:55.946 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-ce4c-407a-aa14-074034ec1969
14:06:55.946 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ce4c-407a-aa14-074034ec1969] RpcClient init label, labels = {module=naming, source=sdk}
14:06:55.946 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ce4c-407a-aa14-074034ec1969] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:06:55.946 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ce4c-407a-aa14-074034ec1969] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:06:55.946 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ce4c-407a-aa14-074034ec1969] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:06:55.946 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ce4c-407a-aa14-074034ec1969] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:06:56.086 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ce4c-407a-aa14-074034ec1969] Success to connect to server [localhost:8848] on start up, connectionId = 1757052415956_127.0.0.1_10286
14:06:56.086 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ce4c-407a-aa14-074034ec1969] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:06:56.086 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ce4c-407a-aa14-074034ec1969] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000022f724f0668
14:06:56.087 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ce4c-407a-aa14-074034ec1969] Notify connected event to listeners.
14:06:56.136 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:06:56.166 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:06:56.275 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 25.961 seconds (JVM running for 27.499)
14:06:56.286 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:06:56.286 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:06:56.286 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:06:56.633 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ce4c-407a-aa14-074034ec1969] Receive server push request, request = NotifySubscriberRequest, requestId = 6
14:06:56.648 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ce4c-407a-aa14-074034ec1969] Ack server push request, request = NotifySubscriberRequest, requestId = 6
17:42:07.967 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:42:07.967 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:42:08.298 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:42:08.298 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@73dfdc63[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:42:08.298 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1757052415956_127.0.0.1_10286
17:42:08.298 [nacos-grpc-client-executor-2589] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1757052415956_127.0.0.1_10286]Ignore complete event,isRunning:false,isAbandon=false
17:42:08.298 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2071f10a[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2590]
17:42:08.331 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:42:08.338 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:42:08.344 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:42:08.344 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
