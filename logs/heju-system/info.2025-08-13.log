09:14:54.033 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:14:55.571 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of dfb3b7b4-bb39-49d0-afae-91db8099cf1e_config-0
09:14:55.736 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 82 ms to scan 1 urls, producing 3 keys and 6 values 
09:14:55.854 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 32 ms to scan 1 urls, producing 4 keys and 9 values 
09:14:55.876 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 3 keys and 10 values 
09:14:55.901 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 1 keys and 5 values 
09:14:55.920 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 1 keys and 7 values 
09:14:55.952 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 2 keys and 8 values 
09:14:55.960 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfb3b7b4-bb39-49d0-afae-91db8099cf1e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:14:55.961 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfb3b7b4-bb39-49d0-afae-91db8099cf1e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001efc13b6f80
09:14:55.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfb3b7b4-bb39-49d0-afae-91db8099cf1e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001efc13b71a0
09:14:55.964 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfb3b7b4-bb39-49d0-afae-91db8099cf1e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:14:55.966 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfb3b7b4-bb39-49d0-afae-91db8099cf1e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:14:56.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfb3b7b4-bb39-49d0-afae-91db8099cf1e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:14:58.259 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfb3b7b4-bb39-49d0-afae-91db8099cf1e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755047697938_127.0.0.1_4337
09:14:58.260 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfb3b7b4-bb39-49d0-afae-91db8099cf1e_config-0] Notify connected event to listeners.
09:14:58.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfb3b7b4-bb39-49d0-afae-91db8099cf1e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:14:58.262 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dfb3b7b4-bb39-49d0-afae-91db8099cf1e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001efc14f0fb0
09:14:58.533 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:15:04.495 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:15:04.496 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:15:04.496 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:15:04.709 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:15:05.609 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:15:05.611 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:15:05.611 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:15:15.600 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:15:20.431 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9cc0dddc-b0ec-4935-8bf5-81e8eef313dd
09:15:20.431 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc0dddc-b0ec-4935-8bf5-81e8eef313dd] RpcClient init label, labels = {module=naming, source=sdk}
09:15:20.434 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc0dddc-b0ec-4935-8bf5-81e8eef313dd] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:15:20.434 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc0dddc-b0ec-4935-8bf5-81e8eef313dd] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:15:20.435 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc0dddc-b0ec-4935-8bf5-81e8eef313dd] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:15:20.436 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc0dddc-b0ec-4935-8bf5-81e8eef313dd] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:15:20.580 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc0dddc-b0ec-4935-8bf5-81e8eef313dd] Success to connect to server [localhost:8848] on start up, connectionId = 1755047720449_127.0.0.1_4440
09:15:20.581 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc0dddc-b0ec-4935-8bf5-81e8eef313dd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:15:20.582 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc0dddc-b0ec-4935-8bf5-81e8eef313dd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001efc14f0fb0
09:15:20.582 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc0dddc-b0ec-4935-8bf5-81e8eef313dd] Notify connected event to listeners.
09:15:20.672 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:15:20.741 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
09:15:21.066 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 28.42 seconds (JVM running for 31.004)
09:15:21.099 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:15:21.101 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:15:21.103 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:15:21.161 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc0dddc-b0ec-4935-8bf5-81e8eef313dd] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:15:21.184 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc0dddc-b0ec-4935-8bf5-81e8eef313dd] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:16:59.393 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:17:01.107 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc0dddc-b0ec-4935-8bf5-81e8eef313dd] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:17:01.107 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cc0dddc-b0ec-4935-8bf5-81e8eef313dd] Ack server push request, request = NotifySubscriberRequest, requestId = 13
09:17:01.537 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:17:01.538 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:17:01.645 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:17:01.645 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
10:11:17.557 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:11:17.561 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:11:17.891 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:11:17.892 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4394dae4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:11:17.892 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755047720449_127.0.0.1_4440
10:11:17.895 [nacos-grpc-client-executor-682] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755047720449_127.0.0.1_4440]Ignore complete event,isRunning:false,isAbandon=false
10:11:17.902 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@254582a[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 683]
10:11:18.065 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:11:18.071 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
10:11:18.086 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
10:11:18.086 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:11:18.086 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:11:18.086 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:11:18.095 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:11:18.095 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:11:32.785 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:11:34.610 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 95f7365b-6ca3-4e55-a038-b8f5ead368a0_config-0
10:11:34.761 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 92 ms to scan 1 urls, producing 3 keys and 6 values 
10:11:34.838 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 4 keys and 9 values 
10:11:34.858 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 3 keys and 10 values 
10:11:34.880 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 1 keys and 5 values 
10:11:34.890 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
10:11:34.906 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
10:11:34.908 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f7365b-6ca3-4e55-a038-b8f5ead368a0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:11:34.908 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f7365b-6ca3-4e55-a038-b8f5ead368a0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000017b043b6af8
10:11:34.908 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f7365b-6ca3-4e55-a038-b8f5ead368a0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000017b043b6d18
10:11:34.908 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f7365b-6ca3-4e55-a038-b8f5ead368a0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:11:34.908 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f7365b-6ca3-4e55-a038-b8f5ead368a0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:11:34.923 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f7365b-6ca3-4e55-a038-b8f5ead368a0_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:11:37.148 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f7365b-6ca3-4e55-a038-b8f5ead368a0_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755051096750_127.0.0.1_3270
10:11:37.148 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f7365b-6ca3-4e55-a038-b8f5ead368a0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:11:37.148 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f7365b-6ca3-4e55-a038-b8f5ead368a0_config-0] Notify connected event to listeners.
10:11:37.148 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95f7365b-6ca3-4e55-a038-b8f5ead368a0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000017b044f0668
10:11:37.374 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:11:41.696 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:11:41.696 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:11:41.696 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:11:41.874 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:11:42.516 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:11:42.516 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:11:42.516 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:11:54.613 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:11:59.790 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b40ec5ff-555b-427a-8d15-96a184a84da1
10:11:59.791 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b40ec5ff-555b-427a-8d15-96a184a84da1] RpcClient init label, labels = {module=naming, source=sdk}
10:11:59.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b40ec5ff-555b-427a-8d15-96a184a84da1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:11:59.794 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b40ec5ff-555b-427a-8d15-96a184a84da1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:11:59.795 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b40ec5ff-555b-427a-8d15-96a184a84da1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:11:59.795 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b40ec5ff-555b-427a-8d15-96a184a84da1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:11:59.930 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b40ec5ff-555b-427a-8d15-96a184a84da1] Success to connect to server [localhost:8848] on start up, connectionId = 1755051119808_127.0.0.1_3378
10:11:59.931 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b40ec5ff-555b-427a-8d15-96a184a84da1] Notify connected event to listeners.
10:11:59.931 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b40ec5ff-555b-427a-8d15-96a184a84da1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:11:59.932 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b40ec5ff-555b-427a-8d15-96a184a84da1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000017b044f0668
10:11:59.996 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:12:00.044 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
10:12:00.275 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 29.788 seconds (JVM running for 32.469)
10:12:00.316 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:12:00.318 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:12:00.320 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:12:00.511 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b40ec5ff-555b-427a-8d15-96a184a84da1] Receive server push request, request = NotifySubscriberRequest, requestId = 20
10:12:00.526 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b40ec5ff-555b-427a-8d15-96a184a84da1] Ack server push request, request = NotifySubscriberRequest, requestId = 20
10:14:35.701 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:14:37.363 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:14:37.363 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:17:47.762 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:17:47.766 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:17:48.091 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:17:48.091 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@28bece79[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:17:48.091 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755051119808_127.0.0.1_3378
10:17:48.091 [nacos-grpc-client-executor-81] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755051119808_127.0.0.1_3378]Ignore complete event,isRunning:false,isAbandon=false
10:17:48.100 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@24bd074b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 82]
10:17:48.254 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:17:48.254 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:17:48.263 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:17:48.263 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:17:48.263 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:17:48.263 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:17:59.310 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:18:00.325 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1e2df268-ae32-43df-8931-179577585b51_config-0
10:18:00.421 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 3 keys and 6 values 
10:18:00.459 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 4 keys and 9 values 
10:18:00.470 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
10:18:00.474 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
10:18:00.490 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
10:18:00.490 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
10:18:00.506 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e2df268-ae32-43df-8931-179577585b51_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:18:00.507 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e2df268-ae32-43df-8931-179577585b51_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000242013b6af8
10:18:00.507 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e2df268-ae32-43df-8931-179577585b51_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000242013b6d18
10:18:00.508 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e2df268-ae32-43df-8931-179577585b51_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:18:00.509 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e2df268-ae32-43df-8931-179577585b51_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:18:00.519 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e2df268-ae32-43df-8931-179577585b51_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:18:01.511 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e2df268-ae32-43df-8931-179577585b51_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755051481281_127.0.0.1_4763
10:18:01.512 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e2df268-ae32-43df-8931-179577585b51_config-0] Notify connected event to listeners.
10:18:01.513 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e2df268-ae32-43df-8931-179577585b51_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:18:01.513 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e2df268-ae32-43df-8931-179577585b51_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000242014f0668
10:18:01.615 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:18:04.691 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:18:04.691 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:18:04.691 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:18:04.839 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:18:05.449 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:18:05.449 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:18:05.449 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:18:12.079 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:18:15.123 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cecd623b-e211-4922-9d13-2574e8b89591
10:18:15.123 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cecd623b-e211-4922-9d13-2574e8b89591] RpcClient init label, labels = {module=naming, source=sdk}
10:18:15.123 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cecd623b-e211-4922-9d13-2574e8b89591] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:18:15.123 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cecd623b-e211-4922-9d13-2574e8b89591] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:18:15.123 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cecd623b-e211-4922-9d13-2574e8b89591] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:18:15.123 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cecd623b-e211-4922-9d13-2574e8b89591] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:18:15.245 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cecd623b-e211-4922-9d13-2574e8b89591] Success to connect to server [localhost:8848] on start up, connectionId = 1755051495123_127.0.0.1_4796
10:18:15.245 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cecd623b-e211-4922-9d13-2574e8b89591] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:18:15.245 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cecd623b-e211-4922-9d13-2574e8b89591] Notify connected event to listeners.
10:18:15.245 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cecd623b-e211-4922-9d13-2574e8b89591] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000242014f0668
10:18:15.291 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:18:15.317 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
10:18:15.417 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 17.134 seconds (JVM running for 18.851)
10:18:15.437 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:18:15.437 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:18:15.437 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:18:15.853 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cecd623b-e211-4922-9d13-2574e8b89591] Receive server push request, request = NotifySubscriberRequest, requestId = 27
10:18:15.870 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cecd623b-e211-4922-9d13-2574e8b89591] Ack server push request, request = NotifySubscriberRequest, requestId = 27
10:18:34.733 [http-nio-9600-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:18:36.102 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:18:36.102 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:31:06.138 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:31:06.146 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:31:06.488 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:31:06.488 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3c30ce4e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:31:06.488 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755051495123_127.0.0.1_4796
10:31:06.491 [nacos-grpc-client-executor-163] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755051495123_127.0.0.1_4796]Ignore complete event,isRunning:false,isAbandon=false
10:31:06.495 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3ddb278f[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 164]
10:31:06.673 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:31:06.678 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:31:06.692 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:31:06.692 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:31:06.694 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:31:06.695 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:31:22.009 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:31:23.239 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c5edcf3a-9238-4626-a8b8-9a5bfe664ab1_config-0
10:31:23.343 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 55 ms to scan 1 urls, producing 3 keys and 6 values 
10:31:23.396 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
10:31:23.410 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
10:31:23.419 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
10:31:23.441 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 1 keys and 7 values 
10:31:23.452 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
10:31:23.457 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c5edcf3a-9238-4626-a8b8-9a5bfe664ab1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:31:23.457 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c5edcf3a-9238-4626-a8b8-9a5bfe664ab1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001db953b6d38
10:31:23.457 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c5edcf3a-9238-4626-a8b8-9a5bfe664ab1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001db953b6f58
10:31:23.457 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c5edcf3a-9238-4626-a8b8-9a5bfe664ab1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:31:23.457 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c5edcf3a-9238-4626-a8b8-9a5bfe664ab1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:31:23.477 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c5edcf3a-9238-4626-a8b8-9a5bfe664ab1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:31:24.600 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c5edcf3a-9238-4626-a8b8-9a5bfe664ab1_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755052284344_127.0.0.1_7737
10:31:24.602 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c5edcf3a-9238-4626-a8b8-9a5bfe664ab1_config-0] Notify connected event to listeners.
10:31:24.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c5edcf3a-9238-4626-a8b8-9a5bfe664ab1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:31:24.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c5edcf3a-9238-4626-a8b8-9a5bfe664ab1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001db954f0ad8
10:31:24.718 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:31:27.951 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:31:27.952 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:31:27.953 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:31:28.101 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:31:28.690 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:31:28.691 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:31:28.691 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:31:35.627 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:31:38.634 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3b5953e1-6265-4b55-9fd6-4e091c028d12
10:31:38.634 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b5953e1-6265-4b55-9fd6-4e091c028d12] RpcClient init label, labels = {module=naming, source=sdk}
10:31:38.636 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b5953e1-6265-4b55-9fd6-4e091c028d12] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:31:38.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b5953e1-6265-4b55-9fd6-4e091c028d12] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:31:38.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b5953e1-6265-4b55-9fd6-4e091c028d12] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:31:38.638 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b5953e1-6265-4b55-9fd6-4e091c028d12] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:31:38.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b5953e1-6265-4b55-9fd6-4e091c028d12] Success to connect to server [localhost:8848] on start up, connectionId = 1755052298640_127.0.0.1_7788
10:31:38.769 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b5953e1-6265-4b55-9fd6-4e091c028d12] Notify connected event to listeners.
10:31:38.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b5953e1-6265-4b55-9fd6-4e091c028d12] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:31:38.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b5953e1-6265-4b55-9fd6-4e091c028d12] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001db954f0ad8
10:31:38.809 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:31:38.835 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
10:31:38.969 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 18.511 seconds (JVM running for 21.571)
10:31:38.983 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:31:38.984 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:31:38.984 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:31:39.317 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b5953e1-6265-4b55-9fd6-4e091c028d12] Receive server push request, request = NotifySubscriberRequest, requestId = 33
10:31:39.332 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3b5953e1-6265-4b55-9fd6-4e091c028d12] Ack server push request, request = NotifySubscriberRequest, requestId = 33
10:31:58.086 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:31:59.159 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:31:59.160 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:33:23.945 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:33:23.950 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:33:24.268 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:33:24.268 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6db7d7cf[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:33:24.268 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755052298640_127.0.0.1_7788
10:33:24.268 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755052298640_127.0.0.1_7788]Ignore complete event,isRunning:false,isAbandon=false
10:33:24.268 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@889f90f[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 35]
10:33:24.498 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:33:24.502 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:33:24.528 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:33:24.528 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:33:24.531 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:33:24.531 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:33:34.992 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:33:35.973 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 051dbbfc-6795-4ea0-ab03-a427869044c8_config-0
10:33:36.056 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 35 ms to scan 1 urls, producing 3 keys and 6 values 
10:33:36.099 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
10:33:36.109 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
10:33:36.119 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
10:33:36.131 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
10:33:36.141 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
10:33:36.144 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [051dbbfc-6795-4ea0-ab03-a427869044c8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:33:36.145 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [051dbbfc-6795-4ea0-ab03-a427869044c8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001a0cd3add70
10:33:36.145 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [051dbbfc-6795-4ea0-ab03-a427869044c8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001a0cd3adf90
10:33:36.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [051dbbfc-6795-4ea0-ab03-a427869044c8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:33:36.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [051dbbfc-6795-4ea0-ab03-a427869044c8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:33:36.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [051dbbfc-6795-4ea0-ab03-a427869044c8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:33:37.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [051dbbfc-6795-4ea0-ab03-a427869044c8_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755052416994_127.0.0.1_8310
10:33:37.266 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [051dbbfc-6795-4ea0-ab03-a427869044c8_config-0] Notify connected event to listeners.
10:33:37.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [051dbbfc-6795-4ea0-ab03-a427869044c8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:33:37.267 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [051dbbfc-6795-4ea0-ab03-a427869044c8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001a0cd507cb0
10:33:37.383 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:33:40.692 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
10:33:40.692 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:33:40.693 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:33:40.839 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:33:41.444 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:33:41.446 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:33:41.446 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:33:48.204 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:33:51.387 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3490c010-2869-4151-b3bb-22dd9a916682
10:33:51.387 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3490c010-2869-4151-b3bb-22dd9a916682] RpcClient init label, labels = {module=naming, source=sdk}
10:33:51.388 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3490c010-2869-4151-b3bb-22dd9a916682] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:33:51.389 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3490c010-2869-4151-b3bb-22dd9a916682] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:33:51.389 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3490c010-2869-4151-b3bb-22dd9a916682] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:33:51.389 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3490c010-2869-4151-b3bb-22dd9a916682] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:33:51.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3490c010-2869-4151-b3bb-22dd9a916682] Success to connect to server [localhost:8848] on start up, connectionId = 1755052431398_127.0.0.1_8368
10:33:51.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3490c010-2869-4151-b3bb-22dd9a916682] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:33:51.514 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3490c010-2869-4151-b3bb-22dd9a916682] Notify connected event to listeners.
10:33:51.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3490c010-2869-4151-b3bb-22dd9a916682] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001a0cd507cb0
10:33:51.566 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
10:33:51.593 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
10:33:51.717 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 17.622 seconds (JVM running for 19.1)
10:33:51.732 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
10:33:51.732 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
10:33:51.733 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
10:33:52.108 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3490c010-2869-4151-b3bb-22dd9a916682] Receive server push request, request = NotifySubscriberRequest, requestId = 40
10:33:52.122 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3490c010-2869-4151-b3bb-22dd9a916682] Ack server push request, request = NotifySubscriberRequest, requestId = 40
10:33:52.156 [RMI TCP Connection(8)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:33:57.022 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:33:57.022 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
11:49:35.049 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:49:35.053 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:49:35.380 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:49:35.380 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3f9c147e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:49:35.380 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755052431398_127.0.0.1_8368
11:49:35.384 [nacos-grpc-client-executor-918] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755052431398_127.0.0.1_8368]Ignore complete event,isRunning:false,isAbandon=false
11:49:35.386 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1c513556[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 919]
11:49:35.562 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:49:35.567 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:49:35.576 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:49:35.576 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:49:35.579 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:49:35.579 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:50:07.566 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:50:08.887 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2030cb77-037f-431d-9342-f5a6b5b831a4_config-0
11:50:08.995 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 63 ms to scan 1 urls, producing 3 keys and 6 values 
11:50:09.056 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
11:50:09.063 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 3 ms to scan 1 urls, producing 3 keys and 10 values 
11:50:09.086 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
11:50:09.098 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
11:50:09.114 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
11:50:09.118 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2030cb77-037f-431d-9342-f5a6b5b831a4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:50:09.119 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2030cb77-037f-431d-9342-f5a6b5b831a4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001d9a139bda8
11:50:09.120 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2030cb77-037f-431d-9342-f5a6b5b831a4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001d9a139bfc8
11:50:09.121 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2030cb77-037f-431d-9342-f5a6b5b831a4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:50:09.122 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2030cb77-037f-431d-9342-f5a6b5b831a4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:50:09.133 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2030cb77-037f-431d-9342-f5a6b5b831a4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:50:10.393 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2030cb77-037f-431d-9342-f5a6b5b831a4_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755057010128_127.0.0.1_10078
11:50:10.398 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2030cb77-037f-431d-9342-f5a6b5b831a4_config-0] Notify connected event to listeners.
11:50:10.398 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2030cb77-037f-431d-9342-f5a6b5b831a4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:50:10.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2030cb77-037f-431d-9342-f5a6b5b831a4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001d9a1513ff8
11:50:10.578 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:50:14.509 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:50:14.510 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:50:14.510 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:50:14.686 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:50:15.428 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:50:15.429 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:50:15.429 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:50:28.399 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:50:34.115 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2c7c6ef6-548b-40fe-90fe-be7fa6ba1207
11:50:34.116 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c7c6ef6-548b-40fe-90fe-be7fa6ba1207] RpcClient init label, labels = {module=naming, source=sdk}
11:50:34.118 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c7c6ef6-548b-40fe-90fe-be7fa6ba1207] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:50:34.118 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c7c6ef6-548b-40fe-90fe-be7fa6ba1207] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:50:34.119 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c7c6ef6-548b-40fe-90fe-be7fa6ba1207] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:50:34.119 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c7c6ef6-548b-40fe-90fe-be7fa6ba1207] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:50:34.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c7c6ef6-548b-40fe-90fe-be7fa6ba1207] Success to connect to server [localhost:8848] on start up, connectionId = 1755057034130_127.0.0.1_10186
11:50:34.244 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c7c6ef6-548b-40fe-90fe-be7fa6ba1207] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:50:34.244 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c7c6ef6-548b-40fe-90fe-be7fa6ba1207] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001d9a1513ff8
11:50:34.244 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c7c6ef6-548b-40fe-90fe-be7fa6ba1207] Notify connected event to listeners.
11:50:34.300 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:50:34.334 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
11:50:34.478 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 27.798 seconds (JVM running for 30.525)
11:50:34.494 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:50:34.495 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:50:34.496 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:50:34.790 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c7c6ef6-548b-40fe-90fe-be7fa6ba1207] Receive server push request, request = NotifySubscriberRequest, requestId = 48
11:50:34.806 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c7c6ef6-548b-40fe-90fe-be7fa6ba1207] Ack server push request, request = NotifySubscriberRequest, requestId = 48
12:01:58.360 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:01:58.361 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:01:58.693 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:01:58.693 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@151a0261[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:01:58.693 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755057034130_127.0.0.1_10186
12:01:58.693 [nacos-grpc-client-executor-146] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755057034130_127.0.0.1_10186]Ignore complete event,isRunning:false,isAbandon=false
12:01:58.699 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@12e7f617[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 147]
12:01:58.751 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:01:58.756 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:01:58.765 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:01:58.765 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:02:12.669 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:02:13.947 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 39372ed3-7ed0-4900-8092-9dce21398c4a_config-0
12:02:14.053 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 55 ms to scan 1 urls, producing 3 keys and 6 values 
12:02:14.107 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
12:02:14.121 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
12:02:14.142 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 5 values 
12:02:14.156 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
12:02:14.170 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
12:02:14.174 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39372ed3-7ed0-4900-8092-9dce21398c4a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:02:14.175 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39372ed3-7ed0-4900-8092-9dce21398c4a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001abd53b6f80
12:02:14.175 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39372ed3-7ed0-4900-8092-9dce21398c4a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001abd53b71a0
12:02:14.176 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39372ed3-7ed0-4900-8092-9dce21398c4a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:02:14.177 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39372ed3-7ed0-4900-8092-9dce21398c4a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:02:14.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39372ed3-7ed0-4900-8092-9dce21398c4a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:02:15.447 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39372ed3-7ed0-4900-8092-9dce21398c4a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755057735192_127.0.0.1_13593
12:02:15.448 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39372ed3-7ed0-4900-8092-9dce21398c4a_config-0] Notify connected event to listeners.
12:02:15.448 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39372ed3-7ed0-4900-8092-9dce21398c4a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:02:15.449 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39372ed3-7ed0-4900-8092-9dce21398c4a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001abd54f0fb0
12:02:15.560 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:02:22.671 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
12:02:22.672 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:02:22.672 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:02:23.046 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:02:24.360 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:02:24.360 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:02:24.360 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:02:40.991 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:02:45.559 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e1046f59-2efc-4680-bdfa-b651af5629d8
12:02:45.560 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1046f59-2efc-4680-bdfa-b651af5629d8] RpcClient init label, labels = {module=naming, source=sdk}
12:02:45.563 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1046f59-2efc-4680-bdfa-b651af5629d8] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:02:45.563 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1046f59-2efc-4680-bdfa-b651af5629d8] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:02:45.564 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1046f59-2efc-4680-bdfa-b651af5629d8] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:02:45.564 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1046f59-2efc-4680-bdfa-b651af5629d8] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:02:45.691 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1046f59-2efc-4680-bdfa-b651af5629d8] Success to connect to server [localhost:8848] on start up, connectionId = 1755057765576_127.0.0.1_13724
12:02:45.692 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1046f59-2efc-4680-bdfa-b651af5629d8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:02:45.692 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1046f59-2efc-4680-bdfa-b651af5629d8] Notify connected event to listeners.
12:02:45.692 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1046f59-2efc-4680-bdfa-b651af5629d8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001abd54f0fb0
12:02:45.742 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
12:02:45.777 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
12:02:45.942 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 34.377 seconds (JVM running for 36.907)
12:02:45.966 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
12:02:45.966 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
12:02:45.968 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
12:02:46.227 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1046f59-2efc-4680-bdfa-b651af5629d8] Receive server push request, request = NotifySubscriberRequest, requestId = 53
12:02:46.238 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e1046f59-2efc-4680-bdfa-b651af5629d8] Ack server push request, request = NotifySubscriberRequest, requestId = 53
12:14:32.581 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:14:32.584 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:14:32.909 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:14:32.910 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@710db307[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:14:32.911 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755057765576_127.0.0.1_13724
12:14:32.912 [nacos-grpc-client-executor-151] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755057765576_127.0.0.1_13724]Ignore complete event,isRunning:false,isAbandon=false
12:14:32.916 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7ba59e4[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 152]
12:14:32.976 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:14:32.982 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:14:32.998 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:14:32.998 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:14:47.371 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:14:48.534 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 32fbe9b2-741f-4076-b93d-c4ede6a4881e_config-0
12:14:48.642 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 42 ms to scan 1 urls, producing 3 keys and 6 values 
12:14:48.695 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
12:14:48.706 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
12:14:48.718 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
12:14:48.730 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
12:14:48.744 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
12:14:48.748 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32fbe9b2-741f-4076-b93d-c4ede6a4881e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:14:48.749 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32fbe9b2-741f-4076-b93d-c4ede6a4881e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002c4813b6480
12:14:48.749 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32fbe9b2-741f-4076-b93d-c4ede6a4881e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000002c4813b66a0
12:14:48.750 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32fbe9b2-741f-4076-b93d-c4ede6a4881e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:14:48.751 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32fbe9b2-741f-4076-b93d-c4ede6a4881e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:14:48.764 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32fbe9b2-741f-4076-b93d-c4ede6a4881e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:14:49.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32fbe9b2-741f-4076-b93d-c4ede6a4881e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755058489623_127.0.0.1_2169
12:14:49.969 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32fbe9b2-741f-4076-b93d-c4ede6a4881e_config-0] Notify connected event to listeners.
12:14:49.969 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32fbe9b2-741f-4076-b93d-c4ede6a4881e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:14:49.969 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32fbe9b2-741f-4076-b93d-c4ede6a4881e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002c4814f0668
12:14:50.114 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:14:56.079 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
12:14:56.079 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:14:56.085 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:14:56.992 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:14:58.015 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:14:58.016 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:14:58.016 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:15:09.958 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:15:16.654 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b84f931d-bf3d-48e8-bc55-c7374ae378ab
12:15:16.655 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b84f931d-bf3d-48e8-bc55-c7374ae378ab] RpcClient init label, labels = {module=naming, source=sdk}
12:15:16.658 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b84f931d-bf3d-48e8-bc55-c7374ae378ab] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:15:16.659 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b84f931d-bf3d-48e8-bc55-c7374ae378ab] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:15:16.659 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b84f931d-bf3d-48e8-bc55-c7374ae378ab] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:15:16.660 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b84f931d-bf3d-48e8-bc55-c7374ae378ab] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:15:16.795 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b84f931d-bf3d-48e8-bc55-c7374ae378ab] Success to connect to server [localhost:8848] on start up, connectionId = 1755058516671_127.0.0.1_2307
12:15:16.796 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b84f931d-bf3d-48e8-bc55-c7374ae378ab] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:15:16.796 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b84f931d-bf3d-48e8-bc55-c7374ae378ab] Notify connected event to listeners.
12:15:16.796 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b84f931d-bf3d-48e8-bc55-c7374ae378ab] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002c4814f0668
12:15:16.855 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
12:15:16.897 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
12:15:17.135 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 30.723 seconds (JVM running for 33.811)
12:15:17.154 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
12:15:17.155 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
12:15:17.157 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
12:15:17.367 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b84f931d-bf3d-48e8-bc55-c7374ae378ab] Receive server push request, request = NotifySubscriberRequest, requestId = 61
12:15:17.386 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b84f931d-bf3d-48e8-bc55-c7374ae378ab] Ack server push request, request = NotifySubscriberRequest, requestId = 61
12:17:11.964 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:17:13.742 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
12:17:13.742 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:31:24.629 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:31:24.629 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:31:24.966 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:31:24.966 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@733901e4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:31:24.966 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755058516671_127.0.0.1_2307
12:31:24.969 [nacos-grpc-client-executor-203] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755058516671_127.0.0.1_2307]Ignore complete event,isRunning:false,isAbandon=false
12:31:24.974 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1c9ef86e[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 204]
12:31:25.203 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:31:25.208 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
12:31:25.237 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
12:31:25.237 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:31:25.242 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:31:25.242 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:31:41.853 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:31:43.094 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a6f32399-dbe7-4213-a33f-77139b38c2eb_config-0
12:31:43.220 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 56 ms to scan 1 urls, producing 3 keys and 6 values 
12:31:43.277 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
12:31:43.290 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 3 keys and 10 values 
12:31:43.304 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
12:31:43.319 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
12:31:43.337 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 2 keys and 8 values 
12:31:43.342 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6f32399-dbe7-4213-a33f-77139b38c2eb_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:31:43.343 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6f32399-dbe7-4213-a33f-77139b38c2eb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000207813b71c0
12:31:43.343 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6f32399-dbe7-4213-a33f-77139b38c2eb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000207813b73e0
12:31:43.343 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6f32399-dbe7-4213-a33f-77139b38c2eb_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:31:43.344 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6f32399-dbe7-4213-a33f-77139b38c2eb_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:31:43.358 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6f32399-dbe7-4213-a33f-77139b38c2eb_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:31:44.559 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6f32399-dbe7-4213-a33f-77139b38c2eb_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755059504294_127.0.0.1_5108
12:31:44.561 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6f32399-dbe7-4213-a33f-77139b38c2eb_config-0] Notify connected event to listeners.
12:31:44.561 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6f32399-dbe7-4213-a33f-77139b38c2eb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:31:44.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6f32399-dbe7-4213-a33f-77139b38c2eb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000207814f0fb0
12:31:44.665 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:31:47.954 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
12:31:47.955 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:31:47.955 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:31:48.103 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:31:48.737 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:31:48.739 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:31:48.739 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:31:55.782 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:31:58.660 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 35fd252d-a040-4dc6-b153-c85e20113c9a
12:31:58.660 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35fd252d-a040-4dc6-b153-c85e20113c9a] RpcClient init label, labels = {module=naming, source=sdk}
12:31:58.660 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35fd252d-a040-4dc6-b153-c85e20113c9a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:31:58.660 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35fd252d-a040-4dc6-b153-c85e20113c9a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:31:58.660 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35fd252d-a040-4dc6-b153-c85e20113c9a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:31:58.660 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35fd252d-a040-4dc6-b153-c85e20113c9a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:31:58.792 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35fd252d-a040-4dc6-b153-c85e20113c9a] Success to connect to server [localhost:8848] on start up, connectionId = 1755059518660_127.0.0.1_5153
12:31:58.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35fd252d-a040-4dc6-b153-c85e20113c9a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:31:58.793 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35fd252d-a040-4dc6-b153-c85e20113c9a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000207814f0fb0
12:31:58.793 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35fd252d-a040-4dc6-b153-c85e20113c9a] Notify connected event to listeners.
12:31:58.827 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
12:31:58.857 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
12:31:58.973 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 18.925 seconds (JVM running for 22.221)
12:31:58.987 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
12:31:58.987 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
12:31:58.988 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
12:31:59.324 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35fd252d-a040-4dc6-b153-c85e20113c9a] Receive server push request, request = NotifySubscriberRequest, requestId = 69
12:31:59.329 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [35fd252d-a040-4dc6-b153-c85e20113c9a] Ack server push request, request = NotifySubscriberRequest, requestId = 69
12:32:14.160 [http-nio-9600-exec-5] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:32:15.451 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
12:32:15.451 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
12:32:15.452 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
12:32:15.452 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:32:15.456 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
12:32:15.460 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
12:32:15.460 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:32:15.464 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
12:32:15.464 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
12:32:15.465 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:51:27.227 [lettuce-nioEventLoop-4-1] INFO  i.l.c.p.CommandHandler - [log,217] - null Unexpected exception during request: java.net.SocketException: Connection reset
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:259)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
13:51:27.256 [lettuce-eventExecutorLoop-1-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /***********00:6379
13:51:38.355 [lettuce-eventExecutorLoop-1-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:51:48.952 [lettuce-eventExecutorLoop-1-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:52:00.051 [lettuce-eventExecutorLoop-1-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:52:12.155 [lettuce-eventExecutorLoop-1-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:52:26.353 [lettuce-eventExecutorLoop-1-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:52:44.651 [lettuce-eventExecutorLoop-1-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:53:11.153 [lettuce-eventExecutorLoop-1-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:53:51.253 [lettuce-eventExecutorLoop-1-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:54:31.352 [lettuce-eventExecutorLoop-1-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:55:11.452 [lettuce-eventExecutorLoop-1-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:55:51.552 [lettuce-eventExecutorLoop-1-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:56:31.653 [lettuce-eventExecutorLoop-1-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:57:11.752 [lettuce-eventExecutorLoop-1-20] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:57:51.855 [lettuce-eventExecutorLoop-1-22] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:58:31.954 [lettuce-eventExecutorLoop-1-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:59:12.050 [lettuce-eventExecutorLoop-1-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:59:52.151 [lettuce-eventExecutorLoop-1-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:00:32.256 [lettuce-eventExecutorLoop-1-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:01:12.353 [lettuce-eventExecutorLoop-1-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:01:52.450 [lettuce-eventExecutorLoop-1-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:02:32.552 [lettuce-eventExecutorLoop-1-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:03:12.653 [lettuce-eventExecutorLoop-1-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:03:52.751 [lettuce-eventExecutorLoop-1-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:04:32.850 [lettuce-eventExecutorLoop-1-20] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:05:12.953 [lettuce-eventExecutorLoop-1-22] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:05:53.051 [lettuce-eventExecutorLoop-1-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:06:33.152 [lettuce-eventExecutorLoop-1-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:07:13.252 [lettuce-eventExecutorLoop-1-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:07:53.353 [lettuce-eventExecutorLoop-1-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:08:33.453 [lettuce-eventExecutorLoop-1-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:09:13.552 [lettuce-eventExecutorLoop-1-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:09:53.651 [lettuce-eventExecutorLoop-1-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:10:33.751 [lettuce-eventExecutorLoop-1-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:11:13.852 [lettuce-eventExecutorLoop-1-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:11:53.957 [lettuce-eventExecutorLoop-1-20] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:12:34.056 [lettuce-eventExecutorLoop-1-22] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:13:09.452 [lettuce-eventExecutorLoop-1-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:13:09.471 [lettuce-nioEventLoop-4-2] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to ***********00/<unresolved>:6379
14:17:57.105 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:17:57.108 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:17:57.436 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:17:57.436 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@17283bc[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:17:57.437 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755059518660_127.0.0.1_5153
14:17:57.439 [nacos-grpc-client-executor-1284] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755059518660_127.0.0.1_5153]Ignore complete event,isRunning:false,isAbandon=false
14:17:57.441 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3b59ae9f[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1285]
14:17:57.625 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:17:57.625 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
14:17:57.636 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
14:17:57.636 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:17:57.640 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:17:57.642 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:18:09.448 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:18:10.555 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 943e45b1-26cd-40be-918c-80e1672a4b71_config-0
14:18:10.653 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
14:18:10.702 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
14:18:10.713 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:18:10.724 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
14:18:10.736 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
14:18:10.748 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
14:18:10.752 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [943e45b1-26cd-40be-918c-80e1672a4b71_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:18:10.753 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [943e45b1-26cd-40be-918c-80e1672a4b71_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000161a339e8d8
14:18:10.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [943e45b1-26cd-40be-918c-80e1672a4b71_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000161a339eaf8
14:18:10.755 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [943e45b1-26cd-40be-918c-80e1672a4b71_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:18:10.756 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [943e45b1-26cd-40be-918c-80e1672a4b71_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:18:10.766 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [943e45b1-26cd-40be-918c-80e1672a4b71_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:18:11.837 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [943e45b1-26cd-40be-918c-80e1672a4b71_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755065891611_127.0.0.1_13966
14:18:11.838 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [943e45b1-26cd-40be-918c-80e1672a4b71_config-0] Notify connected event to listeners.
14:18:11.838 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [943e45b1-26cd-40be-918c-80e1672a4b71_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:18:11.839 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [943e45b1-26cd-40be-918c-80e1672a4b71_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000161a3518668
14:18:11.956 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:18:16.060 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:18:16.061 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:18:16.061 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:18:16.280 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:18:17.093 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:18:17.094 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:18:17.094 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:18:34.392 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:18:43.856 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d67f97eb-e28a-4e08-a596-21ec3d671dc7
14:18:43.857 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d67f97eb-e28a-4e08-a596-21ec3d671dc7] RpcClient init label, labels = {module=naming, source=sdk}
14:18:43.858 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d67f97eb-e28a-4e08-a596-21ec3d671dc7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:18:43.858 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d67f97eb-e28a-4e08-a596-21ec3d671dc7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:18:43.858 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d67f97eb-e28a-4e08-a596-21ec3d671dc7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:18:43.858 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d67f97eb-e28a-4e08-a596-21ec3d671dc7] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:18:44.000 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d67f97eb-e28a-4e08-a596-21ec3d671dc7] Success to connect to server [localhost:8848] on start up, connectionId = 1755065923874_127.0.0.1_14116
14:18:44.001 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d67f97eb-e28a-4e08-a596-21ec3d671dc7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:18:44.001 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d67f97eb-e28a-4e08-a596-21ec3d671dc7] Notify connected event to listeners.
14:18:44.001 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d67f97eb-e28a-4e08-a596-21ec3d671dc7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000161a3518668
14:18:44.052 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:18:44.102 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
14:18:44.287 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 35.819 seconds (JVM running for 37.624)
14:18:44.314 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:18:44.315 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:18:44.316 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:18:44.569 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d67f97eb-e28a-4e08-a596-21ec3d671dc7] Receive server push request, request = NotifySubscriberRequest, requestId = 76
14:18:44.587 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d67f97eb-e28a-4e08-a596-21ec3d671dc7] Ack server push request, request = NotifySubscriberRequest, requestId = 76
14:18:44.916 [RMI TCP Connection(24)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:20:27.852 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:20:27.852 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:51:26.061 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:51:26.063 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:51:26.400 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:51:26.400 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@63f29df8[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:51:26.401 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755065923874_127.0.0.1_14116
14:51:26.402 [nacos-grpc-client-executor-403] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755065923874_127.0.0.1_14116]Ignore complete event,isRunning:false,isAbandon=false
14:51:26.404 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6515768[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 404]
14:51:26.557 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:51:26.561 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:51:26.567 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:51:26.567 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:51:26.567 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:51:26.567 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:51:35.479 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:51:36.744 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8d57bc79-7af6-419c-a538-69b70f15f944_config-0
14:51:36.855 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 53 ms to scan 1 urls, producing 3 keys and 6 values 
14:51:36.911 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 4 keys and 9 values 
14:51:36.916 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 1 ms to scan 1 urls, producing 3 keys and 10 values 
14:51:36.940 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 1 keys and 5 values 
14:51:36.955 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
14:51:36.969 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
14:51:36.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d57bc79-7af6-419c-a538-69b70f15f944_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:51:36.974 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d57bc79-7af6-419c-a538-69b70f15f944_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000188da3b6d38
14:51:36.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d57bc79-7af6-419c-a538-69b70f15f944_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000188da3b6f58
14:51:36.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d57bc79-7af6-419c-a538-69b70f15f944_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:51:36.976 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d57bc79-7af6-419c-a538-69b70f15f944_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:51:36.984 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d57bc79-7af6-419c-a538-69b70f15f944_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:51:38.247 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d57bc79-7af6-419c-a538-69b70f15f944_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755067897980_127.0.0.1_7281
14:51:38.248 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d57bc79-7af6-419c-a538-69b70f15f944_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:51:38.248 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d57bc79-7af6-419c-a538-69b70f15f944_config-0] Notify connected event to listeners.
14:51:38.249 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8d57bc79-7af6-419c-a538-69b70f15f944_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000188da4f0668
14:51:38.404 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:51:42.444 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:51:42.445 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:51:42.446 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:51:42.644 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:51:43.377 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:51:43.379 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:51:43.379 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:51:56.448 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:52:02.211 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d63bb5e5-d883-4486-a8f1-420c67cbebbc
14:52:02.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63bb5e5-d883-4486-a8f1-420c67cbebbc] RpcClient init label, labels = {module=naming, source=sdk}
14:52:02.218 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63bb5e5-d883-4486-a8f1-420c67cbebbc] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:52:02.219 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63bb5e5-d883-4486-a8f1-420c67cbebbc] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:52:02.222 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63bb5e5-d883-4486-a8f1-420c67cbebbc] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:52:02.223 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63bb5e5-d883-4486-a8f1-420c67cbebbc] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:52:02.379 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63bb5e5-d883-4486-a8f1-420c67cbebbc] Success to connect to server [localhost:8848] on start up, connectionId = 1755067922244_127.0.0.1_7377
14:52:02.380 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63bb5e5-d883-4486-a8f1-420c67cbebbc] Notify connected event to listeners.
14:52:02.380 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63bb5e5-d883-4486-a8f1-420c67cbebbc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:52:02.382 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63bb5e5-d883-4486-a8f1-420c67cbebbc] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000188da4f0668
14:52:02.493 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:52:02.581 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
14:52:02.943 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 28.473 seconds (JVM running for 30.323)
14:52:02.954 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63bb5e5-d883-4486-a8f1-420c67cbebbc] Receive server push request, request = NotifySubscriberRequest, requestId = 82
14:52:02.966 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:52:02.966 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:52:02.966 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:52:02.966 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d63bb5e5-d883-4486-a8f1-420c67cbebbc] Ack server push request, request = NotifySubscriberRequest, requestId = 82
14:55:11.973 [http-nio-9600-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:55:13.966 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:55:13.966 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:55:13.974 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
14:55:13.974 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
14:55:13.978 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:55:13.983 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:55:13.984 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:55:13.984 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
14:55:13.985 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
14:55:13.985 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:59:39.719 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:59:39.726 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:59:40.140 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:59:40.141 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@146db981[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:59:40.141 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755067922244_127.0.0.1_7377
15:59:40.149 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@16c4248a[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 819]
15:59:40.236 [nacos-grpc-client-executor-819] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755067922244_127.0.0.1_7377]Ignore complete event,isRunning:false,isAbandon=false
15:59:40.564 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:59:40.570 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
15:59:40.581 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
15:59:40.581 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:59:40.583 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:59:40.583 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:59:56.960 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:59:58.534 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cb14af6e-d5ae-49b1-b30d-0c710cfb462c_config-0
15:59:58.654 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 64 ms to scan 1 urls, producing 3 keys and 6 values 
15:59:58.719 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
15:59:58.736 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 3 keys and 10 values 
15:59:58.751 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
15:59:58.763 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
15:59:58.779 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
15:59:58.783 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb14af6e-d5ae-49b1-b30d-0c710cfb462c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:59:58.783 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb14af6e-d5ae-49b1-b30d-0c710cfb462c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000181113b6480
15:59:58.785 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb14af6e-d5ae-49b1-b30d-0c710cfb462c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000181113b66a0
15:59:58.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb14af6e-d5ae-49b1-b30d-0c710cfb462c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:59:58.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb14af6e-d5ae-49b1-b30d-0c710cfb462c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:59:58.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb14af6e-d5ae-49b1-b30d-0c710cfb462c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:00:00.116 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb14af6e-d5ae-49b1-b30d-0c710cfb462c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755071999835_127.0.0.1_6020
16:00:00.117 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb14af6e-d5ae-49b1-b30d-0c710cfb462c_config-0] Notify connected event to listeners.
16:00:00.118 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb14af6e-d5ae-49b1-b30d-0c710cfb462c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:00:00.118 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb14af6e-d5ae-49b1-b30d-0c710cfb462c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000181114f0b60
16:00:00.238 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:00:05.276 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:00:05.277 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:00:05.278 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:00:05.587 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:00:06.690 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:00:06.690 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:00:06.690 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:00:17.234 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:00:22.163 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 57b81ae2-4734-49cb-9fde-b532d6ee9b10
16:00:22.163 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57b81ae2-4734-49cb-9fde-b532d6ee9b10] RpcClient init label, labels = {module=naming, source=sdk}
16:00:22.166 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57b81ae2-4734-49cb-9fde-b532d6ee9b10] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:00:22.167 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57b81ae2-4734-49cb-9fde-b532d6ee9b10] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:00:22.167 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57b81ae2-4734-49cb-9fde-b532d6ee9b10] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:00:22.168 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57b81ae2-4734-49cb-9fde-b532d6ee9b10] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:00:22.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57b81ae2-4734-49cb-9fde-b532d6ee9b10] Success to connect to server [localhost:8848] on start up, connectionId = 1755072022172_127.0.0.1_6146
16:00:22.301 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57b81ae2-4734-49cb-9fde-b532d6ee9b10] Notify connected event to listeners.
16:00:22.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57b81ae2-4734-49cb-9fde-b532d6ee9b10] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:00:22.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57b81ae2-4734-49cb-9fde-b532d6ee9b10] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000181114f0b60
16:00:22.369 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:00:22.423 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
16:00:22.609 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 27.172 seconds (JVM running for 29.834)
16:00:22.649 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:00:22.651 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:00:22.652 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:00:22.890 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57b81ae2-4734-49cb-9fde-b532d6ee9b10] Receive server push request, request = NotifySubscriberRequest, requestId = 89
16:00:22.903 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [57b81ae2-4734-49cb-9fde-b532d6ee9b10] Ack server push request, request = NotifySubscriberRequest, requestId = 89
16:00:26.435 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:00:28.022 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:00:28.022 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:21:42.957 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:21:42.961 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:21:43.286 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:21:43.286 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5c66c6b6[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:21:43.288 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755072022172_127.0.0.1_6146
16:21:43.292 [nacos-grpc-client-executor-269] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755072022172_127.0.0.1_6146]Ignore complete event,isRunning:false,isAbandon=false
16:21:43.297 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@481ebbe8[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 270]
16:21:43.504 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:21:43.508 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:21:43.515 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:21:43.515 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:21:43.515 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:21:43.515 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:21:55.854 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:21:58.284 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 47fb3c17-38ae-4064-abd9-aa45f8efe5a0_config-0
16:21:58.473 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 85 ms to scan 1 urls, producing 3 keys and 6 values 
16:21:58.560 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 4 keys and 9 values 
16:21:58.567 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
16:21:58.595 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 5 values 
16:21:58.601 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 3 ms to scan 1 urls, producing 1 keys and 7 values 
16:21:58.628 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
16:21:58.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47fb3c17-38ae-4064-abd9-aa45f8efe5a0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:21:58.634 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47fb3c17-38ae-4064-abd9-aa45f8efe5a0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000145203b6af8
16:21:58.634 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47fb3c17-38ae-4064-abd9-aa45f8efe5a0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000145203b6d18
16:21:58.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47fb3c17-38ae-4064-abd9-aa45f8efe5a0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:21:58.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47fb3c17-38ae-4064-abd9-aa45f8efe5a0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:21:58.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47fb3c17-38ae-4064-abd9-aa45f8efe5a0_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:22:00.700 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47fb3c17-38ae-4064-abd9-aa45f8efe5a0_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755073320266_127.0.0.1_10612
16:22:00.700 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47fb3c17-38ae-4064-abd9-aa45f8efe5a0_config-0] Notify connected event to listeners.
16:22:00.700 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47fb3c17-38ae-4064-abd9-aa45f8efe5a0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:22:00.700 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47fb3c17-38ae-4064-abd9-aa45f8efe5a0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000145204f0ad8
16:22:00.922 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:22:05.262 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:22:05.263 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:22:05.263 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:22:05.419 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:22:06.052 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:22:06.053 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:22:06.054 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:22:13.467 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:22:16.783 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2bd4e5dc-53de-4d3c-b2e8-c79fc2e1bcbd
16:22:16.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bd4e5dc-53de-4d3c-b2e8-c79fc2e1bcbd] RpcClient init label, labels = {module=naming, source=sdk}
16:22:16.785 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bd4e5dc-53de-4d3c-b2e8-c79fc2e1bcbd] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:22:16.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bd4e5dc-53de-4d3c-b2e8-c79fc2e1bcbd] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:22:16.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bd4e5dc-53de-4d3c-b2e8-c79fc2e1bcbd] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:22:16.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bd4e5dc-53de-4d3c-b2e8-c79fc2e1bcbd] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:22:16.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bd4e5dc-53de-4d3c-b2e8-c79fc2e1bcbd] Success to connect to server [localhost:8848] on start up, connectionId = 1755073336796_127.0.0.1_10664
16:22:16.916 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bd4e5dc-53de-4d3c-b2e8-c79fc2e1bcbd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:22:16.916 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bd4e5dc-53de-4d3c-b2e8-c79fc2e1bcbd] Notify connected event to listeners.
16:22:16.916 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bd4e5dc-53de-4d3c-b2e8-c79fc2e1bcbd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000145204f0ad8
16:22:16.966 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:22:16.998 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
16:22:17.124 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 23.45 seconds (JVM running for 26.337)
16:22:17.136 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:22:17.136 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:22:17.142 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:22:17.449 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bd4e5dc-53de-4d3c-b2e8-c79fc2e1bcbd] Receive server push request, request = NotifySubscriberRequest, requestId = 97
16:22:17.466 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bd4e5dc-53de-4d3c-b2e8-c79fc2e1bcbd] Ack server push request, request = NotifySubscriberRequest, requestId = 97
16:23:39.630 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:23:42.753 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:23:42.753 [http-nio-9600-exec-6] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:23:42.753 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
16:23:42.753 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
16:23:42.753 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-5} inited
16:23:42.761 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:23:42.771 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:23:42.771 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:23:42.772 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
16:23:42.773 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
16:23:42.774 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:23:42.774 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-5} closing ...
16:23:42.775 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-5} closed
16:23:42.775 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:18:48.349 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:18:48.352 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:18:48.681 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:18:48.681 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7e6422af[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:18:48.682 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755073336796_127.0.0.1_10664
17:18:48.683 [nacos-grpc-client-executor-686] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755073336796_127.0.0.1_10664]Ignore complete event,isRunning:false,isAbandon=false
17:18:48.686 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5481ae35[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 687]
17:18:48.837 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:18:48.838 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
17:18:48.839 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
17:18:48.840 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:18:48.841 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:18:48.841 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:18:54.399 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:18:55.073 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fddc9e42-835c-4d54-84e5-b8cfd210b565_config-0
17:18:55.125 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 
17:18:55.156 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
17:18:55.162 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
17:18:55.170 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
17:18:55.179 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
17:18:55.189 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
17:18:55.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddc9e42-835c-4d54-84e5-b8cfd210b565_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:18:55.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddc9e42-835c-4d54-84e5-b8cfd210b565_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000153be3cef80
17:18:55.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddc9e42-835c-4d54-84e5-b8cfd210b565_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000153be3cf1a0
17:18:55.194 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddc9e42-835c-4d54-84e5-b8cfd210b565_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:18:55.194 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddc9e42-835c-4d54-84e5-b8cfd210b565_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:18:55.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddc9e42-835c-4d54-84e5-b8cfd210b565_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:18:56.101 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddc9e42-835c-4d54-84e5-b8cfd210b565_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755076735856_127.0.0.1_8998
17:18:56.102 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddc9e42-835c-4d54-84e5-b8cfd210b565_config-0] Notify connected event to listeners.
17:18:56.102 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddc9e42-835c-4d54-84e5-b8cfd210b565_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:18:56.102 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fddc9e42-835c-4d54-84e5-b8cfd210b565_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000153be508d48
17:18:56.225 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:19:03.232 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:19:03.233 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:19:03.236 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:19:03.676 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:19:05.639 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:19:05.642 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:19:05.643 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:19:14.384 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:19:17.385 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c004ecca-f0f3-4b51-b223-c76991238e95
17:19:17.385 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c004ecca-f0f3-4b51-b223-c76991238e95] RpcClient init label, labels = {module=naming, source=sdk}
17:19:17.386 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c004ecca-f0f3-4b51-b223-c76991238e95] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:19:17.386 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c004ecca-f0f3-4b51-b223-c76991238e95] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:19:17.386 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c004ecca-f0f3-4b51-b223-c76991238e95] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:19:17.386 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c004ecca-f0f3-4b51-b223-c76991238e95] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:19:17.517 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c004ecca-f0f3-4b51-b223-c76991238e95] Success to connect to server [localhost:8848] on start up, connectionId = 1755076757390_127.0.0.1_9071
17:19:17.517 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c004ecca-f0f3-4b51-b223-c76991238e95] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:19:17.517 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c004ecca-f0f3-4b51-b223-c76991238e95] Notify connected event to listeners.
17:19:17.517 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c004ecca-f0f3-4b51-b223-c76991238e95] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000153be508d48
17:19:17.558 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:19:17.591 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
17:19:17.706 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 23.877 seconds (JVM running for 24.873)
17:19:17.719 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:19:17.719 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:19:17.719 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:19:17.888 [RMI TCP Connection(22)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:19:18.053 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c004ecca-f0f3-4b51-b223-c76991238e95] Receive server push request, request = NotifySubscriberRequest, requestId = 103
17:19:18.065 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c004ecca-f0f3-4b51-b223-c76991238e95] Ack server push request, request = NotifySubscriberRequest, requestId = 103
17:22:12.359 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
17:22:12.360 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:22:12.359 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
17:22:12.363 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
17:22:12.371 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:22:12.372 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
17:22:12.372 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:22:12.373 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
17:22:12.374 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
17:22:12.375 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:58:48.733 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:58:48.736 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:58:49.054 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:58:49.054 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5d0a7465[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:58:49.054 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755076757390_127.0.0.1_9071
18:58:49.059 [nacos-grpc-client-executor-1208] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755076757390_127.0.0.1_9071]Ignore complete event,isRunning:false,isAbandon=false
18:58:49.063 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@72cd7135[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1209]
18:58:49.267 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:58:49.271 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
18:58:49.274 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
18:58:49.274 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:58:49.277 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:58:49.277 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
18:59:06.946 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:59:08.178 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 903b2b92-8fe9-41f6-a5cc-ba0e1f038f22_config-0
18:59:08.313 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 60 ms to scan 1 urls, producing 3 keys and 6 values 
18:59:08.380 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
18:59:08.395 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
18:59:08.419 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 5 values 
18:59:08.440 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
18:59:08.459 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
18:59:08.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [903b2b92-8fe9-41f6-a5cc-ba0e1f038f22_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:59:08.466 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [903b2b92-8fe9-41f6-a5cc-ba0e1f038f22_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000025c0c39e480
18:59:08.467 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [903b2b92-8fe9-41f6-a5cc-ba0e1f038f22_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000025c0c39e6a0
18:59:08.468 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [903b2b92-8fe9-41f6-a5cc-ba0e1f038f22_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:59:08.469 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [903b2b92-8fe9-41f6-a5cc-ba0e1f038f22_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:59:08.488 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [903b2b92-8fe9-41f6-a5cc-ba0e1f038f22_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:59:10.255 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [903b2b92-8fe9-41f6-a5cc-ba0e1f038f22_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755082749954_127.0.0.1_3026
18:59:10.255 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [903b2b92-8fe9-41f6-a5cc-ba0e1f038f22_config-0] Notify connected event to listeners.
18:59:10.255 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [903b2b92-8fe9-41f6-a5cc-ba0e1f038f22_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:59:10.255 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [903b2b92-8fe9-41f6-a5cc-ba0e1f038f22_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000025c0c518228
18:59:10.458 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:59:15.684 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
18:59:15.685 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:59:15.685 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
18:59:15.951 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:59:16.857 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
18:59:16.860 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
18:59:16.860 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
18:59:28.156 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:59:32.759 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 93ed3990-65ad-4e80-a7ca-7bb106b50980
18:59:32.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93ed3990-65ad-4e80-a7ca-7bb106b50980] RpcClient init label, labels = {module=naming, source=sdk}
18:59:32.763 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93ed3990-65ad-4e80-a7ca-7bb106b50980] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:59:32.763 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93ed3990-65ad-4e80-a7ca-7bb106b50980] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:59:32.764 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93ed3990-65ad-4e80-a7ca-7bb106b50980] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:59:32.765 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93ed3990-65ad-4e80-a7ca-7bb106b50980] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:59:32.898 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93ed3990-65ad-4e80-a7ca-7bb106b50980] Success to connect to server [localhost:8848] on start up, connectionId = 1755082772774_127.0.0.1_3121
18:59:32.899 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93ed3990-65ad-4e80-a7ca-7bb106b50980] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:59:32.899 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93ed3990-65ad-4e80-a7ca-7bb106b50980] Notify connected event to listeners.
18:59:32.899 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93ed3990-65ad-4e80-a7ca-7bb106b50980] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000025c0c518228
18:59:32.962 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
18:59:32.999 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ***********:9600 register finished
18:59:33.197 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 27.368 seconds (JVM running for 29.052)
18:59:33.219 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
18:59:33.220 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
18:59:33.221 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
18:59:33.441 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93ed3990-65ad-4e80-a7ca-7bb106b50980] Receive server push request, request = NotifySubscriberRequest, requestId = 113
18:59:33.463 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93ed3990-65ad-4e80-a7ca-7bb106b50980] Ack server push request, request = NotifySubscriberRequest, requestId = 113
18:59:33.468 [RMI TCP Connection(10)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:01:15.539 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93ed3990-65ad-4e80-a7ca-7bb106b50980] Receive server push request, request = NotifySubscriberRequest, requestId = 116
19:01:15.542 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93ed3990-65ad-4e80-a7ca-7bb106b50980] Ack server push request, request = NotifySubscriberRequest, requestId = 116
19:01:15.683 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
19:01:15.684 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:01:15.701 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-7} inited
19:01:15.701 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
19:01:15.701 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
19:01:15.701 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-6} inited
19:01:15.701 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-5} inited
19:01:15.702 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
19:01:15.708 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
19:01:15.709 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:01:15.709 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-7} closing ...
19:01:15.709 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-7} closed
19:01:15.710 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:01:15.710 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-5} closing ...
19:01:15.711 [http-nio-9600-exec-6] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-5} closed
19:01:15.711 [http-nio-9600-exec-6] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:01:15.711 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-6} closing ...
19:01:15.712 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-6} closed
19:01:15.712 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
19:01:15.712 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
19:01:15.713 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
19:01:15.713 [http-nio-9600-exec-7] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
22:20:00.836 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
22:20:00.836 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
22:20:01.169 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
22:20:01.169 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@748ac5c6[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
22:20:01.169 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755082772774_127.0.0.1_3121
22:20:01.169 [nacos-grpc-client-executor-2423] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755082772774_127.0.0.1_3121]Ignore complete event,isRunning:false,isAbandon=false
22:20:01.181 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@45b830d6[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2424]
22:20:01.372 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
22:20:01.374 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
22:20:01.376 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
22:20:01.376 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
22:20:01.378 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
22:20:01.378 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
