09:44:46.707 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:44:48.381 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1eebe529-d471-4f07-830c-6aa52e9e2a83_config-0
09:44:48.547 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 68 ms to scan 1 urls, producing 3 keys and 6 values 
09:44:48.623 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 4 keys and 9 values 
09:44:48.639 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 3 keys and 10 values 
09:44:48.655 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 1 keys and 5 values 
09:44:48.675 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 20 ms to scan 1 urls, producing 1 keys and 7 values 
09:44:48.686 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
09:44:48.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eebe529-d471-4f07-830c-6aa52e9e2a83_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:44:48.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eebe529-d471-4f07-830c-6aa52e9e2a83_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001b3be3bbda8
09:44:48.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eebe529-d471-4f07-830c-6aa52e9e2a83_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001b3be3bbfc8
09:44:48.702 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eebe529-d471-4f07-830c-6aa52e9e2a83_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:44:48.703 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eebe529-d471-4f07-830c-6aa52e9e2a83_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:44:48.720 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eebe529-d471-4f07-830c-6aa52e9e2a83_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:44:50.435 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eebe529-d471-4f07-830c-6aa52e9e2a83_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756172690094_127.0.0.1_2328
09:44:50.435 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eebe529-d471-4f07-830c-6aa52e9e2a83_config-0] Notify connected event to listeners.
09:44:50.435 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eebe529-d471-4f07-830c-6aa52e9e2a83_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:44:50.435 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1eebe529-d471-4f07-830c-6aa52e9e2a83_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001b3be4f36d8
09:44:50.656 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:44:59.133 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:44:59.133 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:44:59.133 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:44:59.374 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:45:00.272 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:45:00.273 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:45:00.274 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:45:10.331 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:45:14.621 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 66e916bf-0847-41e8-add1-087d6aa2537a
09:45:14.622 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66e916bf-0847-41e8-add1-087d6aa2537a] RpcClient init label, labels = {module=naming, source=sdk}
09:45:14.624 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66e916bf-0847-41e8-add1-087d6aa2537a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:45:14.625 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66e916bf-0847-41e8-add1-087d6aa2537a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:45:14.625 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66e916bf-0847-41e8-add1-087d6aa2537a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:45:14.626 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66e916bf-0847-41e8-add1-087d6aa2537a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:14.757 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66e916bf-0847-41e8-add1-087d6aa2537a] Success to connect to server [localhost:8848] on start up, connectionId = 1756172714639_127.0.0.1_2376
09:45:14.758 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66e916bf-0847-41e8-add1-087d6aa2537a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:45:14.758 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66e916bf-0847-41e8-add1-087d6aa2537a] Notify connected event to listeners.
09:45:14.758 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66e916bf-0847-41e8-add1-087d6aa2537a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001b3be4f36d8
09:45:14.841 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:45:14.871 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:45:15.032 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 29.745 seconds (JVM running for 45.491)
09:45:15.049 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:45:15.050 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:45:15.051 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:45:15.352 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66e916bf-0847-41e8-add1-087d6aa2537a] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:45:15.367 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66e916bf-0847-41e8-add1-087d6aa2537a] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:47:10.949 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:47:12.518 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66e916bf-0847-41e8-add1-087d6aa2537a] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:47:12.518 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [66e916bf-0847-41e8-add1-087d6aa2537a] Ack server push request, request = NotifySubscriberRequest, requestId = 13
09:47:12.701 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:47:12.702 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:47:12.816 [http-nio-9600-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:47:12.816 [http-nio-9600-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
16:34:09.426 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:34:09.442 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:34:09.768 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:34:09.768 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@481cd02b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:34:09.768 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756172714639_127.0.0.1_2376
16:34:09.774 [nacos-grpc-client-executor-5088] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756172714639_127.0.0.1_2376]Ignore complete event,isRunning:false,isAbandon=false
16:34:09.777 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@57485b66[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 5089]
16:34:09.930 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:34:09.943 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
16:34:09.943 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
16:34:09.943 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:34:09.943 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:34:09.943 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:34:09.943 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:34:09.943 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:34:36.738 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:34:37.323 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ee56fe17-18ba-4c92-a873-1bc8150ec8bf_config-0
16:34:37.380 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 3 keys and 6 values 
16:34:37.417 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 4 keys and 9 values 
16:34:37.417 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
16:34:37.433 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
16:34:37.433 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 7 values 
16:34:37.448 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
16:34:37.448 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee56fe17-18ba-4c92-a873-1bc8150ec8bf_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:34:37.448 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee56fe17-18ba-4c92-a873-1bc8150ec8bf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000252c63b6d38
16:34:37.448 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee56fe17-18ba-4c92-a873-1bc8150ec8bf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000252c63b6f58
16:34:37.448 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee56fe17-18ba-4c92-a873-1bc8150ec8bf_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:34:37.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee56fe17-18ba-4c92-a873-1bc8150ec8bf_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:34:37.455 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee56fe17-18ba-4c92-a873-1bc8150ec8bf_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:34:38.135 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee56fe17-18ba-4c92-a873-1bc8150ec8bf_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756197277941_127.0.0.1_2852
16:34:38.135 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee56fe17-18ba-4c92-a873-1bc8150ec8bf_config-0] Notify connected event to listeners.
16:34:38.135 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee56fe17-18ba-4c92-a873-1bc8150ec8bf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:34:38.135 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee56fe17-18ba-4c92-a873-1bc8150ec8bf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000252c64f0ad8
16:34:38.246 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:34:40.664 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
16:34:40.664 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:34:40.664 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:34:40.777 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:34:41.401 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:34:41.401 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:34:41.401 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:34:46.377 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:34:48.348 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 58a79d48-76a8-4169-816a-e23f3ce401d1
16:34:48.348 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58a79d48-76a8-4169-816a-e23f3ce401d1] RpcClient init label, labels = {module=naming, source=sdk}
16:34:48.358 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58a79d48-76a8-4169-816a-e23f3ce401d1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:34:48.358 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58a79d48-76a8-4169-816a-e23f3ce401d1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:34:48.358 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58a79d48-76a8-4169-816a-e23f3ce401d1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:34:48.358 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58a79d48-76a8-4169-816a-e23f3ce401d1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:34:48.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58a79d48-76a8-4169-816a-e23f3ce401d1] Success to connect to server [localhost:8848] on start up, connectionId = 1756197288366_127.0.0.1_2865
16:34:48.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58a79d48-76a8-4169-816a-e23f3ce401d1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:34:48.476 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58a79d48-76a8-4169-816a-e23f3ce401d1] Notify connected event to listeners.
16:34:48.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58a79d48-76a8-4169-816a-e23f3ce401d1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000252c64f0ad8
16:34:48.507 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
16:34:48.522 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
16:34:48.615 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.313 seconds (JVM running for 14.118)
16:34:48.624 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
16:34:48.624 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
16:34:48.624 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
16:34:49.005 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58a79d48-76a8-4169-816a-e23f3ce401d1] Receive server push request, request = NotifySubscriberRequest, requestId = 21
16:34:49.025 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58a79d48-76a8-4169-816a-e23f3ce401d1] Ack server push request, request = NotifySubscriberRequest, requestId = 21
16:39:38.251 [http-nio-9600-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:39:40.369 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:39:40.370 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
20:31:06.367 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:31:06.371 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:31:06.694 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:31:06.694 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5ac000d1[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:31:06.694 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756197288366_127.0.0.1_2865
20:31:06.696 [nacos-grpc-client-executor-2842] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756197288366_127.0.0.1_2865]Ignore complete event,isRunning:false,isAbandon=false
20:31:06.700 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5b79859f[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2843]
20:31:06.918 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:31:06.929 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
20:31:06.937 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
20:31:06.937 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:31:06.939 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:31:06.940 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
