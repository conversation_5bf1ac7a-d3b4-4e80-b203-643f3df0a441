09:22:48.117 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:22:49.117 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b4e43dc6-19c3-4c6b-88fe-8956915b6a7f_config-0
09:22:49.216 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 42 ms to scan 1 urls, producing 3 keys and 6 values 
09:22:49.263 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 4 keys and 9 values 
09:22:49.271 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 3 keys and 10 values 
09:22:49.280 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 6 ms to scan 1 urls, producing 1 keys and 5 values 
09:22:49.289 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 1 keys and 7 values 
09:22:49.296 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
09:22:49.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4e43dc6-19c3-4c6b-88fe-8956915b6a7f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:22:49.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4e43dc6-19c3-4c6b-88fe-8956915b6a7f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002c1843b71c0
09:22:49.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4e43dc6-19c3-4c6b-88fe-8956915b6a7f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000002c1843b73e0
09:22:49.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4e43dc6-19c3-4c6b-88fe-8956915b6a7f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:22:49.303 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4e43dc6-19c3-4c6b-88fe-8956915b6a7f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:22:49.312 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4e43dc6-19c3-4c6b-88fe-8956915b6a7f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:22:50.370 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4e43dc6-19c3-4c6b-88fe-8956915b6a7f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1757294570067_127.0.0.1_4743
09:22:50.374 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4e43dc6-19c3-4c6b-88fe-8956915b6a7f_config-0] Notify connected event to listeners.
09:22:50.375 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4e43dc6-19c3-4c6b-88fe-8956915b6a7f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:22:50.377 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4e43dc6-19c3-4c6b-88fe-8956915b6a7f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002c1844f0fb0
09:22:50.623 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:22:55.288 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:22:55.290 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:22:55.291 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:22:55.530 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:22:56.413 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:22:56.414 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:22:56.415 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:23:05.261 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:23:08.600 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 68f3c7a0-4d3f-4111-8fa4-78aa7624cda4
09:23:08.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68f3c7a0-4d3f-4111-8fa4-78aa7624cda4] RpcClient init label, labels = {module=naming, source=sdk}
09:23:08.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68f3c7a0-4d3f-4111-8fa4-78aa7624cda4] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:23:08.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68f3c7a0-4d3f-4111-8fa4-78aa7624cda4] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:23:08.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68f3c7a0-4d3f-4111-8fa4-78aa7624cda4] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:23:08.604 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68f3c7a0-4d3f-4111-8fa4-78aa7624cda4] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:23:08.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68f3c7a0-4d3f-4111-8fa4-78aa7624cda4] Success to connect to server [localhost:8848] on start up, connectionId = 1757294588615_127.0.0.1_4787
09:23:08.741 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68f3c7a0-4d3f-4111-8fa4-78aa7624cda4] Notify connected event to listeners.
09:23:08.742 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68f3c7a0-4d3f-4111-8fa4-78aa7624cda4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:23:08.742 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68f3c7a0-4d3f-4111-8fa4-78aa7624cda4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002c1844f0fb0
09:23:08.854 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:23:08.906 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:23:09.035 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 21.715 seconds (JVM running for 23.325)
09:23:09.055 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:23:09.055 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:23:09.056 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:23:09.358 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68f3c7a0-4d3f-4111-8fa4-78aa7624cda4] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:23:09.386 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [68f3c7a0-4d3f-4111-8fa4-78aa7624cda4] Ack server push request, request = NotifySubscriberRequest, requestId = 5
17:40:15.270 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:40:15.274 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:40:15.605 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:40:15.605 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@67d4af78[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:40:15.605 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1757294588615_127.0.0.1_4787
17:40:15.608 [nacos-grpc-client-executor-5971] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1757294588615_127.0.0.1_4787]Ignore complete event,isRunning:false,isAbandon=false
17:40:15.611 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4e8da5a1[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 5972]
17:40:15.652 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:40:15.657 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:40:15.659 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:40:15.659 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
