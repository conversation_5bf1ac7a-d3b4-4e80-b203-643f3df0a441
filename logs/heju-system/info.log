09:02:13.403 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:02:13.407 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:02:13.736 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:02:13.737 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4670e82e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:02:13.737 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756257806611_127.0.0.1_14555
09:02:13.740 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2954d2df[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 17519]
09:02:13.740 [nacos-grpc-client-executor-17519] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756257806611_127.0.0.1_14555]Ignore complete event,isRunning:false,isAbandon=false
09:02:13.915 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:02:13.934 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
09:02:13.934 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
09:02:13.934 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
09:02:13.934 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
09:02:13.934 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:02:13.934 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:02:13.934 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:35:28.989 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:35:30.077 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9913f8ce-625b-49bc-99b5-7b032b20833e_config-0
09:35:30.176 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 35 ms to scan 1 urls, producing 3 keys and 6 values 
09:35:30.223 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
09:35:30.232 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
09:35:30.241 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
09:35:30.251 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:35:30.262 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
09:35:30.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9913f8ce-625b-49bc-99b5-7b032b20833e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:35:30.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9913f8ce-625b-49bc-99b5-7b032b20833e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002b7d639e8d8
09:35:30.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9913f8ce-625b-49bc-99b5-7b032b20833e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002b7d639eaf8
09:35:30.267 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9913f8ce-625b-49bc-99b5-7b032b20833e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:35:30.267 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9913f8ce-625b-49bc-99b5-7b032b20833e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:35:30.277 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9913f8ce-625b-49bc-99b5-7b032b20833e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:35:31.706 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9913f8ce-625b-49bc-99b5-7b032b20833e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756344931414_127.0.0.1_9615
09:35:31.707 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9913f8ce-625b-49bc-99b5-7b032b20833e_config-0] Notify connected event to listeners.
09:35:31.707 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9913f8ce-625b-49bc-99b5-7b032b20833e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:35:31.708 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9913f8ce-625b-49bc-99b5-7b032b20833e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002b7d6518668
09:35:31.947 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:35:38.152 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:35:38.153 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:35:38.153 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:35:38.429 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:35:39.360 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:35:39.361 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:35:39.362 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:35:47.601 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:35:50.799 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5f422e36-5e6f-491c-acad-0f8f6774e2fd
09:35:50.799 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f422e36-5e6f-491c-acad-0f8f6774e2fd] RpcClient init label, labels = {module=naming, source=sdk}
09:35:50.803 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f422e36-5e6f-491c-acad-0f8f6774e2fd] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:35:50.803 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f422e36-5e6f-491c-acad-0f8f6774e2fd] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:35:50.803 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f422e36-5e6f-491c-acad-0f8f6774e2fd] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:35:50.804 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f422e36-5e6f-491c-acad-0f8f6774e2fd] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:35:50.938 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f422e36-5e6f-491c-acad-0f8f6774e2fd] Success to connect to server [localhost:8848] on start up, connectionId = 1756344950814_127.0.0.1_9705
09:35:50.938 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f422e36-5e6f-491c-acad-0f8f6774e2fd] Notify connected event to listeners.
09:35:50.938 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f422e36-5e6f-491c-acad-0f8f6774e2fd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:35:50.938 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f422e36-5e6f-491c-acad-0f8f6774e2fd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002b7d6518668
09:35:51.006 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:35:51.041 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:35:51.168 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 22.906 seconds (JVM running for 25.696)
09:35:51.184 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:35:51.185 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:35:51.186 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:35:51.511 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f422e36-5e6f-491c-acad-0f8f6774e2fd] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:35:51.528 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:35:51.531 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f422e36-5e6f-491c-acad-0f8f6774e2fd] Ack server push request, request = NotifySubscriberRequest, requestId = 5
09:41:25.579 [nacos-grpc-client-executor-77] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f422e36-5e6f-491c-acad-0f8f6774e2fd] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:41:25.579 [nacos-grpc-client-executor-77] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5f422e36-5e6f-491c-acad-0f8f6774e2fd] Ack server push request, request = NotifySubscriberRequest, requestId = 12
09:41:26.646 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:41:26.646 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:41:26.693 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:41:26.694 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
11:05:30.810 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:05:30.819 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:05:31.172 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:05:31.172 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7763c62b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:05:31.172 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756344950814_127.0.0.1_9705
11:05:31.172 [nacos-grpc-client-executor-1090] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756344950814_127.0.0.1_9705]Ignore complete event,isRunning:false,isAbandon=false
11:05:31.187 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@782ab5cb[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1091]
11:05:31.336 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:05:31.347 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
11:05:31.359 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
11:05:31.362 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:05:31.364 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:05:31.366 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:05:31.368 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:05:31.369 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:06:01.379 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:06:02.184 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 91ecba43-de8f-4d97-98f6-cf9e2b53cbb7_config-0
11:06:02.250 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 34 ms to scan 1 urls, producing 3 keys and 6 values 
11:06:02.281 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 4 keys and 9 values 
11:06:02.296 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
11:06:02.312 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
11:06:02.320 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
11:06:02.328 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
11:06:02.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91ecba43-de8f-4d97-98f6-cf9e2b53cbb7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:06:02.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91ecba43-de8f-4d97-98f6-cf9e2b53cbb7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002508a3b8b08
11:06:02.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91ecba43-de8f-4d97-98f6-cf9e2b53cbb7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000002508a3b8d28
11:06:02.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91ecba43-de8f-4d97-98f6-cf9e2b53cbb7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:06:02.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91ecba43-de8f-4d97-98f6-cf9e2b53cbb7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:06:02.343 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91ecba43-de8f-4d97-98f6-cf9e2b53cbb7_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:06:03.298 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91ecba43-de8f-4d97-98f6-cf9e2b53cbb7_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756350363059_127.0.0.1_14992
11:06:03.299 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91ecba43-de8f-4d97-98f6-cf9e2b53cbb7_config-0] Notify connected event to listeners.
11:06:03.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91ecba43-de8f-4d97-98f6-cf9e2b53cbb7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:06:03.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91ecba43-de8f-4d97-98f6-cf9e2b53cbb7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002508a4f0ad8
11:06:03.578 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:06:07.769 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
11:06:07.769 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:06:07.769 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:06:07.948 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:06:08.744 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:06:08.751 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:06:08.751 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:06:17.531 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:06:20.740 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7ba5d610-ffba-44be-9808-651b07c89b37
11:06:20.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ba5d610-ffba-44be-9808-651b07c89b37] RpcClient init label, labels = {module=naming, source=sdk}
11:06:20.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ba5d610-ffba-44be-9808-651b07c89b37] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:06:20.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ba5d610-ffba-44be-9808-651b07c89b37] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:06:20.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ba5d610-ffba-44be-9808-651b07c89b37] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:06:20.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ba5d610-ffba-44be-9808-651b07c89b37] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:06:20.874 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ba5d610-ffba-44be-9808-651b07c89b37] Success to connect to server [localhost:8848] on start up, connectionId = 1756350380761_127.0.0.1_1037
11:06:20.876 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ba5d610-ffba-44be-9808-651b07c89b37] Notify connected event to listeners.
11:06:20.876 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ba5d610-ffba-44be-9808-651b07c89b37] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:06:20.876 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ba5d610-ffba-44be-9808-651b07c89b37] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002508a4f0ad8
11:06:20.963 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
11:06:21.012 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
11:06:21.159 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.413 seconds (JVM running for 22.709)
11:06:21.169 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
11:06:21.176 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
11:06:21.176 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
11:06:21.489 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ba5d610-ffba-44be-9808-651b07c89b37] Receive server push request, request = NotifySubscriberRequest, requestId = 19
11:06:21.506 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ba5d610-ffba-44be-9808-651b07c89b37] Ack server push request, request = NotifySubscriberRequest, requestId = 19
12:02:48.694 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:02:48.698 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:02:49.039 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:02:49.041 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5cf4b374[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:02:49.041 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756350380761_127.0.0.1_1037
12:02:49.046 [nacos-grpc-client-executor-688] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756350380761_127.0.0.1_1037]Ignore complete event,isRunning:false,isAbandon=false
12:02:49.049 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@459dcb80[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 689]
12:02:49.121 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:02:49.123 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:02:49.143 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:02:49.143 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:02:56.791 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:02:57.661 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b1d1acf3-e5f3-484b-bf2d-f17b7254de4f_config-0
12:02:57.733 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 3 keys and 6 values 
12:02:57.781 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
12:02:57.781 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 3 keys and 10 values 
12:02:57.805 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 1 keys and 5 values 
12:02:57.815 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
12:02:57.815 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
12:02:57.815 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1d1acf3-e5f3-484b-bf2d-f17b7254de4f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:02:57.815 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1d1acf3-e5f3-484b-bf2d-f17b7254de4f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001ea0139dd70
12:02:57.815 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1d1acf3-e5f3-484b-bf2d-f17b7254de4f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001ea0139df90
12:02:57.815 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1d1acf3-e5f3-484b-bf2d-f17b7254de4f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:02:57.815 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1d1acf3-e5f3-484b-bf2d-f17b7254de4f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:02:57.836 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1d1acf3-e5f3-484b-bf2d-f17b7254de4f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:02:58.824 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1d1acf3-e5f3-484b-bf2d-f17b7254de4f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756353778605_127.0.0.1_10384
12:02:58.824 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1d1acf3-e5f3-484b-bf2d-f17b7254de4f_config-0] Notify connected event to listeners.
12:02:58.824 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1d1acf3-e5f3-484b-bf2d-f17b7254de4f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:02:58.824 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b1d1acf3-e5f3-484b-bf2d-f17b7254de4f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001ea01517b88
12:02:59.016 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:03:03.537 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
12:03:03.538 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:03:03.538 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:03:03.837 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:03:04.752 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:03:04.753 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:03:04.753 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:03:12.867 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:03:15.925 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9f93e6fc-bf02-44eb-8d01-3f243979d65b
12:03:15.925 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f93e6fc-bf02-44eb-8d01-3f243979d65b] RpcClient init label, labels = {module=naming, source=sdk}
12:03:15.927 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f93e6fc-bf02-44eb-8d01-3f243979d65b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:03:15.928 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f93e6fc-bf02-44eb-8d01-3f243979d65b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:03:15.928 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f93e6fc-bf02-44eb-8d01-3f243979d65b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:03:15.928 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f93e6fc-bf02-44eb-8d01-3f243979d65b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:03:16.062 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f93e6fc-bf02-44eb-8d01-3f243979d65b] Success to connect to server [localhost:8848] on start up, connectionId = 1756353795937_127.0.0.1_10502
12:03:16.062 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f93e6fc-bf02-44eb-8d01-3f243979d65b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:03:16.062 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f93e6fc-bf02-44eb-8d01-3f243979d65b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001ea01517b88
12:03:16.064 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f93e6fc-bf02-44eb-8d01-3f243979d65b] Notify connected event to listeners.
12:03:16.135 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
12:03:16.175 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
12:03:16.322 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 20.236 seconds (JVM running for 21.354)
12:03:16.339 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
12:03:16.339 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
12:03:16.339 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
12:03:16.627 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f93e6fc-bf02-44eb-8d01-3f243979d65b] Receive server push request, request = NotifySubscriberRequest, requestId = 25
12:03:16.647 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f93e6fc-bf02-44eb-8d01-3f243979d65b] Ack server push request, request = NotifySubscriberRequest, requestId = 25
12:03:16.892 [RMI TCP Connection(8)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:39:17.507 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:39:17.507 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:00:28.208 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:00:28.212 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:00:28.534 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:00:28.534 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@72f002c2[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:00:28.534 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756353795937_127.0.0.1_10502
14:00:28.536 [nacos-grpc-client-executor-1414] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756353795937_127.0.0.1_10502]Ignore complete event,isRunning:false,isAbandon=false
14:00:28.538 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5f333f85[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1415]
14:00:28.699 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:00:28.703 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:00:28.706 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:00:28.706 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:00:28.706 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:00:28.706 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:00:34.769 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:00:35.303 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f4f55d90-90c5-4219-9309-0c50842d5812_config-0
14:00:35.348 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 3 keys and 6 values 
14:00:35.373 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
14:00:35.379 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
14:00:35.386 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
14:00:35.394 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
14:00:35.400 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
14:00:35.402 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4f55d90-90c5-4219-9309-0c50842d5812_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:00:35.403 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4f55d90-90c5-4219-9309-0c50842d5812_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000012b8939eaf8
14:00:35.403 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4f55d90-90c5-4219-9309-0c50842d5812_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000012b8939ed18
14:00:35.403 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4f55d90-90c5-4219-9309-0c50842d5812_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:00:35.404 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4f55d90-90c5-4219-9309-0c50842d5812_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:00:35.410 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4f55d90-90c5-4219-9309-0c50842d5812_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:00:36.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4f55d90-90c5-4219-9309-0c50842d5812_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756360835914_127.0.0.1_13218
14:00:36.093 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4f55d90-90c5-4219-9309-0c50842d5812_config-0] Notify connected event to listeners.
14:00:36.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4f55d90-90c5-4219-9309-0c50842d5812_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:00:36.095 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4f55d90-90c5-4219-9309-0c50842d5812_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000012b89518d48
14:00:36.178 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:00:38.649 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
14:00:38.649 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:00:38.650 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:00:38.765 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:00:39.433 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:00:39.434 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:00:39.434 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:00:44.594 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:00:46.613 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bc4ca311-14d9-403e-9684-92e153ac0383
14:00:46.613 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4ca311-14d9-403e-9684-92e153ac0383] RpcClient init label, labels = {module=naming, source=sdk}
14:00:46.614 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4ca311-14d9-403e-9684-92e153ac0383] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:00:46.614 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4ca311-14d9-403e-9684-92e153ac0383] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:00:46.614 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4ca311-14d9-403e-9684-92e153ac0383] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:00:46.615 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4ca311-14d9-403e-9684-92e153ac0383] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:00:46.743 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4ca311-14d9-403e-9684-92e153ac0383] Success to connect to server [localhost:8848] on start up, connectionId = 1756360846623_127.0.0.1_13247
14:00:46.743 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4ca311-14d9-403e-9684-92e153ac0383] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:00:46.744 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4ca311-14d9-403e-9684-92e153ac0383] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000012b89518d48
14:00:46.744 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4ca311-14d9-403e-9684-92e153ac0383] Notify connected event to listeners.
14:00:46.785 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
14:00:46.806 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
14:00:46.903 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 12.663 seconds (JVM running for 13.591)
14:00:46.928 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
14:00:46.928 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
14:00:46.928 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
14:00:47.359 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4ca311-14d9-403e-9684-92e153ac0383] Receive server push request, request = NotifySubscriberRequest, requestId = 34
14:00:47.366 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:00:47.376 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bc4ca311-14d9-403e-9684-92e153ac0383] Ack server push request, request = NotifySubscriberRequest, requestId = 34
14:01:20.699 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:01:20.699 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
