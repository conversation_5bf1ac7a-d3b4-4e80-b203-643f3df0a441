09:22:47.978 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:23:01.818 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6184d9cc-439c-4e4e-a720-0d26fa5cc8de_config-0
09:23:02.119 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 145 ms to scan 1 urls, producing 3 keys and 6 values 
09:23:02.336 [main] INFO  o.r.Reflections - [scan,232] - <PERSON>flections took 35 ms to scan 1 urls, producing 4 keys and 9 values 
09:23:02.384 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 41 ms to scan 1 urls, producing 3 keys and 10 values 
09:23:02.423 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 1 keys and 5 values 
09:23:02.453 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 21 ms to scan 1 urls, producing 1 keys and 7 values 
09:23:02.491 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 2 keys and 8 values 
09:23:02.503 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6184d9cc-439c-4e4e-a720-0d26fa5cc8de_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:23:02.505 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6184d9cc-439c-4e4e-a720-0d26fa5cc8de_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000188533b8b08
09:23:02.506 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6184d9cc-439c-4e4e-a720-0d26fa5cc8de_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000188533b8d28
09:23:02.509 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6184d9cc-439c-4e4e-a720-0d26fa5cc8de_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:23:02.512 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6184d9cc-439c-4e4e-a720-0d26fa5cc8de_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:23:02.547 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6184d9cc-439c-4e4e-a720-0d26fa5cc8de_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:23:09.420 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6184d9cc-439c-4e4e-a720-0d26fa5cc8de_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:23:09.445 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6184d9cc-439c-4e4e-a720-0d26fa5cc8de_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:23:09.485 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6184d9cc-439c-4e4e-a720-0d26fa5cc8de_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:23:09.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6184d9cc-439c-4e4e-a720-0d26fa5cc8de_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:23:09.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6184d9cc-439c-4e4e-a720-0d26fa5cc8de_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000188534c8650
09:23:09.624 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6184d9cc-439c-4e4e-a720-0d26fa5cc8de_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:23:09.845 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6184d9cc-439c-4e4e-a720-0d26fa5cc8de_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:23:10.168 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6184d9cc-439c-4e4e-a720-0d26fa5cc8de_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:23:10.602 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6184d9cc-439c-4e4e-a720-0d26fa5cc8de_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:23:11.123 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6184d9cc-439c-4e4e-a720-0d26fa5cc8de_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:23:11.669 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:23:11.755 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6184d9cc-439c-4e4e-a720-0d26fa5cc8de_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:23:12.480 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6184d9cc-439c-4e4e-a720-0d26fa5cc8de_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:23:13.306 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6184d9cc-439c-4e4e-a720-0d26fa5cc8de_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:23:14.796 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6184d9cc-439c-4e4e-a720-0d26fa5cc8de_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:23:15.863 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6184d9cc-439c-4e4e-a720-0d26fa5cc8de_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:23:17.050 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6184d9cc-439c-4e4e-a720-0d26fa5cc8de_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:23:17.136 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:23:17.136 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:23:17.136 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:23:17.293 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:23:18.155 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:23:18.157 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:23:18.157 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:23:18.437 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6184d9cc-439c-4e4e-a720-0d26fa5cc8de_config-0] Success to connect a server [localhost:8848], connectionId = 1756257798304_127.0.0.1_14544
09:23:18.437 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6184d9cc-439c-4e4e-a720-0d26fa5cc8de_config-0] Notify connected event to listeners.
09:23:24.139 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:23:26.590 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ba714dfe-09d1-46f3-b2e9-8ca9ed64ec07
09:23:26.591 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba714dfe-09d1-46f3-b2e9-8ca9ed64ec07] RpcClient init label, labels = {module=naming, source=sdk}
09:23:26.592 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba714dfe-09d1-46f3-b2e9-8ca9ed64ec07] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:23:26.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba714dfe-09d1-46f3-b2e9-8ca9ed64ec07] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:23:26.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba714dfe-09d1-46f3-b2e9-8ca9ed64ec07] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:23:26.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba714dfe-09d1-46f3-b2e9-8ca9ed64ec07] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:23:27.162 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba714dfe-09d1-46f3-b2e9-8ca9ed64ec07] Success to connect to server [localhost:8848] on start up, connectionId = 1756257806611_127.0.0.1_14555
09:23:27.162 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba714dfe-09d1-46f3-b2e9-8ca9ed64ec07] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:23:27.162 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba714dfe-09d1-46f3-b2e9-8ca9ed64ec07] Notify connected event to listeners.
09:23:27.162 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba714dfe-09d1-46f3-b2e9-8ca9ed64ec07] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000188534c8650
09:23:27.417 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:23:27.471 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:23:27.593 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 43.651 seconds (JVM running for 51.105)
09:23:27.606 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:23:27.606 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:23:27.607 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:23:29.962 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba714dfe-09d1-46f3-b2e9-8ca9ed64ec07] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:23:29.962 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba714dfe-09d1-46f3-b2e9-8ca9ed64ec07] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:35:24.457 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:35:26.028 [nacos-grpc-client-executor-162] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba714dfe-09d1-46f3-b2e9-8ca9ed64ec07] Receive server push request, request = NotifySubscriberRequest, requestId = 16
09:35:26.028 [nacos-grpc-client-executor-162] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba714dfe-09d1-46f3-b2e9-8ca9ed64ec07] Ack server push request, request = NotifySubscriberRequest, requestId = 16
09:35:26.110 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:35:26.110 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:35:26.129 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:35:26.130 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
