09:23:53.697 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:23:54.525 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 54c2da45-c113-40ab-ac0e-b1179431ce72_config-0
09:23:54.609 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 34 ms to scan 1 urls, producing 3 keys and 6 values 
09:23:54.644 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 4 keys and 9 values 
09:23:54.656 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:23:54.668 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:23:54.678 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 1 keys and 7 values 
09:23:54.687 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:23:54.689 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54c2da45-c113-40ab-ac0e-b1179431ce72_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:23:54.690 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54c2da45-c113-40ab-ac0e-b1179431ce72_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002aa1539eaf8
09:23:54.690 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54c2da45-c113-40ab-ac0e-b1179431ce72_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002aa1539ed18
09:23:54.690 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54c2da45-c113-40ab-ac0e-b1179431ce72_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:23:54.691 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54c2da45-c113-40ab-ac0e-b1179431ce72_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:23:54.697 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54c2da45-c113-40ab-ac0e-b1179431ce72_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:23:55.578 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54c2da45-c113-40ab-ac0e-b1179431ce72_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755825835379_127.0.0.1_7955
09:23:55.578 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54c2da45-c113-40ab-ac0e-b1179431ce72_config-0] Notify connected event to listeners.
09:23:55.578 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54c2da45-c113-40ab-ac0e-b1179431ce72_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:23:55.579 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54c2da45-c113-40ab-ac0e-b1179431ce72_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002aa15518ad8
09:23:55.709 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:23:59.575 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:23:59.576 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:23:59.576 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:23:59.704 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:24:00.302 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:24:00.303 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:24:00.303 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:24:07.056 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:24:09.835 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 14caf3ba-6015-463a-b020-ba2a442d66d7
09:24:09.835 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14caf3ba-6015-463a-b020-ba2a442d66d7] RpcClient init label, labels = {module=naming, source=sdk}
09:24:09.837 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14caf3ba-6015-463a-b020-ba2a442d66d7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:24:09.837 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14caf3ba-6015-463a-b020-ba2a442d66d7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:24:09.837 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14caf3ba-6015-463a-b020-ba2a442d66d7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:24:09.837 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14caf3ba-6015-463a-b020-ba2a442d66d7] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:09.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14caf3ba-6015-463a-b020-ba2a442d66d7] Success to connect to server [localhost:8848] on start up, connectionId = 1755825849847_127.0.0.1_7974
09:24:09.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14caf3ba-6015-463a-b020-ba2a442d66d7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:09.973 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14caf3ba-6015-463a-b020-ba2a442d66d7] Notify connected event to listeners.
09:24:09.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14caf3ba-6015-463a-b020-ba2a442d66d7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002aa15518ad8
09:24:10.092 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:24:10.121 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:24:10.252 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 17.53 seconds (JVM running for 19.883)
09:24:10.266 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:24:10.267 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:24:10.267 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:24:10.495 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:24:10.554 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14caf3ba-6015-463a-b020-ba2a442d66d7] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:24:10.569 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14caf3ba-6015-463a-b020-ba2a442d66d7] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:26:47.444 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14caf3ba-6015-463a-b020-ba2a442d66d7] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:26:47.444 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [14caf3ba-6015-463a-b020-ba2a442d66d7] Ack server push request, request = NotifySubscriberRequest, requestId = 13
09:26:47.810 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:26:47.810 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:26:47.862 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:26:47.862 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
13:57:27.739 [nacos-grpc-client-executor-3297] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54c2da45-c113-40ab-ac0e-b1179431ce72_config-0] Receive server push request, request = ConfigChangeNotifyRequest, requestId = 15
13:57:27.739 [nacos-grpc-client-executor-3297] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [54c2da45-c113-40ab-ac0e-b1179431ce72_config-0] Ack server push request, request = ConfigChangeNotifyRequest, requestId = 15
13:58:16.849 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:58:16.853 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:58:17.196 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:58:17.197 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@274bc5b1[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:58:17.197 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755825849847_127.0.0.1_7974
13:58:17.200 [nacos-grpc-client-executor-3308] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755825849847_127.0.0.1_7974]Ignore complete event,isRunning:false,isAbandon=false
13:58:17.203 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@********[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 3309]
13:58:17.371 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:58:17.381 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
13:58:17.384 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
13:58:17.384 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:58:17.385 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:58:17.385 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:58:17.387 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:58:17.387 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:58:51.571 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:58:53.648 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7eba2bf8-d75f-4861-a4bc-a53df5070e76_config-0
13:58:53.839 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 80 ms to scan 1 urls, producing 3 keys and 6 values 
13:58:53.957 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 4 keys and 9 values 
13:58:53.983 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 3 keys and 10 values 
13:58:54.016 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 1 keys and 5 values 
13:58:54.039 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
13:58:54.056 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
13:58:54.073 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7eba2bf8-d75f-4861-a4bc-a53df5070e76_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:58:54.073 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7eba2bf8-d75f-4861-a4bc-a53df5070e76_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000023ec039c730
13:58:54.073 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7eba2bf8-d75f-4861-a4bc-a53df5070e76_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000023ec039c950
13:58:54.077 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7eba2bf8-d75f-4861-a4bc-a53df5070e76_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:58:54.077 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7eba2bf8-d75f-4861-a4bc-a53df5070e76_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:58:54.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7eba2bf8-d75f-4861-a4bc-a53df5070e76_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:58:56.488 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7eba2bf8-d75f-4861-a4bc-a53df5070e76_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755842336122_127.0.0.1_1243
13:58:56.489 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7eba2bf8-d75f-4861-a4bc-a53df5070e76_config-0] Notify connected event to listeners.
13:58:56.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7eba2bf8-d75f-4861-a4bc-a53df5070e76_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:58:56.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7eba2bf8-d75f-4861-a4bc-a53df5070e76_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000023ec0514200
13:58:56.739 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:59:05.305 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
13:59:05.305 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:59:05.305 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:59:05.522 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:59:06.434 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:59:06.439 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:59:06.439 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:59:13.539 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:59:16.189 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 270ce379-2687-426c-864d-6593c6b3e566
13:59:16.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [270ce379-2687-426c-864d-6593c6b3e566] RpcClient init label, labels = {module=naming, source=sdk}
13:59:16.194 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [270ce379-2687-426c-864d-6593c6b3e566] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:59:16.194 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [270ce379-2687-426c-864d-6593c6b3e566] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:59:16.194 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [270ce379-2687-426c-864d-6593c6b3e566] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:59:16.194 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [270ce379-2687-426c-864d-6593c6b3e566] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:59:16.321 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [270ce379-2687-426c-864d-6593c6b3e566] Success to connect to server [localhost:8848] on start up, connectionId = 1755842356194_127.0.0.1_1266
13:59:16.322 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [270ce379-2687-426c-864d-6593c6b3e566] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:59:16.322 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [270ce379-2687-426c-864d-6593c6b3e566] Notify connected event to listeners.
13:59:16.322 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [270ce379-2687-426c-864d-6593c6b3e566] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000023ec0514200
13:59:16.355 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
13:59:16.389 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
13:59:16.489 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 26.706 seconds (JVM running for 33.394)
13:59:16.504 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
13:59:16.504 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
13:59:16.505 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
13:59:16.871 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [270ce379-2687-426c-864d-6593c6b3e566] Receive server push request, request = NotifySubscriberRequest, requestId = 10
13:59:16.872 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [270ce379-2687-426c-864d-6593c6b3e566] Ack server push request, request = NotifySubscriberRequest, requestId = 10
13:59:18.492 [http-nio-9600-exec-3] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:59:19.572 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
13:59:19.572 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:59:19.587 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
13:59:19.587 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
13:59:19.590 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
13:59:19.594 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
13:59:19.595 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:59:19.595 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:59:19.597 [http-nio-9600-exec-5] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:59:19.597 [http-nio-9600-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:05:23.913 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:05:23.913 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:05:24.235 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:05:24.236 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@14c451fb[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:05:24.236 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755842356194_127.0.0.1_1266
17:05:24.237 [nacos-grpc-client-executor-1970] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755842356194_127.0.0.1_1266]Ignore complete event,isRunning:false,isAbandon=false
17:05:24.240 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7030331c[Running, pool size = 7, active threads = 0, queued tasks = 0, completed tasks = 1971]
17:05:24.377 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:05:24.378 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
17:05:24.379 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
17:05:24.379 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:05:24.379 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:05:24.379 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:05:31.239 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:05:31.834 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fe046ca7-0045-4471-8ced-b850d2538874_config-0
17:05:31.890 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 3 keys and 6 values 
17:05:31.918 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
17:05:31.926 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
17:05:31.933 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
17:05:31.941 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
17:05:31.949 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
17:05:31.951 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe046ca7-0045-4471-8ced-b850d2538874_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:05:31.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe046ca7-0045-4471-8ced-b850d2538874_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000226cb39f660
17:05:31.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe046ca7-0045-4471-8ced-b850d2538874_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000226cb39f880
17:05:31.953 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe046ca7-0045-4471-8ced-b850d2538874_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:05:31.953 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe046ca7-0045-4471-8ced-b850d2538874_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:05:31.959 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe046ca7-0045-4471-8ced-b850d2538874_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:05:32.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe046ca7-0045-4471-8ced-b850d2538874_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755853532491_127.0.0.1_11328
17:05:32.683 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe046ca7-0045-4471-8ced-b850d2538874_config-0] Notify connected event to listeners.
17:05:32.683 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe046ca7-0045-4471-8ced-b850d2538874_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:05:32.684 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe046ca7-0045-4471-8ced-b850d2538874_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000226cb518fb0
17:05:32.770 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:05:35.414 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:05:35.414 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:05:35.414 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:05:35.539 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:05:36.040 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:05:36.041 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:05:36.041 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:05:41.433 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:05:43.520 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9588e5a0-281e-4bf6-b31a-18047d1c48d2
17:05:43.520 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9588e5a0-281e-4bf6-b31a-18047d1c48d2] RpcClient init label, labels = {module=naming, source=sdk}
17:05:43.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9588e5a0-281e-4bf6-b31a-18047d1c48d2] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:05:43.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9588e5a0-281e-4bf6-b31a-18047d1c48d2] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:05:43.522 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9588e5a0-281e-4bf6-b31a-18047d1c48d2] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:05:43.522 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9588e5a0-281e-4bf6-b31a-18047d1c48d2] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:05:43.652 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9588e5a0-281e-4bf6-b31a-18047d1c48d2] Success to connect to server [localhost:8848] on start up, connectionId = 1755853543529_127.0.0.1_11337
17:05:43.652 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9588e5a0-281e-4bf6-b31a-18047d1c48d2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:05:43.653 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9588e5a0-281e-4bf6-b31a-18047d1c48d2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000226cb518fb0
17:05:43.652 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9588e5a0-281e-4bf6-b31a-18047d1c48d2] Notify connected event to listeners.
17:05:43.691 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:05:43.714 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:05:43.808 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 13.136 seconds (JVM running for 14.086)
17:05:43.819 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:05:43.819 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:05:43.820 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:05:44.213 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9588e5a0-281e-4bf6-b31a-18047d1c48d2] Receive server push request, request = NotifySubscriberRequest, requestId = 17
17:05:44.225 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9588e5a0-281e-4bf6-b31a-18047d1c48d2] Ack server push request, request = NotifySubscriberRequest, requestId = 17
17:05:44.275 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:07:03.113 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:07:03.123 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:26:55.686 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:26:55.689 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:26:56.019 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:26:56.019 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4f81c45[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:26:56.019 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755853543529_127.0.0.1_11337
17:26:56.023 [nacos-grpc-client-executor-225] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755853543529_127.0.0.1_11337]Ignore complete event,isRunning:false,isAbandon=false
17:26:56.024 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@70bd783a[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 226]
17:26:56.185 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:26:56.188 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:26:56.191 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:26:56.192 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:26:56.193 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:26:56.193 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:27:01.932 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:27:02.485 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ca1bc9ff-f36c-4c65-9e73-d83b2dac2b28_config-0
17:27:02.534 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 3 keys and 6 values 
17:27:02.570 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
17:27:02.576 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
17:27:02.583 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
17:27:02.589 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
17:27:02.600 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
17:27:02.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca1bc9ff-f36c-4c65-9e73-d83b2dac2b28_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:27:02.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca1bc9ff-f36c-4c65-9e73-d83b2dac2b28_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001b0e43ce480
17:27:02.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca1bc9ff-f36c-4c65-9e73-d83b2dac2b28_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001b0e43ce6a0
17:27:02.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca1bc9ff-f36c-4c65-9e73-d83b2dac2b28_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:27:02.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca1bc9ff-f36c-4c65-9e73-d83b2dac2b28_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:27:02.609 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca1bc9ff-f36c-4c65-9e73-d83b2dac2b28_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:27:03.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca1bc9ff-f36c-4c65-9e73-d83b2dac2b28_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755854823109_127.0.0.1_12554
17:27:03.300 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca1bc9ff-f36c-4c65-9e73-d83b2dac2b28_config-0] Notify connected event to listeners.
17:27:03.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca1bc9ff-f36c-4c65-9e73-d83b2dac2b28_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:27:03.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ca1bc9ff-f36c-4c65-9e73-d83b2dac2b28_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001b0e4508228
17:27:03.387 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:27:05.950 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:27:05.950 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:27:05.950 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:27:06.064 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:27:07.067 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:27:07.068 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:27:07.068 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:27:12.949 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:27:18.172 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cde25ef8-226d-414d-9214-2eb46b46c23a
17:27:18.174 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cde25ef8-226d-414d-9214-2eb46b46c23a] RpcClient init label, labels = {module=naming, source=sdk}
17:27:18.177 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cde25ef8-226d-414d-9214-2eb46b46c23a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:27:18.177 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cde25ef8-226d-414d-9214-2eb46b46c23a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:27:18.179 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cde25ef8-226d-414d-9214-2eb46b46c23a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:27:18.179 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cde25ef8-226d-414d-9214-2eb46b46c23a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:27:18.377 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cde25ef8-226d-414d-9214-2eb46b46c23a] Success to connect to server [localhost:8848] on start up, connectionId = 1755854838226_127.0.0.1_12581
17:27:18.377 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cde25ef8-226d-414d-9214-2eb46b46c23a] Notify connected event to listeners.
17:27:18.377 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cde25ef8-226d-414d-9214-2eb46b46c23a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:27:18.377 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cde25ef8-226d-414d-9214-2eb46b46c23a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001b0e4508228
17:27:18.469 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:27:18.540 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:27:18.948 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cde25ef8-226d-414d-9214-2eb46b46c23a] Receive server push request, request = NotifySubscriberRequest, requestId = 22
17:27:18.960 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 17.522 seconds (JVM running for 18.35)
17:27:18.986 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cde25ef8-226d-414d-9214-2eb46b46c23a] Ack server push request, request = NotifySubscriberRequest, requestId = 22
17:27:19.013 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:27:19.015 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:27:19.019 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:27:19.270 [RMI TCP Connection(8)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:27:28.236 [http-nio-9600-exec-3] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:27:28.252 [http-nio-9600-exec-3] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:27:28.252 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
17:27:28.252 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
17:27:28.256 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:27:28.261 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:27:28.261 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:27:28.262 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
17:27:28.262 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
17:27:28.262 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:03:53.154 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:03:53.157 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:03:53.497 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:03:53.497 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3d3de3dc[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:03:53.497 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755854838226_127.0.0.1_12581
18:03:53.497 [nacos-grpc-client-executor-453] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755854838226_127.0.0.1_12581]Ignore complete event,isRunning:false,isAbandon=false
18:03:53.499 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7bb9356a[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 454]
18:03:53.628 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:03:53.628 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
18:03:53.628 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
18:03:53.628 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:03:53.628 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:03:53.628 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
