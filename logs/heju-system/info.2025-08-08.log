09:18:57.879 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:18:59.009 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0c169c31-dcf5-4953-a4f7-254df653aea1_config-0
09:18:59.121 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 53 ms to scan 1 urls, producing 3 keys and 6 values 
09:18:59.184 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 21 ms to scan 1 urls, producing 4 keys and 9 values 
09:18:59.195 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 3 keys and 10 values 
09:18:59.201 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 1 keys and 5 values 
09:18:59.201 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 0 ms to scan 1 urls, producing 1 keys and 7 values 
09:18:59.224 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
09:18:59.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c169c31-dcf5-4953-a4f7-254df653aea1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:18:59.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c169c31-dcf5-4953-a4f7-254df653aea1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000226843b6d38
09:18:59.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c169c31-dcf5-4953-a4f7-254df653aea1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000226843b6f58
09:18:59.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c169c31-dcf5-4953-a4f7-254df653aea1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:18:59.228 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c169c31-dcf5-4953-a4f7-254df653aea1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:18:59.240 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c169c31-dcf5-4953-a4f7-254df653aea1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:00.744 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c169c31-dcf5-4953-a4f7-254df653aea1_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754615940402_127.0.0.1_12128
09:19:00.744 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c169c31-dcf5-4953-a4f7-254df653aea1_config-0] Notify connected event to listeners.
09:19:00.744 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c169c31-dcf5-4953-a4f7-254df653aea1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:19:00.744 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0c169c31-dcf5-4953-a4f7-254df653aea1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000226844f0ad8
09:19:00.997 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:19:07.043 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
09:19:07.046 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:19:07.046 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:19:07.376 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:19:08.394 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:19:08.396 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:19:08.396 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:19:18.246 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:19:21.979 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 851177b9-3407-431e-b381-b502047487ea
09:19:21.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [851177b9-3407-431e-b381-b502047487ea] RpcClient init label, labels = {module=naming, source=sdk}
09:19:21.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [851177b9-3407-431e-b381-b502047487ea] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:19:21.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [851177b9-3407-431e-b381-b502047487ea] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:19:21.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [851177b9-3407-431e-b381-b502047487ea] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:19:21.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [851177b9-3407-431e-b381-b502047487ea] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:22.122 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [851177b9-3407-431e-b381-b502047487ea] Success to connect to server [localhost:8848] on start up, connectionId = 1754615961995_127.0.0.1_12189
09:19:22.124 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [851177b9-3407-431e-b381-b502047487ea] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:19:22.124 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [851177b9-3407-431e-b381-b502047487ea] Notify connected event to listeners.
09:19:22.124 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [851177b9-3407-431e-b381-b502047487ea] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000226844f0ad8
09:19:22.177 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
09:19:22.216 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
09:19:22.350 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 25.265 seconds (JVM running for 28.088)
09:19:22.371 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
09:19:22.371 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
09:19:22.371 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
09:19:22.727 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [851177b9-3407-431e-b381-b502047487ea] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:19:22.758 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [851177b9-3407-431e-b381-b502047487ea] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:21:16.228 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:21:19.796 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [851177b9-3407-431e-b381-b502047487ea] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:21:19.796 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [851177b9-3407-431e-b381-b502047487ea] Ack server push request, request = NotifySubscriberRequest, requestId = 13
09:21:20.610 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
09:21:20.610 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
09:21:20.958 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
09:21:20.961 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [slave] success
15:00:17.663 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:00:17.677 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:00:17.993 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:00:17.993 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@331b061f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:00:17.993 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754615961995_127.0.0.1_12189
15:00:17.993 [nacos-grpc-client-executor-4209] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754615961995_127.0.0.1_12189]Ignore complete event,isRunning:false,isAbandon=false
15:00:18.001 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2a6459a5[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 4210]
15:00:18.160 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:00:18.178 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
15:00:18.188 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
15:00:18.190 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:00:18.192 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:00:18.192 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:00:18.194 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:00:18.194 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:00:46.488 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:00:47.111 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5ccac6ca-2da0-4e92-90e4-4a3671e2ebc1_config-0
15:00:47.160 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 3 keys and 6 values 
15:00:47.194 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 4 keys and 9 values 
15:00:47.214 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 3 keys and 10 values 
15:00:47.220 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
15:00:47.226 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 7 values 
15:00:47.226 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
15:00:47.226 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ccac6ca-2da0-4e92-90e4-4a3671e2ebc1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:00:47.226 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ccac6ca-2da0-4e92-90e4-4a3671e2ebc1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001b0be3b9288
15:00:47.226 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ccac6ca-2da0-4e92-90e4-4a3671e2ebc1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001b0be3b94a8
15:00:47.226 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ccac6ca-2da0-4e92-90e4-4a3671e2ebc1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:00:47.226 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ccac6ca-2da0-4e92-90e4-4a3671e2ebc1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:00:47.226 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ccac6ca-2da0-4e92-90e4-4a3671e2ebc1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:00:48.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ccac6ca-2da0-4e92-90e4-4a3671e2ebc1_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754636447856_127.0.0.1_5517
15:00:48.078 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ccac6ca-2da0-4e92-90e4-4a3671e2ebc1_config-0] Notify connected event to listeners.
15:00:48.078 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ccac6ca-2da0-4e92-90e4-4a3671e2ebc1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:00:48.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5ccac6ca-2da0-4e92-90e4-4a3671e2ebc1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001b0be4f0fb0
15:00:48.231 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:00:51.393 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:00:51.393 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:00:51.393 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:00:51.527 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:00:52.180 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:00:52.180 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:00:52.180 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:00:58.250 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:01:00.883 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7491619d-fd63-428e-92e4-c21e18978327
15:01:00.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7491619d-fd63-428e-92e4-c21e18978327] RpcClient init label, labels = {module=naming, source=sdk}
15:01:00.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7491619d-fd63-428e-92e4-c21e18978327] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:01:00.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7491619d-fd63-428e-92e4-c21e18978327] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:01:00.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7491619d-fd63-428e-92e4-c21e18978327] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:01:00.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7491619d-fd63-428e-92e4-c21e18978327] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:01:01.026 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7491619d-fd63-428e-92e4-c21e18978327] Success to connect to server [localhost:8848] on start up, connectionId = 1754636460910_127.0.0.1_5534
15:01:01.027 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7491619d-fd63-428e-92e4-c21e18978327] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:01:01.027 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7491619d-fd63-428e-92e4-c21e18978327] Notify connected event to listeners.
15:01:01.027 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7491619d-fd63-428e-92e4-c21e18978327] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001b0be4f0fb0
15:01:01.065 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:01:01.082 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:01:01.182 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 15.15 seconds (JVM running for 17.189)
15:01:01.204 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:01:01.204 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:01:01.204 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:01:01.638 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7491619d-fd63-428e-92e4-c21e18978327] Receive server push request, request = NotifySubscriberRequest, requestId = 24
15:01:01.653 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7491619d-fd63-428e-92e4-c21e18978327] Ack server push request, request = NotifySubscriberRequest, requestId = 24
15:01:32.812 [http-nio-9600-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:01:33.910 [http-nio-9600-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-4} inited
15:01:33.910 [http-nio-9600-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:01:33.929 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:01:33.931 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-4} closing ...
15:01:33.939 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-4} closed
15:01:33.939 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:01:33.965 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-3} inited
15:01:33.965 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
15:01:33.966 [http-nio-9600-exec-7] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
15:01:33.966 [http-nio-9600-exec-7] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
15:18:36.663 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:18:36.666 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:18:37.010 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:18:37.011 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@421d029c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:18:37.011 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754636460910_127.0.0.1_5534
15:18:37.013 [nacos-grpc-client-executor-221] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754636460910_127.0.0.1_5534]Ignore complete event,isRunning:false,isAbandon=false
15:18:37.013 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@16d53c56[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 222]
15:18:37.221 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:18:37.225 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-3} closing ...
15:18:37.228 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-3} closed
15:18:37.228 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:18:37.230 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:18:37.230 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:18:46.241 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:18:47.410 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f75e8fe9-2568-4e2e-90d6-4fd45179768b_config-0
15:18:47.536 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 61 ms to scan 1 urls, producing 3 keys and 6 values 
15:18:47.659 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 52 ms to scan 1 urls, producing 4 keys and 9 values 
15:18:47.691 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 3 keys and 10 values 
15:18:47.717 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 5 values 
15:18:47.737 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
15:18:47.759 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
15:18:47.765 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f75e8fe9-2568-4e2e-90d6-4fd45179768b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:18:47.766 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f75e8fe9-2568-4e2e-90d6-4fd45179768b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000019c453b71c0
15:18:47.767 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f75e8fe9-2568-4e2e-90d6-4fd45179768b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000019c453b73e0
15:18:47.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f75e8fe9-2568-4e2e-90d6-4fd45179768b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:18:47.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f75e8fe9-2568-4e2e-90d6-4fd45179768b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:18:47.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f75e8fe9-2568-4e2e-90d6-4fd45179768b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:18:50.493 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f75e8fe9-2568-4e2e-90d6-4fd45179768b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754637530007_127.0.0.1_9109
15:18:50.494 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f75e8fe9-2568-4e2e-90d6-4fd45179768b_config-0] Notify connected event to listeners.
15:18:50.651 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f75e8fe9-2568-4e2e-90d6-4fd45179768b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:18:50.658 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f75e8fe9-2568-4e2e-90d6-4fd45179768b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000019c454f0668
15:18:50.992 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:19:02.458 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
15:19:02.459 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:19:02.459 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:19:02.713 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:19:03.825 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:19:03.826 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:19:03.827 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:19:10.965 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:19:13.858 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 46b42650-e884-4a58-aa3b-7cb098056593
15:19:13.858 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b42650-e884-4a58-aa3b-7cb098056593] RpcClient init label, labels = {module=naming, source=sdk}
15:19:13.859 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b42650-e884-4a58-aa3b-7cb098056593] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:19:13.859 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b42650-e884-4a58-aa3b-7cb098056593] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:19:13.859 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b42650-e884-4a58-aa3b-7cb098056593] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:19:13.859 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b42650-e884-4a58-aa3b-7cb098056593] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:19:13.990 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b42650-e884-4a58-aa3b-7cb098056593] Success to connect to server [localhost:8848] on start up, connectionId = 1754637553867_127.0.0.1_9205
15:19:13.991 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b42650-e884-4a58-aa3b-7cb098056593] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:19:13.991 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b42650-e884-4a58-aa3b-7cb098056593] Notify connected event to listeners.
15:19:13.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b42650-e884-4a58-aa3b-7cb098056593] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000019c454f0668
15:19:14.037 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
15:19:14.114 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
15:19:14.231 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 29.086 seconds (JVM running for 31.166)
15:19:14.243 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
15:19:14.243 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
15:19:14.243 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
15:19:14.555 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b42650-e884-4a58-aa3b-7cb098056593] Receive server push request, request = NotifySubscriberRequest, requestId = 32
15:19:14.570 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [46b42650-e884-4a58-aa3b-7cb098056593] Ack server push request, request = NotifySubscriberRequest, requestId = 32
15:19:21.227 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:19:22.489 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
15:19:22.490 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:09:23.401 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:09:23.409 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:09:23.743 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:09:23.744 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@32e78bb8[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:09:23.744 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754637553867_127.0.0.1_9205
17:09:23.746 [nacos-grpc-client-executor-1306] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754637553867_127.0.0.1_9205]Ignore complete event,isRunning:false,isAbandon=false
17:09:23.754 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@143a03e1[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1307]
17:09:23.992 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:09:23.998 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
17:09:24.013 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
17:09:24.013 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:09:24.015 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:09:24.016 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:09:36.272 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:09:37.451 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8e7292bd-b5b3-4ced-ab0d-dfa801c89037_config-0
17:09:37.546 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 49 ms to scan 1 urls, producing 3 keys and 6 values 
17:09:37.595 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
17:09:37.605 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
17:09:37.611 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
17:09:37.626 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
17:09:37.636 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
17:09:37.638 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e7292bd-b5b3-4ced-ab0d-dfa801c89037_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:09:37.641 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e7292bd-b5b3-4ced-ab0d-dfa801c89037_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000019e5d3b6480
17:09:37.641 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e7292bd-b5b3-4ced-ab0d-dfa801c89037_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000019e5d3b66a0
17:09:37.641 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e7292bd-b5b3-4ced-ab0d-dfa801c89037_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:09:37.641 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e7292bd-b5b3-4ced-ab0d-dfa801c89037_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:09:37.652 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e7292bd-b5b3-4ced-ab0d-dfa801c89037_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:09:38.671 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e7292bd-b5b3-4ced-ab0d-dfa801c89037_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754644178446_127.0.0.1_9970
17:09:38.671 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e7292bd-b5b3-4ced-ab0d-dfa801c89037_config-0] Notify connected event to listeners.
17:09:38.672 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e7292bd-b5b3-4ced-ab0d-dfa801c89037_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:09:38.673 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e7292bd-b5b3-4ced-ab0d-dfa801c89037_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000019e5d4f0668
17:09:38.779 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:09:41.814 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
17:09:41.814 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:09:41.814 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:09:41.953 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:09:42.581 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:09:42.581 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:09:42.581 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:09:48.759 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:09:51.403 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ad387364-ae5a-47ae-82e6-************
17:09:51.404 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad387364-ae5a-47ae-82e6-************] RpcClient init label, labels = {module=naming, source=sdk}
17:09:51.405 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad387364-ae5a-47ae-82e6-************] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:09:51.405 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad387364-ae5a-47ae-82e6-************] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:09:51.405 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad387364-ae5a-47ae-82e6-************] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:09:51.405 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad387364-ae5a-47ae-82e6-************] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:09:51.535 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad387364-ae5a-47ae-82e6-************] Success to connect to server [localhost:8848] on start up, connectionId = 1754644191416_127.0.0.1_10058
17:09:51.535 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad387364-ae5a-47ae-82e6-************] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:09:51.535 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad387364-ae5a-47ae-82e6-************] Notify connected event to listeners.
17:09:51.536 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad387364-ae5a-47ae-82e6-************] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000019e5d4f0668
17:09:51.577 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
17:09:51.605 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
17:09:51.713 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 16.69 seconds (JVM running for 19.018)
17:09:51.727 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
17:09:51.727 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
17:09:51.727 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
17:09:52.154 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad387364-ae5a-47ae-82e6-************] Receive server push request, request = NotifySubscriberRequest, requestId = 42
17:09:52.165 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ad387364-ae5a-47ae-82e6-************] Ack server push request, request = NotifySubscriberRequest, requestId = 42
17:09:57.689 [http-nio-9600-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:09:59.381 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
17:09:59.381 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:08:24.155 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:08:24.158 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:08:24.485 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:08:24.486 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3875be96[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:08:24.486 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754644191416_127.0.0.1_10058
18:08:24.487 [nacos-grpc-client-executor-711] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754644191416_127.0.0.1_10058]Ignore complete event,isRunning:false,isAbandon=false
18:08:24.487 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1a404ca6[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 712]
18:08:24.629 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:08:24.629 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
18:08:24.639 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
18:08:24.639 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:08:24.640 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:08:24.640 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
18:09:01.781 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:09:02.545 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3adb47f6-c412-4f7c-aea3-b79595fc5abf_config-0
18:09:02.611 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 3 keys and 6 values 
18:09:02.650 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
18:09:02.653 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 2 ms to scan 1 urls, producing 3 keys and 10 values 
18:09:02.665 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
18:09:02.674 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
18:09:02.684 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
18:09:02.688 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3adb47f6-c412-4f7c-aea3-b79595fc5abf_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:09:02.688 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3adb47f6-c412-4f7c-aea3-b79595fc5abf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001d22939e8d8
18:09:02.688 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3adb47f6-c412-4f7c-aea3-b79595fc5abf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001d22939eaf8
18:09:02.688 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3adb47f6-c412-4f7c-aea3-b79595fc5abf_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:09:02.688 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3adb47f6-c412-4f7c-aea3-b79595fc5abf_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:09:02.688 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3adb47f6-c412-4f7c-aea3-b79595fc5abf_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:09:03.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3adb47f6-c412-4f7c-aea3-b79595fc5abf_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754647743296_127.0.0.1_6686
18:09:03.490 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3adb47f6-c412-4f7c-aea3-b79595fc5abf_config-0] Notify connected event to listeners.
18:09:03.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3adb47f6-c412-4f7c-aea3-b79595fc5abf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:09:03.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3adb47f6-c412-4f7c-aea3-b79595fc5abf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d229518ad8
18:09:03.602 [main] INFO  c.h.s.HeJuSystemApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:09:11.533 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9600"]
18:09:11.537 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:09:11.538 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
18:09:12.342 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:09:14.661 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
18:09:14.665 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
18:09:14.665 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
18:09:36.918 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:09:41.360 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d88c68b5-3778-47fa-9975-3096c80bed55
18:09:41.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d88c68b5-3778-47fa-9975-3096c80bed55] RpcClient init label, labels = {module=naming, source=sdk}
18:09:41.362 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d88c68b5-3778-47fa-9975-3096c80bed55] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:09:41.363 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d88c68b5-3778-47fa-9975-3096c80bed55] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:09:41.363 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d88c68b5-3778-47fa-9975-3096c80bed55] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:09:41.364 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d88c68b5-3778-47fa-9975-3096c80bed55] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:09:41.486 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d88c68b5-3778-47fa-9975-3096c80bed55] Success to connect to server [localhost:8848] on start up, connectionId = 1754647781369_127.0.0.1_6987
18:09:41.486 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d88c68b5-3778-47fa-9975-3096c80bed55] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:09:41.486 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d88c68b5-3778-47fa-9975-3096c80bed55] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d229518ad8
18:09:41.486 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d88c68b5-3778-47fa-9975-3096c80bed55] Notify connected event to listeners.
18:09:41.531 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9600"]
18:09:41.564 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-system ************:9600 register finished
18:09:41.734 [main] INFO  c.h.s.HeJuSystemApplication - [logStarted,61] - Started HeJuSystemApplication in 40.504 seconds (JVM running for 41.393)
18:09:41.770 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system-dev.yml, group=DEFAULT_GROUP
18:09:41.770 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system, group=DEFAULT_GROUP
18:09:41.771 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-system.yml, group=DEFAULT_GROUP
18:09:42.099 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d88c68b5-3778-47fa-9975-3096c80bed55] Receive server push request, request = NotifySubscriberRequest, requestId = 56
18:09:42.101 [RMI TCP Connection(8)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:09:42.116 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d88c68b5-3778-47fa-9975-3096c80bed55] Ack server push request, request = NotifySubscriberRequest, requestId = 56
18:13:29.469 [http-nio-9600-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
18:13:29.469 [http-nio-9600-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
18:15:36.778 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:15:36.781 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:15:37.101 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:15:37.101 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2ecf35bd[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:15:37.102 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754647781369_127.0.0.1_6987
18:15:37.104 [nacos-grpc-client-executor-80] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754647781369_127.0.0.1_6987]Ignore complete event,isRunning:false,isAbandon=false
18:15:37.106 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@791dd363[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 81]
18:15:37.241 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:15:37.244 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
18:15:37.249 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
18:15:37.249 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:15:37.250 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:15:37.250 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
