09:22:27.367 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:22:27.950 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0
09:22:27.982 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 3 keys and 6 values 
09:22:28.016 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 4 keys and 9 values 
09:22:28.016 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 0 ms to scan 1 urls, producing 3 keys and 10 values 
09:22:28.029 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 0 ms to scan 1 urls, producing 1 keys and 5 values 
09:22:28.049 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 7 values 
09:22:28.056 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
09:22:28.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:22:28.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000019ae539da58
09:22:28.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000019ae539dc78
09:22:28.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:22:28.062 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:22:28.068 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:22:29.576 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:22:29.624 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:22:29.645 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:22:29.645 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:22:29.646 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000019ae54ab938
09:22:29.782 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:30.002 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:30.316 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:31.223 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:31.783 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:32.250 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:22:32.397 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:33.126 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:33.934 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:34.854 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:35.866 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:37.096 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:38.517 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:38.720 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:22:38.722 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:22:38.722 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:22:39.185 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:22:40.373 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:42.006 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:42.088 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:22:42.093 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:22:42.094 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:22:43.640 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:45.324 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:47.126 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:48.425 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:22:49.188 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:51.167 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:53.224 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:53.344 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of dadb56c8-1525-4b9c-8fff-eeeae6a3efc9
09:22:53.345 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dadb56c8-1525-4b9c-8fff-eeeae6a3efc9] RpcClient init label, labels = {module=naming, source=sdk}
09:22:53.346 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dadb56c8-1525-4b9c-8fff-eeeae6a3efc9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:22:53.346 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dadb56c8-1525-4b9c-8fff-eeeae6a3efc9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:22:53.348 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dadb56c8-1525-4b9c-8fff-eeeae6a3efc9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:22:53.348 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dadb56c8-1525-4b9c-8fff-eeeae6a3efc9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:22:53.355 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dadb56c8-1525-4b9c-8fff-eeeae6a3efc9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:22:53.361 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dadb56c8-1525-4b9c-8fff-eeeae6a3efc9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:22:53.367 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dadb56c8-1525-4b9c-8fff-eeeae6a3efc9] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:22:53.367 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dadb56c8-1525-4b9c-8fff-eeeae6a3efc9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:22:53.367 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dadb56c8-1525-4b9c-8fff-eeeae6a3efc9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000019ae54ab938
09:22:53.523 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dadb56c8-1525-4b9c-8fff-eeeae6a3efc9] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:53.698 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:22:53.731 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dadb56c8-1525-4b9c-8fff-eeeae6a3efc9] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:54.057 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dadb56c8-1525-4b9c-8fff-eeeae6a3efc9] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:54.469 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dadb56c8-1525-4b9c-8fff-eeeae6a3efc9] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:54.709 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:22:54.710 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3a35a792[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:22:54.710 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@254dfd34[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
09:22:54.710 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dadb56c8-1525-4b9c-8fff-eeeae6a3efc9] Client is shutdown, stop reconnect to server
09:22:54.713 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f4adfb57-3614-4831-93af-3e7d6f37d5a6
09:22:54.713 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4adfb57-3614-4831-93af-3e7d6f37d5a6] RpcClient init label, labels = {module=naming, source=sdk}
09:22:54.713 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4adfb57-3614-4831-93af-3e7d6f37d5a6] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:22:54.713 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4adfb57-3614-4831-93af-3e7d6f37d5a6] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:22:54.713 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4adfb57-3614-4831-93af-3e7d6f37d5a6] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:22:54.714 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4adfb57-3614-4831-93af-3e7d6f37d5a6] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:22:54.721 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4adfb57-3614-4831-93af-3e7d6f37d5a6] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:22:54.728 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4adfb57-3614-4831-93af-3e7d6f37d5a6] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:22:54.733 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4adfb57-3614-4831-93af-3e7d6f37d5a6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:22:54.733 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4adfb57-3614-4831-93af-3e7d6f37d5a6] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:22:54.733 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4adfb57-3614-4831-93af-3e7d6f37d5a6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000019ae54ab938
09:22:54.854 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4adfb57-3614-4831-93af-3e7d6f37d5a6] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:55.072 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4adfb57-3614-4831-93af-3e7d6f37d5a6] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:55.098 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:22:55.102 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:22:55.113 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:22:55.113 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:22:55.335 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6fb6c5a0-0294-46b6-870c-47cccc568f73_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:55.411 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4adfb57-3614-4831-93af-3e7d6f37d5a6] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:55.595 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9700"]
09:22:55.595 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:22:55.622 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9700"]
09:22:55.627 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9700"]
09:22:55.820 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f4adfb57-3614-4831-93af-3e7d6f37d5a6] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:23:43.695 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:23:45.219 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4b3e209e-83dd-4acf-9b85-10b33436d44c_config-0
09:23:45.334 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 64 ms to scan 1 urls, producing 3 keys and 6 values 
09:23:45.382 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 4 keys and 9 values 
09:23:45.399 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 3 keys and 10 values 
09:23:45.430 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 1 keys and 5 values 
09:23:45.452 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 1 keys and 7 values 
09:23:45.473 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
09:23:45.477 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b3e209e-83dd-4acf-9b85-10b33436d44c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:23:45.478 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b3e209e-83dd-4acf-9b85-10b33436d44c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001fd5838cfb8
09:23:45.479 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b3e209e-83dd-4acf-9b85-10b33436d44c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001fd5838d1d8
09:23:45.479 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b3e209e-83dd-4acf-9b85-10b33436d44c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:23:45.480 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b3e209e-83dd-4acf-9b85-10b33436d44c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:23:45.493 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b3e209e-83dd-4acf-9b85-10b33436d44c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:23:48.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b3e209e-83dd-4acf-9b85-10b33436d44c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756257828007_127.0.0.1_14608
09:23:48.301 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b3e209e-83dd-4acf-9b85-10b33436d44c_config-0] Notify connected event to listeners.
09:23:48.302 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b3e209e-83dd-4acf-9b85-10b33436d44c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:23:48.303 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b3e209e-83dd-4acf-9b85-10b33436d44c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001fd584e5418
09:23:48.639 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:23:55.500 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:23:55.501 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:23:55.501 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:23:55.994 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:23:57.401 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:23:57.405 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:23:57.406 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:24:02.989 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:24:07.488 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d5e1208f-cf82-493b-8573-a0234765c6b3
09:24:07.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5e1208f-cf82-493b-8573-a0234765c6b3] RpcClient init label, labels = {module=naming, source=sdk}
09:24:07.491 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5e1208f-cf82-493b-8573-a0234765c6b3] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:24:07.491 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5e1208f-cf82-493b-8573-a0234765c6b3] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:24:07.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5e1208f-cf82-493b-8573-a0234765c6b3] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:24:07.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5e1208f-cf82-493b-8573-a0234765c6b3] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:07.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5e1208f-cf82-493b-8573-a0234765c6b3] Success to connect to server [localhost:8848] on start up, connectionId = 1756257847499_127.0.0.1_14702
09:24:07.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5e1208f-cf82-493b-8573-a0234765c6b3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:07.617 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5e1208f-cf82-493b-8573-a0234765c6b3] Notify connected event to listeners.
09:24:07.618 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5e1208f-cf82-493b-8573-a0234765c6b3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001fd584e5418
09:24:07.673 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:24:07.709 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
09:24:07.907 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 26.507 seconds (JVM running for 28.314)
09:24:07.919 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:24:07.921 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:24:07.922 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:24:08.202 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5e1208f-cf82-493b-8573-a0234765c6b3] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:24:08.219 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5e1208f-cf82-493b-8573-a0234765c6b3] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:24:08.468 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:24:42.960 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:24:42.970 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:24:43.300 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:24:43.301 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@866d669[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:24:43.301 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756257847499_127.0.0.1_14702
09:24:43.304 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756257847499_127.0.0.1_14702]Ignore complete event,isRunning:false,isAbandon=false
09:24:43.306 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@54b98e8b[Running, pool size = 8, active threads = 0, queued tasks = 0, completed tasks = 23]
09:24:43.447 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:24:43.450 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:24:43.457 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:24:43.457 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:24:49.651 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:24:50.628 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d0507a8f-c326-49f7-a9bd-b0724abae0bb_config-0
09:24:50.715 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 3 keys and 6 values 
09:24:50.749 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
09:24:50.762 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
09:24:50.776 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
09:24:50.797 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 1 keys and 7 values 
09:24:50.811 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
09:24:50.816 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0507a8f-c326-49f7-a9bd-b0724abae0bb_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:24:50.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0507a8f-c326-49f7-a9bd-b0724abae0bb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001a98139fd80
09:24:50.818 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0507a8f-c326-49f7-a9bd-b0724abae0bb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001a9813a0000
09:24:50.818 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0507a8f-c326-49f7-a9bd-b0724abae0bb_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:24:50.820 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0507a8f-c326-49f7-a9bd-b0724abae0bb_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:24:50.834 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0507a8f-c326-49f7-a9bd-b0724abae0bb_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:51.942 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0507a8f-c326-49f7-a9bd-b0724abae0bb_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756257891730_127.0.0.1_14797
09:24:51.943 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0507a8f-c326-49f7-a9bd-b0724abae0bb_config-0] Notify connected event to listeners.
09:24:51.944 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0507a8f-c326-49f7-a9bd-b0724abae0bb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:51.945 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d0507a8f-c326-49f7-a9bd-b0724abae0bb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001a98151a0a0
09:24:52.073 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:24:54.699 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:24:54.700 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:24:54.700 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:24:54.821 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:24:55.492 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:24:55.494 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:24:55.494 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:24:58.430 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:25:01.009 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c3fb9f60-49dc-438d-bf8b-d115df8b33e3
09:25:01.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3fb9f60-49dc-438d-bf8b-d115df8b33e3] RpcClient init label, labels = {module=naming, source=sdk}
09:25:01.011 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3fb9f60-49dc-438d-bf8b-d115df8b33e3] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:25:01.011 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3fb9f60-49dc-438d-bf8b-d115df8b33e3] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:25:01.011 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3fb9f60-49dc-438d-bf8b-d115df8b33e3] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:25:01.012 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3fb9f60-49dc-438d-bf8b-d115df8b33e3] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:25:01.140 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3fb9f60-49dc-438d-bf8b-d115df8b33e3] Success to connect to server [localhost:8848] on start up, connectionId = 1756257901020_127.0.0.1_14843
09:25:01.141 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3fb9f60-49dc-438d-bf8b-d115df8b33e3] Notify connected event to listeners.
09:25:01.141 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3fb9f60-49dc-438d-bf8b-d115df8b33e3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:25:01.141 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3fb9f60-49dc-438d-bf8b-d115df8b33e3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001a98151a0a0
09:25:01.189 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:25:01.220 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
09:25:01.343 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 12.683 seconds (JVM running for 14.042)
09:25:01.355 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:25:01.358 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:25:01.359 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:25:01.449 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:25:01.748 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3fb9f60-49dc-438d-bf8b-d115df8b33e3] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:25:01.768 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3fb9f60-49dc-438d-bf8b-d115df8b33e3] Ack server push request, request = NotifySubscriberRequest, requestId = 12
09:35:26.459 [nacos-grpc-client-executor-137] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3fb9f60-49dc-438d-bf8b-d115df8b33e3] Receive server push request, request = NotifySubscriberRequest, requestId = 17
09:35:26.460 [nacos-grpc-client-executor-137] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c3fb9f60-49dc-438d-bf8b-d115df8b33e3] Ack server push request, request = NotifySubscriberRequest, requestId = 17
