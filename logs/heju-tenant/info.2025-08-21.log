09:01:07.351 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:01:07.356 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:01:07.701 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:01:07.708 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6a8c2da4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:01:07.710 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755653316468_127.0.0.1_8781
09:01:07.726 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4cbe3a12[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 10117]
09:01:07.743 [nacos-grpc-client-executor-10117] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755653316468_127.0.0.1_8781]Ignore complete event,isRunning:false,isAbandon=false
09:01:07.963 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:01:07.967 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:01:07.970 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:01:07.970 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:32:03.947 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:32:04.676 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9d85aa4d-93e5-46e9-a161-8b33685a2584_config-0
09:32:04.722 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 3 keys and 6 values 
09:32:04.753 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 4 keys and 9 values 
09:32:04.778 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:32:04.788 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:32:04.797 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
09:32:04.797 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
09:32:04.813 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d85aa4d-93e5-46e9-a161-8b33685a2584_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:32:04.813 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d85aa4d-93e5-46e9-a161-8b33685a2584_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000012acf39da58
09:32:04.813 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d85aa4d-93e5-46e9-a161-8b33685a2584_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000012acf39dc78
09:32:04.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d85aa4d-93e5-46e9-a161-8b33685a2584_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:32:04.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d85aa4d-93e5-46e9-a161-8b33685a2584_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:32:04.828 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d85aa4d-93e5-46e9-a161-8b33685a2584_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:32:05.773 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d85aa4d-93e5-46e9-a161-8b33685a2584_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755739925567_127.0.0.1_13228
09:32:05.773 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d85aa4d-93e5-46e9-a161-8b33685a2584_config-0] Notify connected event to listeners.
09:32:05.773 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d85aa4d-93e5-46e9-a161-8b33685a2584_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:32:05.773 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d85aa4d-93e5-46e9-a161-8b33685a2584_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000012acf515f18
09:32:05.915 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:32:09.406 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:32:09.406 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:32:09.406 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:32:09.564 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:32:10.221 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:32:10.221 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:32:10.221 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:32:12.453 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:32:15.681 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f2befa54-3e3d-45b6-a88f-f79c8c4e776c
09:32:15.681 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2befa54-3e3d-45b6-a88f-f79c8c4e776c] RpcClient init label, labels = {module=naming, source=sdk}
09:32:15.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2befa54-3e3d-45b6-a88f-f79c8c4e776c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:32:15.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2befa54-3e3d-45b6-a88f-f79c8c4e776c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:32:15.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2befa54-3e3d-45b6-a88f-f79c8c4e776c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:32:15.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2befa54-3e3d-45b6-a88f-f79c8c4e776c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:32:15.808 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2befa54-3e3d-45b6-a88f-f79c8c4e776c] Success to connect to server [localhost:8848] on start up, connectionId = 1755739935691_127.0.0.1_13247
09:32:15.808 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2befa54-3e3d-45b6-a88f-f79c8c4e776c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:32:15.808 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2befa54-3e3d-45b6-a88f-f79c8c4e776c] Notify connected event to listeners.
09:32:15.808 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2befa54-3e3d-45b6-a88f-f79c8c4e776c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000012acf515f18
09:32:15.870 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:32:15.895 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
09:32:16.037 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 12.664 seconds (JVM running for 19.127)
09:32:16.047 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:32:16.047 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:32:16.047 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:32:16.446 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2befa54-3e3d-45b6-a88f-f79c8c4e776c] Receive server push request, request = NotifySubscriberRequest, requestId = 95
09:32:16.462 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2befa54-3e3d-45b6-a88f-f79c8c4e776c] Ack server push request, request = NotifySubscriberRequest, requestId = 95
09:46:23.673 [http-nio-9700-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:07:45.807 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:07:45.807 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:07:46.136 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:07:46.136 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@48319064[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:07:46.136 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755739935691_127.0.0.1_13247
14:07:46.139 [nacos-grpc-client-executor-3314] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755739935691_127.0.0.1_13247]Ignore complete event,isRunning:false,isAbandon=false
14:07:46.143 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2ce25405[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3315]
14:07:46.190 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:07:46.190 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:07:46.190 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:07:46.190 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:10:17.969 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:10:18.931 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of dc11292e-b96e-43ae-9429-0f4b2bfab854_config-0
14:10:19.033 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 49 ms to scan 1 urls, producing 3 keys and 6 values 
14:10:19.065 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
14:10:19.079 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
14:10:19.096 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 5 values 
14:10:19.121 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 1 keys and 7 values 
14:10:19.137 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
14:10:19.145 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc11292e-b96e-43ae-9429-0f4b2bfab854_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:10:19.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc11292e-b96e-43ae-9429-0f4b2bfab854_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000023cc93a0200
14:10:19.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc11292e-b96e-43ae-9429-0f4b2bfab854_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000023cc93a0420
14:10:19.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc11292e-b96e-43ae-9429-0f4b2bfab854_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:10:19.148 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc11292e-b96e-43ae-9429-0f4b2bfab854_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:10:19.164 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc11292e-b96e-43ae-9429-0f4b2bfab854_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:20.969 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc11292e-b96e-43ae-9429-0f4b2bfab854_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755756620665_127.0.0.1_6196
14:10:20.972 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc11292e-b96e-43ae-9429-0f4b2bfab854_config-0] Notify connected event to listeners.
14:10:20.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc11292e-b96e-43ae-9429-0f4b2bfab854_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:10:20.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dc11292e-b96e-43ae-9429-0f4b2bfab854_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000023cc951a958
14:10:21.283 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:10:30.370 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
14:10:30.371 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:10:30.374 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:10:30.895 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:10:32.548 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:10:32.551 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:10:32.552 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:10:38.937 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:10:44.414 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7c8784f3-1e86-4ea9-afa4-6de77e6eb173
14:10:44.414 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c8784f3-1e86-4ea9-afa4-6de77e6eb173] RpcClient init label, labels = {module=naming, source=sdk}
14:10:44.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c8784f3-1e86-4ea9-afa4-6de77e6eb173] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:10:44.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c8784f3-1e86-4ea9-afa4-6de77e6eb173] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:10:44.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c8784f3-1e86-4ea9-afa4-6de77e6eb173] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:10:44.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c8784f3-1e86-4ea9-afa4-6de77e6eb173] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:44.546 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c8784f3-1e86-4ea9-afa4-6de77e6eb173] Success to connect to server [localhost:8848] on start up, connectionId = 1755756644425_127.0.0.1_6250
14:10:44.547 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c8784f3-1e86-4ea9-afa4-6de77e6eb173] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:10:44.547 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c8784f3-1e86-4ea9-afa4-6de77e6eb173] Notify connected event to listeners.
14:10:44.547 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c8784f3-1e86-4ea9-afa4-6de77e6eb173] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000023cc951a958
14:10:44.602 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
14:10:44.637 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
14:10:44.799 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 27.48 seconds (JVM running for 28.555)
14:10:44.813 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
14:10:44.816 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
14:10:44.817 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
14:10:44.986 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:10:45.087 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c8784f3-1e86-4ea9-afa4-6de77e6eb173] Receive server push request, request = NotifySubscriberRequest, requestId = 142
14:10:45.103 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7c8784f3-1e86-4ea9-afa4-6de77e6eb173] Ack server push request, request = NotifySubscriberRequest, requestId = 142
19:57:39.072 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:57:39.076 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:57:39.405 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:57:39.405 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6381ca6[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:57:39.405 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755756644425_127.0.0.1_6250
19:57:39.408 [nacos-grpc-client-executor-4167] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755756644425_127.0.0.1_6250]Ignore complete event,isRunning:false,isAbandon=false
19:57:39.410 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@e43dc7c[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 4168]
19:57:39.572 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:57:39.572 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:57:39.588 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:57:39.588 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
19:58:37.778 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:58:38.556 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 114b215a-3f6f-481b-9741-8fb6bd9e5ffe_config-0
19:58:38.643 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 34 ms to scan 1 urls, producing 3 keys and 6 values 
19:58:38.679 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
19:58:38.696 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 3 keys and 10 values 
19:58:38.713 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
19:58:38.726 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
19:58:38.739 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
19:58:38.739 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [114b215a-3f6f-481b-9741-8fb6bd9e5ffe_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:58:38.739 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [114b215a-3f6f-481b-9741-8fb6bd9e5ffe_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001d8013b8d60
19:58:38.739 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [114b215a-3f6f-481b-9741-8fb6bd9e5ffe_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001d8013b8f80
19:58:38.739 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [114b215a-3f6f-481b-9741-8fb6bd9e5ffe_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:58:38.739 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [114b215a-3f6f-481b-9741-8fb6bd9e5ffe_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:58:38.755 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [114b215a-3f6f-481b-9741-8fb6bd9e5ffe_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:58:39.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [114b215a-3f6f-481b-9741-8fb6bd9e5ffe_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755777519730_127.0.0.1_14278
19:58:39.986 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [114b215a-3f6f-481b-9741-8fb6bd9e5ffe_config-0] Notify connected event to listeners.
19:58:39.986 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [114b215a-3f6f-481b-9741-8fb6bd9e5ffe_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:58:39.986 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [114b215a-3f6f-481b-9741-8fb6bd9e5ffe_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001d8014f0fb0
19:58:40.259 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:58:49.925 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
19:58:49.925 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:58:49.925 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:58:50.366 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:58:51.844 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
19:58:51.847 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
19:58:51.848 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
19:58:59.439 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:59:04.029 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 690c73a9-8269-4822-bb5e-d1774734e726
19:59:04.030 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [690c73a9-8269-4822-bb5e-d1774734e726] RpcClient init label, labels = {module=naming, source=sdk}
19:59:04.032 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [690c73a9-8269-4822-bb5e-d1774734e726] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:59:04.032 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [690c73a9-8269-4822-bb5e-d1774734e726] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:59:04.033 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [690c73a9-8269-4822-bb5e-d1774734e726] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:59:04.033 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [690c73a9-8269-4822-bb5e-d1774734e726] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:59:04.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [690c73a9-8269-4822-bb5e-d1774734e726] Success to connect to server [localhost:8848] on start up, connectionId = 1755777544038_127.0.0.1_9235
19:59:04.153 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [690c73a9-8269-4822-bb5e-d1774734e726] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:59:04.153 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [690c73a9-8269-4822-bb5e-d1774734e726] Notify connected event to listeners.
19:59:04.153 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [690c73a9-8269-4822-bb5e-d1774734e726] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001d8014f0fb0
19:59:04.197 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
19:59:04.228 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
19:59:04.365 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 27.246 seconds (JVM running for 29.571)
19:59:04.372 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
19:59:04.372 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
19:59:04.372 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
19:59:04.686 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [690c73a9-8269-4822-bb5e-d1774734e726] Receive server push request, request = NotifySubscriberRequest, requestId = 188
19:59:04.697 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [690c73a9-8269-4822-bb5e-d1774734e726] Ack server push request, request = NotifySubscriberRequest, requestId = 188
19:59:48.548 [http-nio-9700-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:59:50.454 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [690c73a9-8269-4822-bb5e-d1774734e726] Receive server push request, request = NotifySubscriberRequest, requestId = 193
19:59:50.454 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [690c73a9-8269-4822-bb5e-d1774734e726] Ack server push request, request = NotifySubscriberRequest, requestId = 193
