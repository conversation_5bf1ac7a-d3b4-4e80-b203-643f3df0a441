09:23:07.198 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:23:08.091 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1fbffff2-7490-4593-909a-16f5e4b6f329_config-0
09:23:08.172 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 34 ms to scan 1 urls, producing 3 keys and 6 values 
09:23:08.203 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 4 keys and 9 values 
09:23:08.214 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 3 keys and 10 values 
09:23:08.233 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 1 keys and 5 values 
09:23:08.246 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 7 values 
09:23:08.255 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
09:23:08.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fbffff2-7490-4593-909a-16f5e4b6f329_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:23:08.259 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fbffff2-7490-4593-909a-16f5e4b6f329_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002154c3ceff8
09:23:08.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fbffff2-7490-4593-909a-16f5e4b6f329_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002154c3cf218
09:23:08.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fbffff2-7490-4593-909a-16f5e4b6f329_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:23:08.261 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fbffff2-7490-4593-909a-16f5e4b6f329_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:23:08.271 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fbffff2-7490-4593-909a-16f5e4b6f329_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:23:09.414 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fbffff2-7490-4593-909a-16f5e4b6f329_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755566589169_127.0.0.1_11099
09:23:09.416 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fbffff2-7490-4593-909a-16f5e4b6f329_config-0] Notify connected event to listeners.
09:23:09.416 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fbffff2-7490-4593-909a-16f5e4b6f329_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:23:09.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fbffff2-7490-4593-909a-16f5e4b6f329_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002154c508fb0
09:23:09.628 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:23:14.776 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:23:14.778 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:23:14.780 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:23:15.221 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:23:16.936 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:23:16.940 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:23:16.941 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:23:29.977 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:23:40.800 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1f3c7a93-1730-4588-8c2b-fd1d8df0cadb
09:23:40.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] RpcClient init label, labels = {module=naming, source=sdk}
09:23:40.818 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:23:40.820 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:23:40.821 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:23:40.822 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:23:42.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Success to connect to server [localhost:8848] on start up, connectionId = 1755566621181_127.0.0.1_11205
09:23:42.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:23:42.109 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002154c508fb0
09:23:42.310 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Notify connected event to listeners.
09:23:42.832 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:23:43.435 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
09:23:43.607 [nacos-grpc-client-executor-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:23:43.609 [nacos-grpc-client-executor-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:23:44.005 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:23:44.040 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:23:44.384 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 37.982 seconds (JVM running for 39.235)
09:23:44.427 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:23:44.437 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:23:44.440 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:23:44.639 [RMI TCP Connection(17)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:25:17.331 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 14
09:25:17.332 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 14
10:15:15.493 [nacos-grpc-client-executor-631] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 17
10:15:15.509 [nacos-grpc-client-executor-631] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 17
10:16:21.646 [nacos-grpc-client-executor-645] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 21
10:16:21.669 [nacos-grpc-client-executor-645] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 21
10:44:26.127 [nacos-grpc-client-executor-983] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 24
10:44:26.147 [nacos-grpc-client-executor-983] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 24
10:44:51.494 [nacos-grpc-client-executor-988] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 28
10:44:51.510 [nacos-grpc-client-executor-988] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 28
10:55:51.088 [nacos-grpc-client-executor-1120] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 31
10:55:51.112 [nacos-grpc-client-executor-1120] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 31
10:56:17.221 [nacos-grpc-client-executor-1125] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 34
10:56:17.245 [nacos-grpc-client-executor-1125] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 34
11:17:25.033 [nacos-grpc-client-executor-1379] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 38
11:17:25.054 [nacos-grpc-client-executor-1379] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 38
11:17:54.901 [nacos-grpc-client-executor-1385] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 42
11:17:54.930 [nacos-grpc-client-executor-1385] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 42
11:18:05.166 [nacos-grpc-client-executor-1387] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 45
11:18:05.180 [nacos-grpc-client-executor-1387] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 45
11:18:29.529 [nacos-grpc-client-executor-1393] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 49
11:18:29.548 [nacos-grpc-client-executor-1393] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 49
11:20:31.834 [nacos-grpc-client-executor-1418] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 52
11:20:31.853 [nacos-grpc-client-executor-1418] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 52
11:21:03.018 [nacos-grpc-client-executor-1425] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 56
11:21:03.037 [nacos-grpc-client-executor-1425] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 56
11:23:14.199 [nacos-grpc-client-executor-1451] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 59
11:23:14.219 [nacos-grpc-client-executor-1451] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 59
11:23:38.816 [nacos-grpc-client-executor-1456] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 63
11:23:38.836 [nacos-grpc-client-executor-1456] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 63
13:18:52.621 [nacos-grpc-client-executor-2837] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 66
13:18:52.637 [nacos-grpc-client-executor-2837] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 66
13:19:10.079 [nacos-grpc-client-executor-2842] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 70
13:19:10.091 [nacos-grpc-client-executor-2842] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 70
16:42:51.634 [nacos-grpc-client-executor-5284] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 73
16:42:51.634 [nacos-grpc-client-executor-5284] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 73
16:43:12.514 [nacos-grpc-client-executor-5288] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 77
16:43:12.528 [nacos-grpc-client-executor-5288] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 77
16:46:32.782 [nacos-grpc-client-executor-5328] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 80
16:46:32.810 [nacos-grpc-client-executor-5328] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 80
16:46:59.017 [nacos-grpc-client-executor-5334] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 84
16:46:59.026 [nacos-grpc-client-executor-5334] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 84
16:48:03.921 [nacos-grpc-client-executor-5347] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 87
16:48:03.948 [nacos-grpc-client-executor-5347] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 87
16:48:29.889 [nacos-grpc-client-executor-5353] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 91
16:48:29.906 [nacos-grpc-client-executor-5353] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 91
16:51:47.364 [nacos-grpc-client-executor-5393] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 94
16:51:47.380 [nacos-grpc-client-executor-5393] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 94
16:52:11.523 [nacos-grpc-client-executor-5398] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 98
16:52:11.540 [nacos-grpc-client-executor-5398] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 98
18:18:30.472 [nacos-grpc-client-executor-6432] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 103
18:18:30.472 [nacos-grpc-client-executor-6432] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 103
18:19:17.090 [nacos-grpc-client-executor-6443] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 107
18:19:17.099 [nacos-grpc-client-executor-6443] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 107
18:25:14.887 [nacos-grpc-client-executor-6514] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 112
18:25:14.899 [nacos-grpc-client-executor-6514] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 112
18:25:37.270 [nacos-grpc-client-executor-6519] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 117
18:25:37.285 [nacos-grpc-client-executor-6519] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 117
18:34:14.087 [nacos-grpc-client-executor-6622] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 122
18:34:14.087 [nacos-grpc-client-executor-6622] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 122
18:35:21.424 [nacos-grpc-client-executor-6637] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 127
18:35:21.438 [nacos-grpc-client-executor-6637] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 127
18:39:58.655 [nacos-grpc-client-executor-6693] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 132
18:39:58.672 [nacos-grpc-client-executor-6693] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 132
18:40:16.541 [nacos-grpc-client-executor-6696] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 137
18:40:16.557 [nacos-grpc-client-executor-6696] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 137
19:30:28.170 [nacos-grpc-client-executor-7334] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 141
19:30:28.185 [nacos-grpc-client-executor-7334] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 141
19:30:46.214 [nacos-grpc-client-executor-7338] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 146
19:30:46.231 [nacos-grpc-client-executor-7338] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 146
19:44:37.747 [nacos-grpc-client-executor-7517] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 150
19:44:37.765 [nacos-grpc-client-executor-7517] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 150
19:44:58.825 [nacos-grpc-client-executor-7523] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 154
19:44:58.840 [nacos-grpc-client-executor-7523] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 154
19:59:27.219 [nacos-grpc-client-executor-7696] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 159
19:59:27.244 [nacos-grpc-client-executor-7696] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 159
19:59:45.601 [nacos-grpc-client-executor-7700] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 164
19:59:45.631 [nacos-grpc-client-executor-7700] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 164
20:12:47.824 [nacos-grpc-client-executor-7856] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Receive server push request, request = NotifySubscriberRequest, requestId = 168
20:12:47.829 [nacos-grpc-client-executor-7856] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1f3c7a93-1730-4588-8c2b-fd1d8df0cadb] Ack server push request, request = NotifySubscriberRequest, requestId = 168
20:12:50.433 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:12:50.435 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:12:50.760 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:12:50.761 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@49064b05[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:12:50.761 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755566621181_127.0.0.1_11205
20:12:50.761 [nacos-grpc-client-executor-7859] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755566621181_127.0.0.1_11205]Ignore complete event,isRunning:false,isAbandon=false
20:12:50.766 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1f9ab83[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 7860]
20:12:50.916 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:12:50.920 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:12:50.924 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:12:50.924 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:15:18.396 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:15:20.274 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 697a58b2-9f0b-4938-8084-1166967f915b_config-0
20:15:20.409 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 63 ms to scan 1 urls, producing 3 keys and 6 values 
20:15:20.479 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 4 keys and 9 values 
20:15:20.505 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 3 keys and 10 values 
20:15:20.538 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 1 keys and 5 values 
20:15:20.562 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 1 keys and 7 values 
20:15:20.600 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 33 ms to scan 1 urls, producing 2 keys and 8 values 
20:15:20.609 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [697a58b2-9f0b-4938-8084-1166967f915b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:15:20.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [697a58b2-9f0b-4938-8084-1166967f915b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000016f9c3b7d80
20:15:20.612 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [697a58b2-9f0b-4938-8084-1166967f915b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000016f9c3b8000
20:15:20.613 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [697a58b2-9f0b-4938-8084-1166967f915b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:15:20.614 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [697a58b2-9f0b-4938-8084-1166967f915b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:15:20.633 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [697a58b2-9f0b-4938-8084-1166967f915b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:15:22.722 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [697a58b2-9f0b-4938-8084-1166967f915b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755605722410_127.0.0.1_14220
20:15:22.723 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [697a58b2-9f0b-4938-8084-1166967f915b_config-0] Notify connected event to listeners.
20:15:22.724 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [697a58b2-9f0b-4938-8084-1166967f915b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:15:22.727 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [697a58b2-9f0b-4938-8084-1166967f915b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000016f9c4f1ca0
20:15:23.048 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:15:31.665 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
20:15:31.665 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:15:31.665 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
20:15:31.962 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:15:33.010 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
20:15:33.010 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
20:15:33.010 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
20:15:36.964 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
20:15:40.418 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 89bd970d-76dd-4223-b9a6-bb481dcba12c
20:15:40.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89bd970d-76dd-4223-b9a6-bb481dcba12c] RpcClient init label, labels = {module=naming, source=sdk}
20:15:40.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89bd970d-76dd-4223-b9a6-bb481dcba12c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
20:15:40.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89bd970d-76dd-4223-b9a6-bb481dcba12c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
20:15:40.423 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89bd970d-76dd-4223-b9a6-bb481dcba12c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
20:15:40.423 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89bd970d-76dd-4223-b9a6-bb481dcba12c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:15:40.543 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89bd970d-76dd-4223-b9a6-bb481dcba12c] Success to connect to server [localhost:8848] on start up, connectionId = 1755605740423_127.0.0.1_14259
20:15:40.543 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89bd970d-76dd-4223-b9a6-bb481dcba12c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:15:40.543 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89bd970d-76dd-4223-b9a6-bb481dcba12c] Notify connected event to listeners.
20:15:40.543 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89bd970d-76dd-4223-b9a6-bb481dcba12c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000016f9c4f1ca0
20:15:40.588 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
20:15:40.604 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
20:15:40.736 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 23.966 seconds (JVM running for 26.585)
20:15:40.752 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
20:15:40.757 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
20:15:40.757 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
20:15:41.133 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89bd970d-76dd-4223-b9a6-bb481dcba12c] Receive server push request, request = NotifySubscriberRequest, requestId = 175
20:15:41.152 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [89bd970d-76dd-4223-b9a6-bb481dcba12c] Ack server push request, request = NotifySubscriberRequest, requestId = 175
20:41:07.088 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:41:07.090 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:41:07.412 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:41:07.412 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3fb0336e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:41:07.412 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755605740423_127.0.0.1_14259
20:41:07.412 [nacos-grpc-client-executor-314] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755605740423_127.0.0.1_14259]Ignore complete event,isRunning:false,isAbandon=false
20:41:07.417 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@55f5f6b8[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 315]
20:41:07.431 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:41:07.439 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:41:07.444 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:41:07.444 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
