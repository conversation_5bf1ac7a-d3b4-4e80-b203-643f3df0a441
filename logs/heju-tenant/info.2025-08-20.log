09:28:19.987 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:28:20.832 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 00ebf222-6ed8-476c-a638-106624a52027_config-0
09:28:20.892 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 32 ms to scan 1 urls, producing 3 keys and 6 values 
09:28:20.916 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 4 keys and 9 values 
09:28:20.927 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 3 keys and 10 values 
09:28:20.938 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:28:20.953 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 1 keys and 7 values 
09:28:20.963 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
09:28:20.967 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00ebf222-6ed8-476c-a638-106624a52027_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:28:20.967 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00ebf222-6ed8-476c-a638-106624a52027_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001f74739f8e0
09:28:20.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00ebf222-6ed8-476c-a638-106624a52027_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001f74739fb00
09:28:20.969 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00ebf222-6ed8-476c-a638-106624a52027_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:28:20.969 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00ebf222-6ed8-476c-a638-106624a52027_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:28:20.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00ebf222-6ed8-476c-a638-106624a52027_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:28:21.969 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00ebf222-6ed8-476c-a638-106624a52027_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755653301739_127.0.0.1_8730
09:28:21.971 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00ebf222-6ed8-476c-a638-106624a52027_config-0] Notify connected event to listeners.
09:28:21.974 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00ebf222-6ed8-476c-a638-106624a52027_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:28:21.975 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00ebf222-6ed8-476c-a638-106624a52027_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001f747519a90
09:28:22.168 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:28:25.600 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:28:25.601 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:28:25.601 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:28:25.820 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:28:27.367 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:28:27.369 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:28:27.369 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:28:33.733 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:28:36.456 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c04fe6c1-9c57-4de1-b005-cc5ebea9890a
09:28:36.457 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] RpcClient init label, labels = {module=naming, source=sdk}
09:28:36.459 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:28:36.459 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:28:36.459 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:28:36.460 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:28:36.588 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Success to connect to server [localhost:8848] on start up, connectionId = 1755653316468_127.0.0.1_8781
09:28:36.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:28:36.593 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Notify connected event to listeners.
09:28:36.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001f747519a90
09:28:36.646 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:28:36.685 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
09:28:36.811 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 17.5 seconds (JVM running for 18.967)
09:28:36.822 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:28:36.825 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:28:36.826 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:28:37.112 [RMI TCP Connection(14)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:28:37.218 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:28:37.238 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:29:12.012 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:29:12.014 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Ack server push request, request = NotifySubscriberRequest, requestId = 13
10:52:07.414 [nacos-grpc-client-executor-1017] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Receive server push request, request = NotifySubscriberRequest, requestId = 15
10:52:07.448 [nacos-grpc-client-executor-1017] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Ack server push request, request = NotifySubscriberRequest, requestId = 15
10:52:39.546 [nacos-grpc-client-executor-1025] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Receive server push request, request = NotifySubscriberRequest, requestId = 18
10:52:39.569 [nacos-grpc-client-executor-1025] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Ack server push request, request = NotifySubscriberRequest, requestId = 18
11:17:39.268 [nacos-grpc-client-executor-1325] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Receive server push request, request = NotifySubscriberRequest, requestId = 22
11:17:39.289 [nacos-grpc-client-executor-1325] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Ack server push request, request = NotifySubscriberRequest, requestId = 22
11:18:06.716 [nacos-grpc-client-executor-1331] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Receive server push request, request = NotifySubscriberRequest, requestId = 25
11:18:06.737 [nacos-grpc-client-executor-1331] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Ack server push request, request = NotifySubscriberRequest, requestId = 25
11:19:14.472 [nacos-grpc-client-executor-1346] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Receive server push request, request = NotifySubscriberRequest, requestId = 29
11:19:14.538 [nacos-grpc-client-executor-1346] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Ack server push request, request = NotifySubscriberRequest, requestId = 29
11:19:41.547 [nacos-grpc-client-executor-1352] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Receive server push request, request = NotifySubscriberRequest, requestId = 32
11:19:41.567 [nacos-grpc-client-executor-1352] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Ack server push request, request = NotifySubscriberRequest, requestId = 32
11:21:04.037 [nacos-grpc-client-executor-1368] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Receive server push request, request = NotifySubscriberRequest, requestId = 36
11:21:04.062 [nacos-grpc-client-executor-1368] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Ack server push request, request = NotifySubscriberRequest, requestId = 36
11:21:30.262 [nacos-grpc-client-executor-1375] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Receive server push request, request = NotifySubscriberRequest, requestId = 39
11:21:30.282 [nacos-grpc-client-executor-1375] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Ack server push request, request = NotifySubscriberRequest, requestId = 39
13:30:37.055 [nacos-grpc-client-executor-2921] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Receive server push request, request = NotifySubscriberRequest, requestId = 45
13:30:37.062 [nacos-grpc-client-executor-2921] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Ack server push request, request = NotifySubscriberRequest, requestId = 45
13:31:26.271 [nacos-grpc-client-executor-2932] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Receive server push request, request = NotifySubscriberRequest, requestId = 49
13:31:26.287 [nacos-grpc-client-executor-2932] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Ack server push request, request = NotifySubscriberRequest, requestId = 49
15:33:42.450 [nacos-grpc-client-executor-4398] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Receive server push request, request = NotifySubscriberRequest, requestId = 54
15:33:42.465 [nacos-grpc-client-executor-4398] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Ack server push request, request = NotifySubscriberRequest, requestId = 54
15:34:00.031 [nacos-grpc-client-executor-4402] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Receive server push request, request = NotifySubscriberRequest, requestId = 58
15:34:00.049 [nacos-grpc-client-executor-4402] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Ack server push request, request = NotifySubscriberRequest, requestId = 58
15:37:17.618 [nacos-grpc-client-executor-4441] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Receive server push request, request = NotifySubscriberRequest, requestId = 63
15:37:17.634 [nacos-grpc-client-executor-4441] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Ack server push request, request = NotifySubscriberRequest, requestId = 63
15:37:33.595 [nacos-grpc-client-executor-4444] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Receive server push request, request = NotifySubscriberRequest, requestId = 67
15:37:33.600 [nacos-grpc-client-executor-4444] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Ack server push request, request = NotifySubscriberRequest, requestId = 67
16:05:29.740 [nacos-grpc-client-executor-4779] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Receive server push request, request = NotifySubscriberRequest, requestId = 72
16:05:29.756 [nacos-grpc-client-executor-4779] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Ack server push request, request = NotifySubscriberRequest, requestId = 72
16:05:54.872 [nacos-grpc-client-executor-4784] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Receive server push request, request = NotifySubscriberRequest, requestId = 76
16:05:54.888 [nacos-grpc-client-executor-4784] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Ack server push request, request = NotifySubscriberRequest, requestId = 76
19:22:23.079 [nacos-grpc-client-executor-7139] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Receive server push request, request = NotifySubscriberRequest, requestId = 81
19:22:23.103 [nacos-grpc-client-executor-7139] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Ack server push request, request = NotifySubscriberRequest, requestId = 81
19:22:53.133 [nacos-grpc-client-executor-7145] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Receive server push request, request = NotifySubscriberRequest, requestId = 85
19:22:53.159 [nacos-grpc-client-executor-7145] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c04fe6c1-9c57-4de1-b005-cc5ebea9890a] Ack server push request, request = NotifySubscriberRequest, requestId = 85
