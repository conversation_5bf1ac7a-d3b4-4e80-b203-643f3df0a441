10:40:47.560 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:40:49.898 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7d6daeb4-e323-41cd-9c22-24b0d4fa5097_config-0
10:40:49.983 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 41 ms to scan 1 urls, producing 3 keys and 6 values 
10:40:50.048 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 34 ms to scan 1 urls, producing 4 keys and 9 values 
10:40:50.071 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 3 keys and 10 values 
10:40:50.093 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 5 values 
10:40:50.112 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 1 keys and 7 values 
10:40:50.126 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
10:40:50.130 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d6daeb4-e323-41cd-9c22-24b0d4fa5097_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:40:50.130 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d6daeb4-e323-41cd-9c22-24b0d4fa5097_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000022dbe39c4e0
10:40:50.130 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d6daeb4-e323-41cd-9c22-24b0d4fa5097_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000022dbe39c700
10:40:50.130 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d6daeb4-e323-41cd-9c22-24b0d4fa5097_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:40:50.130 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d6daeb4-e323-41cd-9c22-24b0d4fa5097_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:40:50.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d6daeb4-e323-41cd-9c22-24b0d4fa5097_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:40:52.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d6daeb4-e323-41cd-9c22-24b0d4fa5097_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754793651989_127.0.0.1_4604
10:40:52.306 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d6daeb4-e323-41cd-9c22-24b0d4fa5097_config-0] Notify connected event to listeners.
10:40:52.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d6daeb4-e323-41cd-9c22-24b0d4fa5097_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:40:52.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7d6daeb4-e323-41cd-9c22-24b0d4fa5097_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000022dbe518ad8
10:40:52.546 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:41:01.634 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
10:41:01.634 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:41:01.635 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:41:02.183 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:41:05.042 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:41:05.046 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:41:05.047 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:41:16.891 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:41:25.726 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1fa3e02a-ddc7-4491-b0db-004c3b8fddfb
10:41:25.727 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fa3e02a-ddc7-4491-b0db-004c3b8fddfb] RpcClient init label, labels = {module=naming, source=sdk}
10:41:25.736 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fa3e02a-ddc7-4491-b0db-004c3b8fddfb] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:41:25.737 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fa3e02a-ddc7-4491-b0db-004c3b8fddfb] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:41:25.738 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fa3e02a-ddc7-4491-b0db-004c3b8fddfb] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:41:25.741 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fa3e02a-ddc7-4491-b0db-004c3b8fddfb] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:41:26.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fa3e02a-ddc7-4491-b0db-004c3b8fddfb] Success to connect to server [localhost:8848] on start up, connectionId = 1754793685761_127.0.0.1_4982
10:41:26.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fa3e02a-ddc7-4491-b0db-004c3b8fddfb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:41:26.094 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fa3e02a-ddc7-4491-b0db-004c3b8fddfb] Notify connected event to listeners.
10:41:26.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fa3e02a-ddc7-4491-b0db-004c3b8fddfb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000022dbe518ad8
10:41:26.634 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
10:41:26.866 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
10:41:27.499 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fa3e02a-ddc7-4491-b0db-004c3b8fddfb] Receive server push request, request = NotifySubscriberRequest, requestId = 7
10:41:27.732 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fa3e02a-ddc7-4491-b0db-004c3b8fddfb] Ack server push request, request = NotifySubscriberRequest, requestId = 7
10:41:28.191 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 42.717 seconds (JVM running for 65.587)
10:41:28.217 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
10:41:28.222 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
10:41:28.223 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
10:45:04.525 [http-nio-9700-exec-7] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:45:17.432 [nacos-grpc-client-executor-53] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fa3e02a-ddc7-4491-b0db-004c3b8fddfb] Receive server push request, request = NotifySubscriberRequest, requestId = 14
10:45:17.432 [nacos-grpc-client-executor-53] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1fa3e02a-ddc7-4491-b0db-004c3b8fddfb] Ack server push request, request = NotifySubscriberRequest, requestId = 14
14:08:20.112 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:08:20.118 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:08:20.453 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:08:20.460 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@48dd8e25[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:08:20.460 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754793685761_127.0.0.1_4982
14:08:20.469 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@30ecd6cf[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 2488]
14:08:20.725 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:08:20.742 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:08:20.769 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:08:20.770 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
