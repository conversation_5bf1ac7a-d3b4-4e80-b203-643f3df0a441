09:11:09.729 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:11:10.739 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 20be1b9d-8f03-4a21-ba66-dd4623bcca29_config-0
09:11:10.806 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 35 ms to scan 1 urls, producing 3 keys and 6 values 
09:11:10.845 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 20 ms to scan 1 urls, producing 4 keys and 9 values 
09:11:10.858 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:11:10.871 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 5 values 
09:11:10.883 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:11:10.894 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
09:11:10.897 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20be1b9d-8f03-4a21-ba66-dd4623bcca29_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:11:10.899 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20be1b9d-8f03-4a21-ba66-dd4623bcca29_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002ac983a0200
09:11:10.899 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20be1b9d-8f03-4a21-ba66-dd4623bcca29_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002ac983a0420
09:11:10.899 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20be1b9d-8f03-4a21-ba66-dd4623bcca29_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:11:10.900 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20be1b9d-8f03-4a21-ba66-dd4623bcca29_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:11:10.910 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20be1b9d-8f03-4a21-ba66-dd4623bcca29_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:11:13.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20be1b9d-8f03-4a21-ba66-dd4623bcca29_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755220272983_127.0.0.1_1915
09:11:13.520 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20be1b9d-8f03-4a21-ba66-dd4623bcca29_config-0] Notify connected event to listeners.
09:11:13.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20be1b9d-8f03-4a21-ba66-dd4623bcca29_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:11:13.522 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [20be1b9d-8f03-4a21-ba66-dd4623bcca29_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002ac9851a6e0
09:11:15.196 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:11:24.280 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:11:24.283 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:11:24.284 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:11:24.927 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:11:26.849 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:11:26.852 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:11:26.853 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:11:36.803 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:11:48.279 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6a7159b7-9350-4e05-979e-b26987b91c67
09:11:48.280 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] RpcClient init label, labels = {module=naming, source=sdk}
09:11:48.283 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:11:48.283 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:11:48.284 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:11:48.285 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:11:48.422 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Success to connect to server [localhost:8848] on start up, connectionId = 1755220308294_127.0.0.1_2077
09:11:48.422 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:11:48.422 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Notify connected event to listeners.
09:11:48.423 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002ac9851a6e0
09:11:48.481 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:11:48.531 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ***********:9700 register finished
09:11:48.779 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 39.875 seconds (JVM running for 41.518)
09:11:48.799 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:11:48.803 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:11:48.804 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:11:49.016 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:11:49.034 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:11:49.410 [RMI TCP Connection(22)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:29:01.951 [nacos-grpc-client-executor-225] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Receive server push request, request = NotifySubscriberRequest, requestId = 14
09:29:01.953 [nacos-grpc-client-executor-225] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Ack server push request, request = NotifySubscriberRequest, requestId = 14
10:18:40.464 [nacos-grpc-client-executor-820] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Receive server push request, request = NotifySubscriberRequest, requestId = 16
10:18:40.468 [nacos-grpc-client-executor-820] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Ack server push request, request = NotifySubscriberRequest, requestId = 16
10:20:02.500 [nacos-grpc-client-executor-836] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Receive server push request, request = NotifySubscriberRequest, requestId = 19
10:20:02.517 [nacos-grpc-client-executor-836] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Ack server push request, request = NotifySubscriberRequest, requestId = 19
13:38:39.865 [nacos-grpc-client-executor-3215] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Receive server push request, request = NotifySubscriberRequest, requestId = 23
13:38:39.875 [nacos-grpc-client-executor-3215] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Ack server push request, request = NotifySubscriberRequest, requestId = 23
13:39:03.978 [nacos-grpc-client-executor-3220] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Receive server push request, request = NotifySubscriberRequest, requestId = 27
13:39:03.994 [nacos-grpc-client-executor-3220] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Ack server push request, request = NotifySubscriberRequest, requestId = 27
15:33:25.135 [nacos-grpc-client-executor-4593] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Receive server push request, request = NotifySubscriberRequest, requestId = 30
15:33:25.155 [nacos-grpc-client-executor-4593] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Ack server push request, request = NotifySubscriberRequest, requestId = 30
15:34:05.335 [nacos-grpc-client-executor-4601] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Receive server push request, request = NotifySubscriberRequest, requestId = 34
15:34:05.347 [nacos-grpc-client-executor-4601] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Ack server push request, request = NotifySubscriberRequest, requestId = 34
15:41:47.649 [nacos-grpc-client-executor-4693] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Receive server push request, request = NotifySubscriberRequest, requestId = 37
15:41:47.662 [nacos-grpc-client-executor-4693] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Ack server push request, request = NotifySubscriberRequest, requestId = 37
15:41:49.398 [nacos-grpc-client-executor-4694] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Receive server push request, request = NotifySubscriberRequest, requestId = 40
15:41:49.413 [nacos-grpc-client-executor-4694] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Ack server push request, request = NotifySubscriberRequest, requestId = 40
15:53:43.198 [nacos-grpc-client-executor-4838] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Receive server push request, request = NotifySubscriberRequest, requestId = 44
15:53:43.212 [nacos-grpc-client-executor-4838] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Ack server push request, request = NotifySubscriberRequest, requestId = 44
15:54:01.389 [nacos-grpc-client-executor-4842] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Receive server push request, request = NotifySubscriberRequest, requestId = 48
15:54:01.411 [nacos-grpc-client-executor-4842] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Ack server push request, request = NotifySubscriberRequest, requestId = 48
16:06:40.316 [nacos-grpc-client-executor-4993] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Receive server push request, request = NotifySubscriberRequest, requestId = 51
16:06:40.338 [nacos-grpc-client-executor-4993] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Ack server push request, request = NotifySubscriberRequest, requestId = 51
16:07:11.295 [nacos-grpc-client-executor-4999] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Receive server push request, request = NotifySubscriberRequest, requestId = 55
16:07:11.310 [nacos-grpc-client-executor-4999] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Ack server push request, request = NotifySubscriberRequest, requestId = 55
16:43:45.342 [nacos-grpc-client-executor-5438] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Receive server push request, request = NotifySubscriberRequest, requestId = 58
16:43:45.362 [nacos-grpc-client-executor-5438] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Ack server push request, request = NotifySubscriberRequest, requestId = 58
16:44:15.402 [nacos-grpc-client-executor-5445] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Receive server push request, request = NotifySubscriberRequest, requestId = 61
16:44:15.420 [nacos-grpc-client-executor-5445] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Ack server push request, request = NotifySubscriberRequest, requestId = 61
16:49:51.859 [nacos-grpc-client-executor-5512] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Receive server push request, request = NotifySubscriberRequest, requestId = 65
16:49:51.873 [nacos-grpc-client-executor-5512] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Ack server push request, request = NotifySubscriberRequest, requestId = 65
16:50:25.306 [nacos-grpc-client-executor-5520] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Receive server push request, request = NotifySubscriberRequest, requestId = 68
16:50:25.321 [nacos-grpc-client-executor-5520] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Ack server push request, request = NotifySubscriberRequest, requestId = 68
16:53:17.740 [nacos-grpc-client-executor-5554] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Receive server push request, request = NotifySubscriberRequest, requestId = 72
16:53:17.763 [nacos-grpc-client-executor-5554] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Ack server push request, request = NotifySubscriberRequest, requestId = 72
16:53:41.640 [nacos-grpc-client-executor-5559] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Receive server push request, request = NotifySubscriberRequest, requestId = 76
16:53:41.657 [nacos-grpc-client-executor-5559] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Ack server push request, request = NotifySubscriberRequest, requestId = 76
17:03:24.852 [nacos-grpc-client-executor-5676] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Receive server push request, request = NotifySubscriberRequest, requestId = 80
17:03:24.864 [nacos-grpc-client-executor-5676] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Ack server push request, request = NotifySubscriberRequest, requestId = 80
17:03:40.911 [nacos-grpc-client-executor-5679] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Receive server push request, request = NotifySubscriberRequest, requestId = 83
17:03:40.926 [nacos-grpc-client-executor-5679] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6a7159b7-9350-4e05-979e-b26987b91c67] Ack server push request, request = NotifySubscriberRequest, requestId = 83
17:29:19.489 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:29:19.499 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:29:19.823 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:29:19.823 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@13b61797[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:29:19.823 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755220308294_127.0.0.1_2077
17:29:19.823 [nacos-grpc-client-executor-5988] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755220308294_127.0.0.1_2077]Ignore complete event,isRunning:false,isAbandon=false
17:29:19.823 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5843c538[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 5989]
17:29:19.964 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:29:19.974 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:29:19.976 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:29:19.976 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
