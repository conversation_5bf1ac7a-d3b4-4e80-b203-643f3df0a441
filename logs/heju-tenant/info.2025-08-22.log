09:23:45.717 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:23:46.371 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 34d46cd0-3188-4d7e-992b-17b34abbe52d_config-0
09:23:46.438 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 34 ms to scan 1 urls, producing 3 keys and 6 values 
09:23:46.458 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 4 keys and 9 values 
09:23:46.477 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 19 ms to scan 1 urls, producing 3 keys and 10 values 
09:23:46.495 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 1 keys and 5 values 
09:23:46.499 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 2 ms to scan 1 urls, producing 1 keys and 7 values 
09:23:46.499 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
09:23:46.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34d46cd0-3188-4d7e-992b-17b34abbe52d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:23:46.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34d46cd0-3188-4d7e-992b-17b34abbe52d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001459539dd00
09:23:46.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34d46cd0-3188-4d7e-992b-17b34abbe52d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001459539df20
09:23:46.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34d46cd0-3188-4d7e-992b-17b34abbe52d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:23:46.515 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34d46cd0-3188-4d7e-992b-17b34abbe52d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:23:46.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34d46cd0-3188-4d7e-992b-17b34abbe52d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:23:47.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34d46cd0-3188-4d7e-992b-17b34abbe52d_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755825827223_127.0.0.1_7936
09:23:47.454 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34d46cd0-3188-4d7e-992b-17b34abbe52d_config-0] Notify connected event to listeners.
09:23:47.454 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34d46cd0-3188-4d7e-992b-17b34abbe52d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:23:47.458 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34d46cd0-3188-4d7e-992b-17b34abbe52d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000014595515f18
09:23:47.671 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:23:52.855 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:23:52.855 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:23:52.855 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:23:53.135 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:23:54.287 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:23:54.288 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:23:54.288 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:23:56.750 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:23:59.239 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b8e8b522-d33f-4237-83d8-21b7f463eb51
09:23:59.239 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8e8b522-d33f-4237-83d8-21b7f463eb51] RpcClient init label, labels = {module=naming, source=sdk}
09:23:59.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8e8b522-d33f-4237-83d8-21b7f463eb51] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:23:59.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8e8b522-d33f-4237-83d8-21b7f463eb51] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:23:59.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8e8b522-d33f-4237-83d8-21b7f463eb51] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:23:59.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8e8b522-d33f-4237-83d8-21b7f463eb51] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:23:59.364 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8e8b522-d33f-4237-83d8-21b7f463eb51] Success to connect to server [localhost:8848] on start up, connectionId = 1755825839249_127.0.0.1_7959
09:23:59.364 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8e8b522-d33f-4237-83d8-21b7f463eb51] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:23:59.364 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8e8b522-d33f-4237-83d8-21b7f463eb51] Notify connected event to listeners.
09:23:59.364 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8e8b522-d33f-4237-83d8-21b7f463eb51] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000014595515f18
09:23:59.414 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:23:59.438 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
09:23:59.539 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 14.409 seconds (JVM running for 25.093)
09:23:59.548 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:23:59.549 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:23:59.550 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:23:59.998 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8e8b522-d33f-4237-83d8-21b7f463eb51] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:24:00.021 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8e8b522-d33f-4237-83d8-21b7f463eb51] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:26:44.241 [http-nio-9700-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:26:48.077 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8e8b522-d33f-4237-83d8-21b7f463eb51] Receive server push request, request = NotifySubscriberRequest, requestId = 14
09:26:48.077 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8e8b522-d33f-4237-83d8-21b7f463eb51] Ack server push request, request = NotifySubscriberRequest, requestId = 14
13:58:16.987 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:58:16.992 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:58:17.322 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:58:17.322 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@96aa4b0[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:58:17.323 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755825839249_127.0.0.1_7959
13:58:17.326 [nacos-grpc-client-executor-3303] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755825839249_127.0.0.1_7959]Ignore complete event,isRunning:false,isAbandon=false
13:58:17.331 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@235de444[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3304]
13:58:17.498 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:58:17.503 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:58:17.522 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:58:17.522 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:58:48.206 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:58:50.023 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e8cdc97d-5266-4c97-a58f-01204602ec2b_config-0
13:58:50.178 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 74 ms to scan 1 urls, producing 3 keys and 6 values 
13:58:50.262 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 34 ms to scan 1 urls, producing 4 keys and 9 values 
13:58:50.306 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 44 ms to scan 1 urls, producing 3 keys and 10 values 
13:58:50.339 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 33 ms to scan 1 urls, producing 1 keys and 5 values 
13:58:50.359 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 1 keys and 7 values 
13:58:50.385 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 2 keys and 8 values 
13:58:50.389 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8cdc97d-5266-4c97-a58f-01204602ec2b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:58:50.389 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8cdc97d-5266-4c97-a58f-01204602ec2b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x00000219d239da58
13:58:50.389 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8cdc97d-5266-4c97-a58f-01204602ec2b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000219d239dc78
13:58:50.389 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8cdc97d-5266-4c97-a58f-01204602ec2b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:58:50.389 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8cdc97d-5266-4c97-a58f-01204602ec2b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:58:50.423 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8cdc97d-5266-4c97-a58f-01204602ec2b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:58:52.721 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8cdc97d-5266-4c97-a58f-01204602ec2b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755842332356_127.0.0.1_1242
13:58:52.724 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8cdc97d-5266-4c97-a58f-01204602ec2b_config-0] Notify connected event to listeners.
13:58:52.724 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8cdc97d-5266-4c97-a58f-01204602ec2b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:58:52.725 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8cdc97d-5266-4c97-a58f-01204602ec2b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000219d2515d18
13:58:53.038 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:59:00.756 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
13:59:00.768 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:59:00.768 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:59:01.157 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:59:02.805 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:59:02.808 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:59:02.809 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:59:06.655 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:59:09.488 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8f8c0ee8-f381-4e40-8067-68275ee2b1f0
13:59:09.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f8c0ee8-f381-4e40-8067-68275ee2b1f0] RpcClient init label, labels = {module=naming, source=sdk}
13:59:09.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f8c0ee8-f381-4e40-8067-68275ee2b1f0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:59:09.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f8c0ee8-f381-4e40-8067-68275ee2b1f0] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:59:09.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f8c0ee8-f381-4e40-8067-68275ee2b1f0] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:59:09.489 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f8c0ee8-f381-4e40-8067-68275ee2b1f0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:59:09.621 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f8c0ee8-f381-4e40-8067-68275ee2b1f0] Success to connect to server [localhost:8848] on start up, connectionId = 1755842349489_127.0.0.1_1262
13:59:09.622 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f8c0ee8-f381-4e40-8067-68275ee2b1f0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:59:09.622 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f8c0ee8-f381-4e40-8067-68275ee2b1f0] Notify connected event to listeners.
13:59:09.622 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f8c0ee8-f381-4e40-8067-68275ee2b1f0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000219d2515d18
13:59:09.673 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
13:59:09.689 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
13:59:09.805 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 22.816 seconds (JVM running for 33.759)
13:59:09.831 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
13:59:09.831 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
13:59:09.831 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
13:59:10.156 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f8c0ee8-f381-4e40-8067-68275ee2b1f0] Receive server push request, request = NotifySubscriberRequest, requestId = 8
13:59:10.172 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f8c0ee8-f381-4e40-8067-68275ee2b1f0] Ack server push request, request = NotifySubscriberRequest, requestId = 8
16:39:31.735 [http-nio-9700-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:03:53.161 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:03:53.161 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:03:53.519 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:03:53.519 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4c2ef87b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:03:53.519 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755842349489_127.0.0.1_1262
18:03:53.520 [nacos-grpc-client-executor-2942] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755842349489_127.0.0.1_1262]Ignore complete event,isRunning:false,isAbandon=false
18:03:53.520 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@67f45d24[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2943]
18:03:53.540 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:03:53.543 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:03:53.549 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:03:53.549 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
