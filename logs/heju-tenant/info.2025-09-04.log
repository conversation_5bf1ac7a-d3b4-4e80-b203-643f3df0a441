16:41:23.034 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:41:23.731 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 11c91916-4e6d-46f3-983b-bf27cb270cbb_config-0
16:41:23.825 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 43 ms to scan 1 urls, producing 3 keys and 6 values 
16:41:23.856 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 4 keys and 9 values 
16:41:23.867 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 3 keys and 10 values 
16:41:23.880 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
16:41:23.900 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 1 keys and 7 values 
16:41:23.910 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
16:41:23.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11c91916-4e6d-46f3-983b-bf27cb270cbb_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:41:23.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11c91916-4e6d-46f3-983b-bf27cb270cbb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002163b39f8e0
16:41:23.916 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11c91916-4e6d-46f3-983b-bf27cb270cbb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002163b39fb00
16:41:23.917 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11c91916-4e6d-46f3-983b-bf27cb270cbb_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:41:23.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11c91916-4e6d-46f3-983b-bf27cb270cbb_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:41:23.928 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11c91916-4e6d-46f3-983b-bf27cb270cbb_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:41:25.168 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11c91916-4e6d-46f3-983b-bf27cb270cbb_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756975284890_127.0.0.1_3461
16:41:25.168 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11c91916-4e6d-46f3-983b-bf27cb270cbb_config-0] Notify connected event to listeners.
16:41:25.168 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11c91916-4e6d-46f3-983b-bf27cb270cbb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:41:25.168 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11c91916-4e6d-46f3-983b-bf27cb270cbb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002163b519a90
16:41:25.385 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:41:28.730 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
16:41:28.731 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:41:28.731 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:41:28.881 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:41:29.608 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:41:29.610 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:41:29.610 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:41:32.249 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:41:34.642 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 561b14f9-e171-4624-b6cb-6414a4e27448
16:41:34.642 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [561b14f9-e171-4624-b6cb-6414a4e27448] RpcClient init label, labels = {module=naming, source=sdk}
16:41:34.643 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [561b14f9-e171-4624-b6cb-6414a4e27448] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:41:34.643 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [561b14f9-e171-4624-b6cb-6414a4e27448] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:41:34.643 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [561b14f9-e171-4624-b6cb-6414a4e27448] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:41:34.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [561b14f9-e171-4624-b6cb-6414a4e27448] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:41:34.767 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [561b14f9-e171-4624-b6cb-6414a4e27448] Success to connect to server [localhost:8848] on start up, connectionId = 1756975294652_127.0.0.1_3507
16:41:34.767 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [561b14f9-e171-4624-b6cb-6414a4e27448] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:41:34.767 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [561b14f9-e171-4624-b6cb-6414a4e27448] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002163b519a90
16:41:34.767 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [561b14f9-e171-4624-b6cb-6414a4e27448] Notify connected event to listeners.
16:41:34.814 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
16:41:34.841 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
16:41:34.947 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 12.534 seconds (JVM running for 14.763)
16:41:34.959 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
16:41:34.962 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
16:41:34.962 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
16:41:35.365 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:41:35.400 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [561b14f9-e171-4624-b6cb-6414a4e27448] Receive server push request, request = NotifySubscriberRequest, requestId = 3
16:41:35.414 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [561b14f9-e171-4624-b6cb-6414a4e27448] Ack server push request, request = NotifySubscriberRequest, requestId = 3
20:10:41.412 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:10:41.417 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:10:41.750 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:10:41.750 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@622cebf1[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:10:41.751 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756975294652_127.0.0.1_3507
20:10:41.756 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7a4b0885[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 2522]
20:10:41.762 [nacos-grpc-client-executor-2522] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756975294652_127.0.0.1_3507]Ignore complete event,isRunning:false,isAbandon=false
20:10:41.940 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:10:41.954 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:10:41.962 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:10:41.962 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
