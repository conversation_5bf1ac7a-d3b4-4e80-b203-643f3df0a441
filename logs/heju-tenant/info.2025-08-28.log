09:02:13.404 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:02:13.408 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:02:13.734 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:02:13.737 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3f904df6[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:02:13.737 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756257901020_127.0.0.1_14843
09:02:13.740 [nacos-grpc-client-executor-16983] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756257901020_127.0.0.1_14843]Ignore complete event,isRunning:false,isAbandon=false
09:02:13.744 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4ae9397f[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 16984]
09:02:13.897 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:02:13.934 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:02:13.934 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:02:13.934 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:35:37.156 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:35:38.047 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ee981ebb-98f1-42c2-a57f-a1485cf7fde4_config-0
09:35:38.122 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 39 ms to scan 1 urls, producing 3 keys and 6 values 
09:35:38.151 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
09:35:38.161 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
09:35:38.172 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:35:38.185 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
09:35:38.196 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:35:38.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee981ebb-98f1-42c2-a57f-a1485cf7fde4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:35:38.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee981ebb-98f1-42c2-a57f-a1485cf7fde4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002a99239fd80
09:35:38.201 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee981ebb-98f1-42c2-a57f-a1485cf7fde4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002a9923a0000
09:35:38.201 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee981ebb-98f1-42c2-a57f-a1485cf7fde4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:35:38.202 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee981ebb-98f1-42c2-a57f-a1485cf7fde4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:35:38.211 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee981ebb-98f1-42c2-a57f-a1485cf7fde4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:35:39.371 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee981ebb-98f1-42c2-a57f-a1485cf7fde4_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756344939177_127.0.0.1_9649
09:35:39.372 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee981ebb-98f1-42c2-a57f-a1485cf7fde4_config-0] Notify connected event to listeners.
09:35:39.372 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee981ebb-98f1-42c2-a57f-a1485cf7fde4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:35:39.373 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee981ebb-98f1-42c2-a57f-a1485cf7fde4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002a99251a598
09:35:39.550 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:35:42.801 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:35:42.802 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:35:42.802 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:35:42.950 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:35:43.657 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:35:43.659 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:35:43.659 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:35:46.192 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:35:49.044 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1724ae51-752f-450d-919d-90b7a82ba8ea
09:35:49.045 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] RpcClient init label, labels = {module=naming, source=sdk}
09:35:49.046 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:35:49.046 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:35:49.047 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:35:49.047 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:35:49.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Success to connect to server [localhost:8848] on start up, connectionId = 1756344949058_127.0.0.1_9680
09:35:49.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:35:49.184 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Notify connected event to listeners.
09:35:49.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002a99251a598
09:35:49.256 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:35:49.291 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
09:35:49.411 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 13.079 seconds (JVM running for 14.694)
09:35:49.427 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:35:49.430 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:35:49.431 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:35:49.751 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:35:49.772 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:35:49.885 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:41:26.732 [nacos-grpc-client-executor-83] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:41:26.734 [nacos-grpc-client-executor-83] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Ack server push request, request = NotifySubscriberRequest, requestId = 13
11:05:31.396 [nacos-grpc-client-executor-1093] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Receive server push request, request = NotifySubscriberRequest, requestId = 16
11:05:31.416 [nacos-grpc-client-executor-1093] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Ack server push request, request = NotifySubscriberRequest, requestId = 16
11:06:21.463 [nacos-grpc-client-executor-1103] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Receive server push request, request = NotifySubscriberRequest, requestId = 20
11:06:21.485 [nacos-grpc-client-executor-1103] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Ack server push request, request = NotifySubscriberRequest, requestId = 20
12:02:49.264 [nacos-grpc-client-executor-1781] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Receive server push request, request = NotifySubscriberRequest, requestId = 23
12:02:49.279 [nacos-grpc-client-executor-1781] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Ack server push request, request = NotifySubscriberRequest, requestId = 23
12:03:16.613 [nacos-grpc-client-executor-1787] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Receive server push request, request = NotifySubscriberRequest, requestId = 27
12:03:16.632 [nacos-grpc-client-executor-1787] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Ack server push request, request = NotifySubscriberRequest, requestId = 27
14:00:28.724 [nacos-grpc-client-executor-3192] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Receive server push request, request = NotifySubscriberRequest, requestId = 30
14:00:28.745 [nacos-grpc-client-executor-3192] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Ack server push request, request = NotifySubscriberRequest, requestId = 30
14:00:47.313 [nacos-grpc-client-executor-3197] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Receive server push request, request = NotifySubscriberRequest, requestId = 33
14:00:47.326 [nacos-grpc-client-executor-3197] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Ack server push request, request = NotifySubscriberRequest, requestId = 33
15:14:20.922 [nacos-grpc-client-executor-4078] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Receive server push request, request = NotifySubscriberRequest, requestId = 37
15:14:20.936 [nacos-grpc-client-executor-4078] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Ack server push request, request = NotifySubscriberRequest, requestId = 37
15:14:57.645 [nacos-grpc-client-executor-4086] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Receive server push request, request = NotifySubscriberRequest, requestId = 40
15:14:57.656 [nacos-grpc-client-executor-4086] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Ack server push request, request = NotifySubscriberRequest, requestId = 40
16:30:18.876 [nacos-grpc-client-executor-4992] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Receive server push request, request = NotifySubscriberRequest, requestId = 44
16:30:18.897 [nacos-grpc-client-executor-4992] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Ack server push request, request = NotifySubscriberRequest, requestId = 44
16:30:39.523 [nacos-grpc-client-executor-4996] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Receive server push request, request = NotifySubscriberRequest, requestId = 48
16:30:39.541 [nacos-grpc-client-executor-4996] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Ack server push request, request = NotifySubscriberRequest, requestId = 48
16:59:35.279 [nacos-grpc-client-executor-5342] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Receive server push request, request = NotifySubscriberRequest, requestId = 51
16:59:35.298 [nacos-grpc-client-executor-5342] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Ack server push request, request = NotifySubscriberRequest, requestId = 51
16:59:55.470 [nacos-grpc-client-executor-5346] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Receive server push request, request = NotifySubscriberRequest, requestId = 55
16:59:55.478 [nacos-grpc-client-executor-5346] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Ack server push request, request = NotifySubscriberRequest, requestId = 55
17:09:06.143 [nacos-grpc-client-executor-5457] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Receive server push request, request = NotifySubscriberRequest, requestId = 58
17:09:06.158 [nacos-grpc-client-executor-5457] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Ack server push request, request = NotifySubscriberRequest, requestId = 58
17:09:26.324 [nacos-grpc-client-executor-5461] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Receive server push request, request = NotifySubscriberRequest, requestId = 62
17:09:26.332 [nacos-grpc-client-executor-5461] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Ack server push request, request = NotifySubscriberRequest, requestId = 62
17:19:28.113 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:19:28.120 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:19:28.446 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:19:28.446 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4f818292[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:19:28.446 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756344949058_127.0.0.1_9680
17:19:28.446 [nacos-grpc-client-executor-5584] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756344949058_127.0.0.1_9680]Ignore complete event,isRunning:false,isAbandon=false
17:19:28.452 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@df67235[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 5585]
17:19:28.587 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:19:28.587 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:19:28.587 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:19:28.587 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:22:37.379 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:22:39.244 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 84f6dda8-dcaf-4c83-a0fd-814fed6f0452_config-0
17:22:39.392 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 78 ms to scan 1 urls, producing 3 keys and 6 values 
17:22:39.461 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 4 keys and 9 values 
17:22:39.501 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 33 ms to scan 1 urls, producing 3 keys and 10 values 
17:22:39.525 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 5 values 
17:22:39.551 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 1 keys and 7 values 
17:22:39.577 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 2 keys and 8 values 
17:22:39.585 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84f6dda8-dcaf-4c83-a0fd-814fed6f0452_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:22:39.587 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84f6dda8-dcaf-4c83-a0fd-814fed6f0452_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000019ee33b7d80
17:22:39.589 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84f6dda8-dcaf-4c83-a0fd-814fed6f0452_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000019ee33b8000
17:22:39.590 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84f6dda8-dcaf-4c83-a0fd-814fed6f0452_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:22:39.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84f6dda8-dcaf-4c83-a0fd-814fed6f0452_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:22:39.619 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84f6dda8-dcaf-4c83-a0fd-814fed6f0452_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:22:41.607 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84f6dda8-dcaf-4c83-a0fd-814fed6f0452_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756372961260_127.0.0.1_8851
17:22:41.608 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84f6dda8-dcaf-4c83-a0fd-814fed6f0452_config-0] Notify connected event to listeners.
17:22:41.609 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84f6dda8-dcaf-4c83-a0fd-814fed6f0452_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:22:41.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84f6dda8-dcaf-4c83-a0fd-814fed6f0452_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000019ee34f20a0
17:22:41.882 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:22:48.356 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
17:22:48.357 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:22:48.357 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:22:48.648 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:22:49.834 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:22:49.837 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:22:49.837 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:22:54.644 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:22:59.489 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8b06a579-96a7-4e16-a916-72d521ff2c6e
17:22:59.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b06a579-96a7-4e16-a916-72d521ff2c6e] RpcClient init label, labels = {module=naming, source=sdk}
17:22:59.493 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b06a579-96a7-4e16-a916-72d521ff2c6e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:22:59.493 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b06a579-96a7-4e16-a916-72d521ff2c6e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:22:59.493 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b06a579-96a7-4e16-a916-72d521ff2c6e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:22:59.494 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b06a579-96a7-4e16-a916-72d521ff2c6e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:22:59.623 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b06a579-96a7-4e16-a916-72d521ff2c6e] Success to connect to server [localhost:8848] on start up, connectionId = 1756372979506_127.0.0.1_8914
17:22:59.623 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b06a579-96a7-4e16-a916-72d521ff2c6e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:22:59.623 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b06a579-96a7-4e16-a916-72d521ff2c6e] Notify connected event to listeners.
17:22:59.623 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b06a579-96a7-4e16-a916-72d521ff2c6e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000019ee34f20a0
17:22:59.693 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
17:22:59.735 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
17:22:59.930 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 24.151 seconds (JVM running for 27.097)
17:22:59.950 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
17:22:59.953 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
17:22:59.955 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
17:23:00.224 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b06a579-96a7-4e16-a916-72d521ff2c6e] Receive server push request, request = NotifySubscriberRequest, requestId = 67
17:23:00.244 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b06a579-96a7-4e16-a916-72d521ff2c6e] Ack server push request, request = NotifySubscriberRequest, requestId = 67
17:26:53.314 [http-nio-9700-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:22:53.021 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b06a579-96a7-4e16-a916-72d521ff2c6e] Try to reconnect to a new server, server is  not appointed, will choose a random server.
20:22:53.021 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84f6dda8-dcaf-4c83-a0fd-814fed6f0452_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
20:22:53.186 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84f6dda8-dcaf-4c83-a0fd-814fed6f0452_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:22:53.187 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b06a579-96a7-4e16-a916-72d521ff2c6e] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:22:53.405 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b06a579-96a7-4e16-a916-72d521ff2c6e] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:22:53.405 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84f6dda8-dcaf-4c83-a0fd-814fed6f0452_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:22:53.723 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b06a579-96a7-4e16-a916-72d521ff2c6e] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:22:53.723 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84f6dda8-dcaf-4c83-a0fd-814fed6f0452_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:22:54.143 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84f6dda8-dcaf-4c83-a0fd-814fed6f0452_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:22:54.175 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b06a579-96a7-4e16-a916-72d521ff2c6e] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:22:54.679 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b06a579-96a7-4e16-a916-72d521ff2c6e] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:22:54.703 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84f6dda8-dcaf-4c83-a0fd-814fed6f0452_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:22:55.303 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b06a579-96a7-4e16-a916-72d521ff2c6e] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:22:55.315 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84f6dda8-dcaf-4c83-a0fd-814fed6f0452_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:22:56.016 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b06a579-96a7-4e16-a916-72d521ff2c6e] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:22:56.037 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84f6dda8-dcaf-4c83-a0fd-814fed6f0452_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:22:56.417 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:22:56.747 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:22:56.832 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b06a579-96a7-4e16-a916-72d521ff2c6e] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:22:56.848 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [84f6dda8-dcaf-4c83-a0fd-814fed6f0452_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
20:22:57.083 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:22:57.083 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@76ea6dd5[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:22:57.084 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b06a579-96a7-4e16-a916-72d521ff2c6e] Client is shutdown, stop reconnect to server
20:22:57.084 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756372979506_127.0.0.1_8914
20:22:57.085 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7386d45a[Running, pool size = 20, active threads = 0, queued tasks = 0, completed tasks = 2183]
20:22:57.112 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:22:57.114 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:22:57.127 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:22:57.127 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
