09:17:37.020 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:17:38.465 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2517acd4-2bdc-469b-99e5-5785b5c1cd4b_config-0
09:17:38.550 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 35 ms to scan 1 urls, producing 3 keys and 6 values 
09:17:38.581 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 4 keys and 9 values 
09:17:38.594 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 3 keys and 10 values 
09:17:38.604 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:17:38.614 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:17:38.630 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
09:17:38.634 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2517acd4-2bdc-469b-99e5-5785b5c1cd4b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:17:38.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2517acd4-2bdc-469b-99e5-5785b5c1cd4b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000002a8603b8200
09:17:38.636 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2517acd4-2bdc-469b-99e5-5785b5c1cd4b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002a8603b8420
09:17:38.636 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2517acd4-2bdc-469b-99e5-5785b5c1cd4b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:17:38.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2517acd4-2bdc-469b-99e5-5785b5c1cd4b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:17:38.645 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2517acd4-2bdc-469b-99e5-5785b5c1cd4b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:17:40.078 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2517acd4-2bdc-469b-99e5-5785b5c1cd4b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755134259739_127.0.0.1_14573
09:17:40.080 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2517acd4-2bdc-469b-99e5-5785b5c1cd4b_config-0] Notify connected event to listeners.
09:17:40.081 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2517acd4-2bdc-469b-99e5-5785b5c1cd4b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:17:40.085 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2517acd4-2bdc-469b-99e5-5785b5c1cd4b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002a8604f9ca0
09:17:40.365 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:17:46.615 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:17:46.616 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:17:46.617 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:17:46.988 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:17:48.216 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:17:48.218 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:17:48.219 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:17:53.873 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:17:58.486 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1a867e18-6434-4a6d-8c00-9318a9438096
09:17:58.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a867e18-6434-4a6d-8c00-9318a9438096] RpcClient init label, labels = {module=naming, source=sdk}
09:17:58.494 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a867e18-6434-4a6d-8c00-9318a9438096] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:17:58.494 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a867e18-6434-4a6d-8c00-9318a9438096] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:17:58.495 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a867e18-6434-4a6d-8c00-9318a9438096] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:17:58.495 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a867e18-6434-4a6d-8c00-9318a9438096] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:17:58.634 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a867e18-6434-4a6d-8c00-9318a9438096] Success to connect to server [localhost:8848] on start up, connectionId = 1755134278509_127.0.0.1_14691
09:17:58.634 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a867e18-6434-4a6d-8c00-9318a9438096] Notify connected event to listeners.
09:17:58.634 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a867e18-6434-4a6d-8c00-9318a9438096] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:17:58.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a867e18-6434-4a6d-8c00-9318a9438096] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002a8604f9ca0
09:17:58.694 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:17:58.730 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ***********:9700 register finished
09:17:58.957 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 22.757 seconds (JVM running for 24.367)
09:17:58.972 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:17:58.976 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:17:58.977 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:17:59.233 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a867e18-6434-4a6d-8c00-9318a9438096] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:17:59.248 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a867e18-6434-4a6d-8c00-9318a9438096] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:17:59.427 [RMI TCP Connection(12)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:19:49.520 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a867e18-6434-4a6d-8c00-9318a9438096] Receive server push request, request = NotifySubscriberRequest, requestId = 14
09:19:49.521 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a867e18-6434-4a6d-8c00-9318a9438096] Ack server push request, request = NotifySubscriberRequest, requestId = 14
11:39:40.062 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:39:40.077 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:39:40.417 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:39:40.419 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@20c2ca98[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:39:40.419 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755134278509_127.0.0.1_14691
11:39:40.425 [nacos-grpc-client-executor-1714] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755134278509_127.0.0.1_14691]Ignore complete event,isRunning:false,isAbandon=false
11:39:40.437 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@43e2517f[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1715]
11:39:40.614 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:39:40.625 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:39:40.640 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:39:40.641 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:39:57.872 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:39:59.322 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 965f9353-a362-452b-90eb-14d56cf2bfde_config-0
11:39:59.418 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 48 ms to scan 1 urls, producing 3 keys and 6 values 
11:39:59.453 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 4 keys and 9 values 
11:39:59.482 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
11:39:59.514 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
11:39:59.529 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
11:39:59.549 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
11:39:59.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [965f9353-a362-452b-90eb-14d56cf2bfde_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:39:59.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [965f9353-a362-452b-90eb-14d56cf2bfde_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001cd2539cfb8
11:39:59.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [965f9353-a362-452b-90eb-14d56cf2bfde_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001cd2539d1d8
11:39:59.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [965f9353-a362-452b-90eb-14d56cf2bfde_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:39:59.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [965f9353-a362-452b-90eb-14d56cf2bfde_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:39:59.579 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [965f9353-a362-452b-90eb-14d56cf2bfde_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:40:01.248 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [965f9353-a362-452b-90eb-14d56cf2bfde_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755142800930_127.0.0.1_10485
11:40:01.250 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [965f9353-a362-452b-90eb-14d56cf2bfde_config-0] Notify connected event to listeners.
11:40:01.250 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [965f9353-a362-452b-90eb-14d56cf2bfde_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:40:01.253 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [965f9353-a362-452b-90eb-14d56cf2bfde_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001cd25515418
11:40:01.556 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:40:04.717 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
11:40:04.717 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:40:04.717 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:40:04.911 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:40:05.706 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:40:05.708 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:40:05.709 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:40:08.467 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:40:11.288 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f2d9de32-138b-4c6c-b2ef-d8191aad78dd
11:40:11.288 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d9de32-138b-4c6c-b2ef-d8191aad78dd] RpcClient init label, labels = {module=naming, source=sdk}
11:40:11.291 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d9de32-138b-4c6c-b2ef-d8191aad78dd] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:40:11.292 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d9de32-138b-4c6c-b2ef-d8191aad78dd] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:40:11.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d9de32-138b-4c6c-b2ef-d8191aad78dd] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:40:11.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d9de32-138b-4c6c-b2ef-d8191aad78dd] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:40:11.428 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d9de32-138b-4c6c-b2ef-d8191aad78dd] Success to connect to server [localhost:8848] on start up, connectionId = 1755142811297_127.0.0.1_10523
11:40:11.429 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d9de32-138b-4c6c-b2ef-d8191aad78dd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:40:11.429 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d9de32-138b-4c6c-b2ef-d8191aad78dd] Notify connected event to listeners.
11:40:11.430 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d9de32-138b-4c6c-b2ef-d8191aad78dd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001cd25515418
11:40:11.505 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
11:40:11.550 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ***********:9700 register finished
11:40:11.706 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 15.18 seconds (JVM running for 29.246)
11:40:11.718 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
11:40:11.718 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
11:40:11.725 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
11:40:12.042 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d9de32-138b-4c6c-b2ef-d8191aad78dd] Receive server push request, request = NotifySubscriberRequest, requestId = 18
11:40:12.061 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d9de32-138b-4c6c-b2ef-d8191aad78dd] Ack server push request, request = NotifySubscriberRequest, requestId = 18
19:41:06.203 [http-nio-9700-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:02:47.696 [nacos-grpc-client-executor-6032] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d9de32-138b-4c6c-b2ef-d8191aad78dd] Receive server push request, request = NotifySubscriberRequest, requestId = 63
20:02:47.696 [nacos-grpc-client-executor-6032] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f2d9de32-138b-4c6c-b2ef-d8191aad78dd] Ack server push request, request = NotifySubscriberRequest, requestId = 63
20:30:12.932 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:30:12.937 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:30:13.272 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:30:13.274 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6c29b5a7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:30:13.274 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755142811297_127.0.0.1_10523
20:30:13.276 [nacos-grpc-client-executor-6365] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755142811297_127.0.0.1_10523]Ignore complete event,isRunning:false,isAbandon=false
20:30:13.280 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@709b77bf[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 6366]
20:30:13.432 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:30:13.437 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:30:13.441 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:30:13.441 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
