09:33:02.715 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:33:04.196 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0b1021bd-3afd-4922-92de-c2bb8c2ea3e5_config-0
09:33:04.323 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 66 ms to scan 1 urls, producing 3 keys and 6 values 
09:33:04.386 [main] INFO  o.r.Reflections - [scan,232] - <PERSON>flections took 25 ms to scan 1 urls, producing 4 keys and 9 values 
09:33:04.429 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 37 ms to scan 1 urls, producing 3 keys and 10 values 
09:33:04.452 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 17 ms to scan 1 urls, producing 1 keys and 5 values 
09:33:04.478 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 22 ms to scan 1 urls, producing 1 keys and 7 values 
09:33:04.502 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 2 keys and 8 values 
09:33:04.509 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b1021bd-3afd-4922-92de-c2bb8c2ea3e5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:33:04.511 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b1021bd-3afd-4922-92de-c2bb8c2ea3e5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x00000247c239cfb8
09:33:04.512 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b1021bd-3afd-4922-92de-c2bb8c2ea3e5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000247c239d1d8
09:33:04.512 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b1021bd-3afd-4922-92de-c2bb8c2ea3e5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:33:04.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b1021bd-3afd-4922-92de-c2bb8c2ea3e5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:33:04.533 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b1021bd-3afd-4922-92de-c2bb8c2ea3e5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:33:06.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b1021bd-3afd-4922-92de-c2bb8c2ea3e5_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754962386292_127.0.0.1_12690
09:33:06.639 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b1021bd-3afd-4922-92de-c2bb8c2ea3e5_config-0] Notify connected event to listeners.
09:33:06.641 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b1021bd-3afd-4922-92de-c2bb8c2ea3e5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:33:06.641 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b1021bd-3afd-4922-92de-c2bb8c2ea3e5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000247c2514f98
09:33:06.966 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:33:18.900 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:33:18.902 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:33:18.902 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:33:19.509 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:33:21.528 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:33:21.530 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:33:21.530 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:33:28.211 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:33:38.686 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bdc8a6db-879b-4a77-826c-526591f71bb2
09:33:38.686 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdc8a6db-879b-4a77-826c-526591f71bb2] RpcClient init label, labels = {module=naming, source=sdk}
09:33:38.690 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdc8a6db-879b-4a77-826c-526591f71bb2] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:33:38.692 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdc8a6db-879b-4a77-826c-526591f71bb2] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:33:38.692 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdc8a6db-879b-4a77-826c-526591f71bb2] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:33:38.694 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdc8a6db-879b-4a77-826c-526591f71bb2] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:33:38.829 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdc8a6db-879b-4a77-826c-526591f71bb2] Success to connect to server [localhost:8848] on start up, connectionId = 1754962418700_127.0.0.1_12780
09:33:38.829 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdc8a6db-879b-4a77-826c-526591f71bb2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:33:38.829 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdc8a6db-879b-4a77-826c-526591f71bb2] Notify connected event to listeners.
09:33:38.829 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdc8a6db-879b-4a77-826c-526591f71bb2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000247c2514f98
09:33:38.921 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:33:38.995 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ***********:9700 register finished
09:33:39.294 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 37.445 seconds (JVM running for 44.199)
09:33:39.321 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:33:39.325 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:33:39.330 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:33:39.457 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdc8a6db-879b-4a77-826c-526591f71bb2] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:33:39.484 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bdc8a6db-879b-4a77-826c-526591f71bb2] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:37:01.394 [http-nio-9700-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:52:58.380 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:52:58.380 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:52:58.744 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:52:58.744 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@132ae8e1[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:52:58.747 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754962418700_127.0.0.1_12780
14:52:58.751 [nacos-grpc-client-executor-3840] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754962418700_127.0.0.1_12780]Ignore complete event,isRunning:false,isAbandon=false
14:52:58.760 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@48eefb1b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3841]
14:52:58.847 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:52:58.847 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:52:58.885 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:52:58.885 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:55:45.561 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:55:47.319 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 605bce63-8cb3-4a1c-8d0a-544eab4d6988_config-0
14:55:47.438 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 57 ms to scan 1 urls, producing 3 keys and 6 values 
14:55:47.498 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 4 keys and 9 values 
14:55:47.542 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 3 keys and 10 values 
14:55:47.568 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 5 values 
14:55:47.595 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 1 keys and 7 values 
14:55:47.645 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 40 ms to scan 1 urls, producing 2 keys and 8 values 
14:55:47.656 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [605bce63-8cb3-4a1c-8d0a-544eab4d6988_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:55:47.658 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [605bce63-8cb3-4a1c-8d0a-544eab4d6988_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000020ba83b7d80
14:55:47.659 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [605bce63-8cb3-4a1c-8d0a-544eab4d6988_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000020ba83b8000
14:55:47.661 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [605bce63-8cb3-4a1c-8d0a-544eab4d6988_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:55:47.664 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [605bce63-8cb3-4a1c-8d0a-544eab4d6988_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:55:47.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [605bce63-8cb3-4a1c-8d0a-544eab4d6988_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:55:51.974 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [605bce63-8cb3-4a1c-8d0a-544eab4d6988_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754981751240_127.0.0.1_6594
14:55:51.976 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [605bce63-8cb3-4a1c-8d0a-544eab4d6988_config-0] Notify connected event to listeners.
14:55:51.977 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [605bce63-8cb3-4a1c-8d0a-544eab4d6988_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:55:51.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [605bce63-8cb3-4a1c-8d0a-544eab4d6988_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000020ba84f1ca0
14:55:52.280 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:55:59.630 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
14:55:59.631 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:55:59.631 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:55:59.901 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:56:01.171 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:56:01.175 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:56:01.176 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:56:05.477 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:56:08.499 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2c218a3a-1bec-4f69-a8cf-5283106fb865
14:56:08.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c218a3a-1bec-4f69-a8cf-5283106fb865] RpcClient init label, labels = {module=naming, source=sdk}
14:56:08.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c218a3a-1bec-4f69-a8cf-5283106fb865] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:56:08.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c218a3a-1bec-4f69-a8cf-5283106fb865] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:56:08.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c218a3a-1bec-4f69-a8cf-5283106fb865] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:56:08.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c218a3a-1bec-4f69-a8cf-5283106fb865] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:56:08.640 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c218a3a-1bec-4f69-a8cf-5283106fb865] Success to connect to server [localhost:8848] on start up, connectionId = 1754981768513_127.0.0.1_6667
14:56:08.640 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c218a3a-1bec-4f69-a8cf-5283106fb865] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:56:08.640 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c218a3a-1bec-4f69-a8cf-5283106fb865] Notify connected event to listeners.
14:56:08.640 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c218a3a-1bec-4f69-a8cf-5283106fb865] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000020ba84f1ca0
14:56:08.680 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
14:56:08.712 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ***********:9700 register finished
14:56:08.838 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 24.814 seconds (JVM running for 27.738)
14:56:08.845 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
14:56:08.845 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
14:56:08.855 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
14:56:09.277 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c218a3a-1bec-4f69-a8cf-5283106fb865] Receive server push request, request = NotifySubscriberRequest, requestId = 44
14:56:09.288 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2c218a3a-1bec-4f69-a8cf-5283106fb865] Ack server push request, request = NotifySubscriberRequest, requestId = 44
20:35:11.363 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:35:11.363 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:35:11.707 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:35:11.708 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@67b34dec[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:35:11.708 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754981768513_127.0.0.1_6667
20:35:11.713 [nacos-grpc-client-executor-4075] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754981768513_127.0.0.1_6667]Ignore complete event,isRunning:false,isAbandon=false
20:35:11.717 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4ea80499[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 4076]
20:35:11.780 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:35:11.788 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:35:11.888 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:35:11.888 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
