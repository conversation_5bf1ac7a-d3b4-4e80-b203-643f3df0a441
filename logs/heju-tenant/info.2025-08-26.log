09:44:44.390 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:44:45.900 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ae8bd216-4f68-418c-8eb4-1b82ab92a66a_config-0
09:44:46.003 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 34 ms to scan 1 urls, producing 3 keys and 6 values 
09:44:46.062 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 25 ms to scan 1 urls, producing 4 keys and 9 values 
09:44:46.080 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 3 keys and 10 values 
09:44:46.107 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 1 keys and 5 values 
09:44:46.136 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 23 ms to scan 1 urls, producing 1 keys and 7 values 
09:44:46.160 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
09:44:46.164 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8bd216-4f68-418c-8eb4-1b82ab92a66a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:44:46.166 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8bd216-4f68-418c-8eb4-1b82ab92a66a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001ed8939cfb8
09:44:46.166 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8bd216-4f68-418c-8eb4-1b82ab92a66a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001ed8939d1d8
09:44:46.167 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8bd216-4f68-418c-8eb4-1b82ab92a66a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:44:46.168 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8bd216-4f68-418c-8eb4-1b82ab92a66a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:44:46.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8bd216-4f68-418c-8eb4-1b82ab92a66a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:44:48.045 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8bd216-4f68-418c-8eb4-1b82ab92a66a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756172687759_127.0.0.1_2327
09:44:48.045 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8bd216-4f68-418c-8eb4-1b82ab92a66a_config-0] Notify connected event to listeners.
09:44:48.045 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8bd216-4f68-418c-8eb4-1b82ab92a66a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:44:48.045 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae8bd216-4f68-418c-8eb4-1b82ab92a66a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001ed89515418
09:44:48.329 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:44:54.084 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:44:54.084 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:44:54.084 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:44:54.852 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:44:56.384 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:44:56.391 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:44:56.393 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:45:00.624 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:45:04.354 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2bb97a85-346b-4aa0-b525-ca717ca7a3f7
09:45:04.354 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bb97a85-346b-4aa0-b525-ca717ca7a3f7] RpcClient init label, labels = {module=naming, source=sdk}
09:45:04.354 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bb97a85-346b-4aa0-b525-ca717ca7a3f7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:45:04.354 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bb97a85-346b-4aa0-b525-ca717ca7a3f7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:45:04.354 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bb97a85-346b-4aa0-b525-ca717ca7a3f7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:45:04.354 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bb97a85-346b-4aa0-b525-ca717ca7a3f7] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:45:04.493 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bb97a85-346b-4aa0-b525-ca717ca7a3f7] Success to connect to server [localhost:8848] on start up, connectionId = 1756172704375_127.0.0.1_2354
09:45:04.493 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bb97a85-346b-4aa0-b525-ca717ca7a3f7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:45:04.493 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bb97a85-346b-4aa0-b525-ca717ca7a3f7] Notify connected event to listeners.
09:45:04.493 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bb97a85-346b-4aa0-b525-ca717ca7a3f7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001ed89515418
09:45:04.554 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:45:04.598 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
09:45:04.805 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 21.658 seconds (JVM running for 40.695)
09:45:04.814 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:45:04.814 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:45:04.814 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:45:05.098 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bb97a85-346b-4aa0-b525-ca717ca7a3f7] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:45:05.114 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bb97a85-346b-4aa0-b525-ca717ca7a3f7] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:47:05.110 [http-nio-9700-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:47:13.168 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bb97a85-346b-4aa0-b525-ca717ca7a3f7] Receive server push request, request = NotifySubscriberRequest, requestId = 14
09:47:13.168 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bb97a85-346b-4aa0-b525-ca717ca7a3f7] Ack server push request, request = NotifySubscriberRequest, requestId = 14
16:34:09.982 [nacos-grpc-client-executor-4917] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bb97a85-346b-4aa0-b525-ca717ca7a3f7] Receive server push request, request = NotifySubscriberRequest, requestId = 17
16:34:10.000 [nacos-grpc-client-executor-4917] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bb97a85-346b-4aa0-b525-ca717ca7a3f7] Ack server push request, request = NotifySubscriberRequest, requestId = 17
16:34:49.001 [nacos-grpc-client-executor-4926] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bb97a85-346b-4aa0-b525-ca717ca7a3f7] Receive server push request, request = NotifySubscriberRequest, requestId = 20
16:34:49.005 [nacos-grpc-client-executor-4926] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2bb97a85-346b-4aa0-b525-ca717ca7a3f7] Ack server push request, request = NotifySubscriberRequest, requestId = 20
20:31:06.439 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:31:06.440 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:31:06.778 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:31:06.778 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5de7157e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:31:06.780 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756172704375_127.0.0.1_2354
20:31:06.784 [nacos-grpc-client-executor-7757] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756172704375_127.0.0.1_2354]Ignore complete event,isRunning:false,isAbandon=false
20:31:06.794 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7db0be77[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 7758]
20:31:06.989 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:31:07.003 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:31:07.007 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:31:07.007 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
