09:02:13.404 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:02:13.408 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:02:13.734 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:02:13.737 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3f904df6[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:02:13.737 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756257901020_127.0.0.1_14843
09:02:13.740 [nacos-grpc-client-executor-16983] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756257901020_127.0.0.1_14843]Ignore complete event,isRunning:false,isAbandon=false
09:02:13.744 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4ae9397f[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 16984]
09:02:13.897 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:02:13.934 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:02:13.934 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:02:13.934 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:35:37.156 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:35:38.047 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ee981ebb-98f1-42c2-a57f-a1485cf7fde4_config-0
09:35:38.122 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 39 ms to scan 1 urls, producing 3 keys and 6 values 
09:35:38.151 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
09:35:38.161 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
09:35:38.172 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:35:38.185 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
09:35:38.196 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:35:38.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee981ebb-98f1-42c2-a57f-a1485cf7fde4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:35:38.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee981ebb-98f1-42c2-a57f-a1485cf7fde4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002a99239fd80
09:35:38.201 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee981ebb-98f1-42c2-a57f-a1485cf7fde4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002a9923a0000
09:35:38.201 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee981ebb-98f1-42c2-a57f-a1485cf7fde4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:35:38.202 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee981ebb-98f1-42c2-a57f-a1485cf7fde4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:35:38.211 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee981ebb-98f1-42c2-a57f-a1485cf7fde4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:35:39.371 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee981ebb-98f1-42c2-a57f-a1485cf7fde4_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756344939177_127.0.0.1_9649
09:35:39.372 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee981ebb-98f1-42c2-a57f-a1485cf7fde4_config-0] Notify connected event to listeners.
09:35:39.372 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee981ebb-98f1-42c2-a57f-a1485cf7fde4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:35:39.373 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee981ebb-98f1-42c2-a57f-a1485cf7fde4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002a99251a598
09:35:39.550 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:35:42.801 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:35:42.802 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:35:42.802 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:35:42.950 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:35:43.657 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:35:43.659 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:35:43.659 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:35:46.192 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:35:49.044 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1724ae51-752f-450d-919d-90b7a82ba8ea
09:35:49.045 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] RpcClient init label, labels = {module=naming, source=sdk}
09:35:49.046 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:35:49.046 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:35:49.047 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:35:49.047 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:35:49.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Success to connect to server [localhost:8848] on start up, connectionId = 1756344949058_127.0.0.1_9680
09:35:49.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:35:49.184 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Notify connected event to listeners.
09:35:49.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002a99251a598
09:35:49.256 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:35:49.291 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
09:35:49.411 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 13.079 seconds (JVM running for 14.694)
09:35:49.427 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:35:49.430 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:35:49.431 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:35:49.751 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:35:49.772 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1724ae51-752f-450d-919d-90b7a82ba8ea] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:35:49.885 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
