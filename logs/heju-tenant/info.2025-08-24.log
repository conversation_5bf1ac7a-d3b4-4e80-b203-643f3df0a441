10:16:37.364 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:16:39.115 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 792085d5-1ce5-4f44-a2ea-e835fd0554ca_config-0
10:16:39.222 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 51 ms to scan 1 urls, producing 3 keys and 6 values 
10:16:39.264 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 4 keys and 9 values 
10:16:39.292 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 24 ms to scan 1 urls, producing 3 keys and 10 values 
10:16:39.309 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 5 values 
10:16:39.327 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 1 keys and 7 values 
10:16:39.343 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
10:16:39.349 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [792085d5-1ce5-4f44-a2ea-e835fd0554ca_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:16:39.350 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [792085d5-1ce5-4f44-a2ea-e835fd0554ca_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000002074b39da58
10:16:39.351 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [792085d5-1ce5-4f44-a2ea-e835fd0554ca_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002074b39dc78
10:16:39.352 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [792085d5-1ce5-4f44-a2ea-e835fd0554ca_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:16:39.353 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [792085d5-1ce5-4f44-a2ea-e835fd0554ca_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:16:39.367 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [792085d5-1ce5-4f44-a2ea-e835fd0554ca_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:16:40.935 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [792085d5-1ce5-4f44-a2ea-e835fd0554ca_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756001800668_127.0.0.1_11189
10:16:40.936 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [792085d5-1ce5-4f44-a2ea-e835fd0554ca_config-0] Notify connected event to listeners.
10:16:40.936 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [792085d5-1ce5-4f44-a2ea-e835fd0554ca_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:16:40.937 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [792085d5-1ce5-4f44-a2ea-e835fd0554ca_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002074b515f18
10:16:41.151 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:16:48.639 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
10:16:48.646 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:16:48.647 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:16:49.061 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:16:50.612 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:16:50.614 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:16:50.614 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:16:53.594 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:16:56.993 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2a0dee33-9586-4054-aba6-8eed9728da25
10:16:56.994 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a0dee33-9586-4054-aba6-8eed9728da25] RpcClient init label, labels = {module=naming, source=sdk}
10:16:56.996 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a0dee33-9586-4054-aba6-8eed9728da25] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:16:56.997 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a0dee33-9586-4054-aba6-8eed9728da25] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:16:56.998 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a0dee33-9586-4054-aba6-8eed9728da25] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:16:56.999 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a0dee33-9586-4054-aba6-8eed9728da25] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:16:57.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a0dee33-9586-4054-aba6-8eed9728da25] Success to connect to server [localhost:8848] on start up, connectionId = 1756001817019_127.0.0.1_11249
10:16:57.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a0dee33-9586-4054-aba6-8eed9728da25] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:16:57.147 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a0dee33-9586-4054-aba6-8eed9728da25] Notify connected event to listeners.
10:16:57.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a0dee33-9586-4054-aba6-8eed9728da25] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002074b515f18
10:16:57.230 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
10:16:57.293 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
10:16:57.634 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 21.805 seconds (JVM running for 33.233)
10:16:57.661 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
10:16:57.666 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
10:16:57.667 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
10:16:57.786 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a0dee33-9586-4054-aba6-8eed9728da25] Receive server push request, request = NotifySubscriberRequest, requestId = 5
10:16:57.817 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a0dee33-9586-4054-aba6-8eed9728da25] Ack server push request, request = NotifySubscriberRequest, requestId = 5
10:20:49.151 [http-nio-9700-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:20:56.324 [nacos-grpc-client-executor-54] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a0dee33-9586-4054-aba6-8eed9728da25] Receive server push request, request = NotifySubscriberRequest, requestId = 14
10:20:56.326 [nacos-grpc-client-executor-54] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2a0dee33-9586-4054-aba6-8eed9728da25] Ack server push request, request = NotifySubscriberRequest, requestId = 14
18:54:36.318 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:54:36.321 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:54:36.658 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:54:36.658 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4f144b84[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:54:36.658 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756001817019_127.0.0.1_11249
18:54:36.661 [nacos-grpc-client-executor-6554] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756001817019_127.0.0.1_11249]Ignore complete event,isRunning:false,isAbandon=false
18:54:36.664 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1e9dd144[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 6555]
18:54:36.833 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:54:36.844 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:54:36.848 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:54:36.848 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
