10:17:26.249 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:17:27.270 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4c47ffd3-b322-4380-bec5-0dfbef83fdbd_config-0
10:17:27.358 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 42 ms to scan 1 urls, producing 3 keys and 6 values 
10:17:27.437 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 20 ms to scan 1 urls, producing 4 keys and 9 values 
10:17:27.448 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 3 keys and 10 values 
10:17:27.465 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 5 values 
10:17:27.518 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 48 ms to scan 1 urls, producing 1 keys and 7 values 
10:17:27.544 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
10:17:27.551 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c47ffd3-b322-4380-bec5-0dfbef83fdbd_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:17:27.552 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c47ffd3-b322-4380-bec5-0dfbef83fdbd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002618c3a0638
10:17:27.553 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c47ffd3-b322-4380-bec5-0dfbef83fdbd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002618c3a0858
10:17:27.558 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c47ffd3-b322-4380-bec5-0dfbef83fdbd_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:17:27.560 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c47ffd3-b322-4380-bec5-0dfbef83fdbd_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:17:27.593 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c47ffd3-b322-4380-bec5-0dfbef83fdbd_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:17:28.983 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c47ffd3-b322-4380-bec5-0dfbef83fdbd_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755397048754_127.0.0.1_10632
10:17:28.984 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c47ffd3-b322-4380-bec5-0dfbef83fdbd_config-0] Notify connected event to listeners.
10:17:28.984 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c47ffd3-b322-4380-bec5-0dfbef83fdbd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:17:28.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c47ffd3-b322-4380-bec5-0dfbef83fdbd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002618c51a6e0
10:17:29.375 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:17:33.416 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
10:17:33.416 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:17:33.416 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:17:33.631 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:17:34.675 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:17:34.675 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:17:34.675 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:17:37.478 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:17:40.052 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 36d60865-c590-45c4-a7ee-2d648966632a
10:17:40.053 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] RpcClient init label, labels = {module=naming, source=sdk}
10:17:40.054 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:17:40.054 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:17:40.054 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:17:40.055 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:17:40.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Success to connect to server [localhost:8848] on start up, connectionId = 1755397060063_127.0.0.1_10731
10:17:40.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:17:40.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002618c51a6e0
10:17:40.183 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Notify connected event to listeners.
10:17:40.239 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
10:17:40.273 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
10:17:40.407 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 15.139 seconds (JVM running for 16.439)
10:17:40.421 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
10:17:40.424 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
10:17:40.425 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
10:17:40.536 [RMI TCP Connection(13)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:17:40.803 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Receive server push request, request = NotifySubscriberRequest, requestId = 6
10:17:40.821 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Ack server push request, request = NotifySubscriberRequest, requestId = 6
10:19:20.240 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Receive server push request, request = NotifySubscriberRequest, requestId = 14
10:19:20.240 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Ack server push request, request = NotifySubscriberRequest, requestId = 14
13:03:04.534 [nacos-grpc-client-executor-1995] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Receive server push request, request = NotifySubscriberRequest, requestId = 15
13:03:04.558 [nacos-grpc-client-executor-1995] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Ack server push request, request = NotifySubscriberRequest, requestId = 15
13:03:33.541 [nacos-grpc-client-executor-2001] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Receive server push request, request = NotifySubscriberRequest, requestId = 18
13:03:33.565 [nacos-grpc-client-executor-2001] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Ack server push request, request = NotifySubscriberRequest, requestId = 18
14:23:13.262 [nacos-grpc-client-executor-2951] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Receive server push request, request = NotifySubscriberRequest, requestId = 22
14:23:13.295 [nacos-grpc-client-executor-2951] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Ack server push request, request = NotifySubscriberRequest, requestId = 22
14:23:40.494 [nacos-grpc-client-executor-2958] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Receive server push request, request = NotifySubscriberRequest, requestId = 25
14:23:40.513 [nacos-grpc-client-executor-2958] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Ack server push request, request = NotifySubscriberRequest, requestId = 25
14:25:18.521 [nacos-grpc-client-executor-2978] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Receive server push request, request = NotifySubscriberRequest, requestId = 29
14:25:18.543 [nacos-grpc-client-executor-2978] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Ack server push request, request = NotifySubscriberRequest, requestId = 29
14:25:44.545 [nacos-grpc-client-executor-2985] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Receive server push request, request = NotifySubscriberRequest, requestId = 32
14:25:44.563 [nacos-grpc-client-executor-2985] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Ack server push request, request = NotifySubscriberRequest, requestId = 32
15:29:29.672 [nacos-grpc-client-executor-3788] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Receive server push request, request = NotifySubscriberRequest, requestId = 37
15:29:29.696 [nacos-grpc-client-executor-3788] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Ack server push request, request = NotifySubscriberRequest, requestId = 37
15:29:54.963 [nacos-grpc-client-executor-3794] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Receive server push request, request = NotifySubscriberRequest, requestId = 41
15:29:54.977 [nacos-grpc-client-executor-3794] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Ack server push request, request = NotifySubscriberRequest, requestId = 41
15:50:15.128 [nacos-grpc-client-executor-4038] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Receive server push request, request = NotifySubscriberRequest, requestId = 46
15:50:15.144 [nacos-grpc-client-executor-4038] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Ack server push request, request = NotifySubscriberRequest, requestId = 46
15:50:32.180 [nacos-grpc-client-executor-4041] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Receive server push request, request = NotifySubscriberRequest, requestId = 50
15:50:32.196 [nacos-grpc-client-executor-4041] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [36d60865-c590-45c4-a7ee-2d648966632a] Ack server push request, request = NotifySubscriberRequest, requestId = 50
15:58:50.794 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:58:50.794 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:58:51.128 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:58:51.128 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5c43340[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:58:51.128 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755397060063_127.0.0.1_10731
15:58:51.130 [nacos-grpc-client-executor-4144] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755397060063_127.0.0.1_10731]Ignore complete event,isRunning:false,isAbandon=false
15:58:51.134 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@14a8370b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 4145]
15:58:51.295 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:58:51.300 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:58:51.301 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:58:51.301 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:00:56.910 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:00:58.688 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c97ca545-53ad-4132-94ec-a772f88e6275_config-0
16:00:58.797 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 47 ms to scan 1 urls, producing 3 keys and 6 values 
16:00:58.845 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 4 keys and 9 values 
16:00:58.866 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 3 keys and 10 values 
16:00:58.889 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 1 keys and 5 values 
16:00:58.902 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
16:00:58.915 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
16:00:58.921 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c97ca545-53ad-4132-94ec-a772f88e6275_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:00:58.922 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c97ca545-53ad-4132-94ec-a772f88e6275_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000021a843b78e0
16:00:58.924 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c97ca545-53ad-4132-94ec-a772f88e6275_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000021a843b7b00
16:00:58.925 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c97ca545-53ad-4132-94ec-a772f88e6275_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:00:58.927 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c97ca545-53ad-4132-94ec-a772f88e6275_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:00:58.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c97ca545-53ad-4132-94ec-a772f88e6275_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:01:00.664 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c97ca545-53ad-4132-94ec-a772f88e6275_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755417660363_127.0.0.1_4279
16:01:00.666 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c97ca545-53ad-4132-94ec-a772f88e6275_config-0] Notify connected event to listeners.
16:01:00.667 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c97ca545-53ad-4132-94ec-a772f88e6275_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:01:00.668 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c97ca545-53ad-4132-94ec-a772f88e6275_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000021a844f1450
16:01:00.877 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:01:04.643 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
16:01:04.644 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:01:04.644 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:01:04.803 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:01:05.446 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:01:05.448 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:01:05.448 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:01:08.048 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:01:10.970 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9355bad2-1c93-4e68-a2d5-cb9ad2fd8e7d
16:01:10.970 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9355bad2-1c93-4e68-a2d5-cb9ad2fd8e7d] RpcClient init label, labels = {module=naming, source=sdk}
16:01:10.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9355bad2-1c93-4e68-a2d5-cb9ad2fd8e7d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:01:10.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9355bad2-1c93-4e68-a2d5-cb9ad2fd8e7d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:01:10.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9355bad2-1c93-4e68-a2d5-cb9ad2fd8e7d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:01:10.973 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9355bad2-1c93-4e68-a2d5-cb9ad2fd8e7d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:01:11.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9355bad2-1c93-4e68-a2d5-cb9ad2fd8e7d] Success to connect to server [localhost:8848] on start up, connectionId = 1755417670984_127.0.0.1_4305
16:01:11.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9355bad2-1c93-4e68-a2d5-cb9ad2fd8e7d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:01:11.106 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9355bad2-1c93-4e68-a2d5-cb9ad2fd8e7d] Notify connected event to listeners.
16:01:11.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9355bad2-1c93-4e68-a2d5-cb9ad2fd8e7d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000021a844f1450
16:01:11.152 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
16:01:11.179 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
16:01:11.298 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 15.828 seconds (JVM running for 18.471)
16:01:11.311 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
16:01:11.329 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
16:01:11.329 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
16:01:11.647 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9355bad2-1c93-4e68-a2d5-cb9ad2fd8e7d] Receive server push request, request = NotifySubscriberRequest, requestId = 61
16:01:11.661 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9355bad2-1c93-4e68-a2d5-cb9ad2fd8e7d] Ack server push request, request = NotifySubscriberRequest, requestId = 61
19:41:50.509 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:41:50.509 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:41:50.843 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:41:50.843 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@665b0a9f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:41:50.843 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755417670984_127.0.0.1_4305
19:41:50.843 [nacos-grpc-client-executor-2653] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755417670984_127.0.0.1_4305]Ignore complete event,isRunning:false,isAbandon=false
19:41:50.843 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3f6504b8[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2654]
19:41:50.897 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
19:41:50.903 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
19:41:50.931 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
19:41:50.931 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
