09:19:05.480 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:19:06.308 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 22b1e2df-7263-46dc-b204-81dbc81fd103_config-0
09:19:06.377 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 30 ms to scan 1 urls, producing 3 keys and 6 values 
09:19:06.409 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 4 keys and 9 values 
09:19:06.424 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 3 keys and 10 values 
09:19:06.441 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 1 keys and 5 values 
09:19:06.451 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:19:06.461 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:19:06.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1e2df-7263-46dc-b204-81dbc81fd103_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:19:06.466 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1e2df-7263-46dc-b204-81dbc81fd103_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001b9a939f268
09:19:06.466 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1e2df-7263-46dc-b204-81dbc81fd103_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001b9a939f488
09:19:06.467 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1e2df-7263-46dc-b204-81dbc81fd103_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:19:06.468 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1e2df-7263-46dc-b204-81dbc81fd103_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:19:06.477 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1e2df-7263-46dc-b204-81dbc81fd103_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:07.633 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1e2df-7263-46dc-b204-81dbc81fd103_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754615947428_127.0.0.1_12145
09:19:07.634 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1e2df-7263-46dc-b204-81dbc81fd103_config-0] Notify connected event to listeners.
09:19:07.634 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1e2df-7263-46dc-b204-81dbc81fd103_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:19:07.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22b1e2df-7263-46dc-b204-81dbc81fd103_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001b9a9518ad8
09:19:07.790 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:19:11.274 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:19:11.275 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:19:11.275 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:19:11.441 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:19:12.237 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:19:12.238 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:19:12.238 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:19:16.159 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:19:19.148 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0809b26c-32c7-426a-8993-4825f62c939e
09:19:19.150 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0809b26c-32c7-426a-8993-4825f62c939e] RpcClient init label, labels = {module=naming, source=sdk}
09:19:19.163 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0809b26c-32c7-426a-8993-4825f62c939e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:19:19.165 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0809b26c-32c7-426a-8993-4825f62c939e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:19:19.167 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0809b26c-32c7-426a-8993-4825f62c939e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:19:19.167 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0809b26c-32c7-426a-8993-4825f62c939e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:19.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0809b26c-32c7-426a-8993-4825f62c939e] Success to connect to server [localhost:8848] on start up, connectionId = 1754615959183_127.0.0.1_12182
09:19:19.301 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0809b26c-32c7-426a-8993-4825f62c939e] Notify connected event to listeners.
09:19:19.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0809b26c-32c7-426a-8993-4825f62c939e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:19:19.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0809b26c-32c7-426a-8993-4825f62c939e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001b9a9518ad8
09:19:19.356 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:19:19.398 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
09:19:19.529 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 14.781 seconds (JVM running for 16.517)
09:19:19.546 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:19:19.557 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:19:19.560 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:19:19.906 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0809b26c-32c7-426a-8993-4825f62c939e] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:19:19.927 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0809b26c-32c7-426a-8993-4825f62c939e] Ack server push request, request = NotifySubscriberRequest, requestId = 5
09:19:20.068 [RMI TCP Connection(14)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:21:20.962 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0809b26c-32c7-426a-8993-4825f62c939e] Receive server push request, request = NotifySubscriberRequest, requestId = 14
09:21:20.962 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0809b26c-32c7-426a-8993-4825f62c939e] Ack server push request, request = NotifySubscriberRequest, requestId = 14
15:00:18.204 [nacos-grpc-client-executor-4356] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0809b26c-32c7-426a-8993-4825f62c939e] Receive server push request, request = NotifySubscriberRequest, requestId = 19
15:00:18.224 [nacos-grpc-client-executor-4356] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0809b26c-32c7-426a-8993-4825f62c939e] Ack server push request, request = NotifySubscriberRequest, requestId = 19
15:01:01.628 [nacos-grpc-client-executor-4365] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0809b26c-32c7-426a-8993-4825f62c939e] Receive server push request, request = NotifySubscriberRequest, requestId = 23
15:01:01.642 [nacos-grpc-client-executor-4365] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0809b26c-32c7-426a-8993-4825f62c939e] Ack server push request, request = NotifySubscriberRequest, requestId = 23
15:18:37.276 [nacos-grpc-client-executor-4575] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0809b26c-32c7-426a-8993-4825f62c939e] Receive server push request, request = NotifySubscriberRequest, requestId = 28
15:18:37.289 [nacos-grpc-client-executor-4575] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0809b26c-32c7-426a-8993-4825f62c939e] Ack server push request, request = NotifySubscriberRequest, requestId = 28
15:19:14.545 [nacos-grpc-client-executor-4583] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0809b26c-32c7-426a-8993-4825f62c939e] Receive server push request, request = NotifySubscriberRequest, requestId = 33
15:19:14.561 [nacos-grpc-client-executor-4583] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0809b26c-32c7-426a-8993-4825f62c939e] Ack server push request, request = NotifySubscriberRequest, requestId = 33
17:09:24.022 [nacos-grpc-client-executor-5951] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0809b26c-32c7-426a-8993-4825f62c939e] Receive server push request, request = NotifySubscriberRequest, requestId = 37
17:09:24.046 [nacos-grpc-client-executor-5951] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0809b26c-32c7-426a-8993-4825f62c939e] Ack server push request, request = NotifySubscriberRequest, requestId = 37
17:09:52.147 [nacos-grpc-client-executor-5957] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0809b26c-32c7-426a-8993-4825f62c939e] Receive server push request, request = NotifySubscriberRequest, requestId = 41
17:09:52.156 [nacos-grpc-client-executor-5957] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0809b26c-32c7-426a-8993-4825f62c939e] Ack server push request, request = NotifySubscriberRequest, requestId = 41
18:08:24.247 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:08:24.254 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:08:24.586 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:08:24.586 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@556348fc[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:08:24.586 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754615959183_127.0.0.1_12182
18:08:24.587 [nacos-grpc-client-executor-6660] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754615959183_127.0.0.1_12182]Ignore complete event,isRunning:false,isAbandon=false
18:08:24.591 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@57eb11db[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 6661]
18:08:24.720 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:08:24.727 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:08:24.727 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:08:24.727 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
18:09:10.442 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:09:12.680 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ab4b8426-1849-417d-b510-a093b49b59c5_config-0
18:09:12.833 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 83 ms to scan 1 urls, producing 3 keys and 6 values 
18:09:12.907 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 4 keys and 9 values 
18:09:12.955 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 42 ms to scan 1 urls, producing 3 keys and 10 values 
18:09:12.985 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 1 keys and 5 values 
18:09:13.014 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 1 keys and 7 values 
18:09:13.039 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 2 keys and 8 values 
18:09:13.046 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab4b8426-1849-417d-b510-a093b49b59c5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:09:13.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab4b8426-1849-417d-b510-a093b49b59c5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000013bad3b9748
18:09:13.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab4b8426-1849-417d-b510-a093b49b59c5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000013bad3b9968
18:09:13.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab4b8426-1849-417d-b510-a093b49b59c5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:09:13.050 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab4b8426-1849-417d-b510-a093b49b59c5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:09:13.068 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab4b8426-1849-417d-b510-a093b49b59c5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:09:16.166 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab4b8426-1849-417d-b510-a093b49b59c5_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754647755730_127.0.0.1_6760
18:09:16.168 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab4b8426-1849-417d-b510-a093b49b59c5_config-0] Notify connected event to listeners.
18:09:16.170 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab4b8426-1849-417d-b510-a093b49b59c5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:09:16.171 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab4b8426-1849-417d-b510-a093b49b59c5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000013bad4f0fb0
18:09:16.470 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:09:26.940 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
18:09:26.940 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:09:26.942 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
18:09:27.958 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:09:30.037 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
18:09:30.067 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
18:09:30.067 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
18:09:35.009 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:09:39.614 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 81c9579b-d396-4375-a416-632a74527720
18:09:39.614 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81c9579b-d396-4375-a416-632a74527720] RpcClient init label, labels = {module=naming, source=sdk}
18:09:39.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81c9579b-d396-4375-a416-632a74527720] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:09:39.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81c9579b-d396-4375-a416-632a74527720] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:09:39.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81c9579b-d396-4375-a416-632a74527720] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:09:39.618 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81c9579b-d396-4375-a416-632a74527720] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:09:39.741 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81c9579b-d396-4375-a416-632a74527720] Success to connect to server [localhost:8848] on start up, connectionId = 1754647779626_127.0.0.1_6962
18:09:39.741 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81c9579b-d396-4375-a416-632a74527720] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:09:39.741 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81c9579b-d396-4375-a416-632a74527720] Notify connected event to listeners.
18:09:39.742 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81c9579b-d396-4375-a416-632a74527720] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000013bad4f0fb0
18:09:39.801 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
18:09:39.843 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ************:9700 register finished
18:09:40.035 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 31.928 seconds (JVM running for 34.747)
18:09:40.052 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
18:09:40.057 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
18:09:40.058 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
18:09:40.351 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81c9579b-d396-4375-a416-632a74527720] Receive server push request, request = NotifySubscriberRequest, requestId = 54
18:09:40.369 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81c9579b-d396-4375-a416-632a74527720] Ack server push request, request = NotifySubscriberRequest, requestId = 54
18:15:36.771 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:15:36.774 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:15:37.102 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:15:37.102 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@16811e4c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:15:37.102 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754647779626_127.0.0.1_6962
18:15:37.106 [nacos-grpc-client-executor-82] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754647779626_127.0.0.1_6962]Ignore complete event,isRunning:false,isAbandon=false
18:15:37.107 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7c9b9abd[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 83]
18:15:37.127 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
18:15:37.129 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
18:15:37.135 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
18:15:37.135 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
