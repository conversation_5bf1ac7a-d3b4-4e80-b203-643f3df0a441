09:14:22.899 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:14:25.000 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ee48950e-52b3-4dfc-93ea-7c49c6146527_config-0
09:14:25.185 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 99 ms to scan 1 urls, producing 3 keys and 6 values 
09:14:25.248 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 27 ms to scan 1 urls, producing 4 keys and 9 values 
09:14:25.285 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 33 ms to scan 1 urls, producing 3 keys and 10 values 
09:14:25.314 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 19 ms to scan 1 urls, producing 1 keys and 5 values 
09:14:25.339 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 19 ms to scan 1 urls, producing 1 keys and 7 values 
09:14:25.361 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
09:14:25.366 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee48950e-52b3-4dfc-93ea-7c49c6146527_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:14:25.368 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee48950e-52b3-4dfc-93ea-7c49c6146527_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000023a8c39d500
09:14:25.368 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee48950e-52b3-4dfc-93ea-7c49c6146527_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000023a8c39d720
09:14:25.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee48950e-52b3-4dfc-93ea-7c49c6146527_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:14:25.370 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee48950e-52b3-4dfc-93ea-7c49c6146527_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:14:25.385 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee48950e-52b3-4dfc-93ea-7c49c6146527_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:14:27.564 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee48950e-52b3-4dfc-93ea-7c49c6146527_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755047667158_127.0.0.1_4129
09:14:27.565 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee48950e-52b3-4dfc-93ea-7c49c6146527_config-0] Notify connected event to listeners.
09:14:27.566 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee48950e-52b3-4dfc-93ea-7c49c6146527_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:14:27.566 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ee48950e-52b3-4dfc-93ea-7c49c6146527_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000023a8c515d18
09:14:27.917 [main] INFO  c.h.t.HeJuTenantApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:14:45.931 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9700"]
09:14:45.932 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:14:45.932 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:14:46.400 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:14:49.408 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:14:49.411 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:14:49.411 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:14:55.397 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:15:00.487 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2ec371e6-920a-41ef-8393-75f3d96bfe5b
09:15:00.488 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] RpcClient init label, labels = {module=naming, source=sdk}
09:15:00.491 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:15:00.491 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:15:00.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:15:00.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:15:00.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Success to connect to server [localhost:8848] on start up, connectionId = 1755047700502_127.0.0.1_4350
09:15:00.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:15:00.635 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Notify connected event to listeners.
09:15:00.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000023a8c515d18
09:15:00.715 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9700"]
09:15:00.779 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-tenant ***********:9700 register finished
09:15:00.968 [main] INFO  c.h.t.HeJuTenantApplication - [logStarted,61] - Started HeJuTenantApplication in 39.956 seconds (JVM running for 52.327)
09:15:00.988 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant, group=DEFAULT_GROUP
09:15:00.993 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant-dev.yml, group=DEFAULT_GROUP
09:15:00.994 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-tenant.yml, group=DEFAULT_GROUP
09:15:01.283 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:15:01.301 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:16:57.460 [http-nio-9700-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:17:01.974 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 14
09:17:01.974 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 14
10:11:18.176 [nacos-grpc-client-executor-729] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 16
10:11:18.212 [nacos-grpc-client-executor-729] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 16
10:12:00.490 [nacos-grpc-client-executor-738] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 19
10:12:00.505 [nacos-grpc-client-executor-738] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 19
10:17:48.344 [nacos-grpc-client-executor-813] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 23
10:17:48.375 [nacos-grpc-client-executor-813] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 23
10:18:15.843 [nacos-grpc-client-executor-819] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 26
10:18:15.861 [nacos-grpc-client-executor-819] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 26
10:31:06.704 [nacos-grpc-client-executor-973] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 30
10:31:06.728 [nacos-grpc-client-executor-973] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 30
10:31:39.305 [nacos-grpc-client-executor-979] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 34
10:31:39.320 [nacos-grpc-client-executor-979] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 34
10:33:24.533 [nacos-grpc-client-executor-1000] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 37
10:33:24.557 [nacos-grpc-client-executor-1000] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 37
10:33:52.096 [nacos-grpc-client-executor-1006] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 41
10:33:52.112 [nacos-grpc-client-executor-1006] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 41
11:49:35.573 [nacos-grpc-client-executor-1914] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 44
11:49:35.614 [nacos-grpc-client-executor-1914] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 44
11:50:34.776 [nacos-grpc-client-executor-1927] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 47
11:50:34.795 [nacos-grpc-client-executor-1927] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 47
12:01:58.912 [nacos-grpc-client-executor-2064] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 51
12:01:58.931 [nacos-grpc-client-executor-2064] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 51
12:02:46.211 [nacos-grpc-client-executor-2073] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 55
12:02:46.224 [nacos-grpc-client-executor-2073] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 55
12:14:33.125 [nacos-grpc-client-executor-2214] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 58
12:14:33.145 [nacos-grpc-client-executor-2214] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 58
12:15:17.358 [nacos-grpc-client-executor-2224] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 62
12:15:17.361 [nacos-grpc-client-executor-2224] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 62
12:31:25.249 [nacos-grpc-client-executor-2417] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 65
12:31:25.316 [nacos-grpc-client-executor-2417] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 65
12:31:59.314 [nacos-grpc-client-executor-2424] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 68
12:31:59.329 [nacos-grpc-client-executor-2424] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 68
13:51:27.255 [lettuce-nioEventLoop-4-1] INFO  i.l.c.p.CommandHandler - [log,217] - null Unexpected exception during request: java.net.SocketException: Connection reset
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:259)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
13:51:27.324 [lettuce-eventExecutorLoop-1-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /***********00:6379
13:51:38.410 [lettuce-eventExecutorLoop-1-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:51:49.013 [lettuce-eventExecutorLoop-1-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:52:00.115 [lettuce-eventExecutorLoop-1-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:52:12.216 [lettuce-eventExecutorLoop-1-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:52:26.409 [lettuce-eventExecutorLoop-1-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:52:44.708 [lettuce-eventExecutorLoop-1-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:53:11.109 [lettuce-eventExecutorLoop-1-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:53:51.210 [lettuce-eventExecutorLoop-1-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:54:31.308 [lettuce-eventExecutorLoop-1-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:55:11.412 [lettuce-eventExecutorLoop-1-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:55:51.509 [lettuce-eventExecutorLoop-1-17] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:56:31.620 [lettuce-eventExecutorLoop-1-19] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:57:11.719 [lettuce-eventExecutorLoop-1-21] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:57:51.808 [lettuce-eventExecutorLoop-1-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:58:31.919 [lettuce-eventExecutorLoop-1-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:59:12.019 [lettuce-eventExecutorLoop-1-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
13:59:52.118 [lettuce-eventExecutorLoop-1-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:00:32.209 [lettuce-eventExecutorLoop-1-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:01:12.319 [lettuce-eventExecutorLoop-1-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:01:52.419 [lettuce-eventExecutorLoop-1-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:02:32.520 [lettuce-eventExecutorLoop-1-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:03:12.618 [lettuce-eventExecutorLoop-1-17] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:03:52.719 [lettuce-eventExecutorLoop-1-19] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:04:32.818 [lettuce-eventExecutorLoop-1-21] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:05:12.919 [lettuce-eventExecutorLoop-1-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:05:53.019 [lettuce-eventExecutorLoop-1-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:06:33.119 [lettuce-eventExecutorLoop-1-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:07:13.208 [lettuce-eventExecutorLoop-1-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:07:53.314 [lettuce-eventExecutorLoop-1-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:08:33.418 [lettuce-eventExecutorLoop-1-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:09:13.518 [lettuce-eventExecutorLoop-1-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:09:53.615 [lettuce-eventExecutorLoop-1-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:10:33.718 [lettuce-eventExecutorLoop-1-17] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:11:13.817 [lettuce-eventExecutorLoop-1-19] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:11:53.909 [lettuce-eventExecutorLoop-1-21] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:12:34.017 [lettuce-eventExecutorLoop-1-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:13:09.419 [lettuce-eventExecutorLoop-1-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
14:13:09.439 [lettuce-nioEventLoop-4-2] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to ***********00/<unresolved>:6379
14:17:57.688 [nacos-grpc-client-executor-3695] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 72
14:17:57.704 [nacos-grpc-client-executor-3695] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 72
14:18:44.555 [nacos-grpc-client-executor-3704] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 75
14:18:44.569 [nacos-grpc-client-executor-3704] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 75
14:51:26.599 [nacos-grpc-client-executor-4096] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 79
14:51:26.617 [nacos-grpc-client-executor-4096] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 79
14:52:02.926 [nacos-grpc-client-executor-4104] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 83
14:52:02.950 [nacos-grpc-client-executor-4104] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 83
15:59:40.312 [nacos-grpc-client-executor-4915] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 86
15:59:40.433 [nacos-grpc-client-executor-4915] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 86
16:00:22.867 [nacos-grpc-client-executor-4924] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 90
16:00:22.903 [nacos-grpc-client-executor-4924] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 90
16:21:43.567 [nacos-grpc-client-executor-5181] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 93
16:21:43.583 [nacos-grpc-client-executor-5181] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 93
16:22:17.433 [nacos-grpc-client-executor-5188] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 96
16:22:17.450 [nacos-grpc-client-executor-5188] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 96
17:18:48.952 [nacos-grpc-client-executor-5865] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 100
17:18:48.971 [nacos-grpc-client-executor-5865] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 100
17:19:18.037 [nacos-grpc-client-executor-5871] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 104
17:19:18.053 [nacos-grpc-client-executor-5871] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 104
18:58:49.316 [nacos-grpc-client-executor-7063] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 108
18:58:49.341 [nacos-grpc-client-executor-7063] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 108
18:59:33.422 [nacos-grpc-client-executor-7072] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Receive server push request, request = NotifySubscriberRequest, requestId = 112
18:59:33.440 [nacos-grpc-client-executor-7072] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec371e6-920a-41ef-8393-75f3d96bfe5b] Ack server push request, request = NotifySubscriberRequest, requestId = 112
22:20:00.831 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
22:20:00.834 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
22:20:01.169 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
22:20:01.169 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@129985be[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
22:20:01.169 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755047700502_127.0.0.1_4350
22:20:01.169 [nacos-grpc-client-executor-9477] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755047700502_127.0.0.1_4350]Ignore complete event,isRunning:false,isAbandon=false
22:20:01.176 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4bb2eb26[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 9478]
22:20:01.366 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
22:20:01.374 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
22:20:01.377 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
22:20:01.378 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
