09:18:01.585 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:18:03.339 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 55237f29-c3b5-42a9-b066-56f398461d6a_config-0
09:18:03.517 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 88 ms to scan 1 urls, producing 3 keys and 6 values 
09:18:03.579 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 21 ms to scan 1 urls, producing 4 keys and 9 values 
09:18:03.598 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 3 keys and 10 values 
09:18:03.619 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 1 keys and 5 values 
09:18:03.644 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 1 keys and 7 values 
09:18:03.682 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 35 ms to scan 1 urls, producing 2 keys and 8 values 
09:18:03.688 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55237f29-c3b5-42a9-b066-56f398461d6a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:18:03.692 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55237f29-c3b5-42a9-b066-56f398461d6a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002185e3b76c0
09:18:03.693 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55237f29-c3b5-42a9-b066-56f398461d6a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002185e3b78e0
09:18:03.695 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55237f29-c3b5-42a9-b066-56f398461d6a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:18:03.698 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55237f29-c3b5-42a9-b066-56f398461d6a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:18:03.781 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55237f29-c3b5-42a9-b066-56f398461d6a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:18:05.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55237f29-c3b5-42a9-b066-56f398461d6a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750814285535_127.0.0.1_5064
09:18:05.845 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55237f29-c3b5-42a9-b066-56f398461d6a_config-0] Notify connected event to listeners.
09:18:05.847 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55237f29-c3b5-42a9-b066-56f398461d6a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:05.848 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55237f29-c3b5-42a9-b066-56f398461d6a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002185e4f0fb0
09:18:06.243 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:18:09.752 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:18:09.753 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:18:09.753 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:18:09.971 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:18:13.012 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:18:16.431 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f1ee30a6-d38e-40a5-97f0-b052e303e022
09:18:16.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ee30a6-d38e-40a5-97f0-b052e303e022] RpcClient init label, labels = {module=naming, source=sdk}
09:18:16.436 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ee30a6-d38e-40a5-97f0-b052e303e022] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:18:16.436 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ee30a6-d38e-40a5-97f0-b052e303e022] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:18:16.437 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ee30a6-d38e-40a5-97f0-b052e303e022] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:18:16.438 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ee30a6-d38e-40a5-97f0-b052e303e022] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:18:16.577 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ee30a6-d38e-40a5-97f0-b052e303e022] Success to connect to server [localhost:8848] on start up, connectionId = 1750814296450_127.0.0.1_5077
09:18:16.577 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ee30a6-d38e-40a5-97f0-b052e303e022] Notify connected event to listeners.
09:18:16.578 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ee30a6-d38e-40a5-97f0-b052e303e022] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:16.578 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ee30a6-d38e-40a5-97f0-b052e303e022] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002185e4f0fb0
09:18:16.699 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:18:16.791 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
09:18:16.993 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 17.311 seconds (JVM running for 21.051)
09:18:17.008 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
09:18:17.009 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
09:18:17.012 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
09:18:17.302 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:18:17.770 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ee30a6-d38e-40a5-97f0-b052e303e022] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:18:17.803 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ee30a6-d38e-40a5-97f0-b052e303e022] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:20:55.180 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ee30a6-d38e-40a5-97f0-b052e303e022] Server healthy check fail, currentConnection = 1750814296450_127.0.0.1_5077
09:20:55.181 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ee30a6-d38e-40a5-97f0-b052e303e022] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:20:55.865 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ee30a6-d38e-40a5-97f0-b052e303e022] Success to connect a server [localhost:8848], connectionId = 1750814455199_127.0.0.1_5342
09:20:55.866 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ee30a6-d38e-40a5-97f0-b052e303e022] Abandon prev connection, server is localhost:8848, connectionId is 1750814296450_127.0.0.1_5077
09:20:55.867 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750814296450_127.0.0.1_5077
09:20:55.880 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ee30a6-d38e-40a5-97f0-b052e303e022] Notify disconnected event to listeners
09:20:55.884 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ee30a6-d38e-40a5-97f0-b052e303e022] Notify connected event to listeners.
09:20:56.754 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ee30a6-d38e-40a5-97f0-b052e303e022] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:20:56.755 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ee30a6-d38e-40a5-97f0-b052e303e022] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:50:01.663 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:50:01.663 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:50:01.983 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:50:01.984 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4706638[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:50:01.984 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750814455199_127.0.0.1_5342
09:50:01.984 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7fa3810c[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 399]
09:50:01.984 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ee30a6-d38e-40a5-97f0-b052e303e022] Notify disconnected event to listeners
10:03:57.592 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:03:58.128 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 910ce6f5-ba31-4f53-8b25-4c1a8bfe07c2_config-0
10:03:58.197 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 39 ms to scan 1 urls, producing 3 keys and 6 values 
10:03:58.218 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
10:03:58.225 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
10:03:58.234 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
10:03:58.243 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
10:03:58.253 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
10:03:58.256 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [910ce6f5-ba31-4f53-8b25-4c1a8bfe07c2_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:03:58.256 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [910ce6f5-ba31-4f53-8b25-4c1a8bfe07c2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001b0c9396d88
10:03:58.256 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [910ce6f5-ba31-4f53-8b25-4c1a8bfe07c2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001b0c9396fa8
10:03:58.257 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [910ce6f5-ba31-4f53-8b25-4c1a8bfe07c2_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:03:58.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [910ce6f5-ba31-4f53-8b25-4c1a8bfe07c2_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:03:58.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [910ce6f5-ba31-4f53-8b25-4c1a8bfe07c2_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:03:59.006 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [910ce6f5-ba31-4f53-8b25-4c1a8bfe07c2_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750817038806_127.0.0.1_9522
10:03:59.007 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [910ce6f5-ba31-4f53-8b25-4c1a8bfe07c2_config-0] Notify connected event to listeners.
10:03:59.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [910ce6f5-ba31-4f53-8b25-4c1a8bfe07c2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:03:59.012 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [910ce6f5-ba31-4f53-8b25-4c1a8bfe07c2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001b0c9510668
10:03:59.098 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:04:00.741 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
10:04:00.741 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:04:00.741 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:04:00.860 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:04:02.280 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:04:04.182 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9dc911d8-03ff-4916-be25-d8056811a5da
10:04:04.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dc911d8-03ff-4916-be25-d8056811a5da] RpcClient init label, labels = {module=naming, source=sdk}
10:04:04.186 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dc911d8-03ff-4916-be25-d8056811a5da] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:04:04.186 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dc911d8-03ff-4916-be25-d8056811a5da] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:04:04.187 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dc911d8-03ff-4916-be25-d8056811a5da] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:04:04.187 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dc911d8-03ff-4916-be25-d8056811a5da] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:04:04.316 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dc911d8-03ff-4916-be25-d8056811a5da] Success to connect to server [localhost:8848] on start up, connectionId = 1750817044198_127.0.0.1_9538
10:04:04.316 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dc911d8-03ff-4916-be25-d8056811a5da] Notify connected event to listeners.
10:04:04.316 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dc911d8-03ff-4916-be25-d8056811a5da] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:04:04.317 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dc911d8-03ff-4916-be25-d8056811a5da] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001b0c9510668
10:04:04.360 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
10:04:04.382 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
10:04:04.506 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 7.45 seconds (JVM running for 8.295)
10:04:04.515 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
10:04:04.516 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
10:04:04.539 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
10:04:04.611 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:04:04.882 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dc911d8-03ff-4916-be25-d8056811a5da] Receive server push request, request = NotifySubscriberRequest, requestId = 4
10:04:04.907 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9dc911d8-03ff-4916-be25-d8056811a5da] Ack server push request, request = NotifySubscriberRequest, requestId = 4
10:07:20.582 [http-nio-9300-exec-3] INFO  c.h.f.s.IChunkUploadServiceImpl - [deleteChunkUpload,162] - 删除分片上传记录成功，fileId: 1750817022557_cmk1tgl9k
10:07:40.987 [http-nio-9300-exec-7] INFO  c.h.f.s.IChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750817260713_kyep5icb4
10:07:47.073 [http-nio-9300-exec-4] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 0
10:07:47.776 [http-nio-9300-exec-4] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 1
10:07:48.460 [http-nio-9300-exec-4] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 2
10:07:49.167 [http-nio-9300-exec-4] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 3
10:07:49.735 [http-nio-9300-exec-4] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 4
10:07:50.411 [http-nio-9300-exec-4] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 5
10:07:51.002 [http-nio-9300-exec-4] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 6
10:07:51.743 [http-nio-9300-exec-4] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 7
10:07:52.320 [http-nio-9300-exec-4] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 8
10:07:52.907 [http-nio-9300-exec-4] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 9
10:07:53.539 [http-nio-9300-exec-4] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 10
10:07:54.116 [http-nio-9300-exec-4] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 11
10:07:54.540 [http-nio-9300-exec-4] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 12
10:07:55.100 [http-nio-9300-exec-5] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 13
10:07:55.767 [http-nio-9300-exec-5] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 14
10:07:56.364 [http-nio-9300-exec-5] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 15
10:07:58.381 [http-nio-9300-exec-5] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 16
10:08:00.823 [http-nio-9300-exec-5] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 17
10:08:01.423 [http-nio-9300-exec-5] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 18
10:08:02.133 [http-nio-9300-exec-5] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 19
10:08:02.758 [http-nio-9300-exec-5] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 20
10:08:05.678 [http-nio-9300-exec-6] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 21
10:08:06.366 [http-nio-9300-exec-6] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 22
10:08:07.271 [http-nio-9300-exec-6] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 23
10:08:07.922 [http-nio-9300-exec-6] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 24
10:08:08.606 [http-nio-9300-exec-6] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 25
10:08:09.205 [http-nio-9300-exec-6] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 26
10:08:09.795 [http-nio-9300-exec-6] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 27
10:08:10.420 [http-nio-9300-exec-6] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 28
10:08:11.082 [http-nio-9300-exec-6] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 29
10:08:11.703 [http-nio-9300-exec-6] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 30
10:08:15.097 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 31
10:08:16.210 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 32
10:08:16.885 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 33
10:08:17.349 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 34
10:08:18.037 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 35
10:08:18.801 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 36
10:08:19.498 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 37
10:08:20.175 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 38
10:08:20.996 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 39
10:08:21.744 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 40
10:08:22.421 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 41
10:08:23.157 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 42
10:08:23.835 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 43
10:08:24.495 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 44
10:08:25.152 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 45
10:08:25.856 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 46
10:08:26.559 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 47
10:08:29.340 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 48
10:08:30.586 [http-nio-9300-exec-3] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 49
10:08:31.321 [http-nio-9300-exec-2] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 50
10:08:32.064 [http-nio-9300-exec-7] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 51
10:08:32.944 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 52
10:08:33.771 [http-nio-9300-exec-8] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 53
10:08:34.659 [http-nio-9300-exec-10] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 54
10:08:35.523 [http-nio-9300-exec-4] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 55
10:08:36.291 [http-nio-9300-exec-5] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 56
10:08:37.245 [http-nio-9300-exec-6] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 57
10:08:38.077 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 58
10:08:38.852 [http-nio-9300-exec-3] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 59
10:08:39.596 [http-nio-9300-exec-2] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 60
10:08:40.747 [http-nio-9300-exec-7] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 61
10:08:41.832 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 62
10:08:47.969 [http-nio-9300-exec-8] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 63
10:08:48.642 [http-nio-9300-exec-8] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 64
10:08:49.312 [http-nio-9300-exec-10] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 65
10:08:50.024 [http-nio-9300-exec-10] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 66
10:08:50.621 [http-nio-9300-exec-10] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 67
10:08:51.249 [http-nio-9300-exec-10] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 68
10:08:51.954 [http-nio-9300-exec-10] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 69
10:08:52.602 [http-nio-9300-exec-10] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 70
10:08:53.171 [http-nio-9300-exec-10] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 71
10:08:53.803 [http-nio-9300-exec-10] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 72
10:08:54.417 [http-nio-9300-exec-10] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 73
10:08:55.024 [http-nio-9300-exec-10] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 74
10:08:55.606 [http-nio-9300-exec-10] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 75
10:08:56.167 [http-nio-9300-exec-4] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 76
10:08:56.766 [http-nio-9300-exec-4] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 77
10:08:57.646 [http-nio-9300-exec-4] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 78
10:08:58.368 [http-nio-9300-exec-5] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 79
10:08:59.002 [http-nio-9300-exec-5] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 80
10:08:59.689 [http-nio-9300-exec-5] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 81
10:09:00.050 [http-nio-9300-exec-5] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750817260713_kyep5icb4, chunkIndex: 82
10:44:29.838 [http-nio-9300-exec-3] INFO  c.h.f.s.IChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750819469820_2uhbknb7n
10:44:55.535 [http-nio-9300-exec-2] INFO  c.h.f.s.IChunkUploadServiceImpl - [deleteChunkUpload,162] - 删除分片上传记录成功，fileId: 1750819469820_2uhbknb7n
10:45:47.688 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:45:47.690 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:45:48.017 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:45:48.017 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@aecf613[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:45:48.017 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750817044198_127.0.0.1_9538
10:45:48.027 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@515886a5[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 507]
10:45:48.035 [nacos-grpc-client-executor-507] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750817044198_127.0.0.1_9538]Ignore complete event,isRunning:false,isAbandon=false
10:45:53.850 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:45:54.267 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0f439621-d7db-4309-ab75-235528ecdb01_config-0
10:45:54.324 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 3 keys and 6 values 
10:45:54.344 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
10:45:54.349 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
10:45:54.357 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
10:45:54.366 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
10:45:54.375 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
10:45:54.378 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f439621-d7db-4309-ab75-235528ecdb01_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:45:54.378 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f439621-d7db-4309-ab75-235528ecdb01_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001dc843c6b40
10:45:54.378 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f439621-d7db-4309-ab75-235528ecdb01_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001dc843c6d60
10:45:54.379 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f439621-d7db-4309-ab75-235528ecdb01_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:45:54.380 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f439621-d7db-4309-ab75-235528ecdb01_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:45:54.386 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f439621-d7db-4309-ab75-235528ecdb01_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:45:55.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f439621-d7db-4309-ab75-235528ecdb01_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750819554958_127.0.0.1_14257
10:45:55.153 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f439621-d7db-4309-ab75-235528ecdb01_config-0] Notify connected event to listeners.
10:45:55.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f439621-d7db-4309-ab75-235528ecdb01_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:45:55.162 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f439621-d7db-4309-ab75-235528ecdb01_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001dc84500ad8
10:45:55.267 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:45:56.920 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
10:45:56.920 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:45:56.920 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:45:57.037 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:45:58.305 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:45:59.921 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9b787133-a088-4581-9d1c-57b82409319c
10:45:59.922 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b787133-a088-4581-9d1c-57b82409319c] RpcClient init label, labels = {module=naming, source=sdk}
10:45:59.925 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b787133-a088-4581-9d1c-57b82409319c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:45:59.925 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b787133-a088-4581-9d1c-57b82409319c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:45:59.925 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b787133-a088-4581-9d1c-57b82409319c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:45:59.926 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b787133-a088-4581-9d1c-57b82409319c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:46:00.054 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b787133-a088-4581-9d1c-57b82409319c] Success to connect to server [localhost:8848] on start up, connectionId = 1750819559936_127.0.0.1_14267
10:46:00.055 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b787133-a088-4581-9d1c-57b82409319c] Notify connected event to listeners.
10:46:00.054 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b787133-a088-4581-9d1c-57b82409319c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:46:00.059 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b787133-a088-4581-9d1c-57b82409319c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001dc84500ad8
10:46:00.109 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
10:46:00.131 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
10:46:00.229 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 6.919 seconds (JVM running for 7.664)
10:46:00.241 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
10:46:00.245 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
10:46:00.248 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
10:46:00.604 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:46:00.630 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b787133-a088-4581-9d1c-57b82409319c] Receive server push request, request = NotifySubscriberRequest, requestId = 8
10:46:00.693 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9b787133-a088-4581-9d1c-57b82409319c] Ack server push request, request = NotifySubscriberRequest, requestId = 8
10:46:31.567 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750819591217_lj5xjuexg
10:49:46.499 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:49:46.503 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:49:46.830 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:49:46.831 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@550a08b3[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:49:46.831 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750819559936_127.0.0.1_14267
10:49:46.843 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7d4b6faa[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 54]
10:49:46.848 [nacos-grpc-client-executor-54] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750819559936_127.0.0.1_14267]Ignore complete event,isRunning:false,isAbandon=false
10:49:50.136 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:49:50.582 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e6f349a2-18c3-4eac-bba6-d3562c8f1b8c_config-0
10:49:50.626 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
10:49:50.646 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
10:49:50.653 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
10:49:50.660 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
10:49:50.668 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
10:49:50.675 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
10:49:50.677 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6f349a2-18c3-4eac-bba6-d3562c8f1b8c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:49:50.678 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6f349a2-18c3-4eac-bba6-d3562c8f1b8c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001a381396b40
10:49:50.678 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6f349a2-18c3-4eac-bba6-d3562c8f1b8c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001a381396d60
10:49:50.679 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6f349a2-18c3-4eac-bba6-d3562c8f1b8c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:49:50.679 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6f349a2-18c3-4eac-bba6-d3562c8f1b8c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:49:50.687 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6f349a2-18c3-4eac-bba6-d3562c8f1b8c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:49:51.387 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6f349a2-18c3-4eac-bba6-d3562c8f1b8c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750819791204_127.0.0.1_14644
10:49:51.388 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6f349a2-18c3-4eac-bba6-d3562c8f1b8c_config-0] Notify connected event to listeners.
10:49:51.389 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6f349a2-18c3-4eac-bba6-d3562c8f1b8c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:49:51.391 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6f349a2-18c3-4eac-bba6-d3562c8f1b8c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001a381510fd0
10:49:51.487 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:49:53.391 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
10:49:53.391 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:49:53.391 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:49:53.537 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:49:54.941 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:49:56.891 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c50f3506-3bf7-485d-8bf9-baae127a6e40
10:49:56.891 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c50f3506-3bf7-485d-8bf9-baae127a6e40] RpcClient init label, labels = {module=naming, source=sdk}
10:49:56.893 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c50f3506-3bf7-485d-8bf9-baae127a6e40] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:49:56.893 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c50f3506-3bf7-485d-8bf9-baae127a6e40] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:49:56.894 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c50f3506-3bf7-485d-8bf9-baae127a6e40] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:49:56.894 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c50f3506-3bf7-485d-8bf9-baae127a6e40] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:49:57.025 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c50f3506-3bf7-485d-8bf9-baae127a6e40] Success to connect to server [localhost:8848] on start up, connectionId = 1750819796900_127.0.0.1_14664
10:49:57.025 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c50f3506-3bf7-485d-8bf9-baae127a6e40] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:49:57.025 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c50f3506-3bf7-485d-8bf9-baae127a6e40] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001a381510fd0
10:49:57.026 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c50f3506-3bf7-485d-8bf9-baae127a6e40] Notify connected event to listeners.
10:49:57.077 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
10:49:57.102 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
10:49:57.213 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 7.539 seconds (JVM running for 8.462)
10:49:57.222 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
10:49:57.222 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
10:49:57.226 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
10:49:57.420 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:49:57.655 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c50f3506-3bf7-485d-8bf9-baae127a6e40] Receive server push request, request = NotifySubscriberRequest, requestId = 9
10:49:57.676 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c50f3506-3bf7-485d-8bf9-baae127a6e40] Ack server push request, request = NotifySubscriberRequest, requestId = 9
10:50:38.304 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750819837969_q4ogpey86
10:50:40.422 [http-nio-9300-exec-10] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 0
10:50:42.027 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 1
10:50:43.718 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 2
10:50:45.314 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 3
10:50:46.928 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 4
10:50:47.018 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750819837969_q4ogpey86, chunkIndex: 4
10:50:49.665 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 5
10:50:50.493 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 5
10:50:54.556 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 6
10:50:54.843 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 6
10:50:57.536 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 7
10:50:57.883 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 7
10:51:00.385 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 8
10:51:00.697 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 8
10:51:03.561 [http-nio-9300-exec-3] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 9
10:51:03.635 [http-nio-9300-exec-2] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 9
10:51:06.722 [http-nio-9300-exec-4] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 10
10:51:06.985 [http-nio-9300-exec-5] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 10
10:51:08.362 [http-nio-9300-exec-6] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 11
10:51:09.862 [http-nio-9300-exec-7] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 11
10:51:11.362 [http-nio-9300-exec-8] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 12
10:51:13.680 [http-nio-9300-exec-10] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 12
10:51:14.401 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 13
10:51:16.733 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 13
10:51:17.529 [http-nio-9300-exec-3] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 14
10:51:18.977 [http-nio-9300-exec-2] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 14
10:51:21.368 [http-nio-9300-exec-4] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 15
10:51:21.958 [http-nio-9300-exec-5] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 15
10:51:22.217 [http-nio-9300-exec-6] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 16
10:51:22.760 [http-nio-9300-exec-7] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750819837969_q4ogpey86, chunkIndex: 16
11:01:13.310 [http-nio-9300-exec-3] INFO  c.h.f.s.IChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750820473259_2c2atslw7
11:01:15.378 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820473259_2c2atslw7, chunkIndex: 0
11:01:17.078 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820473259_2c2atslw7, chunkIndex: 1
11:01:18.967 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820473259_2c2atslw7, chunkIndex: 2
11:01:21.560 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820473259_2c2atslw7, chunkIndex: 3
11:01:23.798 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820473259_2c2atslw7, chunkIndex: 4
11:01:25.765 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820473259_2c2atslw7, chunkIndex: 5
11:01:27.807 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820473259_2c2atslw7, chunkIndex: 6
11:01:29.911 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820473259_2c2atslw7, chunkIndex: 7
11:01:31.811 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820473259_2c2atslw7, chunkIndex: 8
11:01:33.628 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820473259_2c2atslw7, chunkIndex: 9
11:01:35.861 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820473259_2c2atslw7, chunkIndex: 10
11:01:37.823 [http-nio-9300-exec-3] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820473259_2c2atslw7, chunkIndex: 11
11:01:39.665 [http-nio-9300-exec-2] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820473259_2c2atslw7, chunkIndex: 12
11:01:41.357 [http-nio-9300-exec-4] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820473259_2c2atslw7, chunkIndex: 13
11:01:42.896 [http-nio-9300-exec-5] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820473259_2c2atslw7, chunkIndex: 14
11:01:44.367 [http-nio-9300-exec-6] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820473259_2c2atslw7, chunkIndex: 15
11:01:45.017 [http-nio-9300-exec-8] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820473259_2c2atslw7, chunkIndex: 16
11:06:50.428 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750820810416_zqmgo8uqi
11:06:52.276 [http-nio-9300-exec-7] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820810416_zqmgo8uqi, chunkIndex: 0
11:06:53.905 [http-nio-9300-exec-10] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820810416_zqmgo8uqi, chunkIndex: 1
11:06:55.480 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820810416_zqmgo8uqi, chunkIndex: 2
11:06:58.010 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820810416_zqmgo8uqi, chunkIndex: 3
11:07:00.180 [http-nio-9300-exec-3] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820810416_zqmgo8uqi, chunkIndex: 4
11:07:01.872 [http-nio-9300-exec-2] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820810416_zqmgo8uqi, chunkIndex: 5
11:07:03.879 [http-nio-9300-exec-4] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820810416_zqmgo8uqi, chunkIndex: 6
11:07:05.552 [http-nio-9300-exec-5] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820810416_zqmgo8uqi, chunkIndex: 7
11:07:07.176 [http-nio-9300-exec-6] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820810416_zqmgo8uqi, chunkIndex: 8
11:07:08.949 [http-nio-9300-exec-8] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820810416_zqmgo8uqi, chunkIndex: 9
11:07:10.405 [http-nio-9300-exec-8] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820810416_zqmgo8uqi, chunkIndex: 10
11:07:12.032 [http-nio-9300-exec-7] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820810416_zqmgo8uqi, chunkIndex: 11
11:07:13.978 [http-nio-9300-exec-10] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820810416_zqmgo8uqi, chunkIndex: 12
11:07:15.752 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820810416_zqmgo8uqi, chunkIndex: 13
11:07:17.798 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820810416_zqmgo8uqi, chunkIndex: 14
11:07:20.233 [http-nio-9300-exec-3] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820810416_zqmgo8uqi, chunkIndex: 15
11:07:21.106 [http-nio-9300-exec-2] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750820810416_zqmgo8uqi, chunkIndex: 16
11:10:00.970 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:10:00.987 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:10:01.314 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:10:01.314 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@34617051[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:10:01.314 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750819796900_127.0.0.1_14664
11:10:01.315 [nacos-grpc-client-executor-254] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750819796900_127.0.0.1_14664]Ignore complete event,isRunning:false,isAbandon=false
11:10:01.318 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@319cc8c7[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 255]
11:10:06.309 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:10:06.766 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 88945dfd-1b38-4676-ae76-baa1341b4794_config-0
11:10:06.811 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
11:10:06.831 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
11:10:06.838 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
11:10:06.845 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
11:10:06.856 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
11:10:06.867 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
11:10:06.870 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88945dfd-1b38-4676-ae76-baa1341b4794_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:10:06.871 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88945dfd-1b38-4676-ae76-baa1341b4794_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000014864395f80
11:10:06.871 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88945dfd-1b38-4676-ae76-baa1341b4794_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000148643961a0
11:10:06.872 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88945dfd-1b38-4676-ae76-baa1341b4794_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:10:06.872 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88945dfd-1b38-4676-ae76-baa1341b4794_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:10:06.878 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88945dfd-1b38-4676-ae76-baa1341b4794_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:10:07.705 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88945dfd-1b38-4676-ae76-baa1341b4794_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750821007513_127.0.0.1_2932
11:10:07.706 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88945dfd-1b38-4676-ae76-baa1341b4794_config-0] Notify connected event to listeners.
11:10:07.707 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88945dfd-1b38-4676-ae76-baa1341b4794_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:10:07.713 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88945dfd-1b38-4676-ae76-baa1341b4794_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000014864510228
11:10:07.804 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:10:09.623 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
11:10:09.623 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:10:09.623 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:10:09.740 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:10:11.358 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:10:12.977 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d3cc9261-c5a4-4870-883d-2a8e8b93b4b8
11:10:12.977 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3cc9261-c5a4-4870-883d-2a8e8b93b4b8] RpcClient init label, labels = {module=naming, source=sdk}
11:10:12.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3cc9261-c5a4-4870-883d-2a8e8b93b4b8] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:10:12.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3cc9261-c5a4-4870-883d-2a8e8b93b4b8] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:10:12.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3cc9261-c5a4-4870-883d-2a8e8b93b4b8] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:10:12.979 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3cc9261-c5a4-4870-883d-2a8e8b93b4b8] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:10:13.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3cc9261-c5a4-4870-883d-2a8e8b93b4b8] Success to connect to server [localhost:8848] on start up, connectionId = 1750821012986_127.0.0.1_2939
11:10:13.107 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3cc9261-c5a4-4870-883d-2a8e8b93b4b8] Notify connected event to listeners.
11:10:13.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3cc9261-c5a4-4870-883d-2a8e8b93b4b8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:10:13.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3cc9261-c5a4-4870-883d-2a8e8b93b4b8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000014864510228
11:10:13.154 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
11:10:13.177 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
11:10:13.272 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 7.547 seconds (JVM running for 8.595)
11:10:13.283 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
11:10:13.283 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
11:10:13.285 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
11:10:13.512 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:10:13.716 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3cc9261-c5a4-4870-883d-2a8e8b93b4b8] Receive server push request, request = NotifySubscriberRequest, requestId = 10
11:10:13.729 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3cc9261-c5a4-4870-883d-2a8e8b93b4b8] Ack server push request, request = NotifySubscriberRequest, requestId = 10
11:19:38.552 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:19:38.681 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:19:39.046 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:19:39.046 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@394dc928[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:19:39.046 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750821012986_127.0.0.1_2939
11:19:39.053 [nacos-grpc-client-executor-123] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750821012986_127.0.0.1_2939]Ignore complete event,isRunning:false,isAbandon=false
11:19:39.056 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@70f9b779[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 124]
11:19:44.534 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:19:45.001 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b4d118a1-ccc6-404f-9137-cb964ea07f8b_config-0
11:19:45.050 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
11:19:45.068 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
11:19:45.074 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
11:19:45.081 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
11:19:45.088 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
11:19:45.098 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
11:19:45.100 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d118a1-ccc6-404f-9137-cb964ea07f8b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:19:45.100 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d118a1-ccc6-404f-9137-cb964ea07f8b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002581f3c68d8
11:19:45.100 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d118a1-ccc6-404f-9137-cb964ea07f8b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002581f3c6af8
11:19:45.101 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d118a1-ccc6-404f-9137-cb964ea07f8b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:19:45.101 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d118a1-ccc6-404f-9137-cb964ea07f8b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:19:45.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d118a1-ccc6-404f-9137-cb964ea07f8b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:19:45.921 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d118a1-ccc6-404f-9137-cb964ea07f8b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750821585729_127.0.0.1_3756
11:19:45.922 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d118a1-ccc6-404f-9137-cb964ea07f8b_config-0] Notify connected event to listeners.
11:19:45.922 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d118a1-ccc6-404f-9137-cb964ea07f8b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:19:45.924 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b4d118a1-ccc6-404f-9137-cb964ea07f8b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002581f500668
11:19:46.019 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:19:47.704 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
11:19:47.704 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:19:47.704 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:19:47.812 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:19:49.188 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:19:50.786 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6e84612f-d7cf-4122-be72-a942d692ac85
11:19:50.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e84612f-d7cf-4122-be72-a942d692ac85] RpcClient init label, labels = {module=naming, source=sdk}
11:19:50.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e84612f-d7cf-4122-be72-a942d692ac85] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:19:50.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e84612f-d7cf-4122-be72-a942d692ac85] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:19:50.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e84612f-d7cf-4122-be72-a942d692ac85] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:19:50.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e84612f-d7cf-4122-be72-a942d692ac85] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:19:50.922 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e84612f-d7cf-4122-be72-a942d692ac85] Success to connect to server [localhost:8848] on start up, connectionId = 1750821590799_127.0.0.1_3762
11:19:50.923 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e84612f-d7cf-4122-be72-a942d692ac85] Notify connected event to listeners.
11:19:50.923 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e84612f-d7cf-4122-be72-a942d692ac85] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:19:50.928 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e84612f-d7cf-4122-be72-a942d692ac85] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002581f500668
11:19:50.980 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
11:19:51.003 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
11:19:51.109 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 7.046 seconds (JVM running for 7.772)
11:19:51.116 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
11:19:51.118 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
11:19:51.120 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
11:19:51.220 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:19:51.533 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e84612f-d7cf-4122-be72-a942d692ac85] Receive server push request, request = NotifySubscriberRequest, requestId = 11
11:19:51.546 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e84612f-d7cf-4122-be72-a942d692ac85] Ack server push request, request = NotifySubscriberRequest, requestId = 11
11:20:20.155 [http-nio-9300-exec-3] INFO  c.h.f.s.IChunkUploadServiceImpl - [initChunkUpload,62] - 初始化分片上传成功，fileId: 1750821619743_8f39bgc47
11:20:21.669 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,103] - 分片上传成功，fileId: 1750821619743_8f39bgc47, chunkIndex: 0
11:20:23.485 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,103] - 分片上传成功，fileId: 1750821619743_8f39bgc47, chunkIndex: 1
11:20:25.081 [http-nio-9300-exec-2] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,103] - 分片上传成功，fileId: 1750821619743_8f39bgc47, chunkIndex: 2
11:20:28.457 [http-nio-9300-exec-3] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,103] - 分片上传成功，fileId: 1750821619743_8f39bgc47, chunkIndex: 3
11:20:30.130 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,103] - 分片上传成功，fileId: 1750821619743_8f39bgc47, chunkIndex: 4
11:20:32.143 [http-nio-9300-exec-4] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,103] - 分片上传成功，fileId: 1750821619743_8f39bgc47, chunkIndex: 5
11:20:33.856 [http-nio-9300-exec-5] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,103] - 分片上传成功，fileId: 1750821619743_8f39bgc47, chunkIndex: 6
11:20:35.875 [http-nio-9300-exec-6] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,103] - 分片上传成功，fileId: 1750821619743_8f39bgc47, chunkIndex: 7
11:20:37.821 [http-nio-9300-exec-7] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,103] - 分片上传成功，fileId: 1750821619743_8f39bgc47, chunkIndex: 8
11:20:39.802 [http-nio-9300-exec-8] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,103] - 分片上传成功，fileId: 1750821619743_8f39bgc47, chunkIndex: 9
11:20:41.479 [http-nio-9300-exec-10] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,103] - 分片上传成功，fileId: 1750821619743_8f39bgc47, chunkIndex: 10
11:20:42.939 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,103] - 分片上传成功，fileId: 1750821619743_8f39bgc47, chunkIndex: 11
11:20:44.462 [http-nio-9300-exec-2] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,103] - 分片上传成功，fileId: 1750821619743_8f39bgc47, chunkIndex: 12
11:20:45.975 [http-nio-9300-exec-3] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,103] - 分片上传成功，fileId: 1750821619743_8f39bgc47, chunkIndex: 13
11:20:47.643 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,103] - 分片上传成功，fileId: 1750821619743_8f39bgc47, chunkIndex: 14
11:20:49.313 [http-nio-9300-exec-4] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,103] - 分片上传成功，fileId: 1750821619743_8f39bgc47, chunkIndex: 15
11:20:51.051 [http-nio-9300-exec-4] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,103] - 分片上传成功，fileId: 1750821619743_8f39bgc47, chunkIndex: 16
11:30:24.436 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:30:24.441 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:30:24.769 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:30:24.770 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3554a0ec[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:30:24.770 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750821590799_127.0.0.1_3762
11:30:24.776 [nacos-grpc-client-executor-135] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750821590799_127.0.0.1_3762]Ignore complete event,isRunning:false,isAbandon=false
11:30:24.779 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@35820966[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 136]
11:30:31.073 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:30:31.553 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1a1f5397-8d89-4a97-bc03-721df1b86247_config-0
11:30:31.609 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 3 keys and 6 values 
11:30:31.628 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
11:30:31.636 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
11:30:31.643 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
11:30:31.651 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
11:30:31.659 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
11:30:31.662 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a1f5397-8d89-4a97-bc03-721df1b86247_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:30:31.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a1f5397-8d89-4a97-bc03-721df1b86247_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000023420396b40
11:30:31.664 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a1f5397-8d89-4a97-bc03-721df1b86247_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000023420396d60
11:30:31.664 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a1f5397-8d89-4a97-bc03-721df1b86247_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:30:31.667 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a1f5397-8d89-4a97-bc03-721df1b86247_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:30:31.678 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a1f5397-8d89-4a97-bc03-721df1b86247_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:30:32.630 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a1f5397-8d89-4a97-bc03-721df1b86247_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750822232455_127.0.0.1_4602
11:30:32.632 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a1f5397-8d89-4a97-bc03-721df1b86247_config-0] Notify connected event to listeners.
11:30:32.634 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a1f5397-8d89-4a97-bc03-721df1b86247_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:30:32.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1a1f5397-8d89-4a97-bc03-721df1b86247_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000023420510668
11:30:32.747 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:30:34.579 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
11:30:34.579 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:30:34.579 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:30:34.719 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:30:36.084 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:30:37.720 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8f14002d-854b-409b-8c81-e36676c7b551
11:30:37.720 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f14002d-854b-409b-8c81-e36676c7b551] RpcClient init label, labels = {module=naming, source=sdk}
11:30:37.721 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f14002d-854b-409b-8c81-e36676c7b551] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:30:37.721 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f14002d-854b-409b-8c81-e36676c7b551] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:30:37.722 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f14002d-854b-409b-8c81-e36676c7b551] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:30:37.723 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f14002d-854b-409b-8c81-e36676c7b551] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:30:37.850 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f14002d-854b-409b-8c81-e36676c7b551] Success to connect to server [localhost:8848] on start up, connectionId = 1750822237730_127.0.0.1_4622
11:30:37.851 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f14002d-854b-409b-8c81-e36676c7b551] Notify connected event to listeners.
11:30:37.854 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f14002d-854b-409b-8c81-e36676c7b551] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:30:37.856 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f14002d-854b-409b-8c81-e36676c7b551] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000023420510668
11:30:37.915 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
11:30:37.946 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
11:30:38.045 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 7.445 seconds (JVM running for 8.155)
11:30:38.054 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
11:30:38.056 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
11:30:38.058 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
11:30:38.420 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:30:38.442 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f14002d-854b-409b-8c81-e36676c7b551] Receive server push request, request = NotifySubscriberRequest, requestId = 12
11:30:38.456 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f14002d-854b-409b-8c81-e36676c7b551] Ack server push request, request = NotifySubscriberRequest, requestId = 12
11:32:03.408 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f14002d-854b-409b-8c81-e36676c7b551] Server healthy check fail, currentConnection = 1750822237730_127.0.0.1_4622
11:32:03.410 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f14002d-854b-409b-8c81-e36676c7b551] Try to reconnect to a new server, server is  not appointed, will choose a random server.
11:32:06.456 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f14002d-854b-409b-8c81-e36676c7b551] Success to connect a server [localhost:8848], connectionId = 1750822325957_127.0.0.1_4735
11:32:06.456 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f14002d-854b-409b-8c81-e36676c7b551] Abandon prev connection, server is localhost:8848, connectionId is 1750822237730_127.0.0.1_4622
11:32:06.456 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750822237730_127.0.0.1_4622
11:32:06.696 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f14002d-854b-409b-8c81-e36676c7b551] Notify disconnected event to listeners
11:32:06.696 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f14002d-854b-409b-8c81-e36676c7b551] Notify connected event to listeners.
11:32:06.726 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750822237730_127.0.0.1_4622]Ignore complete event,isRunning:true,isAbandon=true
11:32:07.203 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f14002d-854b-409b-8c81-e36676c7b551] Receive server push request, request = NotifySubscriberRequest, requestId = 13
11:32:07.204 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f14002d-854b-409b-8c81-e36676c7b551] Ack server push request, request = NotifySubscriberRequest, requestId = 13
11:35:43.387 [http-nio-9300-exec-7] INFO  c.h.f.s.IChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750822542871_8ibkxxqn0
11:35:46.844 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750822542871_8ibkxxqn0, chunkIndex: 0
11:35:48.623 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750822542871_8ibkxxqn0, chunkIndex: 1
11:35:50.542 [http-nio-9300-exec-10] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750822542871_8ibkxxqn0, chunkIndex: 2
11:35:53.499 [http-nio-9300-exec-7] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750822542871_8ibkxxqn0, chunkIndex: 3
11:35:55.234 [http-nio-9300-exec-8] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750822542871_8ibkxxqn0, chunkIndex: 4
11:35:57.096 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750822542871_8ibkxxqn0, chunkIndex: 5
11:35:58.999 [http-nio-9300-exec-2] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750822542871_8ibkxxqn0, chunkIndex: 6
11:36:01.043 [http-nio-9300-exec-3] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750822542871_8ibkxxqn0, chunkIndex: 7
11:36:02.867 [http-nio-9300-exec-4] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750822542871_8ibkxxqn0, chunkIndex: 8
11:36:04.749 [http-nio-9300-exec-5] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750822542871_8ibkxxqn0, chunkIndex: 9
11:36:06.693 [http-nio-9300-exec-6] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750822542871_8ibkxxqn0, chunkIndex: 10
11:36:08.501 [http-nio-9300-exec-9] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750822542871_8ibkxxqn0, chunkIndex: 11
11:36:10.270 [http-nio-9300-exec-10] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750822542871_8ibkxxqn0, chunkIndex: 12
11:36:12.089 [http-nio-9300-exec-7] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750822542871_8ibkxxqn0, chunkIndex: 13
11:36:13.976 [http-nio-9300-exec-8] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750822542871_8ibkxxqn0, chunkIndex: 14
11:36:15.651 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750822542871_8ibkxxqn0, chunkIndex: 15
11:36:16.762 [http-nio-9300-exec-2] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750822542871_8ibkxxqn0, chunkIndex: 16
11:40:15.716 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:40:15.722 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:40:16.029 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:40:16.030 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6f9682c5[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:40:16.030 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750822325957_127.0.0.1_4735
11:40:16.034 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@75c22b8c[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 129]
11:40:16.034 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f14002d-854b-409b-8c81-e36676c7b551] Notify disconnected event to listeners
11:40:21.525 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:40:22.066 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a9f1bdf2-3346-418e-b5b1-2138f950e3ba_config-0
11:40:22.126 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 3 keys and 6 values 
11:40:22.145 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
11:40:22.152 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
11:40:22.159 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
11:40:22.167 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
11:40:22.175 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
11:40:22.178 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9f1bdf2-3346-418e-b5b1-2138f950e3ba_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:40:22.178 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9f1bdf2-3346-418e-b5b1-2138f950e3ba_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000196af396230
11:40:22.178 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9f1bdf2-3346-418e-b5b1-2138f950e3ba_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000196af396450
11:40:22.179 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9f1bdf2-3346-418e-b5b1-2138f950e3ba_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:40:22.179 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9f1bdf2-3346-418e-b5b1-2138f950e3ba_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:40:22.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9f1bdf2-3346-418e-b5b1-2138f950e3ba_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:40:22.959 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9f1bdf2-3346-418e-b5b1-2138f950e3ba_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750822822771_127.0.0.1_5659
11:40:22.961 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9f1bdf2-3346-418e-b5b1-2138f950e3ba_config-0] Notify connected event to listeners.
11:40:22.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9f1bdf2-3346-418e-b5b1-2138f950e3ba_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:40:22.965 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a9f1bdf2-3346-418e-b5b1-2138f950e3ba_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000196af510228
11:40:23.077 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:40:24.743 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
11:40:24.743 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:40:24.743 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:40:24.862 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:40:26.363 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:40:28.213 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a2e7d299-3120-4c87-b224-1e7b491ec7f4
11:40:28.213 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2e7d299-3120-4c87-b224-1e7b491ec7f4] RpcClient init label, labels = {module=naming, source=sdk}
11:40:28.215 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2e7d299-3120-4c87-b224-1e7b491ec7f4] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:40:28.215 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2e7d299-3120-4c87-b224-1e7b491ec7f4] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:40:28.216 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2e7d299-3120-4c87-b224-1e7b491ec7f4] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:40:28.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2e7d299-3120-4c87-b224-1e7b491ec7f4] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:40:28.342 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2e7d299-3120-4c87-b224-1e7b491ec7f4] Success to connect to server [localhost:8848] on start up, connectionId = 1750822828228_127.0.0.1_5671
11:40:28.344 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2e7d299-3120-4c87-b224-1e7b491ec7f4] Notify connected event to listeners.
11:40:28.344 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2e7d299-3120-4c87-b224-1e7b491ec7f4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:40:28.344 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2e7d299-3120-4c87-b224-1e7b491ec7f4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000196af510228
11:40:28.392 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
11:40:28.416 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
11:40:28.551 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 7.526 seconds (JVM running for 8.835)
11:40:28.559 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
11:40:28.560 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
11:40:28.562 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
11:40:28.878 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2e7d299-3120-4c87-b224-1e7b491ec7f4] Receive server push request, request = NotifySubscriberRequest, requestId = 15
11:40:28.912 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:40:28.919 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2e7d299-3120-4c87-b224-1e7b491ec7f4] Ack server push request, request = NotifySubscriberRequest, requestId = 15
11:41:48.149 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750822907639_340vz4phw
11:41:51.182 [http-nio-9300-exec-3] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750822907639_340vz4phw, chunkIndex: 0
11:43:06.826 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:43:06.829 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:43:07.161 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:43:07.161 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6a8f16d3[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:43:07.161 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750822828228_127.0.0.1_5671
11:43:07.164 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750822828228_127.0.0.1_5671]Ignore complete event,isRunning:false,isAbandon=false
11:43:07.165 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4b3d3ad0[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 44]
11:43:11.719 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:43:12.304 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3aeb8676-540b-49b6-ad1c-ddefdb5dd058_config-0
11:43:12.359 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 3 keys and 6 values 
11:43:12.379 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
11:43:12.390 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
11:43:12.398 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
11:43:12.408 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
11:43:12.415 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
11:43:12.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3aeb8676-540b-49b6-ad1c-ddefdb5dd058_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:43:12.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3aeb8676-540b-49b6-ad1c-ddefdb5dd058_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000225c13c6b40
11:43:12.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3aeb8676-540b-49b6-ad1c-ddefdb5dd058_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000225c13c6d60
11:43:12.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3aeb8676-540b-49b6-ad1c-ddefdb5dd058_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:43:12.420 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3aeb8676-540b-49b6-ad1c-ddefdb5dd058_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:43:12.426 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3aeb8676-540b-49b6-ad1c-ddefdb5dd058_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:43:13.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3aeb8676-540b-49b6-ad1c-ddefdb5dd058_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750822993038_127.0.0.1_5978
11:43:13.228 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3aeb8676-540b-49b6-ad1c-ddefdb5dd058_config-0] Notify connected event to listeners.
11:43:13.229 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3aeb8676-540b-49b6-ad1c-ddefdb5dd058_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:43:13.230 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3aeb8676-540b-49b6-ad1c-ddefdb5dd058_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000225c1500668
11:43:13.339 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:43:15.960 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
11:43:15.961 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:43:15.961 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:43:16.199 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:43:24.201 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:43:31.292 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6670428f-047f-4e98-9802-a5dab9705880
11:43:31.292 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6670428f-047f-4e98-9802-a5dab9705880] RpcClient init label, labels = {module=naming, source=sdk}
11:43:31.297 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6670428f-047f-4e98-9802-a5dab9705880] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:43:31.298 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6670428f-047f-4e98-9802-a5dab9705880] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:43:31.298 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6670428f-047f-4e98-9802-a5dab9705880] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:43:31.300 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6670428f-047f-4e98-9802-a5dab9705880] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:43:31.437 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6670428f-047f-4e98-9802-a5dab9705880] Success to connect to server [localhost:8848] on start up, connectionId = 1750823011317_127.0.0.1_6025
11:43:31.438 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6670428f-047f-4e98-9802-a5dab9705880] Notify connected event to listeners.
11:43:31.438 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6670428f-047f-4e98-9802-a5dab9705880] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:43:31.439 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6670428f-047f-4e98-9802-a5dab9705880] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000225c1500668
11:43:31.521 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
11:43:31.563 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
11:43:31.844 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 20.743 seconds (JVM running for 21.65)
11:43:31.876 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
11:43:31.878 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
11:43:31.885 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
11:43:32.057 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6670428f-047f-4e98-9802-a5dab9705880] Receive server push request, request = NotifySubscriberRequest, requestId = 16
11:43:32.083 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6670428f-047f-4e98-9802-a5dab9705880] Ack server push request, request = NotifySubscriberRequest, requestId = 16
11:43:32.317 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:43:52.288 [http-nio-9300-exec-1] INFO  c.h.f.s.IChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750823031677_3rtp74a9z
11:43:56.197 [http-nio-9300-exec-3] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750823031677_3rtp74a9z, chunkIndex: 0
12:10:27.132 [http-nio-9300-exec-10] INFO  c.h.f.s.IChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750824627083_gkx1ahx3r
12:10:30.268 [http-nio-9300-exec-2] INFO  c.h.f.s.IChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750824627083_gkx1ahx3r, chunkIndex: 0
14:27:01.278 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:27:01.284 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:27:01.632 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:27:01.632 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@93d766e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:27:01.633 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750823011317_127.0.0.1_6025
14:27:01.637 [nacos-grpc-client-executor-1974] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750823011317_127.0.0.1_6025]Ignore complete event,isRunning:false,isAbandon=false
14:27:01.644 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@60bf11c1[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1975]
14:27:25.347 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:27:26.237 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fc497312-a25e-48e2-a944-41bcc6fe4130_config-0
14:27:26.331 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 53 ms to scan 1 urls, producing 3 keys and 6 values 
14:27:26.367 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
14:27:26.383 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
14:27:26.401 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
14:27:26.416 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
14:27:26.434 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
14:27:26.440 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc497312-a25e-48e2-a944-41bcc6fe4130_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:27:26.441 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc497312-a25e-48e2-a944-41bcc6fe4130_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001a2013af470
14:27:26.442 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc497312-a25e-48e2-a944-41bcc6fe4130_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001a2013af690
14:27:26.443 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc497312-a25e-48e2-a944-41bcc6fe4130_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:27:26.444 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc497312-a25e-48e2-a944-41bcc6fe4130_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:27:26.458 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc497312-a25e-48e2-a944-41bcc6fe4130_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:27:27.778 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc497312-a25e-48e2-a944-41bcc6fe4130_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750832847540_127.0.0.1_10010
14:27:27.779 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc497312-a25e-48e2-a944-41bcc6fe4130_config-0] Notify connected event to listeners.
14:27:27.779 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc497312-a25e-48e2-a944-41bcc6fe4130_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:27:27.780 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fc497312-a25e-48e2-a944-41bcc6fe4130_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001a2014e8fb0
14:27:27.928 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:27:31.449 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
14:27:31.450 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:27:31.450 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:27:31.655 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:27:34.386 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:27:37.059 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 52960736-50ed-4df8-a2df-78462adda0ff
14:27:37.060 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [52960736-50ed-4df8-a2df-78462adda0ff] RpcClient init label, labels = {module=naming, source=sdk}
14:27:37.062 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [52960736-50ed-4df8-a2df-78462adda0ff] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:27:37.062 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [52960736-50ed-4df8-a2df-78462adda0ff] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:27:37.063 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [52960736-50ed-4df8-a2df-78462adda0ff] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:27:37.065 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [52960736-50ed-4df8-a2df-78462adda0ff] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:27:37.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [52960736-50ed-4df8-a2df-78462adda0ff] Success to connect to server [localhost:8848] on start up, connectionId = 1750832857078_127.0.0.1_10031
14:27:37.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [52960736-50ed-4df8-a2df-78462adda0ff] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:27:37.200 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [52960736-50ed-4df8-a2df-78462adda0ff] Notify connected event to listeners.
14:27:37.201 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [52960736-50ed-4df8-a2df-78462adda0ff] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001a2014e8fb0
14:27:37.269 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
14:27:37.311 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
14:27:37.471 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 13.12 seconds (JVM running for 14.571)
14:27:37.488 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
14:27:37.488 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
14:27:37.493 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
14:27:37.754 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [52960736-50ed-4df8-a2df-78462adda0ff] Receive server push request, request = NotifySubscriberRequest, requestId = 43
14:27:37.773 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [52960736-50ed-4df8-a2df-78462adda0ff] Ack server push request, request = NotifySubscriberRequest, requestId = 43
14:30:08.254 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:30:08.269 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:30:08.611 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:30:08.613 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7def89bd[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:30:08.613 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750832857078_127.0.0.1_10031
14:30:08.618 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750832857078_127.0.0.1_10031]Ignore complete event,isRunning:false,isAbandon=false
14:30:08.619 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@51dd5420[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 41]
14:34:11.132 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:34:11.975 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cddfb700-b9f4-4823-ba68-18137233a858_config-0
14:34:12.071 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 47 ms to scan 1 urls, producing 3 keys and 6 values 
14:34:12.108 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
14:34:12.119 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
14:34:12.133 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
14:34:12.149 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
14:34:12.164 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
14:34:12.169 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cddfb700-b9f4-4823-ba68-18137233a858_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:34:12.169 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cddfb700-b9f4-4823-ba68-18137233a858_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002414c396b40
14:34:12.170 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cddfb700-b9f4-4823-ba68-18137233a858_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002414c396d60
14:34:12.171 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cddfb700-b9f4-4823-ba68-18137233a858_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:34:12.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cddfb700-b9f4-4823-ba68-18137233a858_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:34:12.186 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cddfb700-b9f4-4823-ba68-18137233a858_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:34:13.538 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cddfb700-b9f4-4823-ba68-18137233a858_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750833253291_127.0.0.1_10907
14:34:13.540 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cddfb700-b9f4-4823-ba68-18137233a858_config-0] Notify connected event to listeners.
14:34:13.540 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cddfb700-b9f4-4823-ba68-18137233a858_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:34:13.541 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cddfb700-b9f4-4823-ba68-18137233a858_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002414c510ad8
14:34:13.669 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:34:17.046 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
14:34:17.047 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:34:17.047 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:34:17.301 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:34:20.226 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:34:23.085 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f50dd952-981c-4058-8b76-4ed26f63e59a
14:34:23.087 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f50dd952-981c-4058-8b76-4ed26f63e59a] RpcClient init label, labels = {module=naming, source=sdk}
14:34:23.090 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f50dd952-981c-4058-8b76-4ed26f63e59a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:34:23.090 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f50dd952-981c-4058-8b76-4ed26f63e59a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:34:23.091 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f50dd952-981c-4058-8b76-4ed26f63e59a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:34:23.091 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f50dd952-981c-4058-8b76-4ed26f63e59a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:34:23.220 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f50dd952-981c-4058-8b76-4ed26f63e59a] Success to connect to server [localhost:8848] on start up, connectionId = 1750833263104_127.0.0.1_10949
14:34:23.220 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f50dd952-981c-4058-8b76-4ed26f63e59a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:34:23.221 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f50dd952-981c-4058-8b76-4ed26f63e59a] Notify connected event to listeners.
14:34:23.221 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f50dd952-981c-4058-8b76-4ed26f63e59a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002414c510ad8
14:34:23.283 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
14:34:23.325 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
14:34:23.532 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 13.233 seconds (JVM running for 14.551)
14:34:23.549 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
14:34:23.549 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
14:34:23.553 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
14:34:23.812 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f50dd952-981c-4058-8b76-4ed26f63e59a] Receive server push request, request = NotifySubscriberRequest, requestId = 53
14:34:23.830 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f50dd952-981c-4058-8b76-4ed26f63e59a] Ack server push request, request = NotifySubscriberRequest, requestId = 53
14:34:23.924 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:36:52.207 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:36:52.213 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:36:52.551 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:36:52.552 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@117610eb[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:36:52.553 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750833263104_127.0.0.1_10949
14:36:52.559 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750833263104_127.0.0.1_10949]Ignore complete event,isRunning:false,isAbandon=false
14:36:52.559 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6f801747[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 40]
14:40:03.658 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:40:04.672 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 85477048-9380-4088-8479-1d666ecdf79b_config-0
14:40:04.767 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
14:40:04.799 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
14:40:04.814 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
14:40:04.827 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
14:40:04.840 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
14:40:04.854 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
14:40:04.860 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85477048-9380-4088-8479-1d666ecdf79b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:40:04.861 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85477048-9380-4088-8479-1d666ecdf79b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000025b0c3aed88
14:40:04.861 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85477048-9380-4088-8479-1d666ecdf79b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000025b0c3aefa8
14:40:04.862 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85477048-9380-4088-8479-1d666ecdf79b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:40:04.863 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85477048-9380-4088-8479-1d666ecdf79b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:40:04.877 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85477048-9380-4088-8479-1d666ecdf79b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:40:06.519 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85477048-9380-4088-8479-1d666ecdf79b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750833606276_127.0.0.1_11779
14:40:06.520 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85477048-9380-4088-8479-1d666ecdf79b_config-0] Notify connected event to listeners.
14:40:06.520 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85477048-9380-4088-8479-1d666ecdf79b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:40:06.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85477048-9380-4088-8479-1d666ecdf79b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000025b0c4e8ad8
14:40:06.652 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:40:09.933 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
14:40:09.933 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:40:09.933 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:40:10.131 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:40:12.544 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:40:15.449 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 420ff1f8-2fa8-4ff9-b00e-339647d280d9
14:40:15.449 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [420ff1f8-2fa8-4ff9-b00e-339647d280d9] RpcClient init label, labels = {module=naming, source=sdk}
14:40:15.449 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [420ff1f8-2fa8-4ff9-b00e-339647d280d9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:40:15.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [420ff1f8-2fa8-4ff9-b00e-339647d280d9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:40:15.454 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [420ff1f8-2fa8-4ff9-b00e-339647d280d9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:40:15.456 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [420ff1f8-2fa8-4ff9-b00e-339647d280d9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:40:15.591 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [420ff1f8-2fa8-4ff9-b00e-339647d280d9] Success to connect to server [localhost:8848] on start up, connectionId = 1750833615470_127.0.0.1_11836
14:40:15.592 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [420ff1f8-2fa8-4ff9-b00e-339647d280d9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:40:15.592 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [420ff1f8-2fa8-4ff9-b00e-339647d280d9] Notify connected event to listeners.
14:40:15.592 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [420ff1f8-2fa8-4ff9-b00e-339647d280d9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000025b0c4e8ad8
14:40:15.643 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
14:40:15.683 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
14:40:15.874 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 13.255 seconds (JVM running for 14.78)
14:40:15.889 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
14:40:15.895 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
14:40:15.895 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
14:40:16.193 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [420ff1f8-2fa8-4ff9-b00e-339647d280d9] Receive server push request, request = NotifySubscriberRequest, requestId = 71
14:40:16.226 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [420ff1f8-2fa8-4ff9-b00e-339647d280d9] Ack server push request, request = NotifySubscriberRequest, requestId = 71
14:53:01.156 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:53:01.160 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:53:01.502 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:53:01.503 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7b0b9d12[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:53:01.503 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750833615470_127.0.0.1_11836
14:53:01.505 [nacos-grpc-client-executor-161] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750833615470_127.0.0.1_11836]Ignore complete event,isRunning:false,isAbandon=false
14:53:01.508 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2687bf88[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 162]
14:53:59.627 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:54:00.375 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5a541335-a6f3-4fd4-abdd-c9e9e9586569_config-0
14:54:00.455 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 36 ms to scan 1 urls, producing 3 keys and 6 values 
14:54:00.485 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
14:54:00.497 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:54:00.511 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
14:54:00.525 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
14:54:00.543 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
14:54:00.549 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a541335-a6f3-4fd4-abdd-c9e9e9586569_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:54:00.550 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a541335-a6f3-4fd4-abdd-c9e9e9586569_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000224da396b40
14:54:00.551 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a541335-a6f3-4fd4-abdd-c9e9e9586569_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000224da396d60
14:54:00.552 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a541335-a6f3-4fd4-abdd-c9e9e9586569_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:54:00.553 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a541335-a6f3-4fd4-abdd-c9e9e9586569_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:54:00.568 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a541335-a6f3-4fd4-abdd-c9e9e9586569_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:54:01.744 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a541335-a6f3-4fd4-abdd-c9e9e9586569_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750834441494_127.0.0.1_14302
14:54:01.746 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a541335-a6f3-4fd4-abdd-c9e9e9586569_config-0] Notify connected event to listeners.
14:54:01.746 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a541335-a6f3-4fd4-abdd-c9e9e9586569_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:54:01.749 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a541335-a6f3-4fd4-abdd-c9e9e9586569_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000224da510fb0
14:54:01.914 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:54:04.748 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
14:54:04.749 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:54:04.749 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:54:04.962 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:54:07.515 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:54:10.246 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 517ae09b-4b80-48ae-abf7-2d9c5b6b5184
14:54:10.247 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [517ae09b-4b80-48ae-abf7-2d9c5b6b5184] RpcClient init label, labels = {module=naming, source=sdk}
14:54:10.251 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [517ae09b-4b80-48ae-abf7-2d9c5b6b5184] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:54:10.251 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [517ae09b-4b80-48ae-abf7-2d9c5b6b5184] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:54:10.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [517ae09b-4b80-48ae-abf7-2d9c5b6b5184] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:54:10.253 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [517ae09b-4b80-48ae-abf7-2d9c5b6b5184] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:54:10.390 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [517ae09b-4b80-48ae-abf7-2d9c5b6b5184] Success to connect to server [localhost:8848] on start up, connectionId = 1750834450267_127.0.0.1_14325
14:54:10.391 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [517ae09b-4b80-48ae-abf7-2d9c5b6b5184] Notify connected event to listeners.
14:54:10.391 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [517ae09b-4b80-48ae-abf7-2d9c5b6b5184] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:54:10.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [517ae09b-4b80-48ae-abf7-2d9c5b6b5184] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000224da510fb0
14:54:10.464 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
14:54:10.503 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
14:54:10.678 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 11.812 seconds (JVM running for 13.147)
14:54:10.694 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
14:54:10.694 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
14:54:10.698 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
14:54:11.007 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:54:11.011 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [517ae09b-4b80-48ae-abf7-2d9c5b6b5184] Receive server push request, request = NotifySubscriberRequest, requestId = 82
14:54:11.036 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [517ae09b-4b80-48ae-abf7-2d9c5b6b5184] Ack server push request, request = NotifySubscriberRequest, requestId = 82
14:56:16.309 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:56:16.316 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:56:16.643 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:56:16.644 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7091d696[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:56:16.644 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750834450267_127.0.0.1_14325
14:56:16.647 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750834450267_127.0.0.1_14325]Ignore complete event,isRunning:false,isAbandon=false
14:56:16.651 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6637e69[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 36]
15:04:43.695 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:04:44.433 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 991f80da-229f-4cfd-9f23-5e279472b890_config-0
15:04:44.514 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
15:04:44.546 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
15:04:44.559 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
15:04:44.572 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
15:04:44.583 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
15:04:44.600 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
15:04:44.605 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [991f80da-229f-4cfd-9f23-5e279472b890_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:04:44.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [991f80da-229f-4cfd-9f23-5e279472b890_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001a82d396b40
15:04:44.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [991f80da-229f-4cfd-9f23-5e279472b890_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001a82d396d60
15:04:44.608 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [991f80da-229f-4cfd-9f23-5e279472b890_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:04:44.609 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [991f80da-229f-4cfd-9f23-5e279472b890_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:04:44.620 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [991f80da-229f-4cfd-9f23-5e279472b890_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:04:45.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [991f80da-229f-4cfd-9f23-5e279472b890_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750835085533_127.0.0.1_1511
15:04:45.786 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [991f80da-229f-4cfd-9f23-5e279472b890_config-0] Notify connected event to listeners.
15:04:45.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [991f80da-229f-4cfd-9f23-5e279472b890_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:04:45.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [991f80da-229f-4cfd-9f23-5e279472b890_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001a82d510fd0
15:04:45.943 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:04:48.961 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
15:04:48.962 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:04:48.962 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:04:49.181 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:04:51.679 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:04:54.182 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6e982a19-0097-4c91-b3f1-1a653c4d6f49
15:04:54.182 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e982a19-0097-4c91-b3f1-1a653c4d6f49] RpcClient init label, labels = {module=naming, source=sdk}
15:04:54.186 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e982a19-0097-4c91-b3f1-1a653c4d6f49] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:04:54.186 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e982a19-0097-4c91-b3f1-1a653c4d6f49] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:04:54.187 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e982a19-0097-4c91-b3f1-1a653c4d6f49] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:04:54.188 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e982a19-0097-4c91-b3f1-1a653c4d6f49] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:04:54.319 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e982a19-0097-4c91-b3f1-1a653c4d6f49] Success to connect to server [localhost:8848] on start up, connectionId = 1750835094200_127.0.0.1_1526
15:04:54.320 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e982a19-0097-4c91-b3f1-1a653c4d6f49] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:04:54.322 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e982a19-0097-4c91-b3f1-1a653c4d6f49] Notify connected event to listeners.
15:04:54.323 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e982a19-0097-4c91-b3f1-1a653c4d6f49] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001a82d510fd0
15:04:54.416 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
15:04:54.465 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
15:04:54.632 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 11.684 seconds (JVM running for 12.972)
15:04:54.664 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
15:04:54.665 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
15:04:54.669 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
15:04:54.944 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e982a19-0097-4c91-b3f1-1a653c4d6f49] Receive server push request, request = NotifySubscriberRequest, requestId = 108
15:04:54.968 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e982a19-0097-4c91-b3f1-1a653c4d6f49] Ack server push request, request = NotifySubscriberRequest, requestId = 108
15:04:55.163 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:29:41.409 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:29:41.417 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:29:41.846 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:29:41.847 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5aa79d9c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:29:41.847 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750835094200_127.0.0.1_1526
16:29:41.859 [nacos-grpc-client-executor-1024] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750835094200_127.0.0.1_1526]Ignore complete event,isRunning:false,isAbandon=false
16:29:41.880 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@549ccda9[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 1025]
