09:14:16.460 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:14:18.095 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cc85f5c6-7d3c-47c0-a1be-2d703e93112d_config-0
09:14:18.342 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 124 ms to scan 1 urls, producing 3 keys and 6 values 
09:14:18.452 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 35 ms to scan 1 urls, producing 4 keys and 9 values 
09:14:18.480 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 22 ms to scan 1 urls, producing 3 keys and 10 values 
09:14:18.518 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 28 ms to scan 1 urls, producing 1 keys and 5 values 
09:14:18.554 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 29 ms to scan 1 urls, producing 1 keys and 7 values 
09:14:18.595 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 34 ms to scan 1 urls, producing 2 keys and 8 values 
09:14:18.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cc85f5c6-7d3c-47c0-a1be-2d703e93112d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:14:18.608 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cc85f5c6-7d3c-47c0-a1be-2d703e93112d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001a3133b31e8
09:14:18.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cc85f5c6-7d3c-47c0-a1be-2d703e93112d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001a3133b3408
09:14:18.612 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cc85f5c6-7d3c-47c0-a1be-2d703e93112d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:14:18.615 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cc85f5c6-7d3c-47c0-a1be-2d703e93112d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:14:18.641 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cc85f5c6-7d3c-47c0-a1be-2d703e93112d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:14:21.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cc85f5c6-7d3c-47c0-a1be-2d703e93112d_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755047661357_127.0.0.1_4100
09:14:21.845 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cc85f5c6-7d3c-47c0-a1be-2d703e93112d_config-0] Notify connected event to listeners.
09:14:21.846 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cc85f5c6-7d3c-47c0-a1be-2d703e93112d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:14:21.847 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cc85f5c6-7d3c-47c0-a1be-2d703e93112d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001a3134ef0d8
09:14:22.174 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:14:28.347 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:14:28.348 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:14:28.348 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:14:28.877 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:14:36.631 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:14:47.191 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 19fe95b2-7fe7-4d04-9097-a6f71cef99ca
09:14:47.191 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fe95b2-7fe7-4d04-9097-a6f71cef99ca] RpcClient init label, labels = {module=naming, source=sdk}
09:14:47.196 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fe95b2-7fe7-4d04-9097-a6f71cef99ca] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:14:47.197 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fe95b2-7fe7-4d04-9097-a6f71cef99ca] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:14:47.198 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fe95b2-7fe7-4d04-9097-a6f71cef99ca] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:14:47.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fe95b2-7fe7-4d04-9097-a6f71cef99ca] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:14:47.347 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fe95b2-7fe7-4d04-9097-a6f71cef99ca] Success to connect to server [localhost:8848] on start up, connectionId = 1755047687218_127.0.0.1_4267
09:14:47.348 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fe95b2-7fe7-4d04-9097-a6f71cef99ca] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:14:47.348 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fe95b2-7fe7-4d04-9097-a6f71cef99ca] Notify connected event to listeners.
09:14:47.348 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fe95b2-7fe7-4d04-9097-a6f71cef99ca] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001a3134ef0d8
09:14:47.445 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:14:47.518 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ***********:9300 register finished
09:14:47.924 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 32.78 seconds (JVM running for 43.117)
09:14:47.954 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
09:14:47.956 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
09:14:47.961 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fe95b2-7fe7-4d04-9097-a6f71cef99ca] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:14:47.987 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
09:14:47.991 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fe95b2-7fe7-4d04-9097-a6f71cef99ca] Ack server push request, request = NotifySubscriberRequest, requestId = 4
18:11:21.138 [http-nio-9300-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:11:22.486 [nacos-grpc-client-executor-6452] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fe95b2-7fe7-4d04-9097-a6f71cef99ca] Receive server push request, request = NotifySubscriberRequest, requestId = 106
18:11:22.486 [nacos-grpc-client-executor-6452] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fe95b2-7fe7-4d04-9097-a6f71cef99ca] Ack server push request, request = NotifySubscriberRequest, requestId = 106
18:58:49.316 [nacos-grpc-client-executor-7031] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fe95b2-7fe7-4d04-9097-a6f71cef99ca] Receive server push request, request = NotifySubscriberRequest, requestId = 109
18:58:49.339 [nacos-grpc-client-executor-7031] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fe95b2-7fe7-4d04-9097-a6f71cef99ca] Ack server push request, request = NotifySubscriberRequest, requestId = 109
18:59:33.422 [nacos-grpc-client-executor-7040] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fe95b2-7fe7-4d04-9097-a6f71cef99ca] Receive server push request, request = NotifySubscriberRequest, requestId = 114
18:59:33.440 [nacos-grpc-client-executor-7040] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19fe95b2-7fe7-4d04-9097-a6f71cef99ca] Ack server push request, request = NotifySubscriberRequest, requestId = 114
22:20:00.918 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
22:20:00.922 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
22:20:01.265 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
22:20:01.266 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@180ea1b4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
22:20:01.266 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755047687218_127.0.0.1_4267
22:20:01.268 [nacos-grpc-client-executor-9445] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755047687218_127.0.0.1_4267]Ignore complete event,isRunning:false,isAbandon=false
22:20:01.272 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6c88a7b2[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 9446]
