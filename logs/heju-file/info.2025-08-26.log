09:44:42.203 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:44:43.595 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d3137775-56e2-4f2f-becd-dbc6e3882d0b_config-0
09:44:43.750 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 75 ms to scan 1 urls, producing 3 keys and 6 values 
09:44:43.807 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 4 keys and 9 values 
09:44:43.823 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 3 keys and 10 values 
09:44:43.847 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 24 ms to scan 1 urls, producing 1 keys and 5 values 
09:44:43.871 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 20 ms to scan 1 urls, producing 1 keys and 7 values 
09:44:43.886 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
09:44:43.886 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3137775-56e2-4f2f-becd-dbc6e3882d0b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:44:43.886 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3137775-56e2-4f2f-becd-dbc6e3882d0b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001c2a43b38c8
09:44:43.886 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3137775-56e2-4f2f-becd-dbc6e3882d0b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001c2a43b3ae8
09:44:43.899 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3137775-56e2-4f2f-becd-dbc6e3882d0b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:44:43.899 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3137775-56e2-4f2f-becd-dbc6e3882d0b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:44:43.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3137775-56e2-4f2f-becd-dbc6e3882d0b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:44:46.035 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3137775-56e2-4f2f-becd-dbc6e3882d0b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756172685659_127.0.0.1_2319
09:44:46.035 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3137775-56e2-4f2f-becd-dbc6e3882d0b_config-0] Notify connected event to listeners.
09:44:46.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3137775-56e2-4f2f-becd-dbc6e3882d0b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:44:46.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3137775-56e2-4f2f-becd-dbc6e3882d0b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001c2a44efb88
09:44:46.388 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:44:50.688 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:44:50.688 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:44:50.688 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:44:50.946 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:44:53.481 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:44:57.960 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of da8fb735-181f-4706-9a9e-26c5b5597321
09:44:57.960 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da8fb735-181f-4706-9a9e-26c5b5597321] RpcClient init label, labels = {module=naming, source=sdk}
09:44:57.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da8fb735-181f-4706-9a9e-26c5b5597321] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:44:57.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da8fb735-181f-4706-9a9e-26c5b5597321] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:44:57.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da8fb735-181f-4706-9a9e-26c5b5597321] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:44:57.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da8fb735-181f-4706-9a9e-26c5b5597321] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:44:58.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da8fb735-181f-4706-9a9e-26c5b5597321] Success to connect to server [localhost:8848] on start up, connectionId = 1756172697971_127.0.0.1_2337
09:44:58.098 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da8fb735-181f-4706-9a9e-26c5b5597321] Notify connected event to listeners.
09:44:58.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da8fb735-181f-4706-9a9e-26c5b5597321] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:44:58.100 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da8fb735-181f-4706-9a9e-26c5b5597321] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001c2a44efb88
09:44:58.167 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:44:58.216 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
09:44:58.470 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 17.518 seconds (JVM running for 22.405)
09:44:58.487 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
09:44:58.493 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
09:44:58.493 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
09:44:58.675 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da8fb735-181f-4706-9a9e-26c5b5597321] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:44:58.702 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da8fb735-181f-4706-9a9e-26c5b5597321] Ack server push request, request = NotifySubscriberRequest, requestId = 5
20:31:06.432 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:31:06.436 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:31:06.774 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:31:06.774 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1e0657dc[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:31:06.776 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756172697971_127.0.0.1_2337
20:31:06.782 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@67dd4ec[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 7755]
20:31:06.787 [nacos-grpc-client-executor-7755] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756172697971_127.0.0.1_2337]Ignore complete event,isRunning:false,isAbandon=false
