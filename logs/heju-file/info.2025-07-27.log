14:21:37.361 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:21:38.687 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2ba08def-d01b-4f8c-b2b0-c525ebc0df69_config-0
14:21:38.842 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 80 ms to scan 1 urls, producing 3 keys and 6 values 
14:21:38.924 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 25 ms to scan 1 urls, producing 4 keys and 9 values 
14:21:38.940 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 3 keys and 10 values 
14:21:38.961 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 21 ms to scan 1 urls, producing 1 keys and 5 values 
14:21:38.976 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 7 values 
14:21:38.988 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
14:21:38.988 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ba08def-d01b-4f8c-b2b0-c525ebc0df69_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:21:38.988 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ba08def-d01b-4f8c-b2b0-c525ebc0df69_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x00000280453b3b48
14:21:38.988 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ba08def-d01b-4f8c-b2b0-c525ebc0df69_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000280453b3d68
14:21:39.003 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ba08def-d01b-4f8c-b2b0-c525ebc0df69_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:21:39.003 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ba08def-d01b-4f8c-b2b0-c525ebc0df69_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:21:39.020 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ba08def-d01b-4f8c-b2b0-c525ebc0df69_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:21:40.816 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ba08def-d01b-4f8c-b2b0-c525ebc0df69_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753597300514_127.0.0.1_4551
14:21:40.816 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ba08def-d01b-4f8c-b2b0-c525ebc0df69_config-0] Notify connected event to listeners.
14:21:40.816 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ba08def-d01b-4f8c-b2b0-c525ebc0df69_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:21:40.819 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ba08def-d01b-4f8c-b2b0-c525ebc0df69_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000280454f0228
14:21:41.045 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:21:44.486 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
14:21:44.486 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:21:44.486 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:21:44.705 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:21:46.415 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:21:49.077 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f5f0b1c7-df6e-4521-934b-d7c016e2c5ee
14:21:49.077 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5f0b1c7-df6e-4521-934b-d7c016e2c5ee] RpcClient init label, labels = {module=naming, source=sdk}
14:21:49.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5f0b1c7-df6e-4521-934b-d7c016e2c5ee] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:21:49.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5f0b1c7-df6e-4521-934b-d7c016e2c5ee] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:21:49.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5f0b1c7-df6e-4521-934b-d7c016e2c5ee] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:21:49.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5f0b1c7-df6e-4521-934b-d7c016e2c5ee] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:21:49.214 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5f0b1c7-df6e-4521-934b-d7c016e2c5ee] Success to connect to server [localhost:8848] on start up, connectionId = 1753597309087_127.0.0.1_4557
14:21:49.214 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5f0b1c7-df6e-4521-934b-d7c016e2c5ee] Notify connected event to listeners.
14:21:49.214 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5f0b1c7-df6e-4521-934b-d7c016e2c5ee] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:21:49.214 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5f0b1c7-df6e-4521-934b-d7c016e2c5ee] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000280454f0228
14:21:49.260 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
14:21:49.290 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
14:21:49.425 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 13.367 seconds (JVM running for 17.254)
14:21:49.448 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
14:21:49.451 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
14:21:49.455 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
14:21:49.824 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5f0b1c7-df6e-4521-934b-d7c016e2c5ee] Receive server push request, request = NotifySubscriberRequest, requestId = 4
14:21:49.849 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f5f0b1c7-df6e-4521-934b-d7c016e2c5ee] Ack server push request, request = NotifySubscriberRequest, requestId = 4
19:59:06.327 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:59:06.333 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:59:06.672 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:59:06.672 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@29501528[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:59:06.673 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753597309087_127.0.0.1_4557
19:59:06.675 [nacos-grpc-client-executor-4052] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753597309087_127.0.0.1_4557]Ignore complete event,isRunning:false,isAbandon=false
19:59:06.678 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3d67d341[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 4053]
