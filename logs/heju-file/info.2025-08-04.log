09:02:27.819 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:02:28.987 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e9b2f1d6-0dfe-4c6f-b7e0-52c70601cbe2_config-0
09:02:29.123 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 66 ms to scan 1 urls, producing 3 keys and 6 values 
09:02:29.179 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 4 keys and 9 values 
09:02:29.194 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 3 keys and 10 values 
09:02:29.210 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 16 ms to scan 1 urls, producing 1 keys and 5 values 
09:02:29.222 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 1 keys and 7 values 
09:02:29.245 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 2 keys and 8 values 
09:02:29.248 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9b2f1d6-0dfe-4c6f-b7e0-52c70601cbe2_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:02:29.248 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9b2f1d6-0dfe-4c6f-b7e0-52c70601cbe2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x00000283c43b2ad8
09:02:29.248 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9b2f1d6-0dfe-4c6f-b7e0-52c70601cbe2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000283c43b2cf8
09:02:29.248 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9b2f1d6-0dfe-4c6f-b7e0-52c70601cbe2_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:02:29.248 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9b2f1d6-0dfe-4c6f-b7e0-52c70601cbe2_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:02:29.264 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9b2f1d6-0dfe-4c6f-b7e0-52c70601cbe2_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:02:31.341 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9b2f1d6-0dfe-4c6f-b7e0-52c70601cbe2_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754269351004_127.0.0.1_8838
09:02:31.341 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9b2f1d6-0dfe-4c6f-b7e0-52c70601cbe2_config-0] Notify connected event to listeners.
09:02:31.341 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9b2f1d6-0dfe-4c6f-b7e0-52c70601cbe2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:02:31.343 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e9b2f1d6-0dfe-4c6f-b7e0-52c70601cbe2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000283c44ee9a8
09:02:31.650 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:02:36.215 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:02:36.217 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:02:36.218 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:02:36.572 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:02:39.609 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:02:45.999 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 28305368-4c9e-432c-a83b-fcaa02276d5f
09:02:46.000 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28305368-4c9e-432c-a83b-fcaa02276d5f] RpcClient init label, labels = {module=naming, source=sdk}
09:02:46.000 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28305368-4c9e-432c-a83b-fcaa02276d5f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:02:46.000 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28305368-4c9e-432c-a83b-fcaa02276d5f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:02:46.000 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28305368-4c9e-432c-a83b-fcaa02276d5f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:02:46.000 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28305368-4c9e-432c-a83b-fcaa02276d5f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:02:46.137 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28305368-4c9e-432c-a83b-fcaa02276d5f] Success to connect to server [localhost:8848] on start up, connectionId = 1754269366016_127.0.0.1_9124
09:02:46.138 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28305368-4c9e-432c-a83b-fcaa02276d5f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:02:46.138 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28305368-4c9e-432c-a83b-fcaa02276d5f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000283c44ee9a8
09:02:46.138 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28305368-4c9e-432c-a83b-fcaa02276d5f] Notify connected event to listeners.
09:02:46.205 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:02:46.255 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
09:02:46.513 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 19.749 seconds (JVM running for 30.937)
09:02:46.530 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
09:02:46.532 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
09:02:46.535 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
09:02:46.763 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28305368-4c9e-432c-a83b-fcaa02276d5f] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:02:46.774 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28305368-4c9e-432c-a83b-fcaa02276d5f] Ack server push request, request = NotifySubscriberRequest, requestId = 5
11:47:19.919 [http-nio-9300-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:47:21.688 [nacos-grpc-client-executor-1988] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28305368-4c9e-432c-a83b-fcaa02276d5f] Receive server push request, request = NotifySubscriberRequest, requestId = 36
11:47:21.689 [nacos-grpc-client-executor-1988] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28305368-4c9e-432c-a83b-fcaa02276d5f] Ack server push request, request = NotifySubscriberRequest, requestId = 36
13:35:14.319 [nacos-grpc-client-executor-3284] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28305368-4c9e-432c-a83b-fcaa02276d5f] Receive server push request, request = NotifySubscriberRequest, requestId = 40
13:35:14.337 [nacos-grpc-client-executor-3284] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28305368-4c9e-432c-a83b-fcaa02276d5f] Ack server push request, request = NotifySubscriberRequest, requestId = 40
13:35:47.531 [nacos-grpc-client-executor-3291] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28305368-4c9e-432c-a83b-fcaa02276d5f] Receive server push request, request = NotifySubscriberRequest, requestId = 45
13:35:47.543 [nacos-grpc-client-executor-3291] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [28305368-4c9e-432c-a83b-fcaa02276d5f] Ack server push request, request = NotifySubscriberRequest, requestId = 45
18:50:00.049 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:50:00.051 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:50:00.377 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:50:00.377 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@698c7bb0[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:50:00.377 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754269366016_127.0.0.1_9124
18:50:00.377 [nacos-grpc-client-executor-7184] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754269366016_127.0.0.1_9124]Ignore complete event,isRunning:false,isAbandon=false
18:50:00.380 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@f780b5f[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 7185]
