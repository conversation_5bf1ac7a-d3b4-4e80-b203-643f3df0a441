09:02:13.528 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:02:13.538 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:02:13.864 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:02:13.866 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@659a9c40[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:02:13.867 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756257850049_127.0.0.1_14713
09:02:13.934 [nacos-grpc-client-executor-16989] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756257850049_127.0.0.1_14713]Ignore complete event,isRunning:false,isAbandon=false
09:02:13.934 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3c0fe2f2[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 16990]
09:35:32.091 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:35:32.790 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1b33875d-b0b8-4dce-ba9c-88333b6333c4_config-0
09:35:32.877 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 38 ms to scan 1 urls, producing 3 keys and 6 values 
09:35:32.915 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
09:35:32.924 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
09:35:32.935 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:35:32.945 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:35:32.954 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
09:35:32.958 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b33875d-b0b8-4dce-ba9c-88333b6333c4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:35:32.958 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b33875d-b0b8-4dce-ba9c-88333b6333c4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000018fbd3af470
09:35:32.959 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b33875d-b0b8-4dce-ba9c-88333b6333c4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000018fbd3af690
09:35:32.959 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b33875d-b0b8-4dce-ba9c-88333b6333c4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:35:32.960 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b33875d-b0b8-4dce-ba9c-88333b6333c4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:35:32.974 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b33875d-b0b8-4dce-ba9c-88333b6333c4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:35:34.057 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b33875d-b0b8-4dce-ba9c-88333b6333c4_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756344933823_127.0.0.1_9620
09:35:34.058 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b33875d-b0b8-4dce-ba9c-88333b6333c4_config-0] Notify connected event to listeners.
09:35:34.058 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b33875d-b0b8-4dce-ba9c-88333b6333c4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:35:34.059 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b33875d-b0b8-4dce-ba9c-88333b6333c4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000018fbd4e8fb0
09:35:34.210 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:35:37.305 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:35:37.306 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:35:37.306 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:35:37.520 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:35:39.326 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:35:41.715 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 474e12a8-f775-4fca-80dc-122ddb5a74ba
09:35:41.716 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [474e12a8-f775-4fca-80dc-122ddb5a74ba] RpcClient init label, labels = {module=naming, source=sdk}
09:35:41.718 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [474e12a8-f775-4fca-80dc-122ddb5a74ba] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:35:41.718 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [474e12a8-f775-4fca-80dc-122ddb5a74ba] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:35:41.718 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [474e12a8-f775-4fca-80dc-122ddb5a74ba] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:35:41.719 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [474e12a8-f775-4fca-80dc-122ddb5a74ba] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:35:41.848 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [474e12a8-f775-4fca-80dc-122ddb5a74ba] Success to connect to server [localhost:8848] on start up, connectionId = 1756344941728_127.0.0.1_9668
09:35:41.848 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [474e12a8-f775-4fca-80dc-122ddb5a74ba] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:35:41.848 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [474e12a8-f775-4fca-80dc-122ddb5a74ba] Notify connected event to listeners.
09:35:41.848 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [474e12a8-f775-4fca-80dc-122ddb5a74ba] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000018fbd4e8fb0
09:35:41.895 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:35:41.924 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
09:35:42.050 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 10.665 seconds (JVM running for 12.519)
09:35:42.059 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
09:35:42.060 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
09:35:42.062 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
09:35:42.407 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [474e12a8-f775-4fca-80dc-122ddb5a74ba] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:35:42.427 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [474e12a8-f775-4fca-80dc-122ddb5a74ba] Ack server push request, request = NotifySubscriberRequest, requestId = 2
17:19:28.140 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:19:28.144 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:19:28.477 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:19:28.477 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5781a369[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:19:28.477 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756344941728_127.0.0.1_9668
17:19:28.477 [nacos-grpc-client-executor-5565] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756344941728_127.0.0.1_9668]Ignore complete event,isRunning:false,isAbandon=false
17:19:28.486 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@194c50aa[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 5566]
17:23:22.039 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:23:23.043 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1662df5e-066f-44c8-afc8-c19e11009339_config-0
17:23:23.142 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 54 ms to scan 1 urls, producing 3 keys and 6 values 
17:23:23.179 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
17:23:23.193 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
17:23:23.212 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
17:23:23.223 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
17:23:23.244 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
17:23:23.249 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1662df5e-066f-44c8-afc8-c19e11009339_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:23:23.250 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1662df5e-066f-44c8-afc8-c19e11009339_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002c8423b7220
17:23:23.250 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1662df5e-066f-44c8-afc8-c19e11009339_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002c8423b7440
17:23:23.251 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1662df5e-066f-44c8-afc8-c19e11009339_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:23:23.252 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1662df5e-066f-44c8-afc8-c19e11009339_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:23:23.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1662df5e-066f-44c8-afc8-c19e11009339_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:23:24.634 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1662df5e-066f-44c8-afc8-c19e11009339_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756373004347_127.0.0.1_9077
17:23:24.634 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1662df5e-066f-44c8-afc8-c19e11009339_config-0] Notify connected event to listeners.
17:23:24.634 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1662df5e-066f-44c8-afc8-c19e11009339_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:23:24.634 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1662df5e-066f-44c8-afc8-c19e11009339_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002c8424f0668
17:23:24.900 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:23:29.427 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
17:23:29.429 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
17:23:29.430 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
17:23:29.760 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
17:23:32.700 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:23:36.608 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of aa496e62-6ab4-4205-9cfb-74de4889dcdd
17:23:36.608 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa496e62-6ab4-4205-9cfb-74de4889dcdd] RpcClient init label, labels = {module=naming, source=sdk}
17:23:36.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa496e62-6ab4-4205-9cfb-74de4889dcdd] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:23:36.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa496e62-6ab4-4205-9cfb-74de4889dcdd] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:23:36.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa496e62-6ab4-4205-9cfb-74de4889dcdd] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:23:36.612 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa496e62-6ab4-4205-9cfb-74de4889dcdd] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:23:36.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa496e62-6ab4-4205-9cfb-74de4889dcdd] Success to connect to server [localhost:8848] on start up, connectionId = 1756373016623_127.0.0.1_9319
17:23:36.740 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa496e62-6ab4-4205-9cfb-74de4889dcdd] Notify connected event to listeners.
17:23:36.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa496e62-6ab4-4205-9cfb-74de4889dcdd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:23:36.741 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa496e62-6ab4-4205-9cfb-74de4889dcdd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002c8424f0668
17:23:36.800 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
17:23:37.290 [nacos-grpc-client-executor-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa496e62-6ab4-4205-9cfb-74de4889dcdd] Receive server push request, request = NotifySubscriberRequest, requestId = 69
17:23:37.291 [nacos-grpc-client-executor-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa496e62-6ab4-4205-9cfb-74de4889dcdd] Ack server push request, request = NotifySubscriberRequest, requestId = 69
17:23:37.490 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:23:37.490 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@60eb9c29[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:23:37.490 [main] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756373016623_127.0.0.1_9319
17:23:37.493 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756373016623_127.0.0.1_9319]Ignore complete event,isRunning:false,isAbandon=false
17:23:37.496 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@77ac7cd6[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 6]
17:23:37.501 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5a1be787-4c13-4bc3-80df-00da90c94931
17:23:37.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a1be787-4c13-4bc3-80df-00da90c94931] RpcClient init label, labels = {module=naming, source=sdk}
17:23:37.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a1be787-4c13-4bc3-80df-00da90c94931] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:23:37.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a1be787-4c13-4bc3-80df-00da90c94931] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:23:37.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a1be787-4c13-4bc3-80df-00da90c94931] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:23:37.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a1be787-4c13-4bc3-80df-00da90c94931] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
17:23:37.642 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a1be787-4c13-4bc3-80df-00da90c94931] Success to connect to server [localhost:8848] on start up, connectionId = 1756373017515_127.0.0.1_9322
17:23:37.642 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a1be787-4c13-4bc3-80df-00da90c94931] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:23:37.642 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a1be787-4c13-4bc3-80df-00da90c94931] Notify connected event to listeners.
17:23:37.642 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5a1be787-4c13-4bc3-80df-00da90c94931] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002c8424f0668
17:23:37.662 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9300"]
17:23:37.662 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
17:23:37.670 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9300"]
17:23:37.672 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9300"]
