10:40:44.526 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:40:46.838 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 59cc7c2b-20a3-4faf-85f9-1c2c4ef7ef15_config-0
10:40:47.053 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 111 ms to scan 1 urls, producing 3 keys and 6 values 
10:40:47.153 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 36 ms to scan 1 urls, producing 4 keys and 9 values 
10:40:47.188 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 27 ms to scan 1 urls, producing 3 keys and 10 values 
10:40:47.222 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 25 ms to scan 1 urls, producing 1 keys and 5 values 
10:40:47.255 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 26 ms to scan 1 urls, producing 1 keys and 7 values 
10:40:47.283 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 2 keys and 8 values 
10:40:47.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59cc7c2b-20a3-4faf-85f9-1c2c4ef7ef15_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:40:47.295 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59cc7c2b-20a3-4faf-85f9-1c2c4ef7ef15_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001b7db3b2ad8
10:40:47.295 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59cc7c2b-20a3-4faf-85f9-1c2c4ef7ef15_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001b7db3b2cf8
10:40:47.303 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59cc7c2b-20a3-4faf-85f9-1c2c4ef7ef15_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:40:47.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59cc7c2b-20a3-4faf-85f9-1c2c4ef7ef15_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:40:47.331 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59cc7c2b-20a3-4faf-85f9-1c2c4ef7ef15_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:40:50.182 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59cc7c2b-20a3-4faf-85f9-1c2c4ef7ef15_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754793649797_127.0.0.1_4596
10:40:50.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59cc7c2b-20a3-4faf-85f9-1c2c4ef7ef15_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:40:50.183 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59cc7c2b-20a3-4faf-85f9-1c2c4ef7ef15_config-0] Notify connected event to listeners.
10:40:50.183 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [59cc7c2b-20a3-4faf-85f9-1c2c4ef7ef15_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001b7db4ee9a8
10:40:50.404 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:40:56.817 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
10:40:56.818 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:40:56.818 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:40:57.360 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:41:02.361 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:41:11.086 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 76624491-f922-4cec-8271-1b380118565d
10:41:11.086 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [76624491-f922-4cec-8271-1b380118565d] RpcClient init label, labels = {module=naming, source=sdk}
10:41:11.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [76624491-f922-4cec-8271-1b380118565d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:41:11.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [76624491-f922-4cec-8271-1b380118565d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:41:11.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [76624491-f922-4cec-8271-1b380118565d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:41:11.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [76624491-f922-4cec-8271-1b380118565d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:41:11.679 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [76624491-f922-4cec-8271-1b380118565d] Success to connect to server [localhost:8848] on start up, connectionId = 1754793671420_127.0.0.1_4837
10:41:11.679 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [76624491-f922-4cec-8271-1b380118565d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:41:11.679 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [76624491-f922-4cec-8271-1b380118565d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001b7db4ee9a8
10:41:11.679 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [76624491-f922-4cec-8271-1b380118565d] Notify connected event to listeners.
10:41:12.055 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
10:41:12.752 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
10:41:12.773 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [76624491-f922-4cec-8271-1b380118565d] Receive server push request, request = NotifySubscriberRequest, requestId = 2
10:41:12.775 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [76624491-f922-4cec-8271-1b380118565d] Ack server push request, request = NotifySubscriberRequest, requestId = 2
10:41:14.581 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 31.406 seconds (JVM running for 38.648)
10:41:14.611 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
10:41:14.613 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
10:41:14.618 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
14:08:20.103 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:08:20.112 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:08:20.449 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:08:20.449 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1f485386[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:08:20.451 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754793671420_127.0.0.1_4837
14:08:20.458 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@389c3fb8[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 2490]
