09:05:32.181 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:05:32.991 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c4c24e30-243c-4295-87e6-773efc9392c1_config-0
09:05:33.084 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 42 ms to scan 1 urls, producing 3 keys and 6 values 
09:05:33.129 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 4 keys and 9 values 
09:05:33.144 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 3 keys and 10 values 
09:05:33.156 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:05:33.170 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 7 values 
09:05:33.180 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
09:05:33.182 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c24e30-243c-4295-87e6-773efc9392c1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:05:33.182 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c24e30-243c-4295-87e6-773efc9392c1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001dfaf3b3958
09:05:33.182 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c24e30-243c-4295-87e6-773efc9392c1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001dfaf3b3b78
09:05:33.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c24e30-243c-4295-87e6-773efc9392c1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:05:33.185 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c24e30-243c-4295-87e6-773efc9392c1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:05:33.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c24e30-243c-4295-87e6-773efc9392c1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:05:34.865 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c24e30-243c-4295-87e6-773efc9392c1_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754528734418_127.0.0.1_1663
09:05:34.868 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c24e30-243c-4295-87e6-773efc9392c1_config-0] Notify connected event to listeners.
09:05:34.868 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c24e30-243c-4295-87e6-773efc9392c1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:05:34.868 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c24e30-243c-4295-87e6-773efc9392c1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001dfaf4ef718
09:05:35.203 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:05:41.330 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:05:41.332 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:05:41.332 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:05:41.697 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:05:47.796 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:05:54.147 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b93c08f1-f94c-4f8b-a9f0-b8975dff25b6
09:05:54.148 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b93c08f1-f94c-4f8b-a9f0-b8975dff25b6] RpcClient init label, labels = {module=naming, source=sdk}
09:05:54.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b93c08f1-f94c-4f8b-a9f0-b8975dff25b6] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:05:54.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b93c08f1-f94c-4f8b-a9f0-b8975dff25b6] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:05:54.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b93c08f1-f94c-4f8b-a9f0-b8975dff25b6] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:05:54.154 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b93c08f1-f94c-4f8b-a9f0-b8975dff25b6] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:05:54.275 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b93c08f1-f94c-4f8b-a9f0-b8975dff25b6] Success to connect to server [localhost:8848] on start up, connectionId = 1754528754158_127.0.0.1_1720
09:05:54.275 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b93c08f1-f94c-4f8b-a9f0-b8975dff25b6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:05:54.275 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b93c08f1-f94c-4f8b-a9f0-b8975dff25b6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001dfaf4ef718
09:05:54.275 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b93c08f1-f94c-4f8b-a9f0-b8975dff25b6] Notify connected event to listeners.
09:05:54.360 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:05:54.420 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
09:05:54.611 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 23.05 seconds (JVM running for 28.459)
09:05:54.627 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
09:05:54.630 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
09:05:54.649 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
09:05:55.040 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b93c08f1-f94c-4f8b-a9f0-b8975dff25b6] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:05:55.054 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b93c08f1-f94c-4f8b-a9f0-b8975dff25b6] Ack server push request, request = NotifySubscriberRequest, requestId = 1
16:37:59.789 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:37:59.795 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:38:00.111 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:38:00.112 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6c4e89c9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:38:00.113 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754528754158_127.0.0.1_1720
16:38:00.115 [nacos-grpc-client-executor-5432] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754528754158_127.0.0.1_1720]Ignore complete event,isRunning:false,isAbandon=false
16:38:00.121 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1acc86de[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 5433]
