10:16:35.202 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:16:36.771 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ae038337-b65d-44a6-ad3b-20223de94752_config-0
10:16:36.927 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 73 ms to scan 1 urls, producing 3 keys and 6 values 
10:16:37.023 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 36 ms to scan 1 urls, producing 4 keys and 9 values 
10:16:37.041 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 3 keys and 10 values 
10:16:37.060 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 1 keys and 5 values 
10:16:37.081 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 1 keys and 7 values 
10:16:37.101 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
10:16:37.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae038337-b65d-44a6-ad3b-20223de94752_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:16:37.109 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae038337-b65d-44a6-ad3b-20223de94752_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001f29e3938c8
10:16:37.110 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae038337-b65d-44a6-ad3b-20223de94752_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001f29e393ae8
10:16:37.111 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae038337-b65d-44a6-ad3b-20223de94752_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:16:37.113 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae038337-b65d-44a6-ad3b-20223de94752_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:16:37.135 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae038337-b65d-44a6-ad3b-20223de94752_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:16:39.028 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae038337-b65d-44a6-ad3b-20223de94752_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756001798721_127.0.0.1_11188
10:16:39.029 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae038337-b65d-44a6-ad3b-20223de94752_config-0] Notify connected event to listeners.
10:16:39.029 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae038337-b65d-44a6-ad3b-20223de94752_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:16:39.031 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae038337-b65d-44a6-ad3b-20223de94752_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001f29e50f888
10:16:39.230 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:16:43.583 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
10:16:43.583 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:16:43.583 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:16:43.999 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:16:46.969 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:16:51.364 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4c4d89c1-68f6-4dc4-b13c-5f9f33e25c8b
10:16:51.365 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c4d89c1-68f6-4dc4-b13c-5f9f33e25c8b] RpcClient init label, labels = {module=naming, source=sdk}
10:16:51.367 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c4d89c1-68f6-4dc4-b13c-5f9f33e25c8b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:16:51.367 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c4d89c1-68f6-4dc4-b13c-5f9f33e25c8b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:16:51.367 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c4d89c1-68f6-4dc4-b13c-5f9f33e25c8b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:16:51.367 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c4d89c1-68f6-4dc4-b13c-5f9f33e25c8b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:16:51.498 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c4d89c1-68f6-4dc4-b13c-5f9f33e25c8b] Success to connect to server [localhost:8848] on start up, connectionId = 1756001811377_127.0.0.1_11226
10:16:51.499 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c4d89c1-68f6-4dc4-b13c-5f9f33e25c8b] Notify connected event to listeners.
10:16:51.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c4d89c1-68f6-4dc4-b13c-5f9f33e25c8b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:16:51.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c4d89c1-68f6-4dc4-b13c-5f9f33e25c8b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001f29e50f888
10:16:51.557 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
10:16:51.592 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
10:16:51.764 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 17.808 seconds (JVM running for 26.106)
10:16:51.777 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
10:16:51.777 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
10:16:51.780 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
10:16:52.057 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c4d89c1-68f6-4dc4-b13c-5f9f33e25c8b] Receive server push request, request = NotifySubscriberRequest, requestId = 4
10:16:52.070 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4c4d89c1-68f6-4dc4-b13c-5f9f33e25c8b] Ack server push request, request = NotifySubscriberRequest, requestId = 4
18:54:36.285 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:54:36.289 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:54:36.625 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:54:36.625 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2908d7a4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:54:36.625 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756001811377_127.0.0.1_11226
18:54:36.627 [nacos-grpc-client-executor-6212] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756001811377_127.0.0.1_11226]Ignore complete event,isRunning:false,isAbandon=false
18:54:36.630 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@22847e05[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 6213]
