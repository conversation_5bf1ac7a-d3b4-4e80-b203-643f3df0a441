09:17:50.161 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:17:51.294 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 32274d02-7cf9-416a-af64-03da15ef6440_config-0
09:17:51.435 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 66 ms to scan 1 urls, producing 3 keys and 6 values 
09:17:51.502 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 28 ms to scan 1 urls, producing 4 keys and 9 values 
09:17:51.522 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 3 keys and 10 values 
09:17:51.542 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 1 keys and 5 values 
09:17:51.559 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 1 keys and 7 values 
09:17:51.578 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
09:17:51.583 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32274d02-7cf9-416a-af64-03da15ef6440_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:17:51.584 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32274d02-7cf9-416a-af64-03da15ef6440_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001705c3aed88
09:17:51.585 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32274d02-7cf9-416a-af64-03da15ef6440_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001705c3aefa8
09:17:51.586 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32274d02-7cf9-416a-af64-03da15ef6440_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:17:51.588 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32274d02-7cf9-416a-af64-03da15ef6440_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:17:51.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32274d02-7cf9-416a-af64-03da15ef6440_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:17:53.413 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32274d02-7cf9-416a-af64-03da15ef6440_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755134273114_127.0.0.1_14659
09:17:53.414 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32274d02-7cf9-416a-af64-03da15ef6440_config-0] Notify connected event to listeners.
09:17:53.414 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32274d02-7cf9-416a-af64-03da15ef6440_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:17:53.414 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [32274d02-7cf9-416a-af64-03da15ef6440_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001705c4e8ad8
09:17:53.593 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:17:57.646 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:17:57.647 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:17:57.647 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:17:57.870 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:18:00.014 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:18:03.603 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 322fcfd5-fc7c-4082-8db5-8053b076d19d
09:18:03.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [322fcfd5-fc7c-4082-8db5-8053b076d19d] RpcClient init label, labels = {module=naming, source=sdk}
09:18:03.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [322fcfd5-fc7c-4082-8db5-8053b076d19d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:18:03.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [322fcfd5-fc7c-4082-8db5-8053b076d19d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:18:03.607 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [322fcfd5-fc7c-4082-8db5-8053b076d19d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:18:03.607 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [322fcfd5-fc7c-4082-8db5-8053b076d19d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:18:03.731 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [322fcfd5-fc7c-4082-8db5-8053b076d19d] Success to connect to server [localhost:8848] on start up, connectionId = 1755134283617_127.0.0.1_14704
09:18:03.732 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [322fcfd5-fc7c-4082-8db5-8053b076d19d] Notify connected event to listeners.
09:18:03.732 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [322fcfd5-fc7c-4082-8db5-8053b076d19d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:03.732 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [322fcfd5-fc7c-4082-8db5-8053b076d19d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001705c4e8ad8
09:18:03.791 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:18:03.826 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ***********:9300 register finished
09:18:03.983 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 14.983 seconds (JVM running for 17.809)
09:18:03.998 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
09:18:03.999 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
09:18:04.001 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
09:18:04.287 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [322fcfd5-fc7c-4082-8db5-8053b076d19d] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:18:04.305 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [322fcfd5-fc7c-4082-8db5-8053b076d19d] Ack server push request, request = NotifySubscriberRequest, requestId = 7
20:30:13.019 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:30:13.023 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:30:13.149 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:30:13.149 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@734df0a0[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:30:13.149 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755134283617_127.0.0.1_14704
20:30:13.149 [nacos-grpc-client-executor-8070] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755134283617_127.0.0.1_14704]Ignore complete event,isRunning:false,isAbandon=false
20:30:13.162 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@33f9dd0e[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 8071]
