09:01:07.417 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:01:07.421 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:01:07.795 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:01:07.796 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@108cccc9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:01:07.796 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755653294136_127.0.0.1_8681
09:01:07.806 [nacos-grpc-client-executor-10136] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755653294136_127.0.0.1_8681]Ignore complete event,isRunning:false,isAbandon=false
09:01:07.826 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5af1b752[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 10137]
09:32:00.479 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:32:01.066 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ac08c404-ee20-4036-a744-a73dde7d6730_config-0
09:32:01.128 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 33 ms to scan 1 urls, producing 3 keys and 6 values 
09:32:01.146 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
09:32:01.153 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
09:32:01.163 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:32:01.166 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 1 ms to scan 1 urls, producing 1 keys and 7 values 
09:32:01.181 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
09:32:01.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac08c404-ee20-4036-a744-a73dde7d6730_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:32:01.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac08c404-ee20-4036-a744-a73dde7d6730_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001c7c33b2cd0
09:32:01.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac08c404-ee20-4036-a744-a73dde7d6730_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001c7c33b2ef0
09:32:01.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac08c404-ee20-4036-a744-a73dde7d6730_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:32:01.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac08c404-ee20-4036-a744-a73dde7d6730_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:32:01.199 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac08c404-ee20-4036-a744-a73dde7d6730_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:32:02.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac08c404-ee20-4036-a744-a73dde7d6730_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755739921889_127.0.0.1_13223
09:32:02.093 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac08c404-ee20-4036-a744-a73dde7d6730_config-0] Notify connected event to listeners.
09:32:02.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac08c404-ee20-4036-a744-a73dde7d6730_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:32:02.095 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac08c404-ee20-4036-a744-a73dde7d6730_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001c7c34ee9a8
09:32:02.232 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:32:04.478 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:32:04.478 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:32:04.478 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:32:04.646 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:32:06.157 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:32:08.633 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9f2c8218-1b91-424a-b408-a7c09b79c9bd
09:32:08.642 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] RpcClient init label, labels = {module=naming, source=sdk}
09:32:08.642 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:32:08.642 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:32:08.642 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:32:08.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:32:08.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] Success to connect to server [localhost:8848] on start up, connectionId = 1755739928649_127.0.0.1_13233
09:32:08.769 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] Notify connected event to listeners.
09:32:08.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:32:08.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001c7c34ee9a8
09:32:08.808 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:32:08.842 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
09:32:08.992 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 9.094 seconds (JVM running for 13.616)
09:32:09.000 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
09:32:09.000 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
09:32:09.000 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
09:32:09.317 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] Receive server push request, request = NotifySubscriberRequest, requestId = 94
09:32:09.328 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] Ack server push request, request = NotifySubscriberRequest, requestId = 94
11:41:43.287 [http-nio-9300-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:41:44.656 [nacos-grpc-client-executor-1567] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] Receive server push request, request = NotifySubscriberRequest, requestId = 112
11:41:44.657 [nacos-grpc-client-executor-1567] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] Ack server push request, request = NotifySubscriberRequest, requestId = 112
11:48:43.630 [nacos-grpc-client-executor-1652] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] Receive server push request, request = NotifySubscriberRequest, requestId = 113
11:48:43.651 [nacos-grpc-client-executor-1652] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] Ack server push request, request = NotifySubscriberRequest, requestId = 113
11:49:09.053 [nacos-grpc-client-executor-1658] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] Receive server push request, request = NotifySubscriberRequest, requestId = 116
11:49:09.070 [nacos-grpc-client-executor-1658] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] Ack server push request, request = NotifySubscriberRequest, requestId = 116
12:15:09.591 [nacos-grpc-client-executor-1970] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] Receive server push request, request = NotifySubscriberRequest, requestId = 120
12:15:09.591 [nacos-grpc-client-executor-1970] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] Ack server push request, request = NotifySubscriberRequest, requestId = 120
12:32:09.269 [nacos-grpc-client-executor-2182] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] Receive server push request, request = NotifySubscriberRequest, requestId = 124
12:32:09.282 [nacos-grpc-client-executor-2182] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] Ack server push request, request = NotifySubscriberRequest, requestId = 124
12:36:21.637 [nacos-grpc-client-executor-2233] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] Receive server push request, request = NotifySubscriberRequest, requestId = 127
12:36:21.645 [nacos-grpc-client-executor-2233] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] Ack server push request, request = NotifySubscriberRequest, requestId = 127
12:36:26.073 [nacos-grpc-client-executor-2234] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] Receive server push request, request = NotifySubscriberRequest, requestId = 131
12:36:26.089 [nacos-grpc-client-executor-2234] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] Ack server push request, request = NotifySubscriberRequest, requestId = 131
12:36:42.687 [nacos-grpc-client-executor-2237] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] Receive server push request, request = NotifySubscriberRequest, requestId = 134
12:36:42.708 [nacos-grpc-client-executor-2237] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f2c8218-1b91-424a-b408-a7c09b79c9bd] Ack server push request, request = NotifySubscriberRequest, requestId = 134
14:07:45.791 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:07:45.791 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:07:46.126 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:07:46.126 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1e9ccc44[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:07:46.126 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755739928649_127.0.0.1_13233
14:07:46.126 [nacos-grpc-client-executor-3331] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755739928649_127.0.0.1_13233]Ignore complete event,isRunning:false,isAbandon=false
14:07:46.133 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1d3bb35f[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3332]
14:10:20.448 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:10:21.930 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of cb1e3439-418f-484f-b558-76fcdbec24ab_config-0
14:10:22.110 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 92 ms to scan 1 urls, producing 3 keys and 6 values 
14:10:22.209 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 46 ms to scan 1 urls, producing 4 keys and 9 values 
14:10:22.237 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 10 values 
14:10:22.267 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 1 keys and 5 values 
14:10:22.290 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 1 keys and 7 values 
14:10:22.322 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 2 keys and 8 values 
14:10:22.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb1e3439-418f-484f-b558-76fcdbec24ab_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:10:22.331 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb1e3439-418f-484f-b558-76fcdbec24ab_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001d811397220
14:10:22.332 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb1e3439-418f-484f-b558-76fcdbec24ab_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001d811397440
14:10:22.333 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb1e3439-418f-484f-b558-76fcdbec24ab_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:10:22.335 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb1e3439-418f-484f-b558-76fcdbec24ab_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:10:22.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb1e3439-418f-484f-b558-76fcdbec24ab_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:25.313 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb1e3439-418f-484f-b558-76fcdbec24ab_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755756624928_127.0.0.1_6211
14:10:25.315 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb1e3439-418f-484f-b558-76fcdbec24ab_config-0] Notify connected event to listeners.
14:10:25.316 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb1e3439-418f-484f-b558-76fcdbec24ab_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:10:25.316 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [cb1e3439-418f-484f-b558-76fcdbec24ab_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001d811510fb0
14:10:25.584 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:10:31.315 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
14:10:31.316 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:10:31.317 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:10:31.664 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:10:34.793 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:10:39.231 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f0085b17-647c-48d4-83fe-e89900eac176
14:10:39.232 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0085b17-647c-48d4-83fe-e89900eac176] RpcClient init label, labels = {module=naming, source=sdk}
14:10:39.235 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0085b17-647c-48d4-83fe-e89900eac176] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:10:39.236 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0085b17-647c-48d4-83fe-e89900eac176] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:10:39.237 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0085b17-647c-48d4-83fe-e89900eac176] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:10:39.238 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0085b17-647c-48d4-83fe-e89900eac176] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:39.375 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0085b17-647c-48d4-83fe-e89900eac176] Success to connect to server [localhost:8848] on start up, connectionId = 1755756639251_127.0.0.1_6239
14:10:39.381 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0085b17-647c-48d4-83fe-e89900eac176] Notify connected event to listeners.
14:10:39.381 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0085b17-647c-48d4-83fe-e89900eac176] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:10:39.384 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0085b17-647c-48d4-83fe-e89900eac176] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001d811510fb0
14:10:39.556 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
14:10:39.721 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
14:10:40.063 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0085b17-647c-48d4-83fe-e89900eac176] Receive server push request, request = NotifySubscriberRequest, requestId = 138
14:10:40.191 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0085b17-647c-48d4-83fe-e89900eac176] Ack server push request, request = NotifySubscriberRequest, requestId = 138
14:10:40.273 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 20.987 seconds (JVM running for 22.305)
14:10:40.310 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
14:10:40.311 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
14:10:40.316 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
14:10:40.810 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:57:39.220 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:57:39.223 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:57:39.556 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:57:39.556 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@af45579[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:57:39.556 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755756639251_127.0.0.1_6239
19:57:39.558 [nacos-grpc-client-executor-4169] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755756639251_127.0.0.1_6239]Ignore complete event,isRunning:false,isAbandon=false
19:57:39.558 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@8e90d1[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 4170]
19:58:38.667 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:58:39.271 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 42554055-114b-4edc-8f03-7cfd21e2cbf4_config-0
19:58:39.370 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 33 ms to scan 1 urls, producing 3 keys and 6 values 
19:58:39.403 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
19:58:39.412 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
19:58:39.426 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
19:58:39.437 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
19:58:39.446 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
19:58:39.449 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42554055-114b-4edc-8f03-7cfd21e2cbf4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:58:39.451 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42554055-114b-4edc-8f03-7cfd21e2cbf4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000021f3b3aed88
19:58:39.451 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42554055-114b-4edc-8f03-7cfd21e2cbf4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000021f3b3aefa8
19:58:39.452 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42554055-114b-4edc-8f03-7cfd21e2cbf4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:58:39.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42554055-114b-4edc-8f03-7cfd21e2cbf4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:58:39.468 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42554055-114b-4edc-8f03-7cfd21e2cbf4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:58:40.870 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42554055-114b-4edc-8f03-7cfd21e2cbf4_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755777520570_127.0.0.1_14281
19:58:40.870 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42554055-114b-4edc-8f03-7cfd21e2cbf4_config-0] Notify connected event to listeners.
19:58:40.871 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42554055-114b-4edc-8f03-7cfd21e2cbf4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:58:40.872 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42554055-114b-4edc-8f03-7cfd21e2cbf4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000021f3b4e8668
19:58:41.091 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:58:48.184 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
19:58:48.185 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:58:48.186 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:58:48.662 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:58:52.039 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:58:57.961 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a82320f9-f637-4186-8e91-765bd01c4953
19:58:57.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a82320f9-f637-4186-8e91-765bd01c4953] RpcClient init label, labels = {module=naming, source=sdk}
19:58:57.966 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a82320f9-f637-4186-8e91-765bd01c4953] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:58:57.967 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a82320f9-f637-4186-8e91-765bd01c4953] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:58:57.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a82320f9-f637-4186-8e91-765bd01c4953] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:58:57.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a82320f9-f637-4186-8e91-765bd01c4953] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:58:58.111 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a82320f9-f637-4186-8e91-765bd01c4953] Success to connect to server [localhost:8848] on start up, connectionId = 1755777537983_127.0.0.1_9230
19:58:58.111 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a82320f9-f637-4186-8e91-765bd01c4953] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:58:58.111 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a82320f9-f637-4186-8e91-765bd01c4953] Notify connected event to listeners.
19:58:58.111 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a82320f9-f637-4186-8e91-765bd01c4953] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000021f3b4e8668
19:58:58.187 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
19:58:58.224 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
19:58:58.538 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 20.508 seconds (JVM running for 22.251)
19:58:58.559 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
19:58:58.559 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
19:58:58.572 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
19:58:58.694 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a82320f9-f637-4186-8e91-765bd01c4953] Receive server push request, request = NotifySubscriberRequest, requestId = 180
19:58:58.714 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a82320f9-f637-4186-8e91-765bd01c4953] Ack server push request, request = NotifySubscriberRequest, requestId = 180
