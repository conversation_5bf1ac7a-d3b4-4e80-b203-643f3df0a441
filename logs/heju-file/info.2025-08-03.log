10:21:58.567 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:21:59.975 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of dd7b804b-7dbc-4745-95a8-aff84ea89cdd_config-0
10:22:00.115 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 69 ms to scan 1 urls, producing 3 keys and 6 values 
10:22:00.182 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 25 ms to scan 1 urls, producing 4 keys and 9 values 
10:22:00.203 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 3 keys and 10 values 
10:22:00.221 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 1 keys and 5 values 
10:22:00.241 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 20 ms to scan 1 urls, producing 1 keys and 7 values 
10:22:00.271 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 2 keys and 8 values 
10:22:00.279 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd7b804b-7dbc-4745-95a8-aff84ea89cdd_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:22:00.279 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd7b804b-7dbc-4745-95a8-aff84ea89cdd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001de013b3650
10:22:00.279 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd7b804b-7dbc-4745-95a8-aff84ea89cdd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001de013b3870
10:22:00.284 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd7b804b-7dbc-4745-95a8-aff84ea89cdd_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:22:00.284 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd7b804b-7dbc-4745-95a8-aff84ea89cdd_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:22:00.304 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd7b804b-7dbc-4745-95a8-aff84ea89cdd_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:22:02.439 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd7b804b-7dbc-4745-95a8-aff84ea89cdd_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754187722046_127.0.0.1_1043
10:22:02.439 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd7b804b-7dbc-4745-95a8-aff84ea89cdd_config-0] Notify connected event to listeners.
10:22:02.439 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd7b804b-7dbc-4745-95a8-aff84ea89cdd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:22:02.442 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [dd7b804b-7dbc-4745-95a8-aff84ea89cdd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001de014ef0c0
10:22:02.763 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:22:06.867 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
10:22:06.869 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:22:06.869 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:22:07.129 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:22:09.975 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:22:16.088 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ae7a061e-5b98-4514-9897-6d54fbdc2e41
10:22:16.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae7a061e-5b98-4514-9897-6d54fbdc2e41] RpcClient init label, labels = {module=naming, source=sdk}
10:22:16.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae7a061e-5b98-4514-9897-6d54fbdc2e41] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:22:16.093 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae7a061e-5b98-4514-9897-6d54fbdc2e41] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:22:16.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae7a061e-5b98-4514-9897-6d54fbdc2e41] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:22:16.095 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae7a061e-5b98-4514-9897-6d54fbdc2e41] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:22:16.248 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae7a061e-5b98-4514-9897-6d54fbdc2e41] Success to connect to server [localhost:8848] on start up, connectionId = 1754187736111_127.0.0.1_1267
10:22:16.248 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae7a061e-5b98-4514-9897-6d54fbdc2e41] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:22:16.249 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae7a061e-5b98-4514-9897-6d54fbdc2e41] Notify connected event to listeners.
10:22:16.249 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae7a061e-5b98-4514-9897-6d54fbdc2e41] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001de014ef0c0
10:22:16.352 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
10:22:16.410 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
10:22:16.720 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 19.458 seconds (JVM running for 29.748)
10:22:16.744 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
10:22:16.746 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
10:22:16.750 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
10:22:16.894 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae7a061e-5b98-4514-9897-6d54fbdc2e41] Receive server push request, request = NotifySubscriberRequest, requestId = 5
10:22:16.924 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ae7a061e-5b98-4514-9897-6d54fbdc2e41] Ack server push request, request = NotifySubscriberRequest, requestId = 5
18:45:57.598 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:45:57.608 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:45:57.922 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:45:57.927 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@564a57e4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:45:57.927 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754187736111_127.0.0.1_1267
18:45:57.929 [nacos-grpc-client-executor-6050] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754187736111_127.0.0.1_1267]Ignore complete event,isRunning:false,isAbandon=false
18:45:57.931 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@477669fd[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 6051]
