09:39:23.779 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:39:24.444 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 91de9f29-ab7c-4886-ab9c-beef60c48822_config-0
09:39:24.522 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 36 ms to scan 1 urls, producing 3 keys and 6 values 
09:39:24.558 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 4 keys and 9 values 
09:39:24.569 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 3 keys and 10 values 
09:39:24.579 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:39:24.590 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:39:24.598 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
09:39:24.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91de9f29-ab7c-4886-ab9c-beef60c48822_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:39:24.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91de9f29-ab7c-4886-ab9c-beef60c48822_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001873e3b0d60
09:39:24.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91de9f29-ab7c-4886-ab9c-beef60c48822_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001873e3b0f80
09:39:24.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91de9f29-ab7c-4886-ab9c-beef60c48822_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:39:24.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91de9f29-ab7c-4886-ab9c-beef60c48822_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:39:24.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91de9f29-ab7c-4886-ab9c-beef60c48822_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:39:25.549 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91de9f29-ab7c-4886-ab9c-beef60c48822_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753234765338_127.0.0.1_3974
09:39:25.551 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91de9f29-ab7c-4886-ab9c-beef60c48822_config-0] Notify connected event to listeners.
09:39:25.552 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91de9f29-ab7c-4886-ab9c-beef60c48822_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:39:25.553 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [91de9f29-ab7c-4886-ab9c-beef60c48822_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001873e4e8fb0
09:39:25.689 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:39:28.621 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:39:28.622 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:39:28.622 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:39:28.800 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:39:30.454 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:39:32.780 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 47faaa12-e3de-432f-a640-df0cc895d139
09:39:32.780 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47faaa12-e3de-432f-a640-df0cc895d139] RpcClient init label, labels = {module=naming, source=sdk}
09:39:32.782 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47faaa12-e3de-432f-a640-df0cc895d139] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:39:32.782 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47faaa12-e3de-432f-a640-df0cc895d139] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:39:32.783 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47faaa12-e3de-432f-a640-df0cc895d139] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:39:32.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47faaa12-e3de-432f-a640-df0cc895d139] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:39:32.916 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47faaa12-e3de-432f-a640-df0cc895d139] Success to connect to server [localhost:8848] on start up, connectionId = 1753234772793_127.0.0.1_4051
09:39:32.917 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47faaa12-e3de-432f-a640-df0cc895d139] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:39:32.917 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47faaa12-e3de-432f-a640-df0cc895d139] Notify connected event to listeners.
09:39:32.917 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47faaa12-e3de-432f-a640-df0cc895d139] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001873e4e8fb0
09:39:32.959 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:39:32.997 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
09:39:33.170 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 10.036 seconds (JVM running for 12.11)
09:39:33.186 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
09:39:33.187 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
09:39:33.189 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
09:39:33.529 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47faaa12-e3de-432f-a640-df0cc895d139] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:39:33.548 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [47faaa12-e3de-432f-a640-df0cc895d139] Ack server push request, request = NotifySubscriberRequest, requestId = 2
20:22:57.301 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:22:57.305 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:22:57.632 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:22:57.632 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7536a22[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:22:57.633 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753234772793_127.0.0.1_4051
20:22:57.636 [nacos-grpc-client-executor-7724] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753234772793_127.0.0.1_4051]Ignore complete event,isRunning:false,isAbandon=false
20:22:57.640 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@53ed3e43[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 7725]
