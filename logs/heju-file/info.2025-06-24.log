11:59:03.860 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:59:04.657 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 41351bd2-db87-4ae1-ae57-fdb159883035_config-0
11:59:04.756 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 48 ms to scan 1 urls, producing 3 keys and 6 values 
11:59:04.797 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 4 keys and 9 values 
11:59:04.809 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 3 keys and 10 values 
11:59:04.821 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
11:59:04.834 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 7 values 
11:59:04.854 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
11:59:04.860 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41351bd2-db87-4ae1-ae57-fdb159883035_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:59:04.861 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41351bd2-db87-4ae1-ae57-fdb159883035_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000020c09396b40
11:59:04.861 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41351bd2-db87-4ae1-ae57-fdb159883035_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000020c09396d60
11:59:04.862 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41351bd2-db87-4ae1-ae57-fdb159883035_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:59:04.863 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41351bd2-db87-4ae1-ae57-fdb159883035_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:59:04.876 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41351bd2-db87-4ae1-ae57-fdb159883035_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:59:06.075 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41351bd2-db87-4ae1-ae57-fdb159883035_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750737545816_127.0.0.1_6628
11:59:06.076 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41351bd2-db87-4ae1-ae57-fdb159883035_config-0] Notify connected event to listeners.
11:59:06.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41351bd2-db87-4ae1-ae57-fdb159883035_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:59:06.077 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41351bd2-db87-4ae1-ae57-fdb159883035_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000020c09510ad8
11:59:06.250 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:59:09.425 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
11:59:09.426 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:59:09.426 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:59:09.613 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:59:10.473 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
12:04:35.095 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:04:35.901 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b0d3127b-8c62-4824-9715-3f1f93f0df0a_config-0
12:04:35.987 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 51 ms to scan 1 urls, producing 3 keys and 6 values 
12:04:36.018 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
12:04:36.030 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
12:04:36.043 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
12:04:36.056 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
12:04:36.075 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
12:04:36.080 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d3127b-8c62-4824-9715-3f1f93f0df0a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:04:36.081 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d3127b-8c62-4824-9715-3f1f93f0df0a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002d5013c6b40
12:04:36.081 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d3127b-8c62-4824-9715-3f1f93f0df0a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002d5013c6d60
12:04:36.082 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d3127b-8c62-4824-9715-3f1f93f0df0a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:04:36.083 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d3127b-8c62-4824-9715-3f1f93f0df0a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:04:36.095 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d3127b-8c62-4824-9715-3f1f93f0df0a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:04:37.382 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d3127b-8c62-4824-9715-3f1f93f0df0a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750737877117_127.0.0.1_7191
12:04:37.383 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d3127b-8c62-4824-9715-3f1f93f0df0a_config-0] Notify connected event to listeners.
12:04:37.384 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d3127b-8c62-4824-9715-3f1f93f0df0a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:04:37.386 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b0d3127b-8c62-4824-9715-3f1f93f0df0a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002d501500fd0
12:04:37.537 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:04:40.609 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
12:04:40.611 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:04:40.611 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:04:40.807 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:04:41.503 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
12:11:25.194 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:11:26.012 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8bcf5760-9c02-4bcc-927d-164f28e69318_config-0
12:11:26.112 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 52 ms to scan 1 urls, producing 3 keys and 6 values 
12:11:26.145 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
12:11:26.158 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
12:11:26.171 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
12:11:26.183 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
12:11:26.200 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
12:11:26.205 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bcf5760-9c02-4bcc-927d-164f28e69318_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:11:26.207 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bcf5760-9c02-4bcc-927d-164f28e69318_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000018b65396d88
12:11:26.207 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bcf5760-9c02-4bcc-927d-164f28e69318_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000018b65396fa8
12:11:26.208 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bcf5760-9c02-4bcc-927d-164f28e69318_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:11:26.209 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bcf5760-9c02-4bcc-927d-164f28e69318_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:11:26.225 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bcf5760-9c02-4bcc-927d-164f28e69318_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:11:27.469 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bcf5760-9c02-4bcc-927d-164f28e69318_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750738287229_127.0.0.1_8022
12:11:27.470 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bcf5760-9c02-4bcc-927d-164f28e69318_config-0] Notify connected event to listeners.
12:11:27.471 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bcf5760-9c02-4bcc-927d-164f28e69318_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:11:27.472 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8bcf5760-9c02-4bcc-927d-164f28e69318_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000018b65510ad8
12:11:27.617 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:11:30.647 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
12:11:30.648 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:11:30.648 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:11:30.841 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:11:31.495 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
12:14:54.825 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:14:55.624 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 34d9cd9f-8833-41df-8f3a-4af4167105ac_config-0
12:14:55.710 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
12:14:55.743 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
12:14:55.751 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
12:14:55.767 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
12:14:55.776 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 3 ms to scan 1 urls, producing 1 keys and 7 values 
12:14:55.791 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
12:14:55.799 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34d9cd9f-8833-41df-8f3a-4af4167105ac_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:14:55.799 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34d9cd9f-8833-41df-8f3a-4af4167105ac_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000027627396230
12:14:55.799 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34d9cd9f-8833-41df-8f3a-4af4167105ac_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000027627396450
12:14:55.799 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34d9cd9f-8833-41df-8f3a-4af4167105ac_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:14:55.804 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34d9cd9f-8833-41df-8f3a-4af4167105ac_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:14:55.812 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34d9cd9f-8833-41df-8f3a-4af4167105ac_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:14:57.005 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34d9cd9f-8833-41df-8f3a-4af4167105ac_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750738496757_127.0.0.1_8390
12:14:57.006 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34d9cd9f-8833-41df-8f3a-4af4167105ac_config-0] Notify connected event to listeners.
12:14:57.006 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34d9cd9f-8833-41df-8f3a-4af4167105ac_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:14:57.008 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34d9cd9f-8833-41df-8f3a-4af4167105ac_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000027627510228
12:14:57.139 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:14:59.943 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
12:14:59.944 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:14:59.944 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:15:00.136 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:15:00.871 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
13:17:35.467 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:17:36.227 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 42de053c-5624-46f7-87ff-4927114f3b03_config-0
13:17:36.311 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 3 keys and 6 values 
13:17:36.343 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
13:17:36.355 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
13:17:36.369 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
13:17:36.383 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
13:17:36.403 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
13:17:36.408 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42de053c-5624-46f7-87ff-4927114f3b03_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:17:36.408 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42de053c-5624-46f7-87ff-4927114f3b03_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001ed81396b40
13:17:36.409 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42de053c-5624-46f7-87ff-4927114f3b03_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001ed81396d60
13:17:36.410 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42de053c-5624-46f7-87ff-4927114f3b03_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:17:36.411 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42de053c-5624-46f7-87ff-4927114f3b03_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:17:36.427 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42de053c-5624-46f7-87ff-4927114f3b03_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:17:37.683 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42de053c-5624-46f7-87ff-4927114f3b03_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750742257435_127.0.0.1_14286
13:17:37.686 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42de053c-5624-46f7-87ff-4927114f3b03_config-0] Notify connected event to listeners.
13:17:37.687 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42de053c-5624-46f7-87ff-4927114f3b03_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:17:37.688 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42de053c-5624-46f7-87ff-4927114f3b03_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001ed81510668
13:17:37.860 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:17:40.942 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
13:17:40.943 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:17:40.943 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:17:41.143 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:17:43.857 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:17:46.587 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 39f708da-8514-4569-896c-e55f5cf73f9d
13:17:46.588 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39f708da-8514-4569-896c-e55f5cf73f9d] RpcClient init label, labels = {module=naming, source=sdk}
13:17:46.590 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39f708da-8514-4569-896c-e55f5cf73f9d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:17:46.590 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39f708da-8514-4569-896c-e55f5cf73f9d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:17:46.591 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39f708da-8514-4569-896c-e55f5cf73f9d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:17:46.592 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39f708da-8514-4569-896c-e55f5cf73f9d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:17:46.726 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39f708da-8514-4569-896c-e55f5cf73f9d] Success to connect to server [localhost:8848] on start up, connectionId = 1750742266607_127.0.0.1_14304
13:17:46.727 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39f708da-8514-4569-896c-e55f5cf73f9d] Notify connected event to listeners.
13:17:46.728 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39f708da-8514-4569-896c-e55f5cf73f9d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:17:46.729 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39f708da-8514-4569-896c-e55f5cf73f9d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001ed81510668
13:17:46.790 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
13:17:46.840 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
13:17:47.021 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 12.427 seconds (JVM running for 13.803)
13:17:47.043 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
13:17:47.044 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
13:17:47.076 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
13:17:47.318 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39f708da-8514-4569-896c-e55f5cf73f9d] Receive server push request, request = NotifySubscriberRequest, requestId = 2
13:17:47.340 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39f708da-8514-4569-896c-e55f5cf73f9d] Ack server push request, request = NotifySubscriberRequest, requestId = 2
13:17:47.640 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:04:33.345 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750745072190_mddy7wtfr
14:31:48.962 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750746708932_xd4b3t0y5
14:39:12.447 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750747152392_3x1keipuh
14:58:29.576 [http-nio-9300-exec-10] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750747259170_bhb74m7o7
14:58:29.596 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750747338639_6c0y6yq1y
14:58:29.598 [http-nio-9300-exec-11] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750747633235_6r63c06g2
14:58:29.638 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750747525871_gkeaqyq49
14:58:30.611 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750747606572_k89i3tv0x
14:58:31.047 [http-nio-9300-exec-16] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750748181386_60nny2hpa
14:58:31.681 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750748269946_h3qiwt7sj
14:58:31.701 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39f708da-8514-4569-896c-e55f5cf73f9d] Server check success, currentServer is localhost:8848 
14:59:13.364 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750747152392_3x1keipuh
14:59:33.506 [http-nio-9300-exec-17] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [deleteChunkUpload,165] - 删除分片上传记录成功，fileId: 1750748181386_60nny2hpa
15:02:13.781 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [deleteChunkUpload,165] - 删除分片上传记录成功，fileId: 1750748269946_h3qiwt7sj
15:02:21.337 [http-nio-9300-exec-14] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750748541316_9ml5mv3vi
15:08:45.952 [http-nio-9300-exec-17] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750748828117_es5dkkaqf
15:08:45.956 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [42de053c-5624-46f7-87ff-4927114f3b03_config-0] Server check success, currentServer is localhost:8848 
15:42:42.356 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750749418427_k0x535y12
15:42:42.362 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750750799273_ribqnzyi0
15:42:42.365 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750749472396_5sbgcpxg0
15:42:42.370 [http-nio-9300-exec-14] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [deleteChunkUpload,165] - 删除分片上传记录成功，fileId: 1750749418427_k0x535y12
15:42:42.371 [http-nio-9300-exec-12] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [deleteChunkUpload,165] - 删除分片上传记录成功，fileId: 1750750799273_ribqnzyi0
15:42:42.372 [http-nio-9300-exec-17] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750750848009_l243200qf
15:42:42.372 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [deleteChunkUpload,165] - 删除分片上传记录成功，fileId: 1750749472396_5sbgcpxg0
15:42:42.375 [http-nio-9300-exec-16] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [deleteChunkUpload,165] - 删除分片上传记录成功，fileId: 1750750848009_l243200qf
15:42:42.419 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:42:42.421 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:42:42.752 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:42:42.752 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5a43994e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:42:42.752 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750742266607_127.0.0.1_14304
15:42:42.755 [nacos-grpc-client-executor-1043] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750742266607_127.0.0.1_14304]Ignore complete event,isRunning:false,isAbandon=false
15:42:42.756 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@453e0879[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1044]
15:42:51.341 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:42:51.879 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4db8ded5-2010-4ac0-ab89-7570b9195a55_config-0
15:42:51.938 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 3 keys and 6 values 
15:42:51.963 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
15:42:51.971 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
15:42:51.981 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
15:42:51.991 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
15:42:52.004 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
15:42:52.008 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4db8ded5-2010-4ac0-ab89-7570b9195a55_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:42:52.009 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4db8ded5-2010-4ac0-ab89-7570b9195a55_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002673f396230
15:42:52.009 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4db8ded5-2010-4ac0-ab89-7570b9195a55_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002673f396450
15:42:52.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4db8ded5-2010-4ac0-ab89-7570b9195a55_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:42:52.011 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4db8ded5-2010-4ac0-ab89-7570b9195a55_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:42:52.021 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4db8ded5-2010-4ac0-ab89-7570b9195a55_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:42:52.909 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4db8ded5-2010-4ac0-ab89-7570b9195a55_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750750972695_127.0.0.1_4693
15:42:52.909 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4db8ded5-2010-4ac0-ab89-7570b9195a55_config-0] Notify connected event to listeners.
15:42:52.910 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4db8ded5-2010-4ac0-ab89-7570b9195a55_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:42:52.911 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4db8ded5-2010-4ac0-ab89-7570b9195a55_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002673f510228
15:42:53.011 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:42:54.966 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
15:42:54.966 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:42:54.966 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:42:55.110 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:42:57.600 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:43:02.101 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 88c4a11a-2ca5-4e95-901f-576b1bce6253
15:43:02.102 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88c4a11a-2ca5-4e95-901f-576b1bce6253] RpcClient init label, labels = {module=naming, source=sdk}
15:43:02.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88c4a11a-2ca5-4e95-901f-576b1bce6253] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:43:02.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88c4a11a-2ca5-4e95-901f-576b1bce6253] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:43:02.109 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88c4a11a-2ca5-4e95-901f-576b1bce6253] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:43:02.111 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88c4a11a-2ca5-4e95-901f-576b1bce6253] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:43:02.257 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88c4a11a-2ca5-4e95-901f-576b1bce6253] Success to connect to server [localhost:8848] on start up, connectionId = 1750750982133_127.0.0.1_4712
15:43:02.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88c4a11a-2ca5-4e95-901f-576b1bce6253] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:43:02.258 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88c4a11a-2ca5-4e95-901f-576b1bce6253] Notify connected event to listeners.
15:43:02.259 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88c4a11a-2ca5-4e95-901f-576b1bce6253] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002673f510228
15:43:02.371 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
15:43:02.447 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
15:43:02.854 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88c4a11a-2ca5-4e95-901f-576b1bce6253] Receive server push request, request = NotifySubscriberRequest, requestId = 11
15:43:02.910 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 12.156 seconds (JVM running for 12.919)
15:43:02.924 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88c4a11a-2ca5-4e95-901f-576b1bce6253] Ack server push request, request = NotifySubscriberRequest, requestId = 11
15:43:02.940 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
15:43:02.942 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
15:43:02.996 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
15:43:03.577 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:43:27.705 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750751007326_akk2w83bp
15:44:04.419 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4db8ded5-2010-4ac0-ab89-7570b9195a55_config-0] Server healthy check fail, currentConnection = 1750750972695_127.0.0.1_4693
15:44:04.419 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88c4a11a-2ca5-4e95-901f-576b1bce6253] Server healthy check fail, currentConnection = 1750750982133_127.0.0.1_4712
15:44:04.421 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4db8ded5-2010-4ac0-ab89-7570b9195a55_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:44:04.421 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88c4a11a-2ca5-4e95-901f-576b1bce6253] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:44:06.283 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88c4a11a-2ca5-4e95-901f-576b1bce6253] Success to connect a server [localhost:8848], connectionId = 1750751044464_127.0.0.1_4858
15:44:06.286 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88c4a11a-2ca5-4e95-901f-576b1bce6253] Abandon prev connection, server is localhost:8848, connectionId is 1750750982133_127.0.0.1_4712
15:44:06.287 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750750982133_127.0.0.1_4712
15:44:06.801 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4db8ded5-2010-4ac0-ab89-7570b9195a55_config-0] Success to connect a server [localhost:8848], connectionId = 1750751044464_127.0.0.1_4857
15:44:06.803 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4db8ded5-2010-4ac0-ab89-7570b9195a55_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1750750972695_127.0.0.1_4693
15:44:06.804 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750750972695_127.0.0.1_4693
15:44:06.806 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750750982133_127.0.0.1_4712]Ignore complete event,isRunning:false,isAbandon=true
15:44:07.066 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750750972695_127.0.0.1_4693]Ignore complete event,isRunning:false,isAbandon=true
15:44:07.953 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4db8ded5-2010-4ac0-ab89-7570b9195a55_config-0] Notify disconnected event to listeners
15:44:07.954 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88c4a11a-2ca5-4e95-901f-576b1bce6253] Notify disconnected event to listeners
15:44:07.958 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4db8ded5-2010-4ac0-ab89-7570b9195a55_config-0] Notify connected event to listeners.
15:44:07.958 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88c4a11a-2ca5-4e95-901f-576b1bce6253] Notify connected event to listeners.
15:44:18.317 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88c4a11a-2ca5-4e95-901f-576b1bce6253] Receive server push request, request = NotifySubscriberRequest, requestId = 13
15:44:18.318 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [88c4a11a-2ca5-4e95-901f-576b1bce6253] Ack server push request, request = NotifySubscriberRequest, requestId = 13
15:48:36.358 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:48:36.361 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:48:36.694 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:48:36.695 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6d0d2f3c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:48:36.696 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750751044464_127.0.0.1_4858
15:48:36.701 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@474b9b0a[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 27]
15:49:08.502 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:49:09.310 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 17249f83-f15e-4eaf-8e15-860c032c4a8a_config-0
15:49:09.398 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 49 ms to scan 1 urls, producing 3 keys and 6 values 
15:49:09.435 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
15:49:09.447 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
15:49:09.462 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
15:49:09.475 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
15:49:09.489 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
15:49:09.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17249f83-f15e-4eaf-8e15-860c032c4a8a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:49:09.493 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17249f83-f15e-4eaf-8e15-860c032c4a8a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000019a37396b40
15:49:09.494 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17249f83-f15e-4eaf-8e15-860c032c4a8a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000019a37396d60
15:49:09.494 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17249f83-f15e-4eaf-8e15-860c032c4a8a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:49:09.495 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17249f83-f15e-4eaf-8e15-860c032c4a8a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:49:09.509 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17249f83-f15e-4eaf-8e15-860c032c4a8a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:49:10.984 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17249f83-f15e-4eaf-8e15-860c032c4a8a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750751350716_127.0.0.1_5481
15:49:10.986 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17249f83-f15e-4eaf-8e15-860c032c4a8a_config-0] Notify connected event to listeners.
15:49:10.989 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17249f83-f15e-4eaf-8e15-860c032c4a8a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:49:11.002 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17249f83-f15e-4eaf-8e15-860c032c4a8a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000019a37510fd0
15:49:11.206 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:49:15.106 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
15:49:15.107 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:49:15.107 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:49:15.348 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:49:18.457 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:49:22.476 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bad23d02-5e17-4ea4-9725-a964b1a93738
15:49:22.477 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bad23d02-5e17-4ea4-9725-a964b1a93738] RpcClient init label, labels = {module=naming, source=sdk}
15:49:22.480 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bad23d02-5e17-4ea4-9725-a964b1a93738] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:49:22.480 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bad23d02-5e17-4ea4-9725-a964b1a93738] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:49:22.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bad23d02-5e17-4ea4-9725-a964b1a93738] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:49:22.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bad23d02-5e17-4ea4-9725-a964b1a93738] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:49:22.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bad23d02-5e17-4ea4-9725-a964b1a93738] Success to connect to server [localhost:8848] on start up, connectionId = 1750751362496_127.0.0.1_5496
15:49:22.617 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bad23d02-5e17-4ea4-9725-a964b1a93738] Notify connected event to listeners.
15:49:22.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bad23d02-5e17-4ea4-9725-a964b1a93738] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:49:22.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bad23d02-5e17-4ea4-9725-a964b1a93738] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000019a37510fd0
15:49:22.711 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
15:49:22.775 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
15:49:23.086 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 15.142 seconds (JVM running for 15.897)
15:49:23.111 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
15:49:23.112 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
15:49:23.116 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
15:49:23.208 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bad23d02-5e17-4ea4-9725-a964b1a93738] Receive server push request, request = NotifySubscriberRequest, requestId = 16
15:49:23.251 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bad23d02-5e17-4ea4-9725-a964b1a93738] Ack server push request, request = NotifySubscriberRequest, requestId = 16
15:49:23.287 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:52:33.153 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750751552680_of6x9efyz
15:53:34.366 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bad23d02-5e17-4ea4-9725-a964b1a93738] Server healthy check fail, currentConnection = 1750751362496_127.0.0.1_5496
15:53:34.367 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17249f83-f15e-4eaf-8e15-860c032c4a8a_config-0] Server healthy check fail, currentConnection = 1750751350716_127.0.0.1_5481
15:54:14.115 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bad23d02-5e17-4ea4-9725-a964b1a93738] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:54:14.117 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17249f83-f15e-4eaf-8e15-860c032c4a8a_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:54:30.321 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bad23d02-5e17-4ea4-9725-a964b1a93738] Success to connect a server [localhost:8848], connectionId = 1750751663633_127.0.0.1_6136
15:54:30.321 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17249f83-f15e-4eaf-8e15-860c032c4a8a_config-0] Success to connect a server [localhost:8848], connectionId = 1750751664582_127.0.0.1_6135
15:54:30.323 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bad23d02-5e17-4ea4-9725-a964b1a93738] Abandon prev connection, server is localhost:8848, connectionId is 1750751362496_127.0.0.1_5496
15:54:30.323 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17249f83-f15e-4eaf-8e15-860c032c4a8a_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1750751350716_127.0.0.1_5481
15:54:30.324 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750751362496_127.0.0.1_5496
15:54:30.324 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750751350716_127.0.0.1_5481
15:54:30.326 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17249f83-f15e-4eaf-8e15-860c032c4a8a_config-0] Notify disconnected event to listeners
15:54:30.326 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bad23d02-5e17-4ea4-9725-a964b1a93738] Notify disconnected event to listeners
15:54:30.327 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [17249f83-f15e-4eaf-8e15-860c032c4a8a_config-0] Notify connected event to listeners.
15:54:31.040 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bad23d02-5e17-4ea4-9725-a964b1a93738] Notify connected event to listeners.
15:54:51.114 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bad23d02-5e17-4ea4-9725-a964b1a93738] Server check success, currentServer is localhost:8848 
15:54:51.152 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [deleteChunkUpload,165] - 删除分片上传记录成功，fileId: 1750751552680_of6x9efyz
15:54:51.174 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:54:51.177 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:54:51.513 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:54:51.514 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@150bb1f0[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:54:51.514 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750751663633_127.0.0.1_6136
15:54:51.516 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bad23d02-5e17-4ea4-9725-a964b1a93738] Notify disconnected event to listeners
15:54:51.515 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3ab999b4[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 59]
15:54:54.946 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:54:55.424 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6707c154-5361-4e57-8f53-190957332e2f_config-0
15:54:55.468 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 
15:54:55.491 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
15:54:55.497 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
15:54:55.504 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
15:54:55.513 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
15:54:55.522 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
15:54:55.524 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6707c154-5361-4e57-8f53-190957332e2f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:54:55.524 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6707c154-5361-4e57-8f53-190957332e2f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000273813c68d8
15:54:55.525 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6707c154-5361-4e57-8f53-190957332e2f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000273813c6af8
15:54:55.525 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6707c154-5361-4e57-8f53-190957332e2f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:54:55.526 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6707c154-5361-4e57-8f53-190957332e2f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:54:55.536 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6707c154-5361-4e57-8f53-190957332e2f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:54:56.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6707c154-5361-4e57-8f53-190957332e2f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750751696150_127.0.0.1_6208
15:54:56.330 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6707c154-5361-4e57-8f53-190957332e2f_config-0] Notify connected event to listeners.
15:54:56.332 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6707c154-5361-4e57-8f53-190957332e2f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:54:56.336 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6707c154-5361-4e57-8f53-190957332e2f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000027381500ad8
15:54:56.415 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:54:58.291 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
15:54:58.292 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:54:58.292 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:54:58.450 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:55:00.061 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:55:04.501 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 491b64ba-e3b2-42c3-ab0b-6de6190c2910
15:55:04.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [491b64ba-e3b2-42c3-ab0b-6de6190c2910] RpcClient init label, labels = {module=naming, source=sdk}
15:55:04.512 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [491b64ba-e3b2-42c3-ab0b-6de6190c2910] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:55:04.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [491b64ba-e3b2-42c3-ab0b-6de6190c2910] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:55:04.516 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [491b64ba-e3b2-42c3-ab0b-6de6190c2910] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:55:04.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [491b64ba-e3b2-42c3-ab0b-6de6190c2910] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:55:04.681 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [491b64ba-e3b2-42c3-ab0b-6de6190c2910] Success to connect to server [localhost:8848] on start up, connectionId = 1750751704545_127.0.0.1_6233
15:55:04.682 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [491b64ba-e3b2-42c3-ab0b-6de6190c2910] Notify connected event to listeners.
15:55:04.681 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [491b64ba-e3b2-42c3-ab0b-6de6190c2910] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:55:04.683 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [491b64ba-e3b2-42c3-ab0b-6de6190c2910] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000027381500ad8
15:55:04.811 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
15:55:04.906 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
15:55:05.321 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [491b64ba-e3b2-42c3-ab0b-6de6190c2910] Receive server push request, request = NotifySubscriberRequest, requestId = 17
15:55:05.370 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [491b64ba-e3b2-42c3-ab0b-6de6190c2910] Ack server push request, request = NotifySubscriberRequest, requestId = 17
15:55:05.408 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 10.969 seconds (JVM running for 11.711)
15:55:05.470 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
15:55:05.481 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
15:55:05.489 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
15:55:06.019 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:55:10.873 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [deleteChunkUpload,165] - 删除分片上传记录成功，fileId: 1750751698563_0idgw4ek9
15:55:13.003 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750751712641_hs931d3j4
16:12:22.783 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [deleteChunkUpload,165] - 删除分片上传记录成功，fileId: 1750751712641_hs931d3j4
16:12:22.908 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:12:22.918 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:12:23.249 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:12:23.250 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@294d6866[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:12:23.250 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750751704545_127.0.0.1_6233
16:12:23.255 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750751704545_127.0.0.1_6233]Ignore complete event,isRunning:false,isAbandon=false
16:12:23.260 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7e842f64[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 19]
16:12:45.417 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:12:46.188 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f40bca15-06d0-401a-bb84-69c67a1bea21_config-0
16:12:46.267 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
16:12:46.300 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
16:12:46.319 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 3 keys and 10 values 
16:12:46.332 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
16:12:46.347 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
16:12:46.358 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
16:12:46.361 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f40bca15-06d0-401a-bb84-69c67a1bea21_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:12:46.362 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f40bca15-06d0-401a-bb84-69c67a1bea21_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000017746397678
16:12:46.362 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f40bca15-06d0-401a-bb84-69c67a1bea21_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000017746397898
16:12:46.363 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f40bca15-06d0-401a-bb84-69c67a1bea21_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:12:46.363 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f40bca15-06d0-401a-bb84-69c67a1bea21_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:12:46.371 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f40bca15-06d0-401a-bb84-69c67a1bea21_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:12:47.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f40bca15-06d0-401a-bb84-69c67a1bea21_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750752767297_127.0.0.1_9105
16:12:47.514 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f40bca15-06d0-401a-bb84-69c67a1bea21_config-0] Notify connected event to listeners.
16:12:47.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f40bca15-06d0-401a-bb84-69c67a1bea21_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:12:47.515 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f40bca15-06d0-401a-bb84-69c67a1bea21_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000177464cc4d8
16:12:47.654 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:12:50.306 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
16:12:50.307 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:12:50.307 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:12:50.469 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:12:52.830 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:12:55.127 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of af109cf1-063c-457b-946d-63ccdb7ceb54
16:12:55.127 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [af109cf1-063c-457b-946d-63ccdb7ceb54] RpcClient init label, labels = {module=naming, source=sdk}
16:12:55.129 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [af109cf1-063c-457b-946d-63ccdb7ceb54] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:12:55.129 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [af109cf1-063c-457b-946d-63ccdb7ceb54] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:12:55.129 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [af109cf1-063c-457b-946d-63ccdb7ceb54] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:12:55.130 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [af109cf1-063c-457b-946d-63ccdb7ceb54] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:12:55.268 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [af109cf1-063c-457b-946d-63ccdb7ceb54] Success to connect to server [localhost:8848] on start up, connectionId = 1750752775142_127.0.0.1_9122
16:12:55.268 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [af109cf1-063c-457b-946d-63ccdb7ceb54] Notify connected event to listeners.
16:12:55.269 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [af109cf1-063c-457b-946d-63ccdb7ceb54] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:12:55.269 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [af109cf1-063c-457b-946d-63ccdb7ceb54] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000177464cc4d8
16:12:55.338 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
16:12:55.372 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
16:12:55.539 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 10.852 seconds (JVM running for 11.885)
16:12:55.553 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
16:12:55.553 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
16:12:55.560 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
16:12:55.762 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:12:55.851 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [af109cf1-063c-457b-946d-63ccdb7ceb54] Receive server push request, request = NotifySubscriberRequest, requestId = 18
16:12:55.881 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [af109cf1-063c-457b-946d-63ccdb7ceb54] Ack server push request, request = NotifySubscriberRequest, requestId = 18
16:13:31.711 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:13:31.720 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:13:32.063 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:13:32.064 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3c19b61e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:13:32.065 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750752775142_127.0.0.1_9122
16:13:32.069 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750752775142_127.0.0.1_9122]Ignore complete event,isRunning:false,isAbandon=false
16:13:32.070 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3e8f137c[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 20]
16:13:36.305 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:13:37.094 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f1ac5ff0-4f80-4f51-9ef7-5656d5991b43_config-0
16:13:37.172 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 3 keys and 6 values 
16:13:37.213 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 4 keys and 9 values 
16:13:37.226 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
16:13:37.238 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
16:13:37.249 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
16:13:37.260 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
16:13:37.264 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ac5ff0-4f80-4f51-9ef7-5656d5991b43_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:13:37.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ac5ff0-4f80-4f51-9ef7-5656d5991b43_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000250e23878c8
16:13:37.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ac5ff0-4f80-4f51-9ef7-5656d5991b43_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000250e2387ae8
16:13:37.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ac5ff0-4f80-4f51-9ef7-5656d5991b43_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:13:37.267 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ac5ff0-4f80-4f51-9ef7-5656d5991b43_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:13:37.277 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ac5ff0-4f80-4f51-9ef7-5656d5991b43_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:13:38.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ac5ff0-4f80-4f51-9ef7-5656d5991b43_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750752818234_127.0.0.1_9225
16:13:38.492 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ac5ff0-4f80-4f51-9ef7-5656d5991b43_config-0] Notify connected event to listeners.
16:13:38.493 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ac5ff0-4f80-4f51-9ef7-5656d5991b43_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:13:38.494 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f1ac5ff0-4f80-4f51-9ef7-5656d5991b43_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000250e24dcf98
16:13:38.666 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:13:41.600 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
16:13:41.601 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:13:41.602 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:13:41.822 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:13:44.216 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:13:46.740 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 729ee3c2-ea2c-4501-94e7-fa7f78759290
16:13:46.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [729ee3c2-ea2c-4501-94e7-fa7f78759290] RpcClient init label, labels = {module=naming, source=sdk}
16:13:46.742 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [729ee3c2-ea2c-4501-94e7-fa7f78759290] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:13:46.742 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [729ee3c2-ea2c-4501-94e7-fa7f78759290] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:13:46.743 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [729ee3c2-ea2c-4501-94e7-fa7f78759290] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:13:46.743 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [729ee3c2-ea2c-4501-94e7-fa7f78759290] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:13:46.887 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [729ee3c2-ea2c-4501-94e7-fa7f78759290] Success to connect to server [localhost:8848] on start up, connectionId = 1750752826762_127.0.0.1_9244
16:13:46.888 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [729ee3c2-ea2c-4501-94e7-fa7f78759290] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:13:46.888 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [729ee3c2-ea2c-4501-94e7-fa7f78759290] Notify connected event to listeners.
16:13:46.889 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [729ee3c2-ea2c-4501-94e7-fa7f78759290] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000250e24dcf98
16:13:46.959 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
16:13:46.997 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
16:13:47.174 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 11.639 seconds (JVM running for 12.717)
16:13:47.191 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
16:13:47.191 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
16:13:47.196 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
16:13:47.499 [RMI TCP Connection(2)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:13:47.510 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [729ee3c2-ea2c-4501-94e7-fa7f78759290] Receive server push request, request = NotifySubscriberRequest, requestId = 20
16:13:47.540 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [729ee3c2-ea2c-4501-94e7-fa7f78759290] Ack server push request, request = NotifySubscriberRequest, requestId = 20
16:13:55.593 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:13:55.600 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:13:56.104 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [729ee3c2-ea2c-4501-94e7-fa7f78759290] Receive server push request, request = NotifySubscriberRequest, requestId = 21
16:13:56.133 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [729ee3c2-ea2c-4501-94e7-fa7f78759290] Ack server push request, request = NotifySubscriberRequest, requestId = 21
16:13:56.237 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:13:56.238 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6a1b5b0e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:13:56.239 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750752826762_127.0.0.1_9244
16:13:56.243 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750752826762_127.0.0.1_9244]Ignore complete event,isRunning:false,isAbandon=false
16:13:56.246 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@27d016da[Running, pool size = 14, active threads = 0, queued tasks = 0, completed tasks = 14]
16:14:09.556 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:14:10.329 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 60b5b864-3065-4005-925c-442c0ad1db2d_config-0
16:14:10.419 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
16:14:10.453 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
16:14:10.464 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
16:14:10.478 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
16:14:10.490 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
16:14:10.505 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
16:14:10.510 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60b5b864-3065-4005-925c-442c0ad1db2d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:14:10.511 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60b5b864-3065-4005-925c-442c0ad1db2d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000202c5396b40
16:14:10.512 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60b5b864-3065-4005-925c-442c0ad1db2d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000202c5396d60
16:14:10.513 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60b5b864-3065-4005-925c-442c0ad1db2d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:14:10.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60b5b864-3065-4005-925c-442c0ad1db2d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:14:10.528 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60b5b864-3065-4005-925c-442c0ad1db2d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:14:11.758 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60b5b864-3065-4005-925c-442c0ad1db2d_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750752851507_127.0.0.1_9307
16:14:11.760 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60b5b864-3065-4005-925c-442c0ad1db2d_config-0] Notify connected event to listeners.
16:14:11.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60b5b864-3065-4005-925c-442c0ad1db2d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:14:11.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60b5b864-3065-4005-925c-442c0ad1db2d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000202c5510fb0
16:14:11.912 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:14:14.992 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
16:14:14.993 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:14:14.994 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:14:15.201 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:14:17.808 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:14:20.521 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4f527af4-fcc6-47bb-be09-5a366be0aeb8
16:14:20.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f527af4-fcc6-47bb-be09-5a366be0aeb8] RpcClient init label, labels = {module=naming, source=sdk}
16:14:20.525 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f527af4-fcc6-47bb-be09-5a366be0aeb8] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:14:20.525 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f527af4-fcc6-47bb-be09-5a366be0aeb8] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:14:20.526 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f527af4-fcc6-47bb-be09-5a366be0aeb8] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:14:20.527 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f527af4-fcc6-47bb-be09-5a366be0aeb8] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:14:20.667 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f527af4-fcc6-47bb-be09-5a366be0aeb8] Success to connect to server [localhost:8848] on start up, connectionId = 1750752860541_127.0.0.1_9318
16:14:20.668 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f527af4-fcc6-47bb-be09-5a366be0aeb8] Notify connected event to listeners.
16:14:20.667 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f527af4-fcc6-47bb-be09-5a366be0aeb8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:14:20.669 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f527af4-fcc6-47bb-be09-5a366be0aeb8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000202c5510fb0
16:14:20.763 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
16:14:20.809 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
16:14:20.996 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 12.283 seconds (JVM running for 13.595)
16:14:21.016 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
16:14:21.018 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
16:14:21.022 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
16:14:21.273 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f527af4-fcc6-47bb-be09-5a366be0aeb8] Receive server push request, request = NotifySubscriberRequest, requestId = 22
16:14:21.309 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f527af4-fcc6-47bb-be09-5a366be0aeb8] Ack server push request, request = NotifySubscriberRequest, requestId = 22
16:14:21.402 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:14:51.551 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750752890913_5ytkyw94x
16:15:54.871 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [deleteChunkUpload,165] - 删除分片上传记录成功，fileId: 1750752890913_5ytkyw94x
16:15:57.027 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750752956992_eww5w8ulv
16:16:52.484 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750753012466_3le1mxpp0
16:17:19.287 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750753039263_c23f2nnnr
16:18:03.443 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [deleteChunkUpload,165] - 删除分片上传记录成功，fileId: 1750753039263_c23f2nnnr
16:18:11.568 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:18:11.592 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:18:11.949 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:18:11.949 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1de794a6[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:18:11.950 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750752860541_127.0.0.1_9318
16:18:11.953 [nacos-grpc-client-executor-56] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750752860541_127.0.0.1_9318]Ignore complete event,isRunning:false,isAbandon=false
16:18:11.959 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6045e54a[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 57]
16:18:18.312 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:18:19.122 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0
16:18:19.222 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 60 ms to scan 1 urls, producing 3 keys and 6 values 
16:18:19.260 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
16:18:19.272 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
16:18:19.286 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
16:18:19.297 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
16:18:19.313 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
16:18:19.317 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:18:19.318 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000015c4b3af470
16:18:19.319 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000015c4b3af690
16:18:19.320 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:18:19.322 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:18:19.339 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:18:20.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1750753100341_127.0.0.1_9818
16:18:20.613 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Notify connected event to listeners.
16:18:20.614 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:18:20.615 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000015c4b4e8fb0
16:18:20.788 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:18:23.792 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
16:18:23.793 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:18:23.793 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:18:24.001 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:18:26.478 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:18:29.226 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 25e08eed-c8fc-409b-9994-0aaa06aa18c9
16:18:29.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] RpcClient init label, labels = {module=naming, source=sdk}
16:18:29.229 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:18:29.229 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:18:29.230 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:18:29.230 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:18:29.361 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Success to connect to server [localhost:8848] on start up, connectionId = 1750753109244_127.0.0.1_9839
16:18:29.362 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Notify connected event to listeners.
16:18:29.362 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:18:29.363 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000015c4b4e8fb0
16:18:29.441 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
16:18:29.486 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ************:9300 register finished
16:18:29.660 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 12.19 seconds (JVM running for 13.519)
16:18:29.683 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
16:18:29.684 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
16:18:29.690 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
16:18:29.980 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Receive server push request, request = NotifySubscriberRequest, requestId = 23
16:18:30.006 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Ack server push request, request = NotifySubscriberRequest, requestId = 23
16:19:21.762 [http-nio-9300-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:19:22.874 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750753161651_8henzd87e
16:23:44.940 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [deleteChunkUpload,165] - 删除分片上传记录成功，fileId: 1750753161651_8henzd87e
16:31:18.271 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750753878233_act3snun7
16:45:58.423 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750754758396_i7mzepyrh
16:46:02.603 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 0
16:46:03.493 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 1
16:46:04.230 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 2
16:46:05.142 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 3
16:46:06.111 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 4
16:46:06.939 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 5
16:46:07.777 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 6
16:46:08.605 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 7
16:46:10.192 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 8
16:46:11.152 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 9
16:46:12.159 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 10
16:46:13.133 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 11
16:46:14.100 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 12
16:46:15.123 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 13
16:46:16.007 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 14
16:46:16.879 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 15
16:46:17.729 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 16
16:46:18.666 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 17
16:46:19.635 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 18
16:46:20.504 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 19
16:46:21.364 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 20
16:46:22.271 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 21
16:46:23.302 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 22
16:46:24.173 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 23
16:46:25.132 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 24
16:46:26.053 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 25
16:46:26.956 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 26
16:46:27.896 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 27
16:46:28.725 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 28
16:46:29.646 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 29
16:46:30.463 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 30
16:46:31.307 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 31
16:46:32.200 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 32
16:46:33.063 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 33
16:46:33.900 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 34
16:46:34.671 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 35
16:46:35.656 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 36
16:46:36.486 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 37
16:46:37.558 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 38
16:46:38.457 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 39
16:46:39.588 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 40
16:46:40.620 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 41
16:46:41.565 [http-nio-9300-exec-10] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 42
16:46:42.513 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 43
16:46:43.424 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 44
16:46:44.341 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 45
16:46:45.307 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 46
16:46:46.253 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 47
16:46:47.124 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 48
16:46:48.225 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 49
16:46:49.093 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 50
16:46:49.901 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 51
16:46:50.773 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 52
16:46:51.597 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 53
16:46:52.338 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 54
16:46:53.116 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 55
16:46:53.840 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 56
16:46:54.573 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 57
16:46:55.554 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 58
16:46:56.523 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 59
16:46:57.263 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 60
16:46:58.056 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 61
16:46:58.921 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 62
16:46:59.904 [http-nio-9300-exec-10] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 63
16:47:00.746 [http-nio-9300-exec-10] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 64
16:47:01.559 [http-nio-9300-exec-10] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 65
16:47:02.275 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 66
16:47:03.019 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 67
16:47:03.882 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 68
16:47:04.588 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 69
16:47:05.381 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 70
16:47:06.220 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 71
16:47:07.007 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 72
16:47:07.802 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 73
16:47:08.642 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 74
16:47:09.497 [http-nio-9300-exec-10] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 75
16:47:10.312 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 76
16:47:11.006 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 77
16:47:11.954 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 78
16:47:12.846 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 79
16:47:13.593 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 80
16:47:14.314 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 81
16:47:15.207 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 82
16:47:15.961 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 83
16:47:16.875 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 84
16:47:17.691 [http-nio-9300-exec-10] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 85
16:47:18.452 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 86
16:47:19.262 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 87
16:47:19.989 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 88
16:47:20.805 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 89
16:47:21.768 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 90
16:47:22.609 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 91
16:47:23.350 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 92
16:47:24.195 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 93
16:47:24.983 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 94
16:47:26.002 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 95
16:47:26.619 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 96
16:47:27.755 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 97
16:47:28.635 [http-nio-9300-exec-10] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 98
16:47:29.409 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 99
16:47:30.343 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 100
16:47:31.130 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 101
16:47:31.958 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 102
16:47:32.890 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 103
16:47:33.809 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 104
16:47:34.697 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 105
16:47:35.568 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 106
16:47:36.485 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 107
16:47:37.908 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 108
16:47:38.940 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 109
16:47:39.936 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 110
16:47:40.776 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 111
16:47:41.570 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 112
16:47:42.402 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 113
16:47:43.277 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 114
16:47:44.126 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 115
16:47:44.909 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 116
16:47:45.770 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 117
16:47:46.783 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 118
16:47:47.774 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 119
16:47:48.646 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 120
16:47:49.671 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 121
16:47:59.240 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 0
16:47:59.302 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 1
16:47:59.346 [http-nio-9300-exec-10] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 2
16:47:59.402 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 3
16:47:59.454 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 4
16:47:59.497 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 5
16:47:59.539 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 6
16:47:59.580 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 7
16:47:59.637 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 8
16:47:59.691 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 9
16:47:59.733 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 10
16:47:59.772 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 11
16:47:59.821 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 12
16:47:59.859 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 13
16:47:59.923 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 14
16:47:59.967 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 15
16:48:00.009 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 16
16:48:00.050 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 17
16:48:00.087 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 18
16:48:00.149 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 19
16:48:00.192 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 20
16:48:00.231 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 21
16:48:00.272 [http-nio-9300-exec-10] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 22
16:48:00.318 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 23
16:48:00.363 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 24
16:48:00.406 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 25
16:48:00.446 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 26
16:48:00.487 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 27
16:48:00.546 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 28
16:48:00.607 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 29
16:48:00.650 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 30
16:48:00.687 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 31
16:48:00.732 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 32
16:48:00.772 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 33
16:48:00.811 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 34
16:48:00.863 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 35
16:48:00.897 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 36
16:48:00.935 [http-nio-9300-exec-10] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 37
16:48:00.970 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 38
16:48:01.006 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 39
16:48:01.048 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 40
16:48:01.091 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 41
16:48:01.130 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 42
16:48:01.186 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 43
16:48:01.236 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 44
16:48:01.277 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 45
16:48:01.314 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 46
16:48:01.353 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 47
16:48:01.394 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 48
16:48:01.432 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 49
16:48:01.469 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 50
16:48:01.519 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 51
16:48:01.560 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 52
16:48:01.598 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 53
16:48:01.637 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 54
16:48:01.677 [http-nio-9300-exec-10] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 55
16:48:01.720 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 56
16:48:01.782 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 57
16:48:01.823 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 58
16:48:01.863 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 59
16:48:01.899 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 60
16:48:01.936 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 61
16:48:01.973 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 62
16:48:02.028 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 63
16:48:02.069 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 64
16:48:02.110 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 65
16:48:02.150 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 66
16:48:02.186 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 67
16:48:02.233 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 68
16:48:02.281 [http-nio-9300-exec-10] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 69
16:48:02.316 [http-nio-9300-exec-10] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 70
16:48:02.351 [http-nio-9300-exec-10] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 71
16:48:02.392 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 72
16:48:02.431 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 73
16:48:02.470 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 74
16:48:02.509 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 75
16:48:02.559 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 76
16:48:02.597 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 77
16:48:02.638 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 78
16:48:02.678 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 79
16:48:02.730 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 80
16:48:02.781 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 81
16:48:02.821 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 82
16:48:02.862 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 83
16:48:02.896 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 84
16:48:02.958 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 85
16:48:03.029 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 86
16:48:03.090 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 87
16:48:03.188 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 88
16:48:03.274 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 89
16:48:03.343 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 90
16:48:03.417 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 91
16:48:03.491 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 92
16:48:03.565 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 93
16:48:03.630 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 94
16:48:03.699 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 95
16:48:03.771 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 96
16:48:03.835 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 97
16:48:03.902 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 98
16:48:03.968 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 99
16:48:04.044 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 100
16:48:04.115 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 101
16:48:04.186 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 102
16:48:04.249 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 103
16:48:04.331 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 104
16:48:04.407 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 105
16:48:04.486 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 106
16:48:04.560 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 107
16:48:04.628 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 108
16:48:04.696 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 109
16:48:04.752 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 110
16:48:04.825 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 111
16:48:04.898 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 112
16:48:04.967 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 113
16:48:05.032 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 114
16:48:05.098 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 115
16:48:05.170 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 116
16:48:05.235 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 117
16:48:05.306 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 118
16:48:05.363 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 119
16:48:05.439 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 120
16:48:05.504 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,75] - 分片已上传，跳过，fileId: 1750754758396_i7mzepyrh, chunkIndex: 121
16:48:06.404 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 122
16:48:07.237 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 123
16:48:08.120 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 124
16:48:09.105 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 125
16:48:09.978 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 126
16:48:10.938 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 127
16:48:11.839 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 128
16:48:12.865 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 129
16:48:13.882 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 130
16:48:14.799 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 131
16:48:15.653 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 132
16:48:16.513 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 133
16:48:17.423 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 134
16:48:18.293 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 135
16:48:19.275 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 136
16:48:20.228 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 137
16:48:21.135 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 138
16:48:22.005 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 139
16:48:23.035 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 140
16:48:23.920 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 141
16:48:25.008 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 142
16:48:25.920 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 143
16:48:26.878 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 144
16:48:27.925 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 145
16:48:28.848 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 146
16:48:29.653 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 147
16:48:30.715 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 148
16:48:31.544 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 149
16:48:32.344 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 150
16:48:33.254 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 151
16:48:34.431 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 152
16:48:35.388 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 153
16:48:36.363 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 154
16:48:37.426 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 155
16:48:38.521 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 156
16:48:40.630 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 157
16:48:41.577 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 158
16:48:42.364 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 159
16:48:43.204 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 160
16:48:44.076 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 161
16:48:44.937 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 162
16:48:45.862 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 163
16:48:46.396 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750754758396_i7mzepyrh, chunkIndex: 164
17:06:27.235 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [deleteChunkUpload,165] - 删除分片上传记录成功，fileId: 1750754758396_i7mzepyrh
18:33:18.485 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Server healthy check fail, currentConnection = 1750753109244_127.0.0.1_9839
18:33:21.169 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Try to reconnect to a new server, server is  not appointed, will choose a random server.
18:33:45.324 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
18:33:58.096 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
18:33:59.532 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Success to connect a server [localhost:8848], connectionId = 1750761238955_127.0.0.1_3143
18:33:59.532 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Abandon prev connection, server is localhost:8848, connectionId is 1750753109244_127.0.0.1_9839
18:33:59.532 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750753109244_127.0.0.1_9839
18:33:59.538 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Notify disconnected event to listeners
18:33:59.574 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Notify connected event to listeners.
18:33:59.640 [nacos-grpc-client-executor-1628] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750753109244_127.0.0.1_9839]Ignore complete event,isRunning:true,isAbandon=true
18:34:02.744 [nacos-grpc-client-executor-1631] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Receive server push request, request = NotifySubscriberRequest, requestId = 26
18:34:02.744 [nacos-grpc-client-executor-1631] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Ack server push request, request = NotifySubscriberRequest, requestId = 26
18:40:14.434 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Server healthy check fail, currentConnection = 1750753100341_127.0.0.1_9818
18:40:14.435 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
18:40:16.745 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Success to connect a server [localhost:8848], connectionId = 1750761616455_127.0.0.1_3660
18:40:16.746 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1750753100341_127.0.0.1_9818
18:40:16.746 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750753100341_127.0.0.1_9818
18:40:16.753 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Notify disconnected event to listeners
18:40:16.773 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Notify connected event to listeners.
18:40:16.793 [nacos-grpc-client-executor-1706] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750753100341_127.0.0.1_9818]Ignore complete event,isRunning:true,isAbandon=true
19:12:38.360 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Server healthy check fail, currentConnection = 1750761238955_127.0.0.1_3143
19:12:38.360 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Try to reconnect to a new server, server is  not appointed, will choose a random server.
19:12:43.349 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Server healthy check fail, currentConnection = 1750761616455_127.0.0.1_3660
19:12:43.349 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
19:12:49.726 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:12:56.383 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:12:59.975 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:13:08.178 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:13:10.614 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:13:11.977 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Success to connect a server [localhost:8848], connectionId = 1750763591181_127.0.0.1_6245
19:13:13.061 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Abandon prev connection, server is localhost:8848, connectionId is 1750761238955_127.0.0.1_3143
19:13:13.061 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750761238955_127.0.0.1_3143
19:13:11.977 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Success to connect a server [localhost:8848], connectionId = 1750763591153_127.0.0.1_6246
19:13:13.061 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1750761616455_127.0.0.1_3660
19:13:13.061 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750761616455_127.0.0.1_3660
19:13:13.133 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Notify disconnected event to listeners
19:13:13.134 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Notify connected event to listeners.
19:13:13.134 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Notify disconnected event to listeners
19:13:13.134 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Notify connected event to listeners.
19:13:13.682 [nacos-grpc-client-executor-2098] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Receive server push request, request = NotifySubscriberRequest, requestId = 30
19:13:13.683 [nacos-grpc-client-executor-2098] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Ack server push request, request = NotifySubscriberRequest, requestId = 30
19:58:00.774 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Server healthy check fail, currentConnection = 1750763591153_127.0.0.1_6246
19:58:00.776 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
19:58:00.776 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Server healthy check fail, currentConnection = 1750763591181_127.0.0.1_6245
19:58:00.776 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Try to reconnect to a new server, server is  not appointed, will choose a random server.
19:58:07.289 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:58:10.916 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:58:14.227 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
19:58:17.657 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Success to connect a server [localhost:8848], connectionId = 1750766296325_127.0.0.1_10259
19:58:17.657 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Abandon prev connection, server is localhost:8848, connectionId is 1750763591181_127.0.0.1_6245
19:58:17.657 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750763591181_127.0.0.1_6245
19:58:17.661 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Success to connect a server [localhost:8848], connectionId = 1750766296658_127.0.0.1_10260
19:58:17.661 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1750763591153_127.0.0.1_6246
19:58:17.661 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750763591153_127.0.0.1_6246
19:58:17.669 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Notify disconnected event to listeners
19:58:17.670 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Notify disconnected event to listeners
19:58:17.674 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Notify connected event to listeners.
19:58:17.675 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Notify connected event to listeners.
19:58:19.251 [nacos-grpc-client-executor-2637] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Receive server push request, request = NotifySubscriberRequest, requestId = 31
19:58:19.251 [nacos-grpc-client-executor-2637] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [25e08eed-c8fc-409b-9994-0aaa06aa18c9] Ack server push request, request = NotifySubscriberRequest, requestId = 31
19:58:22.528 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e834e29a-1a5a-47d0-8e08-86ef5b911b34_config-0] Server check success, currentServer is localhost:8848 
20:13:59.617 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [initChunkUpload,59] - 初始化分片上传成功，fileId: 1750767239525_rku30lsn6
20:14:01.810 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 0
20:14:03.251 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 1
20:14:04.123 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 2
20:14:04.766 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 3
20:14:05.529 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 4
20:14:06.215 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 5
20:14:07.357 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 6
20:14:07.985 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 7
20:14:09.115 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 8
20:14:09.944 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 9
20:14:10.699 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 10
20:14:11.419 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 11
20:14:12.647 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 12
20:14:13.333 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 13
20:14:14.100 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 14
20:14:14.712 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 15
20:14:15.386 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 16
20:14:16.059 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 17
20:14:16.831 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 18
20:14:17.903 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 19
20:14:18.753 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 20
20:14:19.677 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 21
20:14:20.487 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 22
20:14:21.254 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 23
20:14:21.908 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 24
20:14:22.829 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 25
20:14:23.608 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 26
20:14:24.771 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 27
20:14:25.523 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 28
20:14:26.699 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 29
20:14:27.521 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 30
20:14:28.218 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 31
20:14:28.854 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 32
20:14:29.664 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 33
20:14:30.275 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 34
20:14:30.913 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 35
20:14:31.556 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 36
20:14:32.186 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 37
20:14:32.773 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 38
20:14:33.562 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 39
20:14:34.258 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 40
20:14:34.887 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 41
20:14:35.555 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 42
20:14:36.496 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 43
20:14:37.150 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 44
20:14:37.742 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 45
20:14:38.386 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 46
20:14:42.767 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 47
20:14:43.462 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 48
20:14:44.089 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 49
20:14:44.875 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 50
20:14:45.612 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 51
20:14:46.341 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 52
20:14:47.051 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 53
20:14:47.667 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 54
20:14:48.413 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 55
20:14:49.085 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 56
20:14:49.703 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 57
20:14:50.330 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 58
20:14:51.004 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 59
20:14:51.637 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 60
20:14:52.390 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 61
20:14:53.439 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 62
20:14:54.151 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 63
20:14:54.910 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 64
20:14:55.526 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 65
20:14:56.173 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 66
20:14:56.964 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 67
20:14:57.643 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 68
20:14:58.278 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 69
20:14:59.045 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 70
20:14:59.729 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 71
20:15:00.384 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 72
20:15:01.094 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 73
20:15:01.848 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 74
20:15:02.514 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 75
20:15:03.285 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 76
20:15:04.005 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 77
20:15:04.672 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 78
20:15:05.426 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 79
20:15:06.111 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 80
20:15:06.810 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 81
20:15:07.486 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 82
20:15:08.119 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 83
20:15:08.800 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 84
20:15:09.563 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 85
20:15:10.243 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 86
20:15:11.000 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 87
20:15:11.656 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 88
20:15:12.343 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 89
20:15:13.112 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 90
20:15:13.909 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 91
20:15:14.609 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 92
20:15:15.318 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 93
20:15:16.016 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 94
20:15:16.668 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 95
20:15:17.363 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 96
20:15:18.083 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 97
20:15:18.759 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 98
20:15:19.397 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 99
20:15:20.066 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 100
20:15:20.741 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 101
20:15:21.506 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 102
20:15:22.191 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 103
20:15:22.847 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 104
20:15:23.560 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 105
20:15:24.179 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 106
20:15:25.085 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 107
20:15:25.843 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 108
20:15:26.613 [http-nio-9300-exec-10] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 109
20:15:27.643 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 110
20:15:28.494 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 111
20:15:29.156 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 112
20:15:29.880 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 113
20:15:30.610 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 114
20:15:31.330 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 115
20:15:32.125 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 116
20:15:32.778 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 117
20:15:33.639 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 118
20:15:34.294 [http-nio-9300-exec-10] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 119
20:15:34.968 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 120
20:15:35.675 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 121
20:15:36.370 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 122
20:15:37.011 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 123
20:15:37.612 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 124
20:15:38.193 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 125
20:15:38.889 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 126
20:15:39.479 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 127
20:15:40.231 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 128
20:15:40.915 [http-nio-9300-exec-10] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 129
20:15:41.710 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 130
20:15:42.440 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 131
20:15:43.080 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 132
20:15:43.792 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 133
20:15:44.513 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 134
20:15:45.241 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 135
20:15:45.874 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 136
20:15:46.613 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 137
20:15:47.310 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 138
20:15:47.940 [http-nio-9300-exec-10] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 139
20:15:48.731 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 140
20:15:49.446 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 141
20:15:50.135 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 142
20:15:50.795 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 143
20:15:51.495 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 144
20:15:52.126 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 145
20:15:52.753 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 146
20:15:53.369 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 147
20:15:53.955 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 148
20:15:54.668 [http-nio-9300-exec-10] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 149
20:15:55.264 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 150
20:15:55.984 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 151
20:15:56.625 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 152
20:15:57.255 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 153
20:15:57.966 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 154
20:15:58.609 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 155
20:15:59.289 [http-nio-9300-exec-7] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 156
20:15:59.914 [http-nio-9300-exec-5] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 157
20:16:00.517 [http-nio-9300-exec-6] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 158
20:16:01.172 [http-nio-9300-exec-10] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 159
20:16:01.922 [http-nio-9300-exec-2] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 160
20:16:02.557 [http-nio-9300-exec-9] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 161
20:16:03.235 [http-nio-9300-exec-8] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 162
20:16:03.952 [http-nio-9300-exec-1] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 163
20:16:04.443 [http-nio-9300-exec-3] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [uploadChunk,100] - 分片上传成功，fileId: 1750767239525_rku30lsn6, chunkIndex: 164
20:16:04.474 [http-nio-9300-exec-4] INFO  c.h.f.s.i.ChunkUploadServiceImpl - [mergeChunks,135] - 分片合并成功，fileId: 1750767239525_rku30lsn6, fileUrl: http://192.168.1.200:9001/defalut-bucket/merged/1750767239525_rku30lsn6/iOS 11 新功能 - WWDC17 - 视频 - Apple Developer.ts
20:50:12.969 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:50:12.974 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:50:13.307 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:50:13.308 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@73d18efe[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:50:13.308 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750766296325_127.0.0.1_10259
20:50:13.308 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@72cbd71c[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 3257]
