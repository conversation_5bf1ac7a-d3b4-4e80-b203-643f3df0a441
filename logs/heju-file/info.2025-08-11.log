13:11:20.877 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:11:21.563 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f17d0b81-896e-4aaa-968e-6ae20dafcfcf_config-0
13:11:21.634 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 31 ms to scan 1 urls, producing 3 keys and 6 values 
13:11:21.664 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 4 keys and 9 values 
13:11:21.671 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 3 keys and 10 values 
13:11:21.671 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 0 ms to scan 1 urls, producing 1 keys and 5 values 
13:11:21.688 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 1 keys and 7 values 
13:11:21.697 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
13:11:21.697 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f17d0b81-896e-4aaa-968e-6ae20dafcfcf_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:11:21.697 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f17d0b81-896e-4aaa-968e-6ae20dafcfcf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000024a813b3650
13:11:21.697 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f17d0b81-896e-4aaa-968e-6ae20dafcfcf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000024a813b3870
13:11:21.697 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f17d0b81-896e-4aaa-968e-6ae20dafcfcf_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:11:21.697 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f17d0b81-896e-4aaa-968e-6ae20dafcfcf_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:11:21.712 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f17d0b81-896e-4aaa-968e-6ae20dafcfcf_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:11:22.664 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f17d0b81-896e-4aaa-968e-6ae20dafcfcf_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754889082423_127.0.0.1_13742
13:11:22.666 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f17d0b81-896e-4aaa-968e-6ae20dafcfcf_config-0] Notify connected event to listeners.
13:11:22.666 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f17d0b81-896e-4aaa-968e-6ae20dafcfcf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:11:22.667 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f17d0b81-896e-4aaa-968e-6ae20dafcfcf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000024a814ef718
13:11:22.824 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:11:25.738 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
13:11:25.738 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:11:25.740 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:11:26.032 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:11:29.995 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:11:36.301 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 276273f4-f5d3-470a-89cf-42e851496e3f
13:11:36.303 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [276273f4-f5d3-470a-89cf-42e851496e3f] RpcClient init label, labels = {module=naming, source=sdk}
13:11:36.304 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [276273f4-f5d3-470a-89cf-42e851496e3f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:11:36.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [276273f4-f5d3-470a-89cf-42e851496e3f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:11:36.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [276273f4-f5d3-470a-89cf-42e851496e3f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:11:36.311 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [276273f4-f5d3-470a-89cf-42e851496e3f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:11:36.457 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [276273f4-f5d3-470a-89cf-42e851496e3f] Success to connect to server [localhost:8848] on start up, connectionId = 1754889096329_127.0.0.1_13773
13:11:36.457 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [276273f4-f5d3-470a-89cf-42e851496e3f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:11:36.457 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [276273f4-f5d3-470a-89cf-42e851496e3f] Notify connected event to listeners.
13:11:36.457 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [276273f4-f5d3-470a-89cf-42e851496e3f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000024a814ef718
13:11:36.542 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
13:11:36.615 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ***********:9300 register finished
13:11:36.986 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 16.734 seconds (JVM running for 24.85)
13:11:37.017 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
13:11:37.017 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [276273f4-f5d3-470a-89cf-42e851496e3f] Receive server push request, request = NotifySubscriberRequest, requestId = 5
13:11:37.018 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
13:11:37.018 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
13:11:37.048 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [276273f4-f5d3-470a-89cf-42e851496e3f] Ack server push request, request = NotifySubscriberRequest, requestId = 5
18:31:12.693 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:31:12.696 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:31:13.014 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:31:13.015 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@496e7b50[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:31:13.015 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754889096329_127.0.0.1_13773
18:31:13.017 [nacos-grpc-client-executor-3838] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754889096329_127.0.0.1_13773]Ignore complete event,isRunning:false,isAbandon=false
18:31:13.023 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@37b322ea[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3839]
