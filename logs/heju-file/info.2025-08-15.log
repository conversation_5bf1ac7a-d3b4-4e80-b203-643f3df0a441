09:11:18.966 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:11:22.448 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f500f54f-95d1-4790-9380-737228d95d57_config-0
09:11:22.637 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 85 ms to scan 1 urls, producing 3 keys and 6 values 
09:11:22.728 [main] INFO  o.r.Reflections - [scan,232] - <PERSON>flections took 42 ms to scan 1 urls, producing 4 keys and 9 values 
09:11:22.752 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 20 ms to scan 1 urls, producing 3 keys and 10 values 
09:11:22.782 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 20 ms to scan 1 urls, producing 1 keys and 5 values 
09:11:22.809 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 22 ms to scan 1 urls, producing 1 keys and 7 values 
09:11:22.831 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 2 keys and 8 values 
09:11:22.842 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f500f54f-95d1-4790-9380-737228d95d57_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:11:22.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f500f54f-95d1-4790-9380-737228d95d57_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000002a3323aeb40
09:11:22.846 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f500f54f-95d1-4790-9380-737228d95d57_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002a3323aed60
09:11:22.848 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f500f54f-95d1-4790-9380-737228d95d57_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:11:22.852 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f500f54f-95d1-4790-9380-737228d95d57_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:11:22.885 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f500f54f-95d1-4790-9380-737228d95d57_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:11:27.132 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f500f54f-95d1-4790-9380-737228d95d57_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755220286653_127.0.0.1_2010
09:11:27.135 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f500f54f-95d1-4790-9380-737228d95d57_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:11:27.136 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f500f54f-95d1-4790-9380-737228d95d57_config-0] Notify connected event to listeners.
09:11:27.136 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f500f54f-95d1-4790-9380-737228d95d57_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002a3324f0ad8
09:11:27.439 [main] INFO  c.h.f.HeJuFileApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:11:35.044 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9300"]
09:11:35.047 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:11:35.048 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:11:35.409 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:11:40.477 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:11:48.118 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 97cc0255-39a8-479a-82f5-239c2c7e961a
09:11:48.119 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97cc0255-39a8-479a-82f5-239c2c7e961a] RpcClient init label, labels = {module=naming, source=sdk}
09:11:48.122 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97cc0255-39a8-479a-82f5-239c2c7e961a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:11:48.122 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97cc0255-39a8-479a-82f5-239c2c7e961a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:11:48.123 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97cc0255-39a8-479a-82f5-239c2c7e961a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:11:48.124 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97cc0255-39a8-479a-82f5-239c2c7e961a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:11:48.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97cc0255-39a8-479a-82f5-239c2c7e961a] Success to connect to server [localhost:8848] on start up, connectionId = 1755220308135_127.0.0.1_2076
09:11:48.266 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97cc0255-39a8-479a-82f5-239c2c7e961a] Notify connected event to listeners.
09:11:48.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97cc0255-39a8-479a-82f5-239c2c7e961a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:11:48.267 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97cc0255-39a8-479a-82f5-239c2c7e961a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002a3324f0ad8
09:11:48.326 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9300"]
09:11:48.368 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-file ***********:9300 register finished
09:11:48.597 [main] INFO  c.h.f.HeJuFileApplication - [logStarted,61] - Started HeJuFileApplication in 30.495 seconds (JVM running for 37.57)
09:11:48.611 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file.yml, group=DEFAULT_GROUP
09:11:48.612 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file, group=DEFAULT_GROUP
09:11:48.616 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-file-dev.yml, group=DEFAULT_GROUP
09:11:48.808 [RMI TCP Connection(12)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:11:48.890 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97cc0255-39a8-479a-82f5-239c2c7e961a] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:11:48.907 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [97cc0255-39a8-479a-82f5-239c2c7e961a] Ack server push request, request = NotifySubscriberRequest, requestId = 2
17:29:19.585 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:29:19.590 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:29:19.911 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:29:19.912 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@66f76450[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:29:19.912 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755220308135_127.0.0.1_2076
17:29:19.914 [nacos-grpc-client-executor-5979] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755220308135_127.0.0.1_2076]Ignore complete event,isRunning:false,isAbandon=false
17:29:19.916 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6fb6933e[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 5980]
