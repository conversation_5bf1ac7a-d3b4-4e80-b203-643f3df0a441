11:43:51.500 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:43:52.427 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 55d0f620-a216-4b4d-a6a1-5866ac882484_config-0
11:43:52.510 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 45 ms to scan 1 urls, producing 3 keys and 6 values 
11:43:52.539 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 4 keys and 9 values 
11:43:52.552 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 3 keys and 10 values 
11:43:52.565 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
11:43:52.588 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 1 keys and 7 values 
11:43:52.599 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
11:43:52.605 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55d0f620-a216-4b4d-a6a1-5866ac882484_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:43:52.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55d0f620-a216-4b4d-a6a1-5866ac882484_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001adc03cfd80
11:43:52.607 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55d0f620-a216-4b4d-a6a1-5866ac882484_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001adc03d0000
11:43:52.608 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55d0f620-a216-4b4d-a6a1-5866ac882484_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:43:52.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55d0f620-a216-4b4d-a6a1-5866ac882484_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:43:52.621 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55d0f620-a216-4b4d-a6a1-5866ac882484_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:43:56.383 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55d0f620-a216-4b4d-a6a1-5866ac882484_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:43:59.406 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55d0f620-a216-4b4d-a6a1-5866ac882484_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:44:02.415 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55d0f620-a216-4b4d-a6a1-5866ac882484_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
11:44:02.416 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55d0f620-a216-4b4d-a6a1-5866ac882484_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:44:02.416 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [55d0f620-a216-4b4d-a6a1-5866ac882484_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001adc04d98a0
11:44:04.505 [main] INFO  c.h.g.HeJuGenApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:44:07.322 [main] INFO  i.s.s.b.a.SeataAutoConfiguration - [globalTransactionScanner,63] - Automatically configure Seata
11:44:07.426 [main] INFO  i.s.c.ConfigurationFactory - [load,69] - load Configuration from :Spring Configuration
11:44:07.436 [main] INFO  i.s.c.ConfigurationFactory - [buildConfiguration,121] - load Configuration from :Spring Configuration
11:53:41.833 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:53:42.834 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7949b9a3-cbff-488c-90ec-95032ef59d2a_config-0
11:53:42.917 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 42 ms to scan 1 urls, producing 3 keys and 6 values 
11:53:42.967 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 4 keys and 9 values 
11:53:42.981 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
11:53:42.996 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
11:53:43.008 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
11:53:43.029 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 2 keys and 8 values 
11:53:43.034 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7949b9a3-cbff-488c-90ec-95032ef59d2a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:53:43.035 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7949b9a3-cbff-488c-90ec-95032ef59d2a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000026a013b7d80
11:53:43.035 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7949b9a3-cbff-488c-90ec-95032ef59d2a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000026a013b8000
11:53:43.036 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7949b9a3-cbff-488c-90ec-95032ef59d2a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:53:43.036 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7949b9a3-cbff-488c-90ec-95032ef59d2a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:53:43.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7949b9a3-cbff-488c-90ec-95032ef59d2a_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:53:44.364 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7949b9a3-cbff-488c-90ec-95032ef59d2a_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750305224113_127.0.0.1_1552
11:53:44.365 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7949b9a3-cbff-488c-90ec-95032ef59d2a_config-0] Notify connected event to listeners.
11:53:44.366 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7949b9a3-cbff-488c-90ec-95032ef59d2a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:53:44.367 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7949b9a3-cbff-488c-90ec-95032ef59d2a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000026a014f1ca0
11:53:44.581 [main] INFO  c.h.g.HeJuGenApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:53:49.313 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9400"]
11:53:49.314 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:53:49.315 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:53:49.548 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:53:50.433 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:53:50.435 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:53:50.435 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:53:53.943 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:53:57.623 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 31b66826-59a1-4846-9fa6-cbcd2968bcc3
11:53:57.624 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31b66826-59a1-4846-9fa6-cbcd2968bcc3] RpcClient init label, labels = {module=naming, source=sdk}
11:53:57.627 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31b66826-59a1-4846-9fa6-cbcd2968bcc3] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:53:57.628 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31b66826-59a1-4846-9fa6-cbcd2968bcc3] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:53:57.628 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31b66826-59a1-4846-9fa6-cbcd2968bcc3] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:53:57.629 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31b66826-59a1-4846-9fa6-cbcd2968bcc3] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:53:57.780 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31b66826-59a1-4846-9fa6-cbcd2968bcc3] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750305237647_127.0.0.1_1591
11:53:57.781 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31b66826-59a1-4846-9fa6-cbcd2968bcc3] Notify connected event to listeners.
11:53:57.781 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31b66826-59a1-4846-9fa6-cbcd2968bcc3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:53:57.781 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31b66826-59a1-4846-9fa6-cbcd2968bcc3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000026a014f1ca0
11:53:57.835 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9400"]
11:53:57.886 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gen 192.168.2.43:9400 register finished
11:53:58.064 [main] INFO  c.h.g.HeJuGenApplication - [logStarted,61] - Started HeJuGenApplication in 17.025 seconds (JVM running for 18.426)
11:53:58.079 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gen, group=DEFAULT_GROUP
11:53:58.086 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gen-dev.yml, group=DEFAULT_GROUP
11:53:58.087 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gen.yml, group=DEFAULT_GROUP
11:53:58.386 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31b66826-59a1-4846-9fa6-cbcd2968bcc3] Receive server push request, request = NotifySubscriberRequest, requestId = 8
11:53:58.411 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31b66826-59a1-4846-9fa6-cbcd2968bcc3] Ack server push request, request = NotifySubscriberRequest, requestId = 8
12:06:27.568 [http-nio-9400-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:45:38.624 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:45:38.629 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:45:38.987 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:45:38.989 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1409e7da[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:45:38.989 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750305237647_127.0.0.1_1591
15:45:38.994 [nacos-grpc-client-executor-2776] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750305237647_127.0.0.1_1591]Ignore complete event,isRunning:false,isAbandon=false
15:45:39.002 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@40a2a800[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2777]
15:45:39.244 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:45:39.256 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:45:39.291 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:45:39.291 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:38:48.129 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:38:50.063 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b384dced-6765-45e4-8646-4f84b9be6e5d_config-0
16:38:50.170 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 52 ms to scan 1 urls, producing 3 keys and 6 values 
16:38:50.213 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
16:38:50.238 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 10 values 
16:38:50.269 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 1 keys and 5 values 
16:38:50.291 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
16:38:50.309 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
16:38:50.316 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:38:50.317 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001ee913b78e0
16:38:50.318 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001ee913b7b00
16:38:50.319 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:38:50.320 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:38:50.335 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:38:51.821 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:38:51.833 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:38:51.842 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
16:38:51.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:38:51.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001ee914c1cc0
16:38:51.970 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:52.188 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:52.514 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:52.962 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:53.478 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:54.017 [main] INFO  c.h.g.HeJuGenApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:38:54.098 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:54.813 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:55.630 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:56.544 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:57.674 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:38:58.916 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:00.286 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:01.724 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:02.757 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9400"]
16:39:02.758 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:39:02.758 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:39:03.102 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:39:03.264 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:04.290 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:39:04.292 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:39:04.293 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:39:04.912 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:06.599 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:08.392 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:08.761 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:39:10.380 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:12.414 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:14.498 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:14.560 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0ecf104d-a8f7-4df9-a236-5216a8f760e1
16:39:14.560 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ecf104d-a8f7-4df9-a236-5216a8f760e1] RpcClient init label, labels = {module=naming, source=sdk}
16:39:14.563 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ecf104d-a8f7-4df9-a236-5216a8f760e1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:39:14.563 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ecf104d-a8f7-4df9-a236-5216a8f760e1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:39:14.564 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ecf104d-a8f7-4df9-a236-5216a8f760e1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:39:14.565 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ecf104d-a8f7-4df9-a236-5216a8f760e1] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:14.573 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ecf104d-a8f7-4df9-a236-5216a8f760e1] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:14.579 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ecf104d-a8f7-4df9-a236-5216a8f760e1] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:14.586 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ecf104d-a8f7-4df9-a236-5216a8f760e1] Try to reconnect to a new server, server is  not appointed, will choose a random server.
16:39:14.586 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ecf104d-a8f7-4df9-a236-5216a8f760e1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:39:14.587 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ecf104d-a8f7-4df9-a236-5216a8f760e1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001ee914c1cc0
16:39:14.713 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ecf104d-a8f7-4df9-a236-5216a8f760e1] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:14.927 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9400"]
16:39:14.928 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ecf104d-a8f7-4df9-a236-5216a8f760e1] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:15.243 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ecf104d-a8f7-4df9-a236-5216a8f760e1] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:15.664 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ecf104d-a8f7-4df9-a236-5216a8f760e1] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:15.942 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:39:15.943 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7f6c85c4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:39:15.943 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0ecf104d-a8f7-4df9-a236-5216a8f760e1] Client is shutdown, stop reconnect to server
16:39:15.943 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@319c77de[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
16:39:15.948 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9d334a34-9bb1-4632-8af6-f2abddd2958d
16:39:15.949 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d334a34-9bb1-4632-8af6-f2abddd2958d] RpcClient init label, labels = {module=naming, source=sdk}
16:39:15.949 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d334a34-9bb1-4632-8af6-f2abddd2958d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:39:15.949 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d334a34-9bb1-4632-8af6-f2abddd2958d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:39:15.949 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d334a34-9bb1-4632-8af6-f2abddd2958d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:39:15.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d334a34-9bb1-4632-8af6-f2abddd2958d] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:15.959 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d334a34-9bb1-4632-8af6-f2abddd2958d] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:15.966 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d334a34-9bb1-4632-8af6-f2abddd2958d] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:39:15.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d334a34-9bb1-4632-8af6-f2abddd2958d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:39:15.972 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d334a34-9bb1-4632-8af6-f2abddd2958d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001ee914c1cc0
16:39:15.972 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d334a34-9bb1-4632-8af6-f2abddd2958d] Try to reconnect to a new server, server is  not appointed, will choose a random server.
16:39:16.103 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d334a34-9bb1-4632-8af6-f2abddd2958d] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:16.314 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d334a34-9bb1-4632-8af6-f2abddd2958d] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:16.326 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:39:16.330 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:39:16.343 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:39:16.343 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:39:16.368 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9400"]
16:39:16.368 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
16:39:16.397 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9400"]
16:39:16.401 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9400"]
16:39:16.608 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b384dced-6765-45e4-8646-4f84b9be6e5d_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:16.623 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d334a34-9bb1-4632-8af6-f2abddd2958d] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:39:17.055 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d334a34-9bb1-4632-8af6-f2abddd2958d] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
16:40:51.762 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:40:52.871 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fa7092fd-d0c3-476f-864a-cccd1c902a7b_config-0
16:40:52.962 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
16:40:53.005 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 4 keys and 9 values 
16:40:53.018 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
16:40:53.033 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
16:40:53.050 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
16:40:53.062 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
16:40:53.066 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa7092fd-d0c3-476f-864a-cccd1c902a7b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:40:53.067 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa7092fd-d0c3-476f-864a-cccd1c902a7b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000263af39fd80
16:40:53.067 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa7092fd-d0c3-476f-864a-cccd1c902a7b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000263af3a0000
16:40:53.068 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa7092fd-d0c3-476f-864a-cccd1c902a7b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:40:53.069 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa7092fd-d0c3-476f-864a-cccd1c902a7b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:40:53.083 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa7092fd-d0c3-476f-864a-cccd1c902a7b_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:40:54.546 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa7092fd-d0c3-476f-864a-cccd1c902a7b_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750322454255_127.0.0.1_9187
16:40:54.547 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa7092fd-d0c3-476f-864a-cccd1c902a7b_config-0] Notify connected event to listeners.
16:40:54.548 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa7092fd-d0c3-476f-864a-cccd1c902a7b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:40:54.549 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa7092fd-d0c3-476f-864a-cccd1c902a7b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000263af51a0a0
16:40:54.881 [main] INFO  c.h.g.HeJuGenApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:41:00.504 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9400"]
16:41:00.504 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:41:00.505 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:41:00.859 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:41:02.294 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:41:02.297 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:41:02.297 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:41:06.213 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:41:11.064 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bb88d7ed-5f37-4969-a153-c7c4496ee6f5
16:41:11.064 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb88d7ed-5f37-4969-a153-c7c4496ee6f5] RpcClient init label, labels = {module=naming, source=sdk}
16:41:11.068 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb88d7ed-5f37-4969-a153-c7c4496ee6f5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:41:11.069 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb88d7ed-5f37-4969-a153-c7c4496ee6f5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:41:11.071 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb88d7ed-5f37-4969-a153-c7c4496ee6f5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:41:11.072 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb88d7ed-5f37-4969-a153-c7c4496ee6f5] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
16:41:11.341 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb88d7ed-5f37-4969-a153-c7c4496ee6f5] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750322471170_127.0.0.1_9280
16:41:11.341 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb88d7ed-5f37-4969-a153-c7c4496ee6f5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:41:11.342 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb88d7ed-5f37-4969-a153-c7c4496ee6f5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000263af51a0a0
16:41:11.342 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb88d7ed-5f37-4969-a153-c7c4496ee6f5] Notify connected event to listeners.
16:41:11.434 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9400"]
16:41:11.497 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gen 192.168.2.43:9400 register finished
16:41:11.725 [main] INFO  c.h.g.HeJuGenApplication - [logStarted,61] - Started HeJuGenApplication in 20.968 seconds (JVM running for 22.438)
16:41:11.748 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gen, group=DEFAULT_GROUP
16:41:11.752 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gen-dev.yml, group=DEFAULT_GROUP
16:41:11.753 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gen.yml, group=DEFAULT_GROUP
16:41:11.903 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb88d7ed-5f37-4969-a153-c7c4496ee6f5] Receive server push request, request = NotifySubscriberRequest, requestId = 4
16:41:11.932 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb88d7ed-5f37-4969-a153-c7c4496ee6f5] Ack server push request, request = NotifySubscriberRequest, requestId = 4
16:41:12.319 [RMI TCP Connection(11)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:08:20.702 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb88d7ed-5f37-4969-a153-c7c4496ee6f5] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:08:20.702 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa7092fd-d0c3-476f-864a-cccd1c902a7b_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:08:20.919 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa7092fd-d0c3-476f-864a-cccd1c902a7b_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:20.919 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb88d7ed-5f37-4969-a153-c7c4496ee6f5] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.157 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa7092fd-d0c3-476f-864a-cccd1c902a7b_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.159 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb88d7ed-5f37-4969-a153-c7c4496ee6f5] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.473 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb88d7ed-5f37-4969-a153-c7c4496ee6f5] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.473 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa7092fd-d0c3-476f-864a-cccd1c902a7b_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.892 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa7092fd-d0c3-476f-864a-cccd1c902a7b_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:21.892 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb88d7ed-5f37-4969-a153-c7c4496ee6f5] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:22.413 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb88d7ed-5f37-4969-a153-c7c4496ee6f5] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:22.413 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa7092fd-d0c3-476f-864a-cccd1c902a7b_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:23.027 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa7092fd-d0c3-476f-864a-cccd1c902a7b_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:23.027 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb88d7ed-5f37-4969-a153-c7c4496ee6f5] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:23.744 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa7092fd-d0c3-476f-864a-cccd1c902a7b_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:23.744 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb88d7ed-5f37-4969-a153-c7c4496ee6f5] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:24.578 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa7092fd-d0c3-476f-864a-cccd1c902a7b_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:24.578 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb88d7ed-5f37-4969-a153-c7c4496ee6f5] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:24.841 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:08:25.175 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:08:25.487 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa7092fd-d0c3-476f-864a-cccd1c902a7b_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:25.487 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb88d7ed-5f37-4969-a153-c7c4496ee6f5] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:08:25.513 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:08:25.513 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@298ec094[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:08:25.513 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750322471170_127.0.0.1_9280
17:08:25.513 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@59f6d5bc[Running, pool size = 22, active threads = 0, queued tasks = 0, completed tasks = 361]
17:08:25.513 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb88d7ed-5f37-4969-a153-c7c4496ee6f5] Client is shutdown, stop reconnect to server
17:08:25.693 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:08:25.697 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:08:25.709 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:08:25.711 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
