10:18:51.155 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:18:52.257 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d2e9ab53-e0c7-4500-8047-43b169be1c57_config-0
10:18:52.353 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 48 ms to scan 1 urls, producing 3 keys and 6 values 
10:18:52.396 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 4 keys and 9 values 
10:18:52.425 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 23 ms to scan 1 urls, producing 3 keys and 10 values 
10:18:52.450 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 1 keys and 5 values 
10:18:52.464 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 7 values 
10:18:52.478 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
10:18:52.483 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2e9ab53-e0c7-4500-8047-43b169be1c57_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:18:52.484 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2e9ab53-e0c7-4500-8047-43b169be1c57_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000024c0139d500
10:18:52.485 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2e9ab53-e0c7-4500-8047-43b169be1c57_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000024c0139d720
10:18:52.486 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2e9ab53-e0c7-4500-8047-43b169be1c57_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:18:52.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2e9ab53-e0c7-4500-8047-43b169be1c57_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:18:52.504 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2e9ab53-e0c7-4500-8047-43b169be1c57_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
10:18:53.852 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2e9ab53-e0c7-4500-8047-43b169be1c57_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750558733587_127.0.0.1_5518
10:18:53.854 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2e9ab53-e0c7-4500-8047-43b169be1c57_config-0] Notify connected event to listeners.
10:18:53.855 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2e9ab53-e0c7-4500-8047-43b169be1c57_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:18:53.856 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2e9ab53-e0c7-4500-8047-43b169be1c57_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000024c01515418
10:18:54.086 [main] INFO  c.h.g.HeJuGenApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:18:59.004 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9400"]
10:18:59.006 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:18:59.006 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:18:59.248 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:19:00.439 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:19:00.441 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:19:00.581 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:19:05.817 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:19:12.371 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 86386ab3-af8d-4cf4-9577-d96b4a50e62b
10:19:12.372 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86386ab3-af8d-4cf4-9577-d96b4a50e62b] RpcClient init label, labels = {module=naming, source=sdk}
10:19:12.376 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86386ab3-af8d-4cf4-9577-d96b4a50e62b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:19:12.376 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86386ab3-af8d-4cf4-9577-d96b4a50e62b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:19:12.377 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86386ab3-af8d-4cf4-9577-d96b4a50e62b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:19:12.377 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86386ab3-af8d-4cf4-9577-d96b4a50e62b] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
10:19:12.528 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86386ab3-af8d-4cf4-9577-d96b4a50e62b] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750558752393_127.0.0.1_5545
10:19:12.528 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86386ab3-af8d-4cf4-9577-d96b4a50e62b] Notify connected event to listeners.
10:19:12.528 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86386ab3-af8d-4cf4-9577-d96b4a50e62b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:19:12.529 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86386ab3-af8d-4cf4-9577-d96b4a50e62b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000024c01515418
10:19:12.616 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9400"]
10:19:12.674 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gen 192.168.2.43:9400 register finished
10:19:12.894 [main] INFO  c.h.g.HeJuGenApplication - [logStarted,61] - Started HeJuGenApplication in 22.844 seconds (JVM running for 26.865)
10:19:12.917 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gen, group=DEFAULT_GROUP
10:19:12.921 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gen-dev.yml, group=DEFAULT_GROUP
10:19:12.922 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gen.yml, group=DEFAULT_GROUP
10:19:13.123 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86386ab3-af8d-4cf4-9577-d96b4a50e62b] Receive server push request, request = NotifySubscriberRequest, requestId = 5
10:19:13.151 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86386ab3-af8d-4cf4-9577-d96b4a50e62b] Ack server push request, request = NotifySubscriberRequest, requestId = 5
11:01:38.077 [http-nio-9400-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:02:41.114 [nacos-grpc-client-executor-533] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86386ab3-af8d-4cf4-9577-d96b4a50e62b] Receive server push request, request = NotifySubscriberRequest, requestId = 12
11:02:41.115 [nacos-grpc-client-executor-533] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86386ab3-af8d-4cf4-9577-d96b4a50e62b] Ack server push request, request = NotifySubscriberRequest, requestId = 12
13:47:38.776 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:47:38.799 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:47:39.149 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:47:39.151 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6c72e68b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:47:39.153 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750558752393_127.0.0.1_5545
13:47:39.158 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@757d6d68[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 2512]
13:47:39.158 [nacos-grpc-client-executor-2512] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750558752393_127.0.0.1_5545]Ignore complete event,isRunning:false,isAbandon=false
13:47:39.357 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:47:39.376 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:47:39.394 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:47:39.394 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:47:43.875 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:47:44.875 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e170ea89-0764-4b59-9a65-ba0de92b3ef2_config-0
13:47:44.949 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 38 ms to scan 1 urls, producing 3 keys and 6 values 
13:47:44.984 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
13:47:45.008 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 3 keys and 10 values 
13:47:45.021 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
13:47:45.034 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
13:47:45.045 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
13:47:45.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e170ea89-0764-4b59-9a65-ba0de92b3ef2_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:47:45.050 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e170ea89-0764-4b59-9a65-ba0de92b3ef2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001bfb13bfd80
13:47:45.050 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e170ea89-0764-4b59-9a65-ba0de92b3ef2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001bfb13c0000
13:47:45.052 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e170ea89-0764-4b59-9a65-ba0de92b3ef2_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:47:45.053 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e170ea89-0764-4b59-9a65-ba0de92b3ef2_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:47:45.075 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e170ea89-0764-4b59-9a65-ba0de92b3ef2_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
13:47:46.311 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e170ea89-0764-4b59-9a65-ba0de92b3ef2_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750571266078_127.0.0.1_13917
13:47:46.313 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e170ea89-0764-4b59-9a65-ba0de92b3ef2_config-0] Notify connected event to listeners.
13:47:46.313 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e170ea89-0764-4b59-9a65-ba0de92b3ef2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:47:46.315 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e170ea89-0764-4b59-9a65-ba0de92b3ef2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001bfb14fa0a0
13:47:46.557 [main] INFO  c.h.g.HeJuGenApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:47:50.664 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9400"]
13:47:50.665 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:47:50.665 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:47:50.965 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:47:51.832 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:47:51.836 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:47:51.836 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:47:54.967 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:47:58.505 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d90c2afd-3685-4564-973f-989230499b3b
13:47:58.506 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d90c2afd-3685-4564-973f-989230499b3b] RpcClient init label, labels = {module=naming, source=sdk}
13:47:58.508 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d90c2afd-3685-4564-973f-989230499b3b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:47:58.509 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d90c2afd-3685-4564-973f-989230499b3b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:47:58.509 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d90c2afd-3685-4564-973f-989230499b3b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:47:58.510 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d90c2afd-3685-4564-973f-989230499b3b] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
13:47:58.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d90c2afd-3685-4564-973f-989230499b3b] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750571278522_127.0.0.1_13932
13:47:58.645 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d90c2afd-3685-4564-973f-989230499b3b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:47:58.645 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d90c2afd-3685-4564-973f-989230499b3b] Notify connected event to listeners.
13:47:58.645 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d90c2afd-3685-4564-973f-989230499b3b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001bfb14fa0a0
13:47:58.728 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9400"]
13:47:58.779 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gen 192.168.2.43:9400 register finished
13:47:58.950 [main] INFO  c.h.g.HeJuGenApplication - [logStarted,61] - Started HeJuGenApplication in 16.03 seconds (JVM running for 17.521)
13:47:58.965 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gen, group=DEFAULT_GROUP
13:47:58.992 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gen-dev.yml, group=DEFAULT_GROUP
13:47:58.993 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gen.yml, group=DEFAULT_GROUP
13:47:59.141 [RMI TCP Connection(4)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:47:59.213 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d90c2afd-3685-4564-973f-989230499b3b] Receive server push request, request = NotifySubscriberRequest, requestId = 15
13:47:59.234 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d90c2afd-3685-4564-973f-989230499b3b] Ack server push request, request = NotifySubscriberRequest, requestId = 15
13:54:37.282 [nacos-grpc-client-executor-90] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d90c2afd-3685-4564-973f-989230499b3b] Receive server push request, request = NotifySubscriberRequest, requestId = 16
13:54:37.283 [nacos-grpc-client-executor-90] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d90c2afd-3685-4564-973f-989230499b3b] Ack server push request, request = NotifySubscriberRequest, requestId = 16
14:18:39.686 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:18:39.688 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:18:40.027 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:18:40.027 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2fc4849c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:18:40.027 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750571278522_127.0.0.1_13932
14:18:40.029 [nacos-grpc-client-executor-381] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750571278522_127.0.0.1_13932]Ignore complete event,isRunning:false,isAbandon=false
14:18:40.030 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7d0afc28[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 382]
14:18:40.176 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:18:40.179 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:18:40.190 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:18:40.191 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:18:53.347 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:18:54.013 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b8af7366-073a-4656-987d-c2c1a998eb3a_config-0
14:18:54.055 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 
14:18:54.073 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
14:18:54.088 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
14:18:54.095 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
14:18:54.102 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
14:18:54.108 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
14:18:54.110 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8af7366-073a-4656-987d-c2c1a998eb3a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:18:54.110 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8af7366-073a-4656-987d-c2c1a998eb3a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000002215839d500
14:18:54.111 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8af7366-073a-4656-987d-c2c1a998eb3a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002215839d720
14:18:54.111 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8af7366-073a-4656-987d-c2c1a998eb3a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:18:54.112 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8af7366-073a-4656-987d-c2c1a998eb3a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:18:54.118 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8af7366-073a-4656-987d-c2c1a998eb3a_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:18:54.912 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8af7366-073a-4656-987d-c2c1a998eb3a_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750573134701_127.0.0.1_3489
14:18:54.913 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8af7366-073a-4656-987d-c2c1a998eb3a_config-0] Notify connected event to listeners.
14:18:54.916 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8af7366-073a-4656-987d-c2c1a998eb3a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:18:54.920 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8af7366-073a-4656-987d-c2c1a998eb3a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000022158515418
14:18:55.097 [main] INFO  c.h.g.HeJuGenApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:18:57.409 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9400"]
14:18:57.409 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:18:57.409 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:18:57.524 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:18:58.025 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:18:58.027 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:18:58.027 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:18:59.985 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:19:03.047 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 603657b2-5f77-498f-9306-4bb2cdb08679
14:19:03.050 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [603657b2-5f77-498f-9306-4bb2cdb08679] RpcClient init label, labels = {module=naming, source=sdk}
14:19:03.052 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [603657b2-5f77-498f-9306-4bb2cdb08679] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:19:03.053 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [603657b2-5f77-498f-9306-4bb2cdb08679] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:19:03.053 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [603657b2-5f77-498f-9306-4bb2cdb08679] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:19:03.054 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [603657b2-5f77-498f-9306-4bb2cdb08679] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:19:03.201 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [603657b2-5f77-498f-9306-4bb2cdb08679] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750573143076_127.0.0.1_3524
14:19:03.201 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [603657b2-5f77-498f-9306-4bb2cdb08679] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:19:03.201 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [603657b2-5f77-498f-9306-4bb2cdb08679] Notify connected event to listeners.
14:19:03.201 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [603657b2-5f77-498f-9306-4bb2cdb08679] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000022158515418
14:19:03.272 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9400"]
14:19:03.324 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gen 192.168.2.43:9400 register finished
14:19:03.561 [main] INFO  c.h.g.HeJuGenApplication - [logStarted,61] - Started HeJuGenApplication in 10.794 seconds (JVM running for 21.32)
14:19:03.574 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gen, group=DEFAULT_GROUP
14:19:03.577 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gen-dev.yml, group=DEFAULT_GROUP
14:19:03.578 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gen.yml, group=DEFAULT_GROUP
14:19:03.781 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [603657b2-5f77-498f-9306-4bb2cdb08679] Receive server push request, request = NotifySubscriberRequest, requestId = 19
14:19:03.862 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [603657b2-5f77-498f-9306-4bb2cdb08679] Ack server push request, request = NotifySubscriberRequest, requestId = 19
14:19:36.657 [http-nio-9400-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:23:09.189 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:23:09.192 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:23:09.526 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:23:09.526 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1325c52c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:23:09.526 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750573143076_127.0.0.1_3524
14:23:09.534 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@18ad9e2b[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 59]
14:23:09.543 [nacos-grpc-client-executor-59] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1750573143076_127.0.0.1_3524]Ignore complete event,isRunning:false,isAbandon=false
14:23:09.699 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:23:09.701 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:23:09.707 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:23:09.708 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:23:13.456 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:23:14.053 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1e26f355-3b25-4d81-b64d-5f24e5247760_config-0
14:23:14.120 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
14:23:14.136 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 4 keys and 9 values 
14:23:14.144 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
14:23:14.154 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
14:23:14.166 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
14:23:14.173 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
14:23:14.177 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e26f355-3b25-4d81-b64d-5f24e5247760_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:23:14.177 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e26f355-3b25-4d81-b64d-5f24e5247760_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002725f3a0638
14:23:14.178 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e26f355-3b25-4d81-b64d-5f24e5247760_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002725f3a0858
14:23:14.178 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e26f355-3b25-4d81-b64d-5f24e5247760_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:23:14.179 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e26f355-3b25-4d81-b64d-5f24e5247760_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:23:14.187 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e26f355-3b25-4d81-b64d-5f24e5247760_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:23:14.921 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e26f355-3b25-4d81-b64d-5f24e5247760_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750573394740_127.0.0.1_4006
14:23:14.922 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e26f355-3b25-4d81-b64d-5f24e5247760_config-0] Notify connected event to listeners.
14:23:14.927 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e26f355-3b25-4d81-b64d-5f24e5247760_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:23:14.930 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e26f355-3b25-4d81-b64d-5f24e5247760_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002725f51a6e0
14:23:15.048 [main] INFO  c.h.g.HeJuGenApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:23:17.383 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9400"]
14:23:17.384 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:23:17.384 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:23:17.508 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:23:18.398 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:23:18.399 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:23:18.399 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:23:20.636 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:23:24.604 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9409a12b-045d-4e7b-960d-12bc7f1c945d
14:23:24.604 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9409a12b-045d-4e7b-960d-12bc7f1c945d] RpcClient init label, labels = {module=naming, source=sdk}
14:23:24.607 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9409a12b-045d-4e7b-960d-12bc7f1c945d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:23:24.607 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9409a12b-045d-4e7b-960d-12bc7f1c945d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:23:24.608 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9409a12b-045d-4e7b-960d-12bc7f1c945d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:23:24.609 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9409a12b-045d-4e7b-960d-12bc7f1c945d] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:23:24.749 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9409a12b-045d-4e7b-960d-12bc7f1c945d] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1750573404621_127.0.0.1_4033
14:23:24.749 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9409a12b-045d-4e7b-960d-12bc7f1c945d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:23:24.749 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9409a12b-045d-4e7b-960d-12bc7f1c945d] Notify connected event to listeners.
14:23:24.749 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9409a12b-045d-4e7b-960d-12bc7f1c945d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002725f51a6e0
14:23:24.943 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9400"]
14:23:24.995 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gen 192.168.2.43:9400 register finished
14:23:25.191 [main] INFO  c.h.g.HeJuGenApplication - [logStarted,61] - Started HeJuGenApplication in 12.281 seconds (JVM running for 13.131)
14:23:25.216 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gen, group=DEFAULT_GROUP
14:23:25.227 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gen-dev.yml, group=DEFAULT_GROUP
14:23:25.228 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gen.yml, group=DEFAULT_GROUP
14:23:25.397 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9409a12b-045d-4e7b-960d-12bc7f1c945d] Receive server push request, request = NotifySubscriberRequest, requestId = 22
14:23:25.430 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9409a12b-045d-4e7b-960d-12bc7f1c945d] Ack server push request, request = NotifySubscriberRequest, requestId = 22
14:23:25.611 [RMI TCP Connection(3)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
17:55:55.407 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9409a12b-045d-4e7b-960d-12bc7f1c945d] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:55:55.407 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e26f355-3b25-4d81-b64d-5f24e5247760_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:55:55.604 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e26f355-3b25-4d81-b64d-5f24e5247760_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:55.623 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9409a12b-045d-4e7b-960d-12bc7f1c945d] Fail to connect server, after trying 1 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:55.821 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e26f355-3b25-4d81-b64d-5f24e5247760_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:55.841 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9409a12b-045d-4e7b-960d-12bc7f1c945d] Fail to connect server, after trying 2 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:56.155 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e26f355-3b25-4d81-b64d-5f24e5247760_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:56.158 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9409a12b-045d-4e7b-960d-12bc7f1c945d] Fail to connect server, after trying 3 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:56.569 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e26f355-3b25-4d81-b64d-5f24e5247760_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:56.579 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9409a12b-045d-4e7b-960d-12bc7f1c945d] Fail to connect server, after trying 4 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:57.087 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e26f355-3b25-4d81-b64d-5f24e5247760_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:57.096 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9409a12b-045d-4e7b-960d-12bc7f1c945d] Fail to connect server, after trying 5 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:57.707 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e26f355-3b25-4d81-b64d-5f24e5247760_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:57.720 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9409a12b-045d-4e7b-960d-12bc7f1c945d] Fail to connect server, after trying 6 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:58.424 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e26f355-3b25-4d81-b64d-5f24e5247760_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:58.437 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9409a12b-045d-4e7b-960d-12bc7f1c945d] Fail to connect server, after trying 7 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:59.241 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e26f355-3b25-4d81-b64d-5f24e5247760_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:55:59.248 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9409a12b-045d-4e7b-960d-12bc7f1c945d] Fail to connect server, after trying 8 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:00.189 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e26f355-3b25-4d81-b64d-5f24e5247760_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:00.191 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9409a12b-045d-4e7b-960d-12bc7f1c945d] Fail to connect server, after trying 9 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:01.226 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e26f355-3b25-4d81-b64d-5f24e5247760_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:01.228 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9409a12b-045d-4e7b-960d-12bc7f1c945d] Fail to connect server, after trying 10 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:02.338 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e26f355-3b25-4d81-b64d-5f24e5247760_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:02.344 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9409a12b-045d-4e7b-960d-12bc7f1c945d] Fail to connect server, after trying 11 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:03.550 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e26f355-3b25-4d81-b64d-5f24e5247760_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:03.601 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9409a12b-045d-4e7b-960d-12bc7f1c945d] Fail to connect server, after trying 12 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:03.996 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:56:04.330 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:56:04.678 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:56:04.678 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@52070acc[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:56:04.678 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1750573404621_127.0.0.1_4033
17:56:04.678 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9409a12b-045d-4e7b-960d-12bc7f1c945d] Client is shutdown, stop reconnect to server
17:56:04.683 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2ecf577b[Running, pool size = 27, active threads = 0, queued tasks = 0, completed tasks = 2581]
17:56:04.866 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1e26f355-3b25-4d81-b64d-5f24e5247760_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = '127.0.0.1', server main port = 8848}, error = unknown
17:56:04.866 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:56:04.873 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:56:04.884 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:56:04.884 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
