09:41:00.418 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:41:01.650 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6f9d9a81-1eb3-419e-afbb-754398c79c95_config-0
09:41:01.756 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 43 ms to scan 1 urls, producing 3 keys and 6 values 
09:41:01.835 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 22 ms to scan 1 urls, producing 4 keys and 9 values 
09:41:01.848 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 3 keys and 10 values 
09:41:01.862 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:41:01.880 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 7 values 
09:41:01.891 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:41:01.896 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f9d9a81-1eb3-419e-afbb-754398c79c95_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:41:01.897 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f9d9a81-1eb3-419e-afbb-754398c79c95_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001f1d13b6af8
09:41:01.897 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f9d9a81-1eb3-419e-afbb-754398c79c95_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001f1d13b6d18
09:41:01.898 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f9d9a81-1eb3-419e-afbb-754398c79c95_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:41:01.899 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f9d9a81-1eb3-419e-afbb-754398c79c95_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:41:01.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f9d9a81-1eb3-419e-afbb-754398c79c95_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:41:03.313 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f9d9a81-1eb3-419e-afbb-754398c79c95_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751506863051_127.0.0.1_14916
09:41:03.314 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f9d9a81-1eb3-419e-afbb-754398c79c95_config-0] Notify connected event to listeners.
09:41:03.314 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f9d9a81-1eb3-419e-afbb-754398c79c95_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:41:03.315 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f9d9a81-1eb3-419e-afbb-754398c79c95_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001f1d14f0668
09:41:03.512 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:41:10.154 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:41:10.154 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:41:10.154 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:41:10.623 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:41:10.651 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:41:10.652 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:41:10.673 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751506870623'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

09:41:10.673 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
09:41:10.673 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:41:10.673 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@7f08c343
09:41:15.398 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:41:22.542 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0f25de2e-f479-4601-bc74-cd3b138332b3
09:41:22.542 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f25de2e-f479-4601-bc74-cd3b138332b3] RpcClient init label, labels = {module=naming, source=sdk}
09:41:22.542 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f25de2e-f479-4601-bc74-cd3b138332b3] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:41:22.542 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f25de2e-f479-4601-bc74-cd3b138332b3] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:41:22.542 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f25de2e-f479-4601-bc74-cd3b138332b3] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:41:22.542 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f25de2e-f479-4601-bc74-cd3b138332b3] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:41:22.676 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f25de2e-f479-4601-bc74-cd3b138332b3] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751506882542_127.0.0.1_1115
09:41:22.676 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f25de2e-f479-4601-bc74-cd3b138332b3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:41:22.676 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f25de2e-f479-4601-bc74-cd3b138332b3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001f1d14f0668
09:41:22.676 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f25de2e-f479-4601-bc74-cd3b138332b3] Notify connected event to listeners.
09:41:22.802 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job ************:9500 register finished
09:41:22.984 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 23.488 seconds (JVM running for 25.058)
09:41:23.019 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
09:41:23.021 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
09:41:23.025 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
09:41:23.080 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:41:23.093 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:41:23.290 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f25de2e-f479-4601-bc74-cd3b138332b3] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:41:23.290 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0f25de2e-f479-4601-bc74-cd3b138332b3] Ack server push request, request = NotifySubscriberRequest, requestId = 5
09:41:23.685 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:41:23.685 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@58ec59c4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:41:23.685 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751506882542_127.0.0.1_1115
09:41:23.685 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4397df96[Running, pool size = 12, active threads = 0, queued tasks = 0, completed tasks = 12]
09:41:23.719 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751506870623 shutting down.
09:41:23.719 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751506870623 paused.
09:41:23.719 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751506870623 shutdown complete.
09:41:23.720 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:41:23.721 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:41:23.731 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:41:23.731 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:03:38.138 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:03:39.094 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d6ac555c-65fb-4e32-8ecd-ce1f21bc0bc3_config-0
10:03:39.203 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 42 ms to scan 1 urls, producing 3 keys and 6 values 
10:03:39.267 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 4 keys and 9 values 
10:03:39.278 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
10:03:39.279 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
10:03:39.297 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 3 ms to scan 1 urls, producing 1 keys and 7 values 
10:03:39.309 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
10:03:39.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6ac555c-65fb-4e32-8ecd-ce1f21bc0bc3_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:03:39.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6ac555c-65fb-4e32-8ecd-ce1f21bc0bc3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000025c013b6d38
10:03:39.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6ac555c-65fb-4e32-8ecd-ce1f21bc0bc3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000025c013b6f58
10:03:39.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6ac555c-65fb-4e32-8ecd-ce1f21bc0bc3_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:03:39.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6ac555c-65fb-4e32-8ecd-ce1f21bc0bc3_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:03:39.337 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6ac555c-65fb-4e32-8ecd-ce1f21bc0bc3_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
10:03:40.504 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6ac555c-65fb-4e32-8ecd-ce1f21bc0bc3_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751508220249_127.0.0.1_7558
10:03:40.504 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6ac555c-65fb-4e32-8ecd-ce1f21bc0bc3_config-0] Notify connected event to listeners.
10:03:40.504 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6ac555c-65fb-4e32-8ecd-ce1f21bc0bc3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:03:40.507 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d6ac555c-65fb-4e32-8ecd-ce1f21bc0bc3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000025c014f0ad8
10:03:40.691 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:03:45.083 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
10:03:45.085 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:03:45.085 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:03:45.327 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:03:46.206 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:03:46.206 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:03:46.206 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:03:46.662 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:03:46.694 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:03:46.694 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:03:46.719 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751508226662'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

10:03:46.719 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
10:03:46.719 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:03:46.719 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@3677c508
10:03:51.027 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:03:57.928 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 326c214c-5fe1-4138-8e16-************
10:03:57.928 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] RpcClient init label, labels = {module=naming, source=sdk}
10:03:57.930 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:03:57.933 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:03:57.934 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:03:57.934 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
10:03:58.076 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751508237941_127.0.0.1_7583
10:03:58.077 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Notify connected event to listeners.
10:03:58.077 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:03:58.078 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000025c014f0ad8
10:03:58.164 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
10:03:58.219 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job ************:9500 register finished
10:03:58.384 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 21.129 seconds (JVM running for 22.511)
10:03:58.404 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
10:03:58.404 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
10:03:58.437 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
10:03:58.667 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Receive server push request, request = NotifySubscriberRequest, requestId = 39
10:03:58.690 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Ack server push request, request = NotifySubscriberRequest, requestId = 39
10:03:59.456 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751508226662 started.
10:04:54.622 [http-nio-9500-exec-10] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:07:47.752 [nacos-grpc-client-executor-53] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Receive server push request, request = NotifySubscriberRequest, requestId = 41
10:07:47.752 [nacos-grpc-client-executor-53] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Ack server push request, request = NotifySubscriberRequest, requestId = 41
10:08:01.491 [http-nio-9500-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:08:01.491 [http-nio-9500-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:12:59.181 [nacos-grpc-client-executor-121] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Receive server push request, request = NotifySubscriberRequest, requestId = 42
10:12:59.205 [nacos-grpc-client-executor-121] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Ack server push request, request = NotifySubscriberRequest, requestId = 42
10:14:10.115 [nacos-grpc-client-executor-123] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Receive server push request, request = NotifySubscriberRequest, requestId = 46
10:14:10.136 [nacos-grpc-client-executor-123] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Ack server push request, request = NotifySubscriberRequest, requestId = 46
10:14:10.146 [nacos-grpc-client-executor-127] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Receive server push request, request = NotifySubscriberRequest, requestId = 49
10:14:10.147 [nacos-grpc-client-executor-127] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Ack server push request, request = NotifySubscriberRequest, requestId = 49
10:14:10.156 [nacos-grpc-client-executor-128] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Receive server push request, request = NotifySubscriberRequest, requestId = 50
10:14:10.156 [nacos-grpc-client-executor-128] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Ack server push request, request = NotifySubscriberRequest, requestId = 50
10:14:10.165 [nacos-grpc-client-executor-129] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Receive server push request, request = NotifySubscriberRequest, requestId = 51
10:14:10.166 [nacos-grpc-client-executor-129] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Ack server push request, request = NotifySubscriberRequest, requestId = 51
10:14:10.177 [nacos-grpc-client-executor-130] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Receive server push request, request = NotifySubscriberRequest, requestId = 52
10:14:10.177 [nacos-grpc-client-executor-130] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Ack server push request, request = NotifySubscriberRequest, requestId = 52
10:14:10.187 [nacos-grpc-client-executor-131] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Receive server push request, request = NotifySubscriberRequest, requestId = 53
10:14:10.187 [nacos-grpc-client-executor-131] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Ack server push request, request = NotifySubscriberRequest, requestId = 53
10:14:10.197 [nacos-grpc-client-executor-132] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Receive server push request, request = NotifySubscriberRequest, requestId = 54
10:14:10.198 [nacos-grpc-client-executor-132] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Ack server push request, request = NotifySubscriberRequest, requestId = 54
10:15:15.103 [nacos-grpc-client-executor-146] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Receive server push request, request = NotifySubscriberRequest, requestId = 55
10:15:15.129 [nacos-grpc-client-executor-146] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Ack server push request, request = NotifySubscriberRequest, requestId = 55
10:15:55.422 [nacos-grpc-client-executor-155] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Receive server push request, request = NotifySubscriberRequest, requestId = 58
10:15:55.449 [nacos-grpc-client-executor-155] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [326c214c-5fe1-4138-8e16-************] Ack server push request, request = NotifySubscriberRequest, requestId = 58
10:18:40.703 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751508226662 paused.
10:18:40.781 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:18:40.784 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:18:41.109 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:18:41.110 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7eeeb40[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:18:41.110 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751508237941_127.0.0.1_7583
10:18:41.114 [nacos-grpc-client-executor-193] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751508237941_127.0.0.1_7583]Ignore complete event,isRunning:false,isAbandon=false
10:18:41.117 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6e59d521[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 194]
10:18:41.249 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751508226662 shutting down.
10:18:41.249 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751508226662 paused.
10:18:41.251 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751508226662 shutdown complete.
10:18:41.251 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:18:41.255 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:18:41.264 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:18:41.264 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:18:41.266 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:18:41.266 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:18:45.722 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:18:46.676 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 659a8ace-ed2f-442b-ad7a-f6b780d4663b_config-0
10:18:46.780 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 47 ms to scan 1 urls, producing 3 keys and 6 values 
10:18:46.854 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 4 keys and 9 values 
10:18:46.867 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
10:18:46.881 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
10:18:46.895 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
10:18:46.905 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
10:18:46.912 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [659a8ace-ed2f-442b-ad7a-f6b780d4663b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:18:46.913 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [659a8ace-ed2f-442b-ad7a-f6b780d4663b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000233ba39e480
10:18:46.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [659a8ace-ed2f-442b-ad7a-f6b780d4663b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000233ba39e6a0
10:18:46.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [659a8ace-ed2f-442b-ad7a-f6b780d4663b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:18:46.916 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [659a8ace-ed2f-442b-ad7a-f6b780d4663b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:18:46.935 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [659a8ace-ed2f-442b-ad7a-f6b780d4663b_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
10:18:48.394 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [659a8ace-ed2f-442b-ad7a-f6b780d4663b_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751509128169_127.0.0.1_10321
10:18:48.396 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [659a8ace-ed2f-442b-ad7a-f6b780d4663b_config-0] Notify connected event to listeners.
10:18:48.397 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [659a8ace-ed2f-442b-ad7a-f6b780d4663b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:18:48.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [659a8ace-ed2f-442b-ad7a-f6b780d4663b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000233ba518228
10:18:48.655 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:18:53.638 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
10:18:53.638 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:18:53.639 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:18:53.914 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:18:54.729 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:18:54.732 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:18:54.732 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:18:55.121 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:18:55.147 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:18:55.147 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:18:55.168 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751509135126'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

10:18:55.168 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
10:18:55.168 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:18:55.171 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@75882ac2
10:18:59.231 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:19:05.775 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f0217752-3774-44e7-a0b5-db6b1c06aa94
10:19:05.776 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] RpcClient init label, labels = {module=naming, source=sdk}
10:19:05.778 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:19:05.779 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:19:05.780 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:19:05.780 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
10:19:05.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751509145795_127.0.0.1_10423
10:19:05.915 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Notify connected event to listeners.
10:19:05.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:19:05.916 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000233ba518228
10:19:06.006 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
10:19:06.058 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job ************:9500 register finished
10:19:06.236 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 21.405 seconds (JVM running for 22.872)
10:19:06.256 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
10:19:06.258 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
10:19:06.264 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
10:19:06.528 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Receive server push request, request = NotifySubscriberRequest, requestId = 63
10:19:06.560 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Ack server push request, request = NotifySubscriberRequest, requestId = 63
10:19:06.714 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:19:07.408 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751509135126 started.
10:21:54.623 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Receive server push request, request = NotifySubscriberRequest, requestId = 65
10:21:54.625 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Ack server push request, request = NotifySubscriberRequest, requestId = 65
10:22:01.545 [http-nio-9500-exec-5] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:22:01.545 [http-nio-9500-exec-5] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:23:30.316 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Server healthy check fail, currentConnection = 1751509145795_127.0.0.1_10423
10:23:31.057 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:23:37.326 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [659a8ace-ed2f-442b-ad7a-f6b780d4663b_config-0] Server healthy check fail, currentConnection = 1751509128169_127.0.0.1_10321
10:23:37.328 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [659a8ace-ed2f-442b-ad7a-f6b780d4663b_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:23:40.909 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Success to connect a server [127.0.0.1:8848], connectionId = 1751509420792_127.0.0.1_11205
10:23:40.909 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Abandon prev connection, server is 127.0.0.1:8848, connectionId is 1751509145795_127.0.0.1_10423
10:23:40.909 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751509145795_127.0.0.1_10423
10:23:40.913 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [659a8ace-ed2f-442b-ad7a-f6b780d4663b_config-0] Success to connect a server [127.0.0.1:8848], connectionId = 1751509420792_127.0.0.1_11204
10:23:40.913 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [659a8ace-ed2f-442b-ad7a-f6b780d4663b_config-0] Abandon prev connection, server is 127.0.0.1:8848, connectionId is 1751509128169_127.0.0.1_10321
10:23:40.914 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751509128169_127.0.0.1_10321
10:23:40.916 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [659a8ace-ed2f-442b-ad7a-f6b780d4663b_config-0] Notify disconnected event to listeners
10:23:40.917 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Notify disconnected event to listeners
10:23:40.917 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [659a8ace-ed2f-442b-ad7a-f6b780d4663b_config-0] Notify connected event to listeners.
10:23:40.917 [nacos-grpc-client-executor-68] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751509145795_127.0.0.1_10423]Ignore complete event,isRunning:true,isAbandon=true
10:23:40.919 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Notify connected event to listeners.
10:23:40.919 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Server check success, currentServer is 127.0.0.1:8848 
10:23:44.264 [nacos-grpc-client-executor-75] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Receive server push request, request = NotifySubscriberRequest, requestId = 68
10:23:44.265 [nacos-grpc-client-executor-75] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Ack server push request, request = NotifySubscriberRequest, requestId = 68
10:23:44.377 [nacos-grpc-client-executor-76] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Receive server push request, request = NotifySubscriberRequest, requestId = 69
10:23:44.379 [nacos-grpc-client-executor-76] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Ack server push request, request = NotifySubscriberRequest, requestId = 69
10:33:00.422 [nacos-grpc-client-executor-187] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Receive server push request, request = NotifySubscriberRequest, requestId = 71
10:33:00.446 [nacos-grpc-client-executor-187] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Ack server push request, request = NotifySubscriberRequest, requestId = 71
10:33:37.588 [nacos-grpc-client-executor-195] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Receive server push request, request = NotifySubscriberRequest, requestId = 75
10:33:37.611 [nacos-grpc-client-executor-195] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Ack server push request, request = NotifySubscriberRequest, requestId = 75
10:40:02.477 [nacos-grpc-client-executor-273] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Receive server push request, request = NotifySubscriberRequest, requestId = 78
10:40:02.502 [nacos-grpc-client-executor-273] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Ack server push request, request = NotifySubscriberRequest, requestId = 78
10:40:33.296 [nacos-grpc-client-executor-279] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Receive server push request, request = NotifySubscriberRequest, requestId = 81
10:40:33.327 [nacos-grpc-client-executor-279] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Ack server push request, request = NotifySubscriberRequest, requestId = 81
10:42:59.138 [nacos-grpc-client-executor-309] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Receive server push request, request = NotifySubscriberRequest, requestId = 85
10:42:59.203 [nacos-grpc-client-executor-309] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Ack server push request, request = NotifySubscriberRequest, requestId = 85
10:43:19.254 [nacos-grpc-client-executor-313] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Receive server push request, request = NotifySubscriberRequest, requestId = 88
10:43:19.302 [nacos-grpc-client-executor-313] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Ack server push request, request = NotifySubscriberRequest, requestId = 88
11:23:38.608 [nacos-grpc-client-executor-795] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Receive server push request, request = NotifySubscriberRequest, requestId = 92
11:23:38.653 [nacos-grpc-client-executor-795] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Ack server push request, request = NotifySubscriberRequest, requestId = 92
11:24:07.537 [nacos-grpc-client-executor-801] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Receive server push request, request = NotifySubscriberRequest, requestId = 95
11:24:07.638 [nacos-grpc-client-executor-801] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Ack server push request, request = NotifySubscriberRequest, requestId = 95
11:37:40.646 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751509135126 paused.
11:37:40.698 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:37:40.701 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:37:41.010 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:37:41.010 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4417d2a8[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:37:41.010 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751509420792_127.0.0.1_11205
11:37:41.011 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2866614d[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 965]
11:37:41.012 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0217752-3774-44e7-a0b5-db6b1c06aa94] Notify disconnected event to listeners
11:37:41.164 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751509135126 shutting down.
11:37:41.164 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751509135126 paused.
11:37:41.165 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751509135126 shutdown complete.
11:37:41.165 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:37:41.169 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
11:37:41.173 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
11:37:41.173 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:37:41.173 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:37:41.173 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:37:46.682 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:37:47.260 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b73e66dc-f559-4713-bd78-7df3e07ab0ce_config-0
11:37:47.313 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
11:37:47.348 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
11:37:47.356 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
11:37:47.363 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
11:37:47.374 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
11:37:47.379 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
11:37:47.383 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b73e66dc-f559-4713-bd78-7df3e07ab0ce_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:37:47.384 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b73e66dc-f559-4713-bd78-7df3e07ab0ce_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001d8a43ceaf8
11:37:47.385 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b73e66dc-f559-4713-bd78-7df3e07ab0ce_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001d8a43ced18
11:37:47.385 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b73e66dc-f559-4713-bd78-7df3e07ab0ce_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:37:47.386 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b73e66dc-f559-4713-bd78-7df3e07ab0ce_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:37:47.394 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b73e66dc-f559-4713-bd78-7df3e07ab0ce_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:37:48.129 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b73e66dc-f559-4713-bd78-7df3e07ab0ce_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751513867955_127.0.0.1_5774
11:37:48.131 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b73e66dc-f559-4713-bd78-7df3e07ab0ce_config-0] Notify connected event to listeners.
11:37:48.132 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b73e66dc-f559-4713-bd78-7df3e07ab0ce_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:37:48.133 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b73e66dc-f559-4713-bd78-7df3e07ab0ce_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001d8a4508ad8
11:37:48.239 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:37:50.652 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
11:37:50.652 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:37:50.652 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:37:50.777 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:37:51.301 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:37:51.302 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:37:51.303 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:37:51.539 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
11:37:51.552 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
11:37:51.552 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
11:37:51.564 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751513871541'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

11:37:51.564 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
11:37:51.564 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
11:37:51.566 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@586b78d7
11:37:54.575 [main] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751513871541 shutting down.
11:37:54.575 [main] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751513871541 paused.
11:37:54.576 [main] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751513871541 shutdown complete.
11:37:54.576 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:37:54.578 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:37:54.584 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:37:54.584 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:37:54.585 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
11:46:01.230 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:46:03.139 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3facfaac-f0ba-4392-9375-40e48d5f25be_config-0
11:46:03.393 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 115 ms to scan 1 urls, producing 3 keys and 6 values 
11:46:03.650 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 56 ms to scan 1 urls, producing 4 keys and 9 values 
11:46:03.694 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 36 ms to scan 1 urls, producing 3 keys and 10 values 
11:46:03.733 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 1 keys and 5 values 
11:46:03.778 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 38 ms to scan 1 urls, producing 1 keys and 7 values 
11:46:03.815 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 29 ms to scan 1 urls, producing 2 keys and 8 values 
11:46:03.832 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3facfaac-f0ba-4392-9375-40e48d5f25be_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:46:03.836 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3facfaac-f0ba-4392-9375-40e48d5f25be_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000024b2039eaf8
11:46:03.841 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3facfaac-f0ba-4392-9375-40e48d5f25be_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000024b2039ed18
11:46:03.847 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3facfaac-f0ba-4392-9375-40e48d5f25be_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:46:03.853 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3facfaac-f0ba-4392-9375-40e48d5f25be_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:46:03.898 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3facfaac-f0ba-4392-9375-40e48d5f25be_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:46:10.008 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3facfaac-f0ba-4392-9375-40e48d5f25be_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751514369368_127.0.0.1_9251
11:46:10.012 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3facfaac-f0ba-4392-9375-40e48d5f25be_config-0] Notify connected event to listeners.
11:46:10.015 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3facfaac-f0ba-4392-9375-40e48d5f25be_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:46:10.018 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3facfaac-f0ba-4392-9375-40e48d5f25be_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000024b2051b2d0
11:46:10.643 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:46:20.419 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
11:46:20.420 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:46:20.420 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:46:20.730 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:46:21.826 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:46:21.828 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:46:21.829 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:46:22.424 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
11:46:22.454 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
11:46:22.455 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
11:46:22.483 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751514382429'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

11:46:22.483 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
11:46:22.483 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
11:46:22.487 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@586b78d7
11:46:27.417 [main] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751514382429 shutting down.
11:46:27.418 [main] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751514382429 paused.
11:46:27.418 [main] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751514382429 shutdown complete.
11:46:27.419 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:46:27.424 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:46:27.438 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:46:27.439 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:46:27.443 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
12:00:15.909 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:00:17.444 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d3c3ada4-9139-4aba-82a8-2edfa72c65a0_config-0
12:00:17.568 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 53 ms to scan 1 urls, producing 3 keys and 6 values 
12:00:17.654 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 4 keys and 9 values 
12:00:17.678 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 3 keys and 10 values 
12:00:17.698 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 5 values 
12:00:17.720 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
12:00:17.736 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
12:00:17.741 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3c3ada4-9139-4aba-82a8-2edfa72c65a0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:00:17.742 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3c3ada4-9139-4aba-82a8-2edfa72c65a0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001e8453b6480
12:00:17.742 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3c3ada4-9139-4aba-82a8-2edfa72c65a0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001e8453b66a0
12:00:17.743 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3c3ada4-9139-4aba-82a8-2edfa72c65a0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:00:17.745 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3c3ada4-9139-4aba-82a8-2edfa72c65a0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:00:17.756 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3c3ada4-9139-4aba-82a8-2edfa72c65a0_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:00:19.155 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3c3ada4-9139-4aba-82a8-2edfa72c65a0_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751515218886_127.0.0.1_14447
12:00:19.158 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3c3ada4-9139-4aba-82a8-2edfa72c65a0_config-0] Notify connected event to listeners.
12:00:19.161 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3c3ada4-9139-4aba-82a8-2edfa72c65a0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:00:19.163 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d3c3ada4-9139-4aba-82a8-2edfa72c65a0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001e8454f0228
12:00:19.373 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:00:23.874 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
12:00:23.875 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:00:23.876 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:00:24.098 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:00:25.242 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:00:25.244 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:00:25.245 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:00:26.035 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
12:00:26.079 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
12:00:26.080 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
12:00:26.123 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751515226048'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

12:00:26.124 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
12:00:26.124 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
12:00:26.129 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@5cb6b81b
12:00:37.275 [main] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751515226048 shutting down.
12:00:37.276 [main] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751515226048 paused.
12:00:37.276 [main] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751515226048 shutdown complete.
12:00:37.277 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:00:37.280 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:00:37.443 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:00:37.443 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:00:37.458 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
12:06:23.738 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:06:24.275 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d5d2c163-4647-4313-91c2-0186f833663d_config-0
12:06:24.332 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
12:06:24.373 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
12:06:24.387 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
12:06:24.395 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
12:06:24.404 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
12:06:24.410 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
12:06:24.411 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d2c163-4647-4313-91c2-0186f833663d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:06:24.412 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d2c163-4647-4313-91c2-0186f833663d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002cb913bdd70
12:06:24.413 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d2c163-4647-4313-91c2-0186f833663d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002cb913bdf90
12:06:24.413 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d2c163-4647-4313-91c2-0186f833663d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:06:24.414 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d2c163-4647-4313-91c2-0186f833663d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:06:24.420 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d2c163-4647-4313-91c2-0186f833663d_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:06:25.185 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d2c163-4647-4313-91c2-0186f833663d_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751515585009_127.0.0.1_2925
12:06:25.185 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d2c163-4647-4313-91c2-0186f833663d_config-0] Notify connected event to listeners.
12:06:25.185 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d2c163-4647-4313-91c2-0186f833663d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:06:25.190 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5d2c163-4647-4313-91c2-0186f833663d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002cb914f8228
12:06:25.310 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:06:27.704 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
12:06:27.704 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:06:27.704 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:06:27.832 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:06:28.360 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:06:28.361 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:06:28.361 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:06:28.587 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
12:06:28.601 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
12:06:28.601 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
12:06:28.614 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751515588590'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

12:06:28.615 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
12:06:28.615 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
12:06:28.617 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@4518497e
12:06:30.899 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:06:34.960 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of de747213-635e-4b76-927d-e6d998a136e0
12:06:34.961 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de747213-635e-4b76-927d-e6d998a136e0] RpcClient init label, labels = {module=naming, source=sdk}
12:06:34.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de747213-635e-4b76-927d-e6d998a136e0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:06:34.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de747213-635e-4b76-927d-e6d998a136e0] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:06:34.964 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de747213-635e-4b76-927d-e6d998a136e0] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:06:34.964 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de747213-635e-4b76-927d-e6d998a136e0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:06:35.101 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de747213-635e-4b76-927d-e6d998a136e0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751515594978_127.0.0.1_2992
12:06:35.101 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de747213-635e-4b76-927d-e6d998a136e0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:06:35.101 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de747213-635e-4b76-927d-e6d998a136e0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002cb914f8228
12:06:35.101 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de747213-635e-4b76-927d-e6d998a136e0] Notify connected event to listeners.
12:06:35.153 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
12:06:35.182 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job ************:9500 register finished
12:06:35.388 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 12.127 seconds (JVM running for 12.958)
12:06:35.540 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
12:06:35.542 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
12:06:35.627 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
12:06:35.696 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de747213-635e-4b76-927d-e6d998a136e0] Receive server push request, request = NotifySubscriberRequest, requestId = 111
12:06:35.754 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de747213-635e-4b76-927d-e6d998a136e0] Ack server push request, request = NotifySubscriberRequest, requestId = 111
12:06:35.812 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:06:36.442 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751515588590 started.
12:08:18.499 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de747213-635e-4b76-927d-e6d998a136e0] Receive server push request, request = NotifySubscriberRequest, requestId = 112
12:08:18.499 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de747213-635e-4b76-927d-e6d998a136e0] Ack server push request, request = NotifySubscriberRequest, requestId = 112
12:08:31.861 [http-nio-9500-exec-10] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
12:08:31.862 [http-nio-9500-exec-10] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:09:22.427 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751515588590 paused.
12:09:22.591 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:09:22.594 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:09:22.904 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:09:22.904 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@51aeaa2[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:09:22.905 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751515594978_127.0.0.1_2992
12:09:22.911 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@473f24c3[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 44]
12:09:22.919 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751515594978_127.0.0.1_2992]Ignore complete event,isRunning:false,isAbandon=false
12:09:23.055 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751515588590 shutting down.
12:09:23.055 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751515588590 paused.
12:09:23.056 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751515588590 shutdown complete.
12:09:23.058 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:09:23.062 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
12:09:23.067 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
12:09:23.068 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:09:23.069 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:09:23.069 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:09:28.914 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:09:29.481 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1d5456f7-564c-48f4-a678-4612e3516dc5_config-0
12:09:29.532 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 
12:09:29.571 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
12:09:29.579 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
12:09:29.586 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
12:09:29.593 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
12:09:29.600 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
12:09:29.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d5456f7-564c-48f4-a678-4612e3516dc5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:09:29.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d5456f7-564c-48f4-a678-4612e3516dc5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000013f8939ed38
12:09:29.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d5456f7-564c-48f4-a678-4612e3516dc5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000013f8939ef58
12:09:29.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d5456f7-564c-48f4-a678-4612e3516dc5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:09:29.604 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d5456f7-564c-48f4-a678-4612e3516dc5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:09:29.612 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d5456f7-564c-48f4-a678-4612e3516dc5_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:09:30.398 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d5456f7-564c-48f4-a678-4612e3516dc5_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751515770222_127.0.0.1_4237
12:09:30.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d5456f7-564c-48f4-a678-4612e3516dc5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:09:30.400 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d5456f7-564c-48f4-a678-4612e3516dc5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000013f89519450
12:09:30.401 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d5456f7-564c-48f4-a678-4612e3516dc5_config-0] Notify connected event to listeners.
12:09:30.675 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:09:33.308 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
12:09:33.308 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:09:33.308 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:09:33.439 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:09:34.975 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:09:34.977 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:09:34.977 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:09:35.391 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
12:09:35.419 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
12:09:35.420 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
12:09:35.444 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751515775396'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

12:09:35.445 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
12:09:35.445 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
12:09:35.448 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@4c5c0306
12:09:40.128 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:09:49.282 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a0783afe-c52c-43ee-b0c7-1861889b78fa
12:09:49.283 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0783afe-c52c-43ee-b0c7-1861889b78fa] RpcClient init label, labels = {module=naming, source=sdk}
12:09:49.285 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0783afe-c52c-43ee-b0c7-1861889b78fa] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:09:49.286 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0783afe-c52c-43ee-b0c7-1861889b78fa] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:09:49.286 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0783afe-c52c-43ee-b0c7-1861889b78fa] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:09:49.287 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0783afe-c52c-43ee-b0c7-1861889b78fa] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:09:49.420 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0783afe-c52c-43ee-b0c7-1861889b78fa] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751515789298_127.0.0.1_4388
12:09:49.420 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0783afe-c52c-43ee-b0c7-1861889b78fa] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:09:49.420 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0783afe-c52c-43ee-b0c7-1861889b78fa] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000013f89519450
12:09:49.420 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0783afe-c52c-43ee-b0c7-1861889b78fa] Notify connected event to listeners.
12:09:49.493 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
12:09:49.591 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job ************:9500 register finished
12:09:49.774 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 21.408 seconds (JVM running for 22.278)
12:09:49.787 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
12:09:49.789 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
12:09:49.794 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
12:09:50.008 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0783afe-c52c-43ee-b0c7-1861889b78fa] Receive server push request, request = NotifySubscriberRequest, requestId = 117
12:09:50.029 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a0783afe-c52c-43ee-b0c7-1861889b78fa] Ack server push request, request = NotifySubscriberRequest, requestId = 117
12:09:50.567 [RMI TCP Connection(10)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:09:50.970 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751515775396 started.
12:10:03.769 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751515775396 paused.
12:10:03.823 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:10:03.827 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:10:04.136 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:10:04.136 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3b30fda5[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:10:04.138 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751515789298_127.0.0.1_4388
12:10:04.169 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751515789298_127.0.0.1_4388]Ignore complete event,isRunning:false,isAbandon=false
12:10:04.172 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5d2abcfc[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 12]
12:10:04.304 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751515775396 shutting down.
12:10:04.304 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751515775396 paused.
12:10:04.306 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751515775396 shutdown complete.
12:10:04.308 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:10:04.311 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:10:04.318 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:10:04.319 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:10:09.643 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:10:10.687 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 27d9568c-2fa9-40c5-8059-bbbff62b4e43_config-0
12:10:10.784 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 48 ms to scan 1 urls, producing 3 keys and 6 values 
12:10:10.857 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 4 keys and 9 values 
12:10:10.869 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
12:10:10.883 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
12:10:10.898 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
12:10:10.911 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
12:10:10.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27d9568c-2fa9-40c5-8059-bbbff62b4e43_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:10:10.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27d9568c-2fa9-40c5-8059-bbbff62b4e43_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000023a0139eaf8
12:10:10.916 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27d9568c-2fa9-40c5-8059-bbbff62b4e43_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000023a0139ed18
12:10:10.917 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27d9568c-2fa9-40c5-8059-bbbff62b4e43_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:10:10.918 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27d9568c-2fa9-40c5-8059-bbbff62b4e43_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:10:10.931 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27d9568c-2fa9-40c5-8059-bbbff62b4e43_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:10:12.204 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27d9568c-2fa9-40c5-8059-bbbff62b4e43_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751515811959_127.0.0.1_4594
12:10:12.206 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27d9568c-2fa9-40c5-8059-bbbff62b4e43_config-0] Notify connected event to listeners.
12:10:12.206 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27d9568c-2fa9-40c5-8059-bbbff62b4e43_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:10:12.207 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [27d9568c-2fa9-40c5-8059-bbbff62b4e43_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023a01518fb0
12:10:12.413 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:10:16.872 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
12:10:16.873 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:10:16.873 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:10:17.123 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:10:18.085 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:10:18.088 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:10:18.088 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:10:18.523 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
12:10:18.548 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
12:10:18.548 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
12:10:18.570 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751515818529'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

12:10:18.571 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
12:10:18.571 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
12:10:18.573 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@586b78d7
12:10:22.590 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:10:29.057 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8b76a64d-e55f-40c9-bc74-26f7e4286578
12:10:29.058 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b76a64d-e55f-40c9-bc74-26f7e4286578] RpcClient init label, labels = {module=naming, source=sdk}
12:10:29.060 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b76a64d-e55f-40c9-bc74-26f7e4286578] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:10:29.061 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b76a64d-e55f-40c9-bc74-26f7e4286578] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:10:29.061 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b76a64d-e55f-40c9-bc74-26f7e4286578] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:10:29.062 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b76a64d-e55f-40c9-bc74-26f7e4286578] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:10:29.205 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b76a64d-e55f-40c9-bc74-26f7e4286578] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751515829077_127.0.0.1_4731
12:10:29.206 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b76a64d-e55f-40c9-bc74-26f7e4286578] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:10:29.207 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b76a64d-e55f-40c9-bc74-26f7e4286578] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023a01518fb0
12:10:29.208 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b76a64d-e55f-40c9-bc74-26f7e4286578] Notify connected event to listeners.
12:10:29.297 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
12:10:29.359 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job ************:9500 register finished
12:10:29.538 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 20.787 seconds (JVM running for 22.19)
12:10:29.558 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
12:10:29.559 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
12:10:29.565 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
12:10:29.696 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:10:29.812 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b76a64d-e55f-40c9-bc74-26f7e4286578] Receive server push request, request = NotifySubscriberRequest, requestId = 120
12:10:29.838 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b76a64d-e55f-40c9-bc74-26f7e4286578] Ack server push request, request = NotifySubscriberRequest, requestId = 120
12:10:30.583 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751515818529 started.
12:11:32.489 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751515818529 paused.
12:11:32.565 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:11:32.577 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:11:32.899 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:11:32.900 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7e69d798[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:11:32.902 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751515829077_127.0.0.1_4731
12:11:32.906 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751515829077_127.0.0.1_4731]Ignore complete event,isRunning:false,isAbandon=false
12:11:32.909 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@32ddb7a7[Running, pool size = 8, active threads = 0, queued tasks = 0, completed tasks = 25]
12:11:33.065 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751515818529 shutting down.
12:11:33.065 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751515818529 paused.
12:11:33.066 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751515818529 shutdown complete.
12:11:33.068 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:11:33.073 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:11:33.081 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:11:33.082 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:11:37.255 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:11:38.185 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6d201163-d95a-498d-b821-0888c09eb3eb_config-0
12:11:38.281 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
12:11:38.348 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 4 keys and 9 values 
12:11:38.360 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
12:11:38.374 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
12:11:38.388 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
12:11:38.402 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
12:11:38.408 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d201163-d95a-498d-b821-0888c09eb3eb_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:11:38.409 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d201163-d95a-498d-b821-0888c09eb3eb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000023f5839e8d8
12:11:38.409 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d201163-d95a-498d-b821-0888c09eb3eb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000023f5839eaf8
12:11:38.410 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d201163-d95a-498d-b821-0888c09eb3eb_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:11:38.412 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d201163-d95a-498d-b821-0888c09eb3eb_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:11:38.424 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d201163-d95a-498d-b821-0888c09eb3eb_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:11:39.613 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d201163-d95a-498d-b821-0888c09eb3eb_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751515899371_127.0.0.1_5110
12:11:39.615 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d201163-d95a-498d-b821-0888c09eb3eb_config-0] Notify connected event to listeners.
12:11:39.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d201163-d95a-498d-b821-0888c09eb3eb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:11:39.618 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6d201163-d95a-498d-b821-0888c09eb3eb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023f58518ad8
12:11:39.808 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:11:45.168 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
12:11:45.169 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:11:45.171 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:11:45.611 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:11:46.881 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:11:46.884 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:11:46.884 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:11:47.527 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
12:11:47.565 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
12:11:47.565 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
12:11:47.598 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751515907534'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

12:11:47.599 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
12:11:47.599 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
12:11:47.604 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@408b2ae2
12:11:53.905 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:12:02.941 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1d557d55-22a4-44a9-9c02-2ac0b97865b5
12:12:02.944 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] RpcClient init label, labels = {module=naming, source=sdk}
12:12:02.948 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:12:02.949 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:12:02.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:12:02.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:12:03.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751515922963_127.0.0.1_5223
12:12:03.097 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:12:03.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000023f58518ad8
12:12:03.098 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] Notify connected event to listeners.
12:12:03.186 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
12:12:03.243 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job ************:9500 register finished
12:12:03.418 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 27.03 seconds (JVM running for 28.513)
12:12:03.438 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
12:12:03.438 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
12:12:03.445 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
12:12:03.661 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:12:03.745 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] Receive server push request, request = NotifySubscriberRequest, requestId = 123
12:12:03.771 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] Ack server push request, request = NotifySubscriberRequest, requestId = 123
12:12:04.446 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751515907534 started.
12:12:25.480 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] Receive server push request, request = NotifySubscriberRequest, requestId = 124
12:12:25.480 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] Ack server push request, request = NotifySubscriberRequest, requestId = 124
12:16:14.511 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] Receive server push request, request = NotifySubscriberRequest, requestId = 126
12:16:14.539 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] Ack server push request, request = NotifySubscriberRequest, requestId = 126
12:16:14.551 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] Receive server push request, request = NotifySubscriberRequest, requestId = 129
12:16:14.552 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] Ack server push request, request = NotifySubscriberRequest, requestId = 129
12:16:14.562 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] Receive server push request, request = NotifySubscriberRequest, requestId = 130
12:16:14.563 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] Ack server push request, request = NotifySubscriberRequest, requestId = 130
12:16:14.572 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] Receive server push request, request = NotifySubscriberRequest, requestId = 131
12:16:14.573 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] Ack server push request, request = NotifySubscriberRequest, requestId = 131
12:16:14.841 [http-nio-9500-exec-8] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
12:16:14.841 [http-nio-9500-exec-8] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
12:16:23.187 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] Receive server push request, request = NotifySubscriberRequest, requestId = 133
12:16:23.195 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] Ack server push request, request = NotifySubscriberRequest, requestId = 133
12:25:09.755 [nacos-grpc-client-executor-136] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] Receive server push request, request = NotifySubscriberRequest, requestId = 139
12:25:09.782 [nacos-grpc-client-executor-136] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] Ack server push request, request = NotifySubscriberRequest, requestId = 139
12:25:41.659 [nacos-grpc-client-executor-144] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] Receive server push request, request = NotifySubscriberRequest, requestId = 143
12:25:41.680 [nacos-grpc-client-executor-144] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1d557d55-22a4-44a9-9c02-2ac0b97865b5] Ack server push request, request = NotifySubscriberRequest, requestId = 143
12:32:13.737 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751515907534 paused.
12:32:13.932 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:32:13.951 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:32:14.272 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:32:14.273 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@74ab6c83[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:32:14.274 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751515922963_127.0.0.1_5223
12:32:14.279 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@28b8f1b6[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 225]
12:32:14.279 [nacos-grpc-client-executor-225] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751515922963_127.0.0.1_5223]Ignore complete event,isRunning:false,isAbandon=false
12:32:14.503 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751515907534 shutting down.
12:32:14.503 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751515907534 paused.
12:32:14.504 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751515907534 shutdown complete.
12:32:14.505 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:32:14.511 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
12:32:14.522 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
12:32:14.522 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:32:14.524 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:32:14.525 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:33:50.888 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:33:51.904 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b07ea1bf-878b-4548-b2f3-e0515f965c37_config-0
12:33:52.066 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 58 ms to scan 1 urls, producing 3 keys and 6 values 
12:33:52.130 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 4 keys and 9 values 
12:33:52.143 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
12:33:52.155 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
12:33:52.169 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
12:33:52.180 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
12:33:52.184 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07ea1bf-878b-4548-b2f3-e0515f965c37_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:33:52.185 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07ea1bf-878b-4548-b2f3-e0515f965c37_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002135f39eaf8
12:33:52.185 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07ea1bf-878b-4548-b2f3-e0515f965c37_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002135f39ed18
12:33:52.186 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07ea1bf-878b-4548-b2f3-e0515f965c37_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:33:52.187 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07ea1bf-878b-4548-b2f3-e0515f965c37_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:33:52.202 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07ea1bf-878b-4548-b2f3-e0515f965c37_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:33:53.448 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07ea1bf-878b-4548-b2f3-e0515f965c37_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751517233209_127.0.0.1_11523
12:33:53.450 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07ea1bf-878b-4548-b2f3-e0515f965c37_config-0] Notify connected event to listeners.
12:33:53.451 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07ea1bf-878b-4548-b2f3-e0515f965c37_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:33:53.453 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07ea1bf-878b-4548-b2f3-e0515f965c37_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002135f518ad8
12:33:53.643 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:33:58.181 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
12:33:58.182 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:33:58.182 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:33:58.439 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:33:59.348 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:33:59.350 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:33:59.350 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:33:59.796 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
12:33:59.819 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
12:33:59.819 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
12:33:59.842 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751517239800'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

12:33:59.842 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
12:33:59.842 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
12:33:59.845 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@586b78d7
12:34:03.694 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:34:10.187 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 19b90e40-effd-4eae-8399-26d25402de96
12:34:10.187 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19b90e40-effd-4eae-8399-26d25402de96] RpcClient init label, labels = {module=naming, source=sdk}
12:34:10.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19b90e40-effd-4eae-8399-26d25402de96] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:34:10.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19b90e40-effd-4eae-8399-26d25402de96] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:34:10.190 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19b90e40-effd-4eae-8399-26d25402de96] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:34:10.192 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19b90e40-effd-4eae-8399-26d25402de96] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:34:10.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19b90e40-effd-4eae-8399-26d25402de96] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751517250204_127.0.0.1_11656
12:34:10.333 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19b90e40-effd-4eae-8399-26d25402de96] Notify connected event to listeners.
12:34:10.333 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19b90e40-effd-4eae-8399-26d25402de96] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:34:10.333 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19b90e40-effd-4eae-8399-26d25402de96] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002135f518ad8
12:34:10.421 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
12:34:10.473 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job ************:9500 register finished
12:34:10.663 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 20.658 seconds (JVM running for 22.023)
12:34:10.684 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
12:34:10.685 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
12:34:10.693 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
12:34:10.916 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:34:10.925 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19b90e40-effd-4eae-8399-26d25402de96] Receive server push request, request = NotifySubscriberRequest, requestId = 165
12:34:10.953 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19b90e40-effd-4eae-8399-26d25402de96] Ack server push request, request = NotifySubscriberRequest, requestId = 165
12:34:11.731 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751517239800 started.
12:36:07.886 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19b90e40-effd-4eae-8399-26d25402de96] Receive server push request, request = NotifySubscriberRequest, requestId = 166
12:36:07.886 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [19b90e40-effd-4eae-8399-26d25402de96] Ack server push request, request = NotifySubscriberRequest, requestId = 166
12:37:49.229 [http-nio-9500-exec-10] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
12:37:49.230 [http-nio-9500-exec-10] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
13:33:05.774 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751517239800 paused.
13:33:05.880 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:33:05.883 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:33:06.197 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:33:06.197 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@57afa347[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:33:06.197 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751517250204_127.0.0.1_11656
13:33:06.200 [nacos-grpc-client-executor-728] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751517250204_127.0.0.1_11656]Ignore complete event,isRunning:false,isAbandon=false
13:33:06.203 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@23cb4924[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 729]
13:33:06.357 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751517239800 shutting down.
13:33:06.358 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751517239800 paused.
13:33:06.358 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751517239800 shutdown complete.
13:33:06.360 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:33:06.364 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
13:33:06.374 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
13:33:06.374 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:33:06.376 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:33:06.376 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:04:24.967 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:04:26.721 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 486b60b3-b3fa-4731-bb33-58696e31baf5_config-0
14:04:26.893 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 111 ms to scan 1 urls, producing 3 keys and 6 values 
14:04:26.955 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 4 keys and 9 values 
14:04:26.988 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 3 keys and 10 values 
14:04:27.005 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
14:04:27.026 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
14:04:27.042 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
14:04:27.047 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [486b60b3-b3fa-4731-bb33-58696e31baf5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:04:27.048 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [486b60b3-b3fa-4731-bb33-58696e31baf5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000029f013c0b08
14:04:27.049 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [486b60b3-b3fa-4731-bb33-58696e31baf5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000029f013c0d28
14:04:27.050 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [486b60b3-b3fa-4731-bb33-58696e31baf5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:04:27.050 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [486b60b3-b3fa-4731-bb33-58696e31baf5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:04:27.067 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [486b60b3-b3fa-4731-bb33-58696e31baf5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:04:31.029 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [486b60b3-b3fa-4731-bb33-58696e31baf5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:04:34.037 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [486b60b3-b3fa-4731-bb33-58696e31baf5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:04:37.060 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [486b60b3-b3fa-4731-bb33-58696e31baf5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:04:37.059 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [486b60b3-b3fa-4731-bb33-58696e31baf5_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:04:37.061 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [486b60b3-b3fa-4731-bb33-58696e31baf5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000029f014d4228
14:04:39.165 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:04:43.204 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [486b60b3-b3fa-4731-bb33-58696e31baf5_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:04:44.314 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
14:04:44.315 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:04:44.316 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:04:44.606 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:04:45.672 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:04:45.674 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:04:45.675 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:04:46.250 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:04:46.285 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:04:46.287 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:04:46.319 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751522686256'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

14:04:46.321 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
14:04:46.321 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:04:46.325 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@3e0b1f03
14:04:46.421 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [486b60b3-b3fa-4731-bb33-58696e31baf5_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:04:49.734 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [486b60b3-b3fa-4731-bb33-58696e31baf5_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:04:50.022 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:04:53.155 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [486b60b3-b3fa-4731-bb33-58696e31baf5_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:04:54.814 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c02d0605-eac3-4d40-9e43-a4d5972b613a
14:04:54.815 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c02d0605-eac3-4d40-9e43-a4d5972b613a] RpcClient init label, labels = {module=naming, source=sdk}
14:04:54.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c02d0605-eac3-4d40-9e43-a4d5972b613a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:04:54.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c02d0605-eac3-4d40-9e43-a4d5972b613a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:04:54.818 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c02d0605-eac3-4d40-9e43-a4d5972b613a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:04:54.818 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c02d0605-eac3-4d40-9e43-a4d5972b613a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:04:56.679 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [486b60b3-b3fa-4731-bb33-58696e31baf5_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:04:57.828 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c02d0605-eac3-4d40-9e43-a4d5972b613a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:05:00.301 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [486b60b3-b3fa-4731-bb33-58696e31baf5_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:05:00.846 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c02d0605-eac3-4d40-9e43-a4d5972b613a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:05:03.865 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c02d0605-eac3-4d40-9e43-a4d5972b613a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:05:03.865 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c02d0605-eac3-4d40-9e43-a4d5972b613a] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:05:03.865 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c02d0605-eac3-4d40-9e43-a4d5972b613a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000029f014d4228
14:05:04.037 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [486b60b3-b3fa-4731-bb33-58696e31baf5_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:05:04.190 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
14:05:05.138 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:05:05.138 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@406ea770[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:05:05.138 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4ad113d6[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 3]
14:05:05.144 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of df6713bf-8019-4bf4-8999-46e709a9c122
14:05:05.145 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df6713bf-8019-4bf4-8999-46e709a9c122] RpcClient init label, labels = {module=naming, source=sdk}
14:05:05.145 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df6713bf-8019-4bf4-8999-46e709a9c122] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:05:05.145 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df6713bf-8019-4bf4-8999-46e709a9c122] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:05:05.145 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df6713bf-8019-4bf4-8999-46e709a9c122] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:05:05.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df6713bf-8019-4bf4-8999-46e709a9c122] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:05:05.242 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c02d0605-eac3-4d40-9e43-a4d5972b613a] Client is shutdown, stop reconnect to server
14:05:07.860 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [486b60b3-b3fa-4731-bb33-58696e31baf5_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:05:08.157 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df6713bf-8019-4bf4-8999-46e709a9c122] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:05:11.177 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df6713bf-8019-4bf4-8999-46e709a9c122] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:05:11.782 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [486b60b3-b3fa-4731-bb33-58696e31baf5_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:05:14.190 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df6713bf-8019-4bf4-8999-46e709a9c122] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:05:14.190 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df6713bf-8019-4bf4-8999-46e709a9c122] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:05:14.191 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [df6713bf-8019-4bf4-8999-46e709a9c122] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000029f014d4228
14:05:14.555 [main] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751522686256 shutting down.
14:05:14.555 [main] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751522686256 paused.
14:05:14.556 [main] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751522686256 shutdown complete.
14:05:14.558 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:05:14.562 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:05:14.572 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:05:14.572 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:05:14.595 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9500"]
14:05:14.595 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
14:05:14.625 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9500"]
14:05:14.632 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9500"]
14:09:32.047 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:09:33.093 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-ea18-41cd-bf67-5b58c58a32f8_config-0
14:09:33.189 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 53 ms to scan 1 urls, producing 3 keys and 6 values 
14:09:33.239 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 4 keys and 9 values 
14:09:33.256 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 3 keys and 10 values 
14:09:33.274 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
14:09:33.293 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
14:09:33.305 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
14:09:33.312 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea18-41cd-bf67-5b58c58a32f8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:09:33.313 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea18-41cd-bf67-5b58c58a32f8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000017e9f39f8e0
14:09:33.313 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea18-41cd-bf67-5b58c58a32f8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000017e9f39fb00
14:09:33.314 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea18-41cd-bf67-5b58c58a32f8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:09:33.315 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea18-41cd-bf67-5b58c58a32f8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:09:33.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea18-41cd-bf67-5b58c58a32f8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:09:37.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea18-41cd-bf67-5b58c58a32f8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:09:40.111 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea18-41cd-bf67-5b58c58a32f8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:09:43.131 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea18-41cd-bf67-5b58c58a32f8_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:09:43.132 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea18-41cd-bf67-5b58c58a32f8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:09:43.133 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea18-41cd-bf67-5b58c58a32f8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000017e9f4e9678
14:09:45.190 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:09:49.199 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
14:09:49.199 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:09:49.199 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:09:49.263 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea18-41cd-bf67-5b58c58a32f8_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:09:49.431 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:09:50.259 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:09:50.261 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:09:50.261 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:09:50.665 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:09:50.696 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:09:50.697 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:09:50.723 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751522990671'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

14:09:50.724 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
14:09:50.724 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:09:50.728 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@4549d616
14:09:52.487 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea18-41cd-bf67-5b58c58a32f8_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:09:53.941 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:09:55.811 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea18-41cd-bf67-5b58c58a32f8_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:09:59.230 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea18-41cd-bf67-5b58c58a32f8_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:09:59.603 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f28bae20-e37b-414e-8e50-79077bb662bb
14:09:59.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f28bae20-e37b-414e-8e50-79077bb662bb] RpcClient init label, labels = {module=naming, source=sdk}
14:09:59.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f28bae20-e37b-414e-8e50-79077bb662bb] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:09:59.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f28bae20-e37b-414e-8e50-79077bb662bb] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:09:59.608 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f28bae20-e37b-414e-8e50-79077bb662bb] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:09:59.609 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f28bae20-e37b-414e-8e50-79077bb662bb] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:02.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f28bae20-e37b-414e-8e50-79077bb662bb] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:02.756 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea18-41cd-bf67-5b58c58a32f8_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:10:05.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f28bae20-e37b-414e-8e50-79077bb662bb] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:06.379 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea18-41cd-bf67-5b58c58a32f8_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:10:08.642 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f28bae20-e37b-414e-8e50-79077bb662bb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:10:08.642 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f28bae20-e37b-414e-8e50-79077bb662bb] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:10:08.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f28bae20-e37b-414e-8e50-79077bb662bb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000017e9f4e9678
14:10:08.990 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
14:10:09.922 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:10:09.923 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6682e9b0[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:10:09.923 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@27f7db5a[Running, pool size = 3, active threads = 0, queued tasks = 0, completed tasks = 3]
14:10:09.928 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5c31abc1-b5a6-41a9-a2a8-16cbaa8489aa
14:10:09.928 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c31abc1-b5a6-41a9-a2a8-16cbaa8489aa] RpcClient init label, labels = {module=naming, source=sdk}
14:10:09.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c31abc1-b5a6-41a9-a2a8-16cbaa8489aa] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:10:09.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c31abc1-b5a6-41a9-a2a8-16cbaa8489aa] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:10:09.931 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c31abc1-b5a6-41a9-a2a8-16cbaa8489aa] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:10:09.932 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c31abc1-b5a6-41a9-a2a8-16cbaa8489aa] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:10.026 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f28bae20-e37b-414e-8e50-79077bb662bb] Client is shutdown, stop reconnect to server
14:10:10.090 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea18-41cd-bf67-5b58c58a32f8_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:10:12.949 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c31abc1-b5a6-41a9-a2a8-16cbaa8489aa] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:13.911 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea18-41cd-bf67-5b58c58a32f8_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:10:15.959 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c31abc1-b5a6-41a9-a2a8-16cbaa8489aa] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:17.850 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-ea18-41cd-bf67-5b58c58a32f8_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:10:18.976 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c31abc1-b5a6-41a9-a2a8-16cbaa8489aa] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:10:18.977 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c31abc1-b5a6-41a9-a2a8-16cbaa8489aa] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:10:18.977 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c31abc1-b5a6-41a9-a2a8-16cbaa8489aa] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000017e9f4e9678
14:10:19.329 [main] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751522990671 shutting down.
14:10:19.329 [main] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751522990671 paused.
14:10:19.329 [main] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751522990671 shutdown complete.
14:10:19.332 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:10:19.336 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:10:19.348 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:10:19.348 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:10:19.368 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9500"]
14:10:19.368 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
14:10:19.397 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9500"]
14:10:19.401 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9500"]
14:28:16.650 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:28:17.697 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b07ed2e5-a94e-40dc-8601-6371a91f4a42_config-0
14:28:17.782 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 3 keys and 6 values 
14:28:17.815 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
14:28:17.827 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:28:17.839 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
14:28:17.861 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
14:28:17.873 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
14:28:17.878 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07ed2e5-a94e-40dc-8601-6371a91f4a42_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:28:17.878 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07ed2e5-a94e-40dc-8601-6371a91f4a42_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002d3653bf8e0
14:28:17.879 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07ed2e5-a94e-40dc-8601-6371a91f4a42_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002d3653bfb00
14:28:17.880 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07ed2e5-a94e-40dc-8601-6371a91f4a42_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:28:17.880 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07ed2e5-a94e-40dc-8601-6371a91f4a42_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:28:17.891 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07ed2e5-a94e-40dc-8601-6371a91f4a42_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:28:21.710 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07ed2e5-a94e-40dc-8601-6371a91f4a42_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:28:24.727 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07ed2e5-a94e-40dc-8601-6371a91f4a42_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:28:27.740 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07ed2e5-a94e-40dc-8601-6371a91f4a42_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:28:27.741 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07ed2e5-a94e-40dc-8601-6371a91f4a42_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:28:27.742 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07ed2e5-a94e-40dc-8601-6371a91f4a42_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002d3654d4430
14:28:29.813 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:28:33.875 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07ed2e5-a94e-40dc-8601-6371a91f4a42_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:28:34.718 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
14:28:34.718 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:28:34.718 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:28:34.956 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:28:35.789 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:28:35.791 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:28:35.791 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:28:36.186 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:28:36.208 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:28:36.208 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:28:36.233 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751524116190'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

14:28:36.233 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
14:28:36.233 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:28:36.236 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@1c571162
14:28:37.097 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07ed2e5-a94e-40dc-8601-6371a91f4a42_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:28:39.558 [main] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751524116190 shutting down.
14:28:39.558 [main] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751524116190 paused.
14:28:39.559 [main] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751524116190 shutdown complete.
14:28:39.560 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:28:39.563 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:28:39.573 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:28:39.574 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:28:39.575 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
14:28:40.411 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07ed2e5-a94e-40dc-8601-6371a91f4a42_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
14:33:36.162 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:33:37.169 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of be19628c-9c14-4b72-905a-9537297582ac_config-0
14:33:37.264 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 49 ms to scan 1 urls, producing 3 keys and 6 values 
14:33:37.300 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
14:33:37.314 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
14:33:37.328 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
14:33:37.342 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
14:33:37.360 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
14:33:37.364 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be19628c-9c14-4b72-905a-9537297582ac_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:33:37.366 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be19628c-9c14-4b72-905a-9537297582ac_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000002c3813b6ff8
14:33:37.366 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be19628c-9c14-4b72-905a-9537297582ac_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002c3813b7218
14:33:37.367 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be19628c-9c14-4b72-905a-9537297582ac_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:33:37.367 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be19628c-9c14-4b72-905a-9537297582ac_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:33:37.377 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be19628c-9c14-4b72-905a-9537297582ac_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:33:38.712 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be19628c-9c14-4b72-905a-9537297582ac_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751524418475_127.0.0.1_6341
14:33:38.713 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be19628c-9c14-4b72-905a-9537297582ac_config-0] Notify connected event to listeners.
14:33:38.713 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be19628c-9c14-4b72-905a-9537297582ac_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:33:38.714 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be19628c-9c14-4b72-905a-9537297582ac_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002c3814f0fb0
14:33:38.870 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:33:43.498 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
14:33:43.499 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:33:43.499 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:33:43.790 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:33:44.761 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:33:44.762 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:33:44.763 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:33:45.300 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:33:45.331 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:33:45.332 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:33:45.359 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751524425305'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

14:33:45.360 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
14:33:45.360 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:33:45.364 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@3da55998
14:33:49.095 [main] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751524425305 shutting down.
14:33:49.095 [main] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751524425305 paused.
14:33:49.096 [main] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751524425305 shutdown complete.
14:33:49.097 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:33:49.099 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:33:49.108 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:33:49.108 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:33:49.109 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
14:42:02.353 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:42:03.233 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 74903a2e-ad2a-4393-80b6-1ccb030e3db3_config-0
14:42:03.315 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 3 keys and 6 values 
14:42:03.356 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 4 keys and 9 values 
14:42:03.374 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 3 keys and 10 values 
14:42:03.387 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
14:42:03.400 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
14:42:03.413 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
14:42:03.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74903a2e-ad2a-4393-80b6-1ccb030e3db3_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:42:03.421 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74903a2e-ad2a-4393-80b6-1ccb030e3db3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000002a74d3b7d80
14:42:03.422 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74903a2e-ad2a-4393-80b6-1ccb030e3db3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002a74d3b8000
14:42:03.423 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74903a2e-ad2a-4393-80b6-1ccb030e3db3_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:42:03.424 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74903a2e-ad2a-4393-80b6-1ccb030e3db3_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:42:03.443 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74903a2e-ad2a-4393-80b6-1ccb030e3db3_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:42:04.659 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74903a2e-ad2a-4393-80b6-1ccb030e3db3_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751524924387_127.0.0.1_9415
14:42:04.661 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74903a2e-ad2a-4393-80b6-1ccb030e3db3_config-0] Notify connected event to listeners.
14:42:04.661 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74903a2e-ad2a-4393-80b6-1ccb030e3db3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:42:04.662 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74903a2e-ad2a-4393-80b6-1ccb030e3db3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002a74d4f20a0
14:42:04.840 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:42:08.748 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
14:42:08.749 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:42:08.749 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:42:09.023 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:42:09.868 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:42:09.870 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:42:09.871 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:42:10.361 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:42:10.384 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:42:10.385 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:42:10.406 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751524930367'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

14:42:10.407 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
14:42:10.407 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:42:10.409 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@705eb026
14:42:13.742 [main] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751524930367 shutting down.
14:42:13.743 [main] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751524930367 paused.
14:42:13.744 [main] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751524930367 shutdown complete.
14:42:13.745 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:42:13.748 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:42:13.758 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:42:13.758 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:42:13.760 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
14:48:13.947 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:48:14.783 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 38cdb81c-6b53-4858-a3f3-5534daf464e1_config-0
14:48:14.860 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 36 ms to scan 1 urls, producing 3 keys and 6 values 
14:48:14.889 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
14:48:14.910 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 3 keys and 10 values 
14:48:14.929 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
14:48:14.943 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
14:48:14.957 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
14:48:14.961 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38cdb81c-6b53-4858-a3f3-5534daf464e1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:48:14.961 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38cdb81c-6b53-4858-a3f3-5534daf464e1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000018e1a3ceff8
14:48:14.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38cdb81c-6b53-4858-a3f3-5534daf464e1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000018e1a3cf218
14:48:14.963 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38cdb81c-6b53-4858-a3f3-5534daf464e1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:48:14.964 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38cdb81c-6b53-4858-a3f3-5534daf464e1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:48:14.976 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38cdb81c-6b53-4858-a3f3-5534daf464e1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:48:16.190 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38cdb81c-6b53-4858-a3f3-5534daf464e1_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751525295952_127.0.0.1_11324
14:48:16.192 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38cdb81c-6b53-4858-a3f3-5534daf464e1_config-0] Notify connected event to listeners.
14:48:16.192 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38cdb81c-6b53-4858-a3f3-5534daf464e1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:48:16.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [38cdb81c-6b53-4858-a3f3-5534daf464e1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000018e1a508ad8
14:48:16.372 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:48:20.167 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
14:48:20.168 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:48:20.168 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:48:20.399 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:48:21.206 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:48:21.209 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:48:21.209 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:48:21.582 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:48:21.604 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:48:21.605 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:48:21.627 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751525301586'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

14:48:21.628 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
14:48:21.628 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:48:21.630 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@6fd37997
14:48:25.169 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:48:28.638 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 075a21ee-31fe-4344-9988-7b7f7a0296d0
14:48:28.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [075a21ee-31fe-4344-9988-7b7f7a0296d0] RpcClient init label, labels = {module=naming, source=sdk}
14:48:28.642 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [075a21ee-31fe-4344-9988-7b7f7a0296d0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:48:28.643 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [075a21ee-31fe-4344-9988-7b7f7a0296d0] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:48:28.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [075a21ee-31fe-4344-9988-7b7f7a0296d0] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:48:28.644 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [075a21ee-31fe-4344-9988-7b7f7a0296d0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:48:28.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [075a21ee-31fe-4344-9988-7b7f7a0296d0] Success to connect to server [localhost:8848] on start up, connectionId = 1751525308659_127.0.0.1_11430
14:48:28.790 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [075a21ee-31fe-4344-9988-7b7f7a0296d0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:48:28.790 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [075a21ee-31fe-4344-9988-7b7f7a0296d0] Notify connected event to listeners.
14:48:28.790 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [075a21ee-31fe-4344-9988-7b7f7a0296d0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000018e1a508ad8
14:48:28.874 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
14:48:28.928 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job ************:9500 register finished
14:48:29.091 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 15.952 seconds (JVM running for 17.29)
14:48:29.107 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
14:48:29.107 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
14:48:29.137 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
14:48:29.349 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [075a21ee-31fe-4344-9988-7b7f7a0296d0] Receive server push request, request = NotifySubscriberRequest, requestId = 188
14:48:29.377 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [075a21ee-31fe-4344-9988-7b7f7a0296d0] Ack server push request, request = NotifySubscriberRequest, requestId = 188
14:48:29.766 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:48:30.143 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751525301586 started.
14:48:39.605 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [075a21ee-31fe-4344-9988-7b7f7a0296d0] Receive server push request, request = NotifySubscriberRequest, requestId = 189
14:48:39.607 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [075a21ee-31fe-4344-9988-7b7f7a0296d0] Ack server push request, request = NotifySubscriberRequest, requestId = 189
14:49:25.171 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [075a21ee-31fe-4344-9988-7b7f7a0296d0] Receive server push request, request = NotifySubscriberRequest, requestId = 192
14:49:25.174 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [075a21ee-31fe-4344-9988-7b7f7a0296d0] Ack server push request, request = NotifySubscriberRequest, requestId = 192
15:17:10.892 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751525301586 paused.
15:17:10.975 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:17:10.979 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:17:11.288 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:17:11.290 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3d8fcd71[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:17:11.291 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751525308659_127.0.0.1_11430
15:17:11.296 [nacos-grpc-client-executor-388] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751525308659_127.0.0.1_11430]Ignore complete event,isRunning:false,isAbandon=false
15:17:11.301 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@d63f3ac[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 389]
15:17:11.448 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751525301586 shutting down.
15:17:11.448 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751525301586 paused.
15:17:11.450 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751525301586 shutdown complete.
15:17:11.451 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:17:11.454 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:17:11.462 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:17:11.462 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:17:17.920 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:17:18.816 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1ece6c01-2820-4803-a503-f4cc1e3f90fd_config-0
15:17:18.889 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 3 keys and 6 values 
15:17:18.920 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
15:17:18.940 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 3 keys and 10 values 
15:17:18.959 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 5 values 
15:17:18.973 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
15:17:18.983 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
15:17:18.986 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ece6c01-2820-4803-a503-f4cc1e3f90fd_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:17:18.988 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ece6c01-2820-4803-a503-f4cc1e3f90fd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002238139fd80
15:17:18.988 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ece6c01-2820-4803-a503-f4cc1e3f90fd_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000223813a0000
15:17:18.989 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ece6c01-2820-4803-a503-f4cc1e3f90fd_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:17:18.991 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ece6c01-2820-4803-a503-f4cc1e3f90fd_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:17:19.003 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ece6c01-2820-4803-a503-f4cc1e3f90fd_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:17:20.149 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ece6c01-2820-4803-a503-f4cc1e3f90fd_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751527039928_127.0.0.1_6826
15:17:20.151 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ece6c01-2820-4803-a503-f4cc1e3f90fd_config-0] Notify connected event to listeners.
15:17:20.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ece6c01-2820-4803-a503-f4cc1e3f90fd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:17:20.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1ece6c01-2820-4803-a503-f4cc1e3f90fd_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000022381519ca0
15:17:20.328 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:17:24.322 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
15:17:24.323 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:17:24.323 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:17:24.584 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:17:25.400 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:17:25.402 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:17:25.403 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:17:25.792 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:17:25.817 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:17:25.818 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:17:25.848 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751527045797'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

15:17:25.849 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
15:17:25.849 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:17:25.852 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@6fd37997
15:17:29.550 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:17:33.461 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of efb104fe-771f-4338-ac3d-bf6a1f07c18b
15:17:33.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] RpcClient init label, labels = {module=naming, source=sdk}
15:17:33.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:17:33.463 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:17:33.464 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:17:33.464 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:17:33.595 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Success to connect to server [localhost:8848] on start up, connectionId = 1751527053478_127.0.0.1_6910
15:17:33.595 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:17:33.595 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Notify connected event to listeners.
15:17:33.595 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000022381519ca0
15:17:33.687 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
15:17:33.737 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job ************:9500 register finished
15:17:33.923 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 16.83 seconds (JVM running for 18.209)
15:17:33.945 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
15:17:33.946 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
15:17:33.972 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
15:17:34.190 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:17:34.194 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Receive server push request, request = NotifySubscriberRequest, requestId = 194
15:17:34.220 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Ack server push request, request = NotifySubscriberRequest, requestId = 194
15:17:34.962 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751527045797 started.
15:18:30.218 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Receive server push request, request = NotifySubscriberRequest, requestId = 196
15:18:30.218 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Ack server push request, request = NotifySubscriberRequest, requestId = 196
15:19:01.952 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Receive server push request, request = NotifySubscriberRequest, requestId = 197
15:19:01.954 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Ack server push request, request = NotifySubscriberRequest, requestId = 197
15:20:14.865 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Server healthy check fail, currentConnection = 1751527053478_127.0.0.1_6910
15:20:14.870 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:20:31.749 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Success to connect a server [localhost:8848], connectionId = 1751527229039_127.0.0.1_7949
15:20:31.751 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Abandon prev connection, server is localhost:8848, connectionId is 1751527053478_127.0.0.1_6910
15:20:31.752 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751527053478_127.0.0.1_6910
15:20:31.753 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Notify disconnected event to listeners
15:20:32.381 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Server check success, currentServer is localhost:8848 
15:20:33.208 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Notify connected event to listeners.
15:20:56.309 [nacos-grpc-client-executor-61] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Receive server push request, request = NotifySubscriberRequest, requestId = 201
15:20:58.058 [nacos-grpc-client-executor-61] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Ack server push request, request = NotifySubscriberRequest, requestId = 201
15:20:58.068 [nacos-grpc-client-executor-66] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Receive server push request, request = NotifySubscriberRequest, requestId = 200
15:20:58.069 [nacos-grpc-client-executor-66] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Ack server push request, request = NotifySubscriberRequest, requestId = 200
15:20:58.079 [nacos-grpc-client-executor-67] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Receive server push request, request = NotifySubscriberRequest, requestId = 202
15:20:58.080 [nacos-grpc-client-executor-67] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Ack server push request, request = NotifySubscriberRequest, requestId = 202
15:20:58.089 [nacos-grpc-client-executor-68] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Receive server push request, request = NotifySubscriberRequest, requestId = 203
15:20:58.090 [nacos-grpc-client-executor-68] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Ack server push request, request = NotifySubscriberRequest, requestId = 203
15:20:58.100 [nacos-grpc-client-executor-69] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Receive server push request, request = NotifySubscriberRequest, requestId = 205
15:20:58.101 [nacos-grpc-client-executor-69] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Ack server push request, request = NotifySubscriberRequest, requestId = 205
15:20:58.112 [nacos-grpc-client-executor-70] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Receive server push request, request = NotifySubscriberRequest, requestId = 204
15:20:58.112 [nacos-grpc-client-executor-70] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Ack server push request, request = NotifySubscriberRequest, requestId = 204
15:20:58.124 [nacos-grpc-client-executor-71] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Receive server push request, request = NotifySubscriberRequest, requestId = 206
15:20:58.124 [nacos-grpc-client-executor-71] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Ack server push request, request = NotifySubscriberRequest, requestId = 206
15:20:58.135 [nacos-grpc-client-executor-72] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Receive server push request, request = NotifySubscriberRequest, requestId = 207
15:20:58.136 [nacos-grpc-client-executor-72] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Ack server push request, request = NotifySubscriberRequest, requestId = 207
15:20:58.149 [nacos-grpc-client-executor-73] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Receive server push request, request = NotifySubscriberRequest, requestId = 208
15:20:58.149 [nacos-grpc-client-executor-73] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Ack server push request, request = NotifySubscriberRequest, requestId = 208
15:20:58.160 [nacos-grpc-client-executor-74] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Receive server push request, request = NotifySubscriberRequest, requestId = 209
15:20:58.161 [nacos-grpc-client-executor-74] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Ack server push request, request = NotifySubscriberRequest, requestId = 209
15:20:58.170 [nacos-grpc-client-executor-75] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Receive server push request, request = NotifySubscriberRequest, requestId = 210
15:20:58.170 [nacos-grpc-client-executor-75] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Ack server push request, request = NotifySubscriberRequest, requestId = 210
15:20:58.177 [nacos-grpc-client-executor-76] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Receive server push request, request = NotifySubscriberRequest, requestId = 211
15:20:58.178 [nacos-grpc-client-executor-76] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [efb104fe-771f-4338-ac3d-bf6a1f07c18b] Ack server push request, request = NotifySubscriberRequest, requestId = 211
15:21:29.764 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751527045797 paused.
15:21:29.923 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:21:29.933 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:21:30.243 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:21:30.244 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3d4a61c6[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:21:30.244 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751527229039_127.0.0.1_7949
15:21:30.245 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@d61ca10[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 84]
15:21:30.388 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751527045797 shutting down.
15:21:30.388 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751527045797 paused.
15:21:30.390 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751527045797 shutdown complete.
15:21:30.392 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:21:30.397 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:21:30.410 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:21:30.411 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:21:34.819 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:21:35.773 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fd4e256a-1647-4410-ae6f-fe17a14a9325_config-0
15:21:35.853 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 3 keys and 6 values 
15:21:35.889 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 4 keys and 9 values 
15:21:35.901 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
15:21:35.913 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
15:21:35.934 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 1 keys and 7 values 
15:21:35.945 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
15:21:35.951 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fd4e256a-1647-4410-ae6f-fe17a14a9325_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:21:35.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fd4e256a-1647-4410-ae6f-fe17a14a9325_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001eae539f8e0
15:21:35.953 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fd4e256a-1647-4410-ae6f-fe17a14a9325_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001eae539fb00
15:21:35.954 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fd4e256a-1647-4410-ae6f-fe17a14a9325_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:21:35.955 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fd4e256a-1647-4410-ae6f-fe17a14a9325_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:21:35.969 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fd4e256a-1647-4410-ae6f-fe17a14a9325_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:21:37.201 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fd4e256a-1647-4410-ae6f-fe17a14a9325_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751527296975_127.0.0.1_8267
15:21:37.203 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fd4e256a-1647-4410-ae6f-fe17a14a9325_config-0] Notify connected event to listeners.
15:21:37.204 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fd4e256a-1647-4410-ae6f-fe17a14a9325_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:21:37.206 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fd4e256a-1647-4410-ae6f-fe17a14a9325_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001eae5519450
15:21:37.397 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:21:41.446 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
15:21:41.447 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:21:41.447 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:21:41.676 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:21:42.557 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:21:42.560 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:21:42.560 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:21:43.090 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:21:43.175 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:21:43.176 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:21:43.225 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751527303095'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

15:21:43.226 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
15:21:43.226 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:21:43.231 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@6fd37997
15:21:47.044 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:21:50.759 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7fb8dbc3-8c7a-4109-8714-a06239a66846
15:21:50.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb8dbc3-8c7a-4109-8714-a06239a66846] RpcClient init label, labels = {module=naming, source=sdk}
15:21:50.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb8dbc3-8c7a-4109-8714-a06239a66846] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:21:50.761 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb8dbc3-8c7a-4109-8714-a06239a66846] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:21:50.762 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb8dbc3-8c7a-4109-8714-a06239a66846] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:21:50.763 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb8dbc3-8c7a-4109-8714-a06239a66846] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:21:50.897 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb8dbc3-8c7a-4109-8714-a06239a66846] Success to connect to server [localhost:8848] on start up, connectionId = 1751527310775_127.0.0.1_8337
15:21:50.898 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb8dbc3-8c7a-4109-8714-a06239a66846] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:21:50.898 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb8dbc3-8c7a-4109-8714-a06239a66846] Notify connected event to listeners.
15:21:50.898 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb8dbc3-8c7a-4109-8714-a06239a66846] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001eae5519450
15:21:51.017 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
15:21:51.065 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job ************:9500 register finished
15:21:51.248 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 17.301 seconds (JVM running for 18.775)
15:21:51.267 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
15:21:51.268 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
15:21:51.272 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
15:21:51.484 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb8dbc3-8c7a-4109-8714-a06239a66846] Receive server push request, request = NotifySubscriberRequest, requestId = 214
15:21:51.509 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb8dbc3-8c7a-4109-8714-a06239a66846] Ack server push request, request = NotifySubscriberRequest, requestId = 214
15:21:51.643 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:21:52.357 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751527303095 started.
15:22:05.103 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb8dbc3-8c7a-4109-8714-a06239a66846] Receive server push request, request = NotifySubscriberRequest, requestId = 215
15:22:05.103 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb8dbc3-8c7a-4109-8714-a06239a66846] Ack server push request, request = NotifySubscriberRequest, requestId = 215
15:22:05.652 [nacos-grpc-client-executor-14] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb8dbc3-8c7a-4109-8714-a06239a66846] Receive server push request, request = NotifySubscriberRequest, requestId = 216
15:22:05.653 [nacos-grpc-client-executor-14] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb8dbc3-8c7a-4109-8714-a06239a66846] Ack server push request, request = NotifySubscriberRequest, requestId = 216
15:23:16.882 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751527303095 paused.
15:23:16.986 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:23:17.004 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:23:17.320 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:23:17.320 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@fa14cc5[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:23:17.321 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751527310775_127.0.0.1_8337
15:23:17.325 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751527310775_127.0.0.1_8337]Ignore complete event,isRunning:false,isAbandon=false
15:23:17.328 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@497575e6[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 33]
15:23:17.481 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751527303095 shutting down.
15:23:17.481 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751527303095 paused.
15:23:17.483 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751527303095 shutdown complete.
15:23:17.484 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:23:17.489 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:23:17.498 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:23:17.499 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:23:21.568 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:23:22.490 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 45b480f3-6ae4-4c80-8d7c-169a8b6ff2ac_config-0
15:23:22.568 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 3 keys and 6 values 
15:23:22.602 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
15:23:22.613 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
15:23:22.626 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
15:23:22.646 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 1 keys and 7 values 
15:23:22.658 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
15:23:22.662 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [45b480f3-6ae4-4c80-8d7c-169a8b6ff2ac_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:23:22.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [45b480f3-6ae4-4c80-8d7c-169a8b6ff2ac_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001b1823a0200
15:23:22.663 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [45b480f3-6ae4-4c80-8d7c-169a8b6ff2ac_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001b1823a0420
15:23:22.664 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [45b480f3-6ae4-4c80-8d7c-169a8b6ff2ac_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:23:22.665 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [45b480f3-6ae4-4c80-8d7c-169a8b6ff2ac_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:23:22.684 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [45b480f3-6ae4-4c80-8d7c-169a8b6ff2ac_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:23:23.923 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [45b480f3-6ae4-4c80-8d7c-169a8b6ff2ac_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751527403692_127.0.0.1_8906
15:23:23.925 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [45b480f3-6ae4-4c80-8d7c-169a8b6ff2ac_config-0] Notify connected event to listeners.
15:23:23.925 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [45b480f3-6ae4-4c80-8d7c-169a8b6ff2ac_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:23:23.927 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [45b480f3-6ae4-4c80-8d7c-169a8b6ff2ac_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001b18251a6e0
15:23:24.098 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:23:27.879 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
15:23:27.880 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:23:27.880 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:23:28.106 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:23:28.907 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:23:28.909 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:23:28.909 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:23:29.286 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:23:29.310 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:23:29.310 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:23:29.334 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751527409290'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

15:23:29.335 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
15:23:29.335 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:23:29.338 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@6fd37997
15:23:32.904 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:23:36.643 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 74f43699-3d4c-4fbe-94a3-d6108a250a08
15:23:36.643 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74f43699-3d4c-4fbe-94a3-d6108a250a08] RpcClient init label, labels = {module=naming, source=sdk}
15:23:36.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74f43699-3d4c-4fbe-94a3-d6108a250a08] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:23:36.649 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74f43699-3d4c-4fbe-94a3-d6108a250a08] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:23:36.649 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74f43699-3d4c-4fbe-94a3-d6108a250a08] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:23:36.650 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74f43699-3d4c-4fbe-94a3-d6108a250a08] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:23:36.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74f43699-3d4c-4fbe-94a3-d6108a250a08] Success to connect to server [localhost:8848] on start up, connectionId = 1751527416660_127.0.0.1_9008
15:23:36.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74f43699-3d4c-4fbe-94a3-d6108a250a08] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:23:36.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74f43699-3d4c-4fbe-94a3-d6108a250a08] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001b18251a6e0
15:23:36.787 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74f43699-3d4c-4fbe-94a3-d6108a250a08] Notify connected event to listeners.
15:23:36.896 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
15:23:36.965 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job ************:9500 register finished
15:23:37.305 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 16.558 seconds (JVM running for 17.965)
15:23:37.337 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
15:23:37.338 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
15:23:37.344 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
15:23:37.386 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74f43699-3d4c-4fbe-94a3-d6108a250a08] Receive server push request, request = NotifySubscriberRequest, requestId = 219
15:23:37.411 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74f43699-3d4c-4fbe-94a3-d6108a250a08] Ack server push request, request = NotifySubscriberRequest, requestId = 219
15:23:37.833 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:23:38.384 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751527409290 started.
15:25:05.471 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74f43699-3d4c-4fbe-94a3-d6108a250a08] Receive server push request, request = NotifySubscriberRequest, requestId = 220
15:25:05.471 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74f43699-3d4c-4fbe-94a3-d6108a250a08] Ack server push request, request = NotifySubscriberRequest, requestId = 220
15:27:02.115 [nacos-grpc-client-executor-52] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74f43699-3d4c-4fbe-94a3-d6108a250a08] Receive server push request, request = NotifySubscriberRequest, requestId = 221
15:27:02.118 [nacos-grpc-client-executor-52] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [74f43699-3d4c-4fbe-94a3-d6108a250a08] Ack server push request, request = NotifySubscriberRequest, requestId = 221
15:47:54.945 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751527409290 paused.
15:47:55.025 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:47:55.028 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:47:55.344 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:47:55.344 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@25ebf949[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:47:55.346 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751527416660_127.0.0.1_9008
15:47:55.351 [nacos-grpc-client-executor-330] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751527416660_127.0.0.1_9008]Ignore complete event,isRunning:false,isAbandon=false
15:47:55.359 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3ce3ba73[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 331]
15:47:55.522 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751527409290 shutting down.
15:47:55.523 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751527409290 paused.
15:47:55.525 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751527409290 shutdown complete.
15:47:55.528 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:47:55.532 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:47:55.543 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:47:55.543 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:48:02.306 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:48:03.239 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8b2bba2a-cfff-45cd-9166-dcae9f7e3bd1_config-0
15:48:03.310 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 37 ms to scan 1 urls, producing 3 keys and 6 values 
15:48:03.352 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 4 keys and 9 values 
15:48:03.363 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
15:48:03.376 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
15:48:03.387 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
15:48:03.398 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
15:48:03.403 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b2bba2a-cfff-45cd-9166-dcae9f7e3bd1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:48:03.404 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b2bba2a-cfff-45cd-9166-dcae9f7e3bd1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000186d039f8e0
15:48:03.405 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b2bba2a-cfff-45cd-9166-dcae9f7e3bd1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000186d039fb00
15:48:03.405 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b2bba2a-cfff-45cd-9166-dcae9f7e3bd1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:48:03.407 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b2bba2a-cfff-45cd-9166-dcae9f7e3bd1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:48:03.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b2bba2a-cfff-45cd-9166-dcae9f7e3bd1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:48:04.533 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b2bba2a-cfff-45cd-9166-dcae9f7e3bd1_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751528884309_127.0.0.1_1641
15:48:04.535 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b2bba2a-cfff-45cd-9166-dcae9f7e3bd1_config-0] Notify connected event to listeners.
15:48:04.537 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b2bba2a-cfff-45cd-9166-dcae9f7e3bd1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:48:04.538 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8b2bba2a-cfff-45cd-9166-dcae9f7e3bd1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000186d0519a90
15:48:04.731 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:48:09.026 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
15:48:09.029 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:48:09.029 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:48:09.451 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:48:10.800 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:48:10.802 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:48:10.803 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:48:11.463 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:48:11.503 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:48:11.504 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:48:11.541 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751528891471'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

15:48:11.542 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
15:48:11.542 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:48:11.546 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@6fd37997
15:48:15.858 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:48:19.867 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8111bb6c-795b-4c05-9290-ab4d226d55b3
15:48:19.867 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8111bb6c-795b-4c05-9290-ab4d226d55b3] RpcClient init label, labels = {module=naming, source=sdk}
15:48:19.877 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8111bb6c-795b-4c05-9290-ab4d226d55b3] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:48:19.878 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8111bb6c-795b-4c05-9290-ab4d226d55b3] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:48:19.880 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8111bb6c-795b-4c05-9290-ab4d226d55b3] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:48:19.881 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8111bb6c-795b-4c05-9290-ab4d226d55b3] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:48:20.024 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8111bb6c-795b-4c05-9290-ab4d226d55b3] Success to connect to server [localhost:8848] on start up, connectionId = 1751528899901_127.0.0.1_1695
15:48:20.025 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8111bb6c-795b-4c05-9290-ab4d226d55b3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:48:20.025 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8111bb6c-795b-4c05-9290-ab4d226d55b3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000186d0519a90
15:48:20.025 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8111bb6c-795b-4c05-9290-ab4d226d55b3] Notify connected event to listeners.
15:48:20.147 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
15:48:20.204 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job ************:9500 register finished
15:48:20.485 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 19.091 seconds (JVM running for 20.458)
15:48:20.510 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
15:48:20.512 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
15:48:20.516 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
15:48:20.633 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8111bb6c-795b-4c05-9290-ab4d226d55b3] Receive server push request, request = NotifySubscriberRequest, requestId = 224
15:48:20.659 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8111bb6c-795b-4c05-9290-ab4d226d55b3] Ack server push request, request = NotifySubscriberRequest, requestId = 224
15:48:20.711 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:48:21.582 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751528891471 started.
15:48:32.219 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8111bb6c-795b-4c05-9290-ab4d226d55b3] Receive server push request, request = NotifySubscriberRequest, requestId = 227
15:48:32.220 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8111bb6c-795b-4c05-9290-ab4d226d55b3] Ack server push request, request = NotifySubscriberRequest, requestId = 227
15:48:32.759 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8111bb6c-795b-4c05-9290-ab4d226d55b3] Receive server push request, request = NotifySubscriberRequest, requestId = 228
15:48:32.760 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8111bb6c-795b-4c05-9290-ab4d226d55b3] Ack server push request, request = NotifySubscriberRequest, requestId = 228
15:48:59.102 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8111bb6c-795b-4c05-9290-ab4d226d55b3] Receive server push request, request = NotifySubscriberRequest, requestId = 231
15:48:59.126 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8111bb6c-795b-4c05-9290-ab4d226d55b3] Ack server push request, request = NotifySubscriberRequest, requestId = 231
15:57:23.771 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751528891471 paused.
15:57:23.825 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:57:23.828 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:57:24.136 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:57:24.138 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@445a7a2[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:57:24.139 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751528899901_127.0.0.1_1695
15:57:24.152 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@600127ca[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 135]
15:57:24.164 [nacos-grpc-client-executor-135] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751528899901_127.0.0.1_1695]Ignore complete event,isRunning:false,isAbandon=false
15:57:24.294 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751528891471 shutting down.
15:57:24.295 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751528891471 paused.
15:57:24.297 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751528891471 shutdown complete.
15:57:24.299 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:57:24.301 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:57:24.309 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:57:24.309 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:58:16.937 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:58:17.480 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b04d1480-a6e0-4e63-b17a-763a6360878b_config-0
15:58:17.521 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 6 values 
15:58:17.547 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
15:58:17.555 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
15:58:17.561 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
15:58:17.567 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
15:58:17.574 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
15:58:17.575 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b04d1480-a6e0-4e63-b17a-763a6360878b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:58:17.576 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b04d1480-a6e0-4e63-b17a-763a6360878b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000264013bf268
15:58:17.576 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b04d1480-a6e0-4e63-b17a-763a6360878b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000264013bf488
15:58:17.577 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b04d1480-a6e0-4e63-b17a-763a6360878b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:58:17.577 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b04d1480-a6e0-4e63-b17a-763a6360878b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:58:17.583 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b04d1480-a6e0-4e63-b17a-763a6360878b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:58:18.333 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b04d1480-a6e0-4e63-b17a-763a6360878b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751529498167_127.0.0.1_4429
15:58:18.334 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b04d1480-a6e0-4e63-b17a-763a6360878b_config-0] Notify connected event to listeners.
15:58:18.334 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b04d1480-a6e0-4e63-b17a-763a6360878b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:58:18.336 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b04d1480-a6e0-4e63-b17a-763a6360878b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000264014f9450
15:58:18.495 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:58:20.785 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
15:58:20.786 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:58:20.786 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:58:20.953 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:58:21.546 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:58:21.548 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:58:21.548 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:58:21.777 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:58:21.790 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:58:21.791 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:58:21.804 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751529501780'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

15:58:21.804 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
15:58:21.804 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:58:21.806 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@3da55998
15:58:24.560 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:58:27.337 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3
15:58:27.338 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] RpcClient init label, labels = {module=naming, source=sdk}
15:58:27.340 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:58:27.341 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:58:27.341 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:58:27.342 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
15:58:27.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Success to connect to server [localhost:8848] on start up, connectionId = 1751529507353_127.0.0.1_4486
15:58:27.475 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:58:27.475 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000264014f9450
15:58:27.476 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Notify connected event to listeners.
15:58:27.530 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
15:58:27.561 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job ************:9500 register finished
15:58:27.806 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 11.405 seconds (JVM running for 12.153)
15:58:27.816 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
15:58:27.817 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
15:58:27.822 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
15:58:28.060 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Receive server push request, request = NotifySubscriberRequest, requestId = 237
15:58:28.075 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Ack server push request, request = NotifySubscriberRequest, requestId = 237
15:58:28.270 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:58:28.868 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751529501780 started.
15:58:44.386 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Receive server push request, request = NotifySubscriberRequest, requestId = 238
15:58:44.390 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Ack server push request, request = NotifySubscriberRequest, requestId = 238
15:58:56.622 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Receive server push request, request = NotifySubscriberRequest, requestId = 239
15:59:10.634 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Ack server push request, request = NotifySubscriberRequest, requestId = 239
15:59:18.662 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Receive server push request, request = NotifySubscriberRequest, requestId = 240
15:59:18.664 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Ack server push request, request = NotifySubscriberRequest, requestId = 240
15:59:10.670 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b04d1480-a6e0-4e63-b17a-763a6360878b_config-0] Server healthy check fail, currentConnection = 1751529498167_127.0.0.1_4429
15:59:18.688 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b04d1480-a6e0-4e63-b17a-763a6360878b_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
15:59:18.693 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Server check success, currentServer is localhost:8848 
15:59:18.693 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Receive server push request, request = NotifySubscriberRequest, requestId = 244
15:59:19.316 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Ack server push request, request = NotifySubscriberRequest, requestId = 244
15:59:19.337 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Receive server push request, request = NotifySubscriberRequest, requestId = 245
15:59:19.339 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Ack server push request, request = NotifySubscriberRequest, requestId = 245
15:59:19.345 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Receive server push request, request = NotifySubscriberRequest, requestId = 246
15:59:19.346 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Ack server push request, request = NotifySubscriberRequest, requestId = 246
15:59:20.534 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Receive server push request, request = NotifySubscriberRequest, requestId = 247
15:59:20.534 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Ack server push request, request = NotifySubscriberRequest, requestId = 247
15:59:20.537 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Receive server push request, request = NotifySubscriberRequest, requestId = 248
15:59:20.537 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Ack server push request, request = NotifySubscriberRequest, requestId = 248
15:59:20.546 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Receive server push request, request = NotifySubscriberRequest, requestId = 249
15:59:20.546 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Ack server push request, request = NotifySubscriberRequest, requestId = 249
15:59:20.551 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Receive server push request, request = NotifySubscriberRequest, requestId = 250
15:59:20.551 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Ack server push request, request = NotifySubscriberRequest, requestId = 250
15:59:20.555 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Receive server push request, request = NotifySubscriberRequest, requestId = 251
15:59:20.557 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Ack server push request, request = NotifySubscriberRequest, requestId = 251
15:59:20.580 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Receive server push request, request = NotifySubscriberRequest, requestId = 252
15:59:20.584 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Ack server push request, request = NotifySubscriberRequest, requestId = 252
15:59:20.595 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Receive server push request, request = NotifySubscriberRequest, requestId = 253
15:59:20.596 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Ack server push request, request = NotifySubscriberRequest, requestId = 253
15:59:20.606 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Receive server push request, request = NotifySubscriberRequest, requestId = 254
15:59:20.607 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Ack server push request, request = NotifySubscriberRequest, requestId = 254
15:59:20.619 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Receive server push request, request = NotifySubscriberRequest, requestId = 255
15:59:20.620 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Ack server push request, request = NotifySubscriberRequest, requestId = 255
15:59:20.646 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b04d1480-a6e0-4e63-b17a-763a6360878b_config-0] Success to connect a server [localhost:8848], connectionId = 1751529559640_127.0.0.1_4756
15:59:20.646 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b04d1480-a6e0-4e63-b17a-763a6360878b_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1751529498167_127.0.0.1_4429
15:59:20.646 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751529498167_127.0.0.1_4429
15:59:20.652 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b04d1480-a6e0-4e63-b17a-763a6360878b_config-0] Notify disconnected event to listeners
15:59:20.652 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b04d1480-a6e0-4e63-b17a-763a6360878b_config-0] Notify connected event to listeners.
15:59:20.659 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751529498167_127.0.0.1_4429]Ignore complete event,isRunning:true,isAbandon=true
15:59:49.535 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Receive server push request, request = NotifySubscriberRequest, requestId = 256
15:59:49.538 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Ack server push request, request = NotifySubscriberRequest, requestId = 256
15:59:49.547 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Receive server push request, request = NotifySubscriberRequest, requestId = 257
15:59:49.548 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Ack server push request, request = NotifySubscriberRequest, requestId = 257
15:59:49.555 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Receive server push request, request = NotifySubscriberRequest, requestId = 258
15:59:49.555 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Ack server push request, request = NotifySubscriberRequest, requestId = 258
15:59:49.562 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Receive server push request, request = NotifySubscriberRequest, requestId = 259
15:59:49.562 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Ack server push request, request = NotifySubscriberRequest, requestId = 259
15:59:49.569 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Receive server push request, request = NotifySubscriberRequest, requestId = 260
15:59:49.569 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Ack server push request, request = NotifySubscriberRequest, requestId = 260
16:03:04.641 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Server check success, currentServer is localhost:8848 
16:04:37.944 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751529501780 paused.
16:04:38.027 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:04:38.030 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:04:38.338 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:04:38.338 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@44ff071d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:04:38.339 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751529507353_127.0.0.1_4486
16:04:38.340 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@70fc08d[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 83]
16:04:38.342 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2d0b7e0f-107e-4c17-ad9e-8ca6c03d07f3] Notify disconnected event to listeners
16:04:38.482 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751529501780 shutting down.
16:04:38.483 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751529501780 paused.
16:04:38.483 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751529501780 shutdown complete.
16:04:38.485 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:04:38.488 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:04:38.494 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:04:38.494 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:04:42.886 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:04:43.448 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bd056b64-cc75-4009-ae51-152851e4143c_config-0
16:04:43.493 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 3 keys and 6 values 
16:04:43.512 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
16:04:43.525 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
16:04:43.533 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
16:04:43.540 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
16:04:43.546 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
16:04:43.549 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd056b64-cc75-4009-ae51-152851e4143c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:04:43.549 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd056b64-cc75-4009-ae51-152851e4143c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000027aa13ceff8
16:04:43.550 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd056b64-cc75-4009-ae51-152851e4143c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000027aa13cf218
16:04:43.551 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd056b64-cc75-4009-ae51-152851e4143c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:04:43.552 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd056b64-cc75-4009-ae51-152851e4143c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:04:43.561 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd056b64-cc75-4009-ae51-152851e4143c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:04:44.340 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd056b64-cc75-4009-ae51-152851e4143c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751529884163_127.0.0.1_6275
16:04:44.341 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd056b64-cc75-4009-ae51-152851e4143c_config-0] Notify connected event to listeners.
16:04:44.342 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd056b64-cc75-4009-ae51-152851e4143c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:04:44.344 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bd056b64-cc75-4009-ae51-152851e4143c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000027aa1508fb0
16:04:44.447 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:04:46.794 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
16:04:46.794 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:04:46.794 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:04:46.921 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:04:47.459 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:04:47.461 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:04:47.461 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:04:47.657 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
16:04:47.673 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
16:04:47.673 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
16:04:47.688 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751529887661'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

16:04:47.688 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
16:04:47.688 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
16:04:47.690 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@5dc7881d
16:04:49.957 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:04:52.350 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 806279dd-d41a-4ad4-8654-05b8b3798a65
16:04:52.350 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [806279dd-d41a-4ad4-8654-05b8b3798a65] RpcClient init label, labels = {module=naming, source=sdk}
16:04:52.352 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [806279dd-d41a-4ad4-8654-05b8b3798a65] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:04:52.352 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [806279dd-d41a-4ad4-8654-05b8b3798a65] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:04:52.353 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [806279dd-d41a-4ad4-8654-05b8b3798a65] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:04:52.353 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [806279dd-d41a-4ad4-8654-05b8b3798a65] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:04:52.477 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [806279dd-d41a-4ad4-8654-05b8b3798a65] Success to connect to server [localhost:8848] on start up, connectionId = 1751529892363_127.0.0.1_6304
16:04:52.477 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [806279dd-d41a-4ad4-8654-05b8b3798a65] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:04:52.477 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [806279dd-d41a-4ad4-8654-05b8b3798a65] Notify connected event to listeners.
16:04:52.477 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [806279dd-d41a-4ad4-8654-05b8b3798a65] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000027aa1508fb0
16:04:52.532 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
16:04:52.562 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job ************:9500 register finished
16:04:52.648 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 10.296 seconds (JVM running for 11.089)
16:04:52.658 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
16:04:52.658 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
16:04:52.666 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
16:04:53.022 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:04:53.077 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [806279dd-d41a-4ad4-8654-05b8b3798a65] Receive server push request, request = NotifySubscriberRequest, requestId = 263
16:04:53.093 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [806279dd-d41a-4ad4-8654-05b8b3798a65] Ack server push request, request = NotifySubscriberRequest, requestId = 263
16:04:53.763 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751529887661 started.
16:05:06.095 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [806279dd-d41a-4ad4-8654-05b8b3798a65] Receive server push request, request = NotifySubscriberRequest, requestId = 264
16:05:06.098 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [806279dd-d41a-4ad4-8654-05b8b3798a65] Ack server push request, request = NotifySubscriberRequest, requestId = 264
16:05:14.220 [nacos-grpc-client-executor-14] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [806279dd-d41a-4ad4-8654-05b8b3798a65] Receive server push request, request = NotifySubscriberRequest, requestId = 265
16:05:14.220 [nacos-grpc-client-executor-14] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [806279dd-d41a-4ad4-8654-05b8b3798a65] Ack server push request, request = NotifySubscriberRequest, requestId = 265
16:05:14.229 [nacos-grpc-client-executor-14] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [806279dd-d41a-4ad4-8654-05b8b3798a65] Receive server push request, request = NotifySubscriberRequest, requestId = 266
16:05:14.230 [nacos-grpc-client-executor-14] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [806279dd-d41a-4ad4-8654-05b8b3798a65] Ack server push request, request = NotifySubscriberRequest, requestId = 266
16:07:52.623 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751529887661 paused.
16:07:52.667 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:07:52.671 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:07:52.979 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:07:52.980 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@657efa6d[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:07:52.981 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751529892363_127.0.0.1_6304
16:07:52.986 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7276d2b4[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 53]
16:07:52.995 [nacos-grpc-client-executor-53] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751529892363_127.0.0.1_6304]Ignore complete event,isRunning:false,isAbandon=false
16:07:53.129 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751529887661 shutting down.
16:07:53.129 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751529887661 paused.
16:07:53.130 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751529887661 shutdown complete.
16:07:53.132 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:07:53.135 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:07:53.143 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:07:53.143 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:07:58.456 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:07:59.000 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 800f7107-c2ce-4cb6-85df-ef5f63182e82_config-0
16:07:59.045 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 3 keys and 6 values 
16:07:59.061 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
16:07:59.068 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
16:07:59.074 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
16:07:59.090 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 1 keys and 7 values 
16:07:59.097 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
16:07:59.101 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [800f7107-c2ce-4cb6-85df-ef5f63182e82_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:07:59.102 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [800f7107-c2ce-4cb6-85df-ef5f63182e82_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000261983ceff8
16:07:59.102 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [800f7107-c2ce-4cb6-85df-ef5f63182e82_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000261983cf218
16:07:59.103 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [800f7107-c2ce-4cb6-85df-ef5f63182e82_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:07:59.104 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [800f7107-c2ce-4cb6-85df-ef5f63182e82_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:07:59.113 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [800f7107-c2ce-4cb6-85df-ef5f63182e82_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:07:59.799 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [800f7107-c2ce-4cb6-85df-ef5f63182e82_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751530079633_127.0.0.1_6994
16:07:59.801 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [800f7107-c2ce-4cb6-85df-ef5f63182e82_config-0] Notify connected event to listeners.
16:07:59.803 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [800f7107-c2ce-4cb6-85df-ef5f63182e82_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:07:59.806 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [800f7107-c2ce-4cb6-85df-ef5f63182e82_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000026198508ad8
16:07:59.908 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:08:02.014 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
16:08:02.014 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:08:02.015 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:08:02.142 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:08:02.707 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:08:02.711 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:08:02.711 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:08:02.927 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
16:08:02.941 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
16:08:02.942 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
16:08:02.957 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751530082929'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

16:08:02.957 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
16:08:02.957 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
16:08:02.958 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@8de4206
16:08:05.184 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:08:07.516 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d4789da2-16ea-40e8-9456-5a4510a05c57
16:08:07.517 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] RpcClient init label, labels = {module=naming, source=sdk}
16:08:07.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:08:07.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:08:07.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:08:07.519 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:08:07.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] Success to connect to server [localhost:8848] on start up, connectionId = 1751530087536_127.0.0.1_7038
16:08:07.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:08:07.648 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000026198508ad8
16:08:07.648 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] Notify connected event to listeners.
16:08:07.706 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
16:08:07.771 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job ************:9500 register finished
16:08:07.883 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 9.953 seconds (JVM running for 10.787)
16:08:07.896 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
16:08:07.897 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
16:08:07.899 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
16:08:08.107 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:08:08.339 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] Receive server push request, request = NotifySubscriberRequest, requestId = 268
16:08:08.355 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] Ack server push request, request = NotifySubscriberRequest, requestId = 268
16:08:09.007 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751530082929 started.
16:09:13.912 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] Receive server push request, request = NotifySubscriberRequest, requestId = 270
16:09:13.913 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] Ack server push request, request = NotifySubscriberRequest, requestId = 270
16:09:13.921 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] Receive server push request, request = NotifySubscriberRequest, requestId = 271
16:09:13.929 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] Ack server push request, request = NotifySubscriberRequest, requestId = 271
16:09:13.934 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] Receive server push request, request = NotifySubscriberRequest, requestId = 272
16:09:13.935 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] Ack server push request, request = NotifySubscriberRequest, requestId = 272
16:09:13.942 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] Receive server push request, request = NotifySubscriberRequest, requestId = 273
16:09:13.942 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] Ack server push request, request = NotifySubscriberRequest, requestId = 273
16:09:13.948 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] Receive server push request, request = NotifySubscriberRequest, requestId = 274
16:09:13.949 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] Ack server push request, request = NotifySubscriberRequest, requestId = 274
16:09:13.955 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] Receive server push request, request = NotifySubscriberRequest, requestId = 275
16:09:13.955 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] Ack server push request, request = NotifySubscriberRequest, requestId = 275
16:09:13.961 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] Receive server push request, request = NotifySubscriberRequest, requestId = 276
16:09:13.961 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] Ack server push request, request = NotifySubscriberRequest, requestId = 276
16:09:14.247 [http-nio-9500-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:09:14.248 [http-nio-9500-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
16:09:14.524 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] Receive server push request, request = NotifySubscriberRequest, requestId = 277
16:09:14.526 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4789da2-16ea-40e8-9456-5a4510a05c57] Ack server push request, request = NotifySubscriberRequest, requestId = 277
16:14:21.831 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751530082929 paused.
16:14:21.893 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:14:21.896 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:14:22.219 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:14:22.219 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@296cfb50[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:14:22.219 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751530087536_127.0.0.1_7038
16:14:22.222 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@790a577[Running, pool size = 7, active threads = 1, queued tasks = 0, completed tasks = 97]
16:14:22.226 [nacos-grpc-client-executor-97] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751530087536_127.0.0.1_7038]Ignore complete event,isRunning:false,isAbandon=false
16:14:22.349 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751530082929 shutting down.
16:14:22.349 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751530082929 paused.
16:14:22.349 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751530082929 shutdown complete.
16:14:22.351 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
16:14:22.353 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
16:14:22.359 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
16:14:22.360 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
16:14:22.361 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
16:14:22.361 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
16:50:38.682 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:50:39.244 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b6775349-c37f-4cd9-94a9-7764de6f015f_config-0
16:50:39.284 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 3 keys and 6 values 
16:50:39.304 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
16:50:39.317 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
16:50:39.326 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
16:50:39.333 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
16:50:39.340 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
16:50:39.342 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6775349-c37f-4cd9-94a9-7764de6f015f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:50:39.342 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6775349-c37f-4cd9-94a9-7764de6f015f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001ecda39fd80
16:50:39.343 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6775349-c37f-4cd9-94a9-7764de6f015f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001ecda3a0000
16:50:39.343 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6775349-c37f-4cd9-94a9-7764de6f015f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:50:39.344 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6775349-c37f-4cd9-94a9-7764de6f015f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:50:39.352 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6775349-c37f-4cd9-94a9-7764de6f015f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:50:40.350 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6775349-c37f-4cd9-94a9-7764de6f015f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751532640146_127.0.0.1_3629
16:50:40.351 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6775349-c37f-4cd9-94a9-7764de6f015f_config-0] Notify connected event to listeners.
16:50:40.351 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6775349-c37f-4cd9-94a9-7764de6f015f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:50:40.352 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6775349-c37f-4cd9-94a9-7764de6f015f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001ecda51a0a0
16:50:40.462 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:50:43.814 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
16:50:43.815 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:50:43.815 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:50:44.031 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:50:44.773 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
16:50:44.774 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
16:50:44.775 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
16:50:45.079 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
16:50:45.107 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
16:50:45.108 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
16:50:45.128 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751532645083'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

16:50:45.128 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
16:50:45.128 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
16:50:45.130 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@6fd37997
16:50:50.569 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:51:08.911 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 34b7ee55-39da-4821-8708-ece26d26601f
16:51:08.913 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] RpcClient init label, labels = {module=naming, source=sdk}
16:51:08.939 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:51:08.940 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:51:08.943 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:51:08.945 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:51:09.124 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] Success to connect to server [localhost:8848] on start up, connectionId = 1751532668985_127.0.0.1_3768
16:51:09.126 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] Notify connected event to listeners.
16:51:09.126 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:51:09.127 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001ecda51a0a0
16:51:09.318 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
16:51:09.472 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job ************:9500 register finished
16:51:09.775 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] Receive server push request, request = NotifySubscriberRequest, requestId = 281
16:51:09.843 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] Ack server push request, request = NotifySubscriberRequest, requestId = 281
16:51:10.457 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 32.347 seconds (JVM running for 33.229)
16:51:10.530 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
16:51:10.535 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
16:51:10.549 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
16:51:11.068 [RMI TCP Connection(9)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:51:11.513 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751532645083 started.
16:52:20.325 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] Receive server push request, request = NotifySubscriberRequest, requestId = 290
16:52:20.329 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] Ack server push request, request = NotifySubscriberRequest, requestId = 290
16:52:32.115 [http-nio-9500-exec-4] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
16:52:32.115 [http-nio-9500-exec-4] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
17:35:11.887 [nacos-grpc-client-executor-541] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] Receive server push request, request = NotifySubscriberRequest, requestId = 291
17:35:11.913 [nacos-grpc-client-executor-541] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] Ack server push request, request = NotifySubscriberRequest, requestId = 291
17:35:44.180 [nacos-grpc-client-executor-548] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] Receive server push request, request = NotifySubscriberRequest, requestId = 293
17:35:44.201 [nacos-grpc-client-executor-548] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] Ack server push request, request = NotifySubscriberRequest, requestId = 293
17:57:38.444 [nacos-grpc-client-executor-812] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] Receive server push request, request = NotifySubscriberRequest, requestId = 296
17:57:38.472 [nacos-grpc-client-executor-812] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] Ack server push request, request = NotifySubscriberRequest, requestId = 296
17:58:12.006 [nacos-grpc-client-executor-820] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] Receive server push request, request = NotifySubscriberRequest, requestId = 299
17:58:12.033 [nacos-grpc-client-executor-820] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] Ack server push request, request = NotifySubscriberRequest, requestId = 299
19:11:15.567 [nacos-grpc-client-executor-1695] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] Receive server push request, request = NotifySubscriberRequest, requestId = 301
19:11:15.596 [nacos-grpc-client-executor-1695] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] Ack server push request, request = NotifySubscriberRequest, requestId = 301
19:11:57.352 [nacos-grpc-client-executor-1705] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] Receive server push request, request = NotifySubscriberRequest, requestId = 303
19:11:57.367 [nacos-grpc-client-executor-1705] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] Ack server push request, request = NotifySubscriberRequest, requestId = 303
20:17:11.930 [nacos-grpc-client-executor-2486] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] Receive server push request, request = NotifySubscriberRequest, requestId = 306
20:17:11.961 [nacos-grpc-client-executor-2486] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [34b7ee55-39da-4821-8708-ece26d26601f] Ack server push request, request = NotifySubscriberRequest, requestId = 306
20:17:20.026 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751532645083 paused.
20:17:20.213 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:17:20.223 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:17:20.532 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:17:20.533 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4f5e8a2e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:17:20.533 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751532668985_127.0.0.1_3768
20:17:20.537 [nacos-grpc-client-executor-2491] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751532668985_127.0.0.1_3768]Ignore complete event,isRunning:false,isAbandon=false
20:17:20.542 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3e6d7be0[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 2492]
20:17:20.682 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751532645083 shutting down.
20:17:20.683 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751532645083 paused.
20:17:20.685 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751532645083 shutdown complete.
20:17:20.687 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:17:20.690 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
20:17:20.709 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
20:17:20.709 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:17:20.710 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:17:20.710 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:20:28.265 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:20:28.886 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bb5fa20d-2966-412a-8089-d3546df776b4_config-0
20:20:28.928 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 3 keys and 6 values 
20:20:28.955 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
20:20:28.962 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
20:20:28.969 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
20:20:28.976 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
20:20:28.983 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
20:20:28.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb5fa20d-2966-412a-8089-d3546df776b4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:20:28.986 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb5fa20d-2966-412a-8089-d3546df776b4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002395b39fd80
20:20:28.987 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb5fa20d-2966-412a-8089-d3546df776b4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002395b3a0000
20:20:28.987 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb5fa20d-2966-412a-8089-d3546df776b4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:20:28.988 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb5fa20d-2966-412a-8089-d3546df776b4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:20:28.995 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb5fa20d-2966-412a-8089-d3546df776b4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:20:29.765 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb5fa20d-2966-412a-8089-d3546df776b4_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751545229594_127.0.0.1_12665
20:20:29.766 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb5fa20d-2966-412a-8089-d3546df776b4_config-0] Notify connected event to listeners.
20:20:29.766 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb5fa20d-2966-412a-8089-d3546df776b4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:20:29.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb5fa20d-2966-412a-8089-d3546df776b4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002395b51a6e0
20:20:29.875 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:20:32.388 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
20:20:32.388 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:20:32.388 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
20:20:32.585 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:20:33.415 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
20:20:33.415 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
20:20:33.415 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
20:20:33.848 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
20:20:33.873 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
20:20:33.874 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
20:20:33.895 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751545233852'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

20:20:33.895 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
20:20:33.895 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
20:20:33.900 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@6fd37997
20:20:40.994 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
20:20:48.279 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f7a6f7b5-495c-4d14-98d8-58a731c25587
20:20:48.279 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7a6f7b5-495c-4d14-98d8-58a731c25587] RpcClient init label, labels = {module=naming, source=sdk}
20:20:48.286 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7a6f7b5-495c-4d14-98d8-58a731c25587] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
20:20:48.286 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7a6f7b5-495c-4d14-98d8-58a731c25587] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
20:20:48.287 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7a6f7b5-495c-4d14-98d8-58a731c25587] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
20:20:48.287 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7a6f7b5-495c-4d14-98d8-58a731c25587] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:20:48.411 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7a6f7b5-495c-4d14-98d8-58a731c25587] Success to connect to server [localhost:8848] on start up, connectionId = 1751545248295_127.0.0.1_12716
20:20:48.411 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7a6f7b5-495c-4d14-98d8-58a731c25587] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:20:48.411 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7a6f7b5-495c-4d14-98d8-58a731c25587] Notify connected event to listeners.
20:20:48.411 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7a6f7b5-495c-4d14-98d8-58a731c25587] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002395b51a6e0
20:20:48.469 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
20:20:48.513 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job ************:9500 register finished
20:20:48.686 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 20.94 seconds (JVM running for 22.225)
20:20:48.695 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
20:20:48.703 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
20:20:48.703 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
20:20:49.004 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7a6f7b5-495c-4d14-98d8-58a731c25587] Receive server push request, request = NotifySubscriberRequest, requestId = 312
20:20:49.011 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7a6f7b5-495c-4d14-98d8-58a731c25587] Ack server push request, request = NotifySubscriberRequest, requestId = 312
20:20:49.311 [RMI TCP Connection(1)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
20:20:49.814 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751545233852 started.
20:26:45.031 [nacos-grpc-client-executor-80] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7a6f7b5-495c-4d14-98d8-58a731c25587] Receive server push request, request = NotifySubscriberRequest, requestId = 319
20:26:45.039 [nacos-grpc-client-executor-80] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7a6f7b5-495c-4d14-98d8-58a731c25587] Ack server push request, request = NotifySubscriberRequest, requestId = 319
20:26:53.301 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb5fa20d-2966-412a-8089-d3546df776b4_config-0] Server healthy check fail, currentConnection = 1751545229594_127.0.0.1_12665
20:26:53.301 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb5fa20d-2966-412a-8089-d3546df776b4_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
20:26:53.806 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb5fa20d-2966-412a-8089-d3546df776b4_config-0] Success to connect a server [localhost:8848], connectionId = 1751545613590_127.0.0.1_13439
20:26:53.806 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb5fa20d-2966-412a-8089-d3546df776b4_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1751545229594_127.0.0.1_12665
20:26:53.835 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751545229594_127.0.0.1_12665
20:26:53.866 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb5fa20d-2966-412a-8089-d3546df776b4_config-0] Notify disconnected event to listeners
20:26:53.868 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bb5fa20d-2966-412a-8089-d3546df776b4_config-0] Notify connected event to listeners.
20:26:53.874 [nacos-grpc-client-executor-93] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751545229594_127.0.0.1_12665]Ignore complete event,isRunning:true,isAbandon=true
20:46:23.655 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751545233852 paused.
20:46:23.760 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:46:23.763 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:46:24.111 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:46:24.112 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@12aad373[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:46:24.112 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751545248295_127.0.0.1_12716
20:46:24.112 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1063bd[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 318]
20:46:24.114 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7a6f7b5-495c-4d14-98d8-58a731c25587] Notify disconnected event to listeners
20:46:24.261 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751545233852 shutting down.
20:46:24.262 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751545233852 paused.
20:46:24.263 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751545233852 shutdown complete.
20:46:24.265 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:46:24.267 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:46:24.276 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:46:24.276 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
