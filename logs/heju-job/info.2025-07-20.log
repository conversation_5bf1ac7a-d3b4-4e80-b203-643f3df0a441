12:47:06.801 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:47:07.502 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fd8ca198-412e-438d-b602-1dc26f65bb47_config-0
12:47:07.561 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 37 ms to scan 1 urls, producing 3 keys and 6 values 
12:47:07.581 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 4 keys and 9 values 
12:47:07.586 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 3 ms to scan 1 urls, producing 3 keys and 10 values 
12:47:07.597 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 5 values 
12:47:07.602 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 5 ms to scan 1 urls, producing 1 keys and 7 values 
12:47:07.602 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
12:47:07.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fd8ca198-412e-438d-b602-1dc26f65bb47_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:47:07.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fd8ca198-412e-438d-b602-1dc26f65bb47_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x00000170813b94f0
12:47:07.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fd8ca198-412e-438d-b602-1dc26f65bb47_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000170813b9710
12:47:07.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fd8ca198-412e-438d-b602-1dc26f65bb47_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:47:07.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fd8ca198-412e-438d-b602-1dc26f65bb47_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:47:07.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fd8ca198-412e-438d-b602-1dc26f65bb47_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:47:08.339 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fd8ca198-412e-438d-b602-1dc26f65bb47_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752986828151_127.0.0.1_2548
12:47:08.339 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fd8ca198-412e-438d-b602-1dc26f65bb47_config-0] Notify connected event to listeners.
12:47:08.339 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fd8ca198-412e-438d-b602-1dc26f65bb47_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:47:08.339 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fd8ca198-412e-438d-b602-1dc26f65bb47_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000170814f1210
12:47:08.486 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:47:10.505 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
12:47:10.505 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:47:10.505 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:47:10.624 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:47:11.257 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:47:11.257 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:47:11.272 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:47:11.624 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
12:47:11.647 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
12:47:11.647 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
12:47:11.668 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691752986831624'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

12:47:11.668 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
12:47:11.668 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
12:47:11.668 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@682d8dad
12:47:13.936 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:47:15.789 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of deda9cc3-38bb-4ea7-b566-bafca8b98e38
12:47:15.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [deda9cc3-38bb-4ea7-b566-bafca8b98e38] RpcClient init label, labels = {module=naming, source=sdk}
12:47:15.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [deda9cc3-38bb-4ea7-b566-bafca8b98e38] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:47:15.789 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [deda9cc3-38bb-4ea7-b566-bafca8b98e38] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:47:15.804 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [deda9cc3-38bb-4ea7-b566-bafca8b98e38] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:47:15.804 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [deda9cc3-38bb-4ea7-b566-bafca8b98e38] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
12:47:15.921 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [deda9cc3-38bb-4ea7-b566-bafca8b98e38] Success to connect to server [localhost:8848] on start up, connectionId = 1752986835807_127.0.0.1_2587
12:47:15.921 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [deda9cc3-38bb-4ea7-b566-bafca8b98e38] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:47:15.921 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [deda9cc3-38bb-4ea7-b566-bafca8b98e38] Notify connected event to listeners.
12:47:15.921 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [deda9cc3-38bb-4ea7-b566-bafca8b98e38] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000170814f1210
12:47:15.957 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
12:47:15.978 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job ************:9500 register finished
12:47:16.071 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 9.872 seconds (JVM running for 11.744)
12:47:16.087 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
12:47:16.087 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
12:47:16.087 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
12:47:16.457 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [deda9cc3-38bb-4ea7-b566-bafca8b98e38] Receive server push request, request = NotifySubscriberRequest, requestId = 16
12:47:16.461 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [deda9cc3-38bb-4ea7-b566-bafca8b98e38] Ack server push request, request = NotifySubscriberRequest, requestId = 16
12:47:17.357 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691752986831624 started.
12:47:35.732 [http-nio-9500-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:31:33.545 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691752986831624 paused.
13:31:33.583 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:31:33.593 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:31:33.902 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:31:33.902 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@69ba98da[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:31:33.902 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752986835807_127.0.0.1_2587
13:31:33.904 [nacos-grpc-client-executor-544] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752986835807_127.0.0.1_2587]Ignore complete event,isRunning:false,isAbandon=false
13:31:33.904 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7176ae75[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 545]
13:31:34.025 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691752986831624 shutting down.
13:31:34.026 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691752986831624 paused.
13:31:34.026 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691752986831624 shutdown complete.
13:31:34.026 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:31:34.026 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:31:34.026 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:31:34.026 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:31:39.052 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:31:39.531 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 53bc94ae-5c25-4e96-9fa6-53722719be8a_config-0
13:31:39.579 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 3 keys and 6 values 
13:31:39.595 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
13:31:39.602 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
13:31:39.608 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
13:31:39.614 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 7 values 
13:31:39.621 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
13:31:39.623 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53bc94ae-5c25-4e96-9fa6-53722719be8a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:31:39.624 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53bc94ae-5c25-4e96-9fa6-53722719be8a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000234013a7268
13:31:39.624 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53bc94ae-5c25-4e96-9fa6-53722719be8a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000234013a7488
13:31:39.624 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53bc94ae-5c25-4e96-9fa6-53722719be8a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:31:39.624 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53bc94ae-5c25-4e96-9fa6-53722719be8a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:31:39.631 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53bc94ae-5c25-4e96-9fa6-53722719be8a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:31:40.253 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53bc94ae-5c25-4e96-9fa6-53722719be8a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752989500089_127.0.0.1_7052
13:31:40.254 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53bc94ae-5c25-4e96-9fa6-53722719be8a_config-0] Notify connected event to listeners.
13:31:40.254 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53bc94ae-5c25-4e96-9fa6-53722719be8a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:31:40.255 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [53bc94ae-5c25-4e96-9fa6-53722719be8a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000234015270e0
13:31:40.331 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:31:42.260 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
13:31:42.261 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:31:42.261 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:31:42.381 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:31:42.887 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:31:42.888 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:31:42.888 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:31:43.079 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:31:43.091 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:31:43.091 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:31:43.104 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691752989503080'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

13:31:43.104 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
13:31:43.104 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:31:43.105 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@7369208e
13:31:45.468 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:31:47.256 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fa974fe8-ad19-4a25-8bbe-267fabd2a0c2
13:31:47.257 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa974fe8-ad19-4a25-8bbe-267fabd2a0c2] RpcClient init label, labels = {module=naming, source=sdk}
13:31:47.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa974fe8-ad19-4a25-8bbe-267fabd2a0c2] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:31:47.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa974fe8-ad19-4a25-8bbe-267fabd2a0c2] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:31:47.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa974fe8-ad19-4a25-8bbe-267fabd2a0c2] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:31:47.259 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa974fe8-ad19-4a25-8bbe-267fabd2a0c2] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:31:47.384 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa974fe8-ad19-4a25-8bbe-267fabd2a0c2] Success to connect to server [localhost:8848] on start up, connectionId = 1752989507266_127.0.0.1_7064
13:31:47.384 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa974fe8-ad19-4a25-8bbe-267fabd2a0c2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:31:47.384 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa974fe8-ad19-4a25-8bbe-267fabd2a0c2] Notify connected event to listeners.
13:31:47.384 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa974fe8-ad19-4a25-8bbe-267fabd2a0c2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000234015270e0
13:31:47.421 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
13:31:47.441 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job ************:9500 register finished
13:31:47.534 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 8.959 seconds (JVM running for 9.838)
13:31:47.535 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
13:31:47.535 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
13:31:47.544 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
13:31:47.692 [RMI TCP Connection(3)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:31:47.934 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa974fe8-ad19-4a25-8bbe-267fabd2a0c2] Receive server push request, request = NotifySubscriberRequest, requestId = 20
13:31:47.947 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa974fe8-ad19-4a25-8bbe-267fabd2a0c2] Ack server push request, request = NotifySubscriberRequest, requestId = 20
13:31:48.695 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691752989503080 started.
13:37:16.418 [nacos-grpc-client-executor-77] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa974fe8-ad19-4a25-8bbe-267fabd2a0c2] Receive server push request, request = NotifySubscriberRequest, requestId = 28
13:37:16.419 [nacos-grpc-client-executor-77] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa974fe8-ad19-4a25-8bbe-267fabd2a0c2] Ack server push request, request = NotifySubscriberRequest, requestId = 28
13:51:00.824 [nacos-grpc-client-executor-244] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa974fe8-ad19-4a25-8bbe-267fabd2a0c2] Receive server push request, request = NotifySubscriberRequest, requestId = 29
13:51:00.825 [nacos-grpc-client-executor-244] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa974fe8-ad19-4a25-8bbe-267fabd2a0c2] Ack server push request, request = NotifySubscriberRequest, requestId = 29
13:55:06.395 [nacos-grpc-client-executor-299] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa974fe8-ad19-4a25-8bbe-267fabd2a0c2] Receive server push request, request = NotifySubscriberRequest, requestId = 33
13:55:06.411 [nacos-grpc-client-executor-299] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa974fe8-ad19-4a25-8bbe-267fabd2a0c2] Ack server push request, request = NotifySubscriberRequest, requestId = 33
13:55:26.148 [nacos-grpc-client-executor-303] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa974fe8-ad19-4a25-8bbe-267fabd2a0c2] Receive server push request, request = NotifySubscriberRequest, requestId = 38
13:55:26.194 [nacos-grpc-client-executor-303] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fa974fe8-ad19-4a25-8bbe-267fabd2a0c2] Ack server push request, request = NotifySubscriberRequest, requestId = 38
14:18:42.795 [http-nio-9500-exec-1] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:18:42.795 [http-nio-9500-exec-1] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:32:37.737 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691752989503080 paused.
14:32:37.779 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:32:37.782 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:32:38.088 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:32:38.088 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6cc8bc94[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:32:38.088 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752989507266_127.0.0.1_7064
14:32:38.090 [nacos-grpc-client-executor-592] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752989507266_127.0.0.1_7064]Ignore complete event,isRunning:false,isAbandon=false
14:32:38.091 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@ad51f0b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 593]
14:32:38.212 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691752989503080 shutting down.
14:32:38.213 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691752989503080 paused.
14:32:38.214 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691752989503080 shutdown complete.
14:32:38.214 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:32:38.214 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:32:38.214 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:32:38.214 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:32:38.214 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:32:38.214 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:32:42.298 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:32:42.782 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d4ad9801-4983-46d5-b823-31f7e1ceb7d7_config-0
14:32:42.826 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 3 keys and 6 values 
14:32:42.844 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
14:32:42.857 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
14:32:42.863 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
14:32:42.870 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
14:32:42.876 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
14:32:42.878 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4ad9801-4983-46d5-b823-31f7e1ceb7d7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:32:42.879 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4ad9801-4983-46d5-b823-31f7e1ceb7d7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001efdf3bf8e0
14:32:42.880 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4ad9801-4983-46d5-b823-31f7e1ceb7d7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001efdf3bfb00
14:32:42.881 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4ad9801-4983-46d5-b823-31f7e1ceb7d7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:32:42.882 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4ad9801-4983-46d5-b823-31f7e1ceb7d7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:32:42.888 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4ad9801-4983-46d5-b823-31f7e1ceb7d7_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:32:43.554 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4ad9801-4983-46d5-b823-31f7e1ceb7d7_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752993163389_127.0.0.1_11827
14:32:43.555 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4ad9801-4983-46d5-b823-31f7e1ceb7d7_config-0] Notify connected event to listeners.
14:32:43.556 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4ad9801-4983-46d5-b823-31f7e1ceb7d7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:32:43.557 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4ad9801-4983-46d5-b823-31f7e1ceb7d7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001efdf4f9450
14:32:43.638 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:32:45.658 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
14:32:45.658 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:32:45.658 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:32:45.767 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:32:46.208 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:32:46.209 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:32:46.209 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:32:46.401 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:32:46.414 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:32:46.415 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:32:46.429 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691752993166403'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

14:32:46.430 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
14:32:46.430 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:32:46.431 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@7369208e
14:32:48.642 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:32:50.465 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b6a6a3aa-e946-4c34-9ada-c74c7a5709c5
14:32:50.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6a6a3aa-e946-4c34-9ada-c74c7a5709c5] RpcClient init label, labels = {module=naming, source=sdk}
14:32:50.466 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6a6a3aa-e946-4c34-9ada-c74c7a5709c5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:32:50.466 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6a6a3aa-e946-4c34-9ada-c74c7a5709c5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:32:50.467 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6a6a3aa-e946-4c34-9ada-c74c7a5709c5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:32:50.467 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6a6a3aa-e946-4c34-9ada-c74c7a5709c5] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:32:50.585 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6a6a3aa-e946-4c34-9ada-c74c7a5709c5] Success to connect to server [localhost:8848] on start up, connectionId = 1752993170473_127.0.0.1_11846
14:32:50.585 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6a6a3aa-e946-4c34-9ada-c74c7a5709c5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:32:50.585 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6a6a3aa-e946-4c34-9ada-c74c7a5709c5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001efdf4f9450
14:32:50.585 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6a6a3aa-e946-4c34-9ada-c74c7a5709c5] Notify connected event to listeners.
14:32:50.621 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
14:32:50.641 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job ************:9500 register finished
14:32:50.730 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 8.918 seconds (JVM running for 9.71)
14:32:50.740 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
14:32:50.741 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
14:32:50.743 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
14:32:51.008 [RMI TCP Connection(4)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:32:51.132 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6a6a3aa-e946-4c34-9ada-c74c7a5709c5] Receive server push request, request = NotifySubscriberRequest, requestId = 44
14:32:51.148 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6a6a3aa-e946-4c34-9ada-c74c7a5709c5] Ack server push request, request = NotifySubscriberRequest, requestId = 44
14:32:51.765 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691752993166403 started.
14:33:00.671 [http-nio-9500-exec-8] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
14:33:00.671 [http-nio-9500-exec-8] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
14:33:00.692 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6a6a3aa-e946-4c34-9ada-c74c7a5709c5] Receive server push request, request = NotifySubscriberRequest, requestId = 46
14:33:00.693 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6a6a3aa-e946-4c34-9ada-c74c7a5709c5] Ack server push request, request = NotifySubscriberRequest, requestId = 46
14:33:50.183 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4ad9801-4983-46d5-b823-31f7e1ceb7d7_config-0] Server healthy check fail, currentConnection = 1752993163389_127.0.0.1_11827
14:33:50.186 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6a6a3aa-e946-4c34-9ada-c74c7a5709c5] Server healthy check fail, currentConnection = 1752993170473_127.0.0.1_11846
14:33:50.186 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4ad9801-4983-46d5-b823-31f7e1ceb7d7_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:33:50.188 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6a6a3aa-e946-4c34-9ada-c74c7a5709c5] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:33:58.684 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4ad9801-4983-46d5-b823-31f7e1ceb7d7_config-0] Success to connect a server [localhost:8848], connectionId = 1752993237516_127.0.0.1_11940
14:33:58.685 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4ad9801-4983-46d5-b823-31f7e1ceb7d7_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1752993163389_127.0.0.1_11827
14:33:58.685 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752993163389_127.0.0.1_11827
14:33:58.684 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6a6a3aa-e946-4c34-9ada-c74c7a5709c5] Success to connect a server [localhost:8848], connectionId = 1752993237516_127.0.0.1_11939
14:33:58.686 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6a6a3aa-e946-4c34-9ada-c74c7a5709c5] Abandon prev connection, server is localhost:8848, connectionId is 1752993170473_127.0.0.1_11846
14:33:58.686 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752993170473_127.0.0.1_11846
14:34:08.500 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752993163389_127.0.0.1_11827]Ignore complete event,isRunning:false,isAbandon=true
14:34:10.726 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752993170473_127.0.0.1_11846]Ignore complete event,isRunning:false,isAbandon=true
14:34:14.980 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6a6a3aa-e946-4c34-9ada-c74c7a5709c5] Notify disconnected event to listeners
14:34:14.980 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4ad9801-4983-46d5-b823-31f7e1ceb7d7_config-0] Notify disconnected event to listeners
14:34:14.981 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4ad9801-4983-46d5-b823-31f7e1ceb7d7_config-0] Notify connected event to listeners.
14:34:14.981 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6a6a3aa-e946-4c34-9ada-c74c7a5709c5] Notify connected event to listeners.
14:34:18.528 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6a6a3aa-e946-4c34-9ada-c74c7a5709c5] Receive server push request, request = NotifySubscriberRequest, requestId = 49
14:34:18.529 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6a6a3aa-e946-4c34-9ada-c74c7a5709c5] Ack server push request, request = NotifySubscriberRequest, requestId = 49
14:34:18.538 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6a6a3aa-e946-4c34-9ada-c74c7a5709c5] Receive server push request, request = NotifySubscriberRequest, requestId = 50
14:34:18.554 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b6a6a3aa-e946-4c34-9ada-c74c7a5709c5] Ack server push request, request = NotifySubscriberRequest, requestId = 50
14:38:15.044 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691752993166403 paused.
14:38:15.084 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:38:15.096 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:38:15.406 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:38:15.407 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1fcdaa75[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:38:15.407 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752993237516_127.0.0.1_11939
14:38:15.409 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2040aac9[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 78]
14:38:15.530 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691752993166403 shutting down.
14:38:15.531 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691752993166403 paused.
14:38:15.532 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691752993166403 shutdown complete.
14:38:15.532 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:38:15.534 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
14:38:15.536 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
14:38:15.536 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:38:15.536 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:38:15.536 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:38:19.393 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:38:19.926 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6c2170ce-db48-4e5c-9008-72ee47fe0dcf_config-0
14:38:19.976 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 6 values 
14:38:19.998 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
14:38:20.009 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
14:38:20.012 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
14:38:20.030 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 1 keys and 7 values 
14:38:20.036 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
14:38:20.038 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c2170ce-db48-4e5c-9008-72ee47fe0dcf_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:38:20.039 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c2170ce-db48-4e5c-9008-72ee47fe0dcf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x00000217ba3beff8
14:38:20.039 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c2170ce-db48-4e5c-9008-72ee47fe0dcf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000217ba3bf218
14:38:20.039 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c2170ce-db48-4e5c-9008-72ee47fe0dcf_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:38:20.039 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c2170ce-db48-4e5c-9008-72ee47fe0dcf_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:38:20.044 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c2170ce-db48-4e5c-9008-72ee47fe0dcf_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:38:20.783 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c2170ce-db48-4e5c-9008-72ee47fe0dcf_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752993500585_127.0.0.1_12265
14:38:20.784 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c2170ce-db48-4e5c-9008-72ee47fe0dcf_config-0] Notify connected event to listeners.
14:38:20.785 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c2170ce-db48-4e5c-9008-72ee47fe0dcf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:38:20.786 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6c2170ce-db48-4e5c-9008-72ee47fe0dcf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000217ba4f8fb0
14:38:20.883 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:38:22.807 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
14:38:22.807 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:38:22.808 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:38:22.914 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:38:23.458 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:38:23.459 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:38:23.459 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:38:23.665 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:38:23.679 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:38:23.679 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:38:23.693 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691752993503667'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

14:38:23.693 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
14:38:23.693 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:38:23.695 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@576a8b00
14:38:25.781 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:38:27.767 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0b0d7dc6-aa34-4f9e-8a90-2ce94d2a0ce4
14:38:27.767 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b0d7dc6-aa34-4f9e-8a90-2ce94d2a0ce4] RpcClient init label, labels = {module=naming, source=sdk}
14:38:27.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b0d7dc6-aa34-4f9e-8a90-2ce94d2a0ce4] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:38:27.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b0d7dc6-aa34-4f9e-8a90-2ce94d2a0ce4] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:38:27.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b0d7dc6-aa34-4f9e-8a90-2ce94d2a0ce4] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:38:27.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b0d7dc6-aa34-4f9e-8a90-2ce94d2a0ce4] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:38:27.887 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b0d7dc6-aa34-4f9e-8a90-2ce94d2a0ce4] Success to connect to server [localhost:8848] on start up, connectionId = 1752993507777_127.0.0.1_12278
14:38:27.888 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b0d7dc6-aa34-4f9e-8a90-2ce94d2a0ce4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:38:27.888 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b0d7dc6-aa34-4f9e-8a90-2ce94d2a0ce4] Notify connected event to listeners.
14:38:27.888 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b0d7dc6-aa34-4f9e-8a90-2ce94d2a0ce4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000217ba4f8fb0
14:38:27.928 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
14:38:27.953 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job ************:9500 register finished
14:38:28.041 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 9.104 seconds (JVM running for 9.971)
14:38:28.053 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
14:38:28.054 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
14:38:28.056 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
14:38:28.141 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:38:28.492 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b0d7dc6-aa34-4f9e-8a90-2ce94d2a0ce4] Receive server push request, request = NotifySubscriberRequest, requestId = 52
14:38:28.507 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b0d7dc6-aa34-4f9e-8a90-2ce94d2a0ce4] Ack server push request, request = NotifySubscriberRequest, requestId = 52
14:38:29.130 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691752993503667 started.
14:38:35.160 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b0d7dc6-aa34-4f9e-8a90-2ce94d2a0ce4] Receive server push request, request = NotifySubscriberRequest, requestId = 54
14:38:35.161 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b0d7dc6-aa34-4f9e-8a90-2ce94d2a0ce4] Ack server push request, request = NotifySubscriberRequest, requestId = 54
14:39:00.835 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b0d7dc6-aa34-4f9e-8a90-2ce94d2a0ce4] Receive server push request, request = NotifySubscriberRequest, requestId = 55
14:39:00.835 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b0d7dc6-aa34-4f9e-8a90-2ce94d2a0ce4] Ack server push request, request = NotifySubscriberRequest, requestId = 55
17:57:56.304 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691752993503667 paused.
17:57:56.882 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:57:56.890 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:57:57.205 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:57:57.206 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7eb5ab45[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:57:57.206 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752993507777_127.0.0.1_12278
17:57:57.211 [nacos-grpc-client-executor-2613] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752993507777_127.0.0.1_12278]Ignore complete event,isRunning:false,isAbandon=false
17:57:57.223 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@28e380fc[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2614]
17:57:57.410 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691752993503667 shutting down.
17:57:57.411 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691752993503667 paused.
17:57:57.412 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691752993503667 shutdown complete.
17:57:57.412 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:57:57.412 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:57:57.443 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:57:57.443 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
