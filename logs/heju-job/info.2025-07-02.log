09:22:14.877 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:22:17.060 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4f9084f8-a647-4eee-9481-f33a3e8aa5c6_config-0
09:22:17.249 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 85 ms to scan 1 urls, producing 3 keys and 6 values 
09:22:17.370 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 32 ms to scan 1 urls, producing 4 keys and 9 values 
09:22:17.417 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 40 ms to scan 1 urls, producing 3 keys and 10 values 
09:22:17.450 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 25 ms to scan 1 urls, producing 1 keys and 5 values 
09:22:17.477 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 21 ms to scan 1 urls, producing 1 keys and 7 values 
09:22:17.496 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
09:22:17.506 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f9084f8-a647-4eee-9481-f33a3e8aa5c6_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:22:17.509 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f9084f8-a647-4eee-9481-f33a3e8aa5c6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001a28b3b6f80
09:22:17.511 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f9084f8-a647-4eee-9481-f33a3e8aa5c6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001a28b3b71a0
09:22:17.512 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f9084f8-a647-4eee-9481-f33a3e8aa5c6_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:22:17.514 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f9084f8-a647-4eee-9481-f33a3e8aa5c6_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:22:17.540 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f9084f8-a647-4eee-9481-f33a3e8aa5c6_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:22:21.941 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f9084f8-a647-4eee-9481-f33a3e8aa5c6_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:22:24.951 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f9084f8-a647-4eee-9481-f33a3e8aa5c6_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:22:27.977 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f9084f8-a647-4eee-9481-f33a3e8aa5c6_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:22:27.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f9084f8-a647-4eee-9481-f33a3e8aa5c6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:22:27.980 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f9084f8-a647-4eee-9481-f33a3e8aa5c6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001a28b4c5a28
09:22:30.213 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:22:34.120 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f9084f8-a647-4eee-9481-f33a3e8aa5c6_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:37.371 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f9084f8-a647-4eee-9481-f33a3e8aa5c6_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:40.705 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f9084f8-a647-4eee-9481-f33a3e8aa5c6_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:41.353 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
09:22:41.353 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:22:41.353 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:22:41.998 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:22:43.836 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:22:43.838 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:22:43.838 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:22:44.121 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f9084f8-a647-4eee-9481-f33a3e8aa5c6_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:44.863 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:22:44.936 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:22:44.940 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:22:45.003 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751419364873'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

09:22:45.005 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
09:22:45.005 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:22:45.013 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@20706e70
09:22:47.644 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f9084f8-a647-4eee-9481-f33a3e8aa5c6_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:51.265 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f9084f8-a647-4eee-9481-f33a3e8aa5c6_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:22:54.193 [main] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751419364873 shutting down.
09:22:54.193 [main] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751419364873 paused.
09:22:54.193 [main] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751419364873 shutdown complete.
09:22:54.193 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:22:54.197 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:22:54.216 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:22:54.216 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:22:54.216 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:22:54.988 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f9084f8-a647-4eee-9481-f33a3e8aa5c6_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:23:33.993 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:23:35.900 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0e9e2541-dbfb-49e1-b92c-7c1c31bc9d4e_config-0
09:23:36.030 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 64 ms to scan 1 urls, producing 3 keys and 6 values 
09:23:36.132 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 35 ms to scan 1 urls, producing 4 keys and 9 values 
09:23:36.148 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 3 keys and 10 values 
09:23:36.165 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
09:23:36.206 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
09:23:36.213 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
09:23:36.229 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e9e2541-dbfb-49e1-b92c-7c1c31bc9d4e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:23:36.229 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e9e2541-dbfb-49e1-b92c-7c1c31bc9d4e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000028cd93b6480
09:23:36.229 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e9e2541-dbfb-49e1-b92c-7c1c31bc9d4e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x0000028cd93b66a0
09:23:36.229 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e9e2541-dbfb-49e1-b92c-7c1c31bc9d4e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:23:36.229 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e9e2541-dbfb-49e1-b92c-7c1c31bc9d4e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:23:36.250 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e9e2541-dbfb-49e1-b92c-7c1c31bc9d4e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:23:40.174 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e9e2541-dbfb-49e1-b92c-7c1c31bc9d4e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:23:43.185 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e9e2541-dbfb-49e1-b92c-7c1c31bc9d4e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:23:46.200 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e9e2541-dbfb-49e1-b92c-7c1c31bc9d4e_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:23:46.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e9e2541-dbfb-49e1-b92c-7c1c31bc9d4e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:23:46.202 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e9e2541-dbfb-49e1-b92c-7c1c31bc9d4e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x0000028cd94c5230
09:23:48.361 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:23:50.918 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
09:23:50.918 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:23:50.918 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:23:51.063 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:23:51.691 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:23:51.691 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:23:51.691 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:23:51.948 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:23:51.964 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:23:51.964 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:23:51.977 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751419431950'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

09:23:51.977 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
09:23:51.977 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:23:51.977 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@6e54bef6
09:23:52.344 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e9e2541-dbfb-49e1-b92c-7c1c31bc9d4e_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:23:54.628 [main] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751419431950 shutting down.
09:23:54.628 [main] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751419431950 paused.
09:23:54.628 [main] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751419431950 shutdown complete.
09:23:54.629 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:23:54.630 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:23:54.632 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:23:54.632 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:23:54.636 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:23:55.585 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0e9e2541-dbfb-49e1-b92c-7c1c31bc9d4e_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:32:01.479 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:32:02.043 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8df107f5-2b66-4f9d-a9b2-9ee0ff59aa37_config-0
09:32:02.093 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 6 values 
09:32:02.130 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
09:32:02.138 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
09:32:02.144 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
09:32:02.152 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
09:32:02.159 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
09:32:02.163 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8df107f5-2b66-4f9d-a9b2-9ee0ff59aa37_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:32:02.163 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8df107f5-2b66-4f9d-a9b2-9ee0ff59aa37_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001e3a53ce480
09:32:02.164 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8df107f5-2b66-4f9d-a9b2-9ee0ff59aa37_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001e3a53ce6a0
09:32:02.165 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8df107f5-2b66-4f9d-a9b2-9ee0ff59aa37_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:32:02.165 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8df107f5-2b66-4f9d-a9b2-9ee0ff59aa37_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:32:02.170 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8df107f5-2b66-4f9d-a9b2-9ee0ff59aa37_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:32:02.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8df107f5-2b66-4f9d-a9b2-9ee0ff59aa37_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751419922766_127.0.0.1_4647
09:32:02.931 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8df107f5-2b66-4f9d-a9b2-9ee0ff59aa37_config-0] Notify connected event to listeners.
09:32:02.934 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8df107f5-2b66-4f9d-a9b2-9ee0ff59aa37_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:32:02.939 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8df107f5-2b66-4f9d-a9b2-9ee0ff59aa37_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001e3a5508228
09:32:03.069 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:32:05.676 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
09:32:05.676 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:32:05.676 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:32:05.815 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:32:06.368 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:32:06.370 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:32:06.370 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:32:06.602 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:32:06.617 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:32:06.617 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:32:06.630 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751419926605'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

09:32:06.630 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
09:32:06.630 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:32:06.632 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@3677c508
09:32:09.477 [main] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751419926605 shutting down.
09:32:09.477 [main] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751419926605 paused.
09:32:09.477 [main] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751419926605 shutdown complete.
09:32:09.478 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
09:32:09.480 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
09:32:09.485 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
09:32:09.485 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
09:32:09.486 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:37:28.788 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:37:29.342 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4f2ff33a-6a86-44f6-8421-75a135cf7c86_config-0
09:37:29.409 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 36 ms to scan 1 urls, producing 3 keys and 6 values 
09:37:29.455 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
09:37:29.462 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 3 keys and 10 values 
09:37:29.469 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
09:37:29.475 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
09:37:29.482 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
09:37:29.484 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f2ff33a-6a86-44f6-8421-75a135cf7c86_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:37:29.485 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f2ff33a-6a86-44f6-8421-75a135cf7c86_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001b9a23ce8d8
09:37:29.485 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f2ff33a-6a86-44f6-8421-75a135cf7c86_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001b9a23ceaf8
09:37:29.486 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f2ff33a-6a86-44f6-8421-75a135cf7c86_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:37:29.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f2ff33a-6a86-44f6-8421-75a135cf7c86_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:37:29.498 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f2ff33a-6a86-44f6-8421-75a135cf7c86_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:37:30.465 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f2ff33a-6a86-44f6-8421-75a135cf7c86_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751420250271_127.0.0.1_5123
09:37:30.466 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f2ff33a-6a86-44f6-8421-75a135cf7c86_config-0] Notify connected event to listeners.
09:37:30.466 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f2ff33a-6a86-44f6-8421-75a135cf7c86_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:37:30.466 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4f2ff33a-6a86-44f6-8421-75a135cf7c86_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001b9a2508ad8
09:37:30.557 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:37:33.008 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
09:37:33.008 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:37:33.008 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:37:33.143 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:37:33.707 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:37:33.708 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:37:33.709 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:37:33.928 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:37:33.950 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:37:33.950 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:37:33.970 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751420253937'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

09:37:33.970 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
09:37:33.970 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:37:33.973 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@3677c508
09:37:37.222 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:37:40.949 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of adf454c5-cb84-4e21-9dbc-499618d349bb
09:37:40.949 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adf454c5-cb84-4e21-9dbc-499618d349bb] RpcClient init label, labels = {module=naming, source=sdk}
09:37:40.951 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adf454c5-cb84-4e21-9dbc-499618d349bb] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:37:40.951 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adf454c5-cb84-4e21-9dbc-499618d349bb] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:37:40.951 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adf454c5-cb84-4e21-9dbc-499618d349bb] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:37:40.951 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adf454c5-cb84-4e21-9dbc-499618d349bb] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
09:37:41.095 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adf454c5-cb84-4e21-9dbc-499618d349bb] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751420260960_127.0.0.1_5156
09:37:41.096 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adf454c5-cb84-4e21-9dbc-499618d349bb] Notify connected event to listeners.
09:37:41.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adf454c5-cb84-4e21-9dbc-499618d349bb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:37:41.098 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adf454c5-cb84-4e21-9dbc-499618d349bb] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001b9a2508ad8
09:37:41.130 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
09:37:41.147 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job 192.168.2.43:9500 register finished
09:37:41.393 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 13.139 seconds (JVM running for 15.861)
09:37:41.405 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
09:37:41.406 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
09:37:41.409 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
09:37:41.702 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adf454c5-cb84-4e21-9dbc-499618d349bb] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:37:41.723 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adf454c5-cb84-4e21-9dbc-499618d349bb] Ack server push request, request = NotifySubscriberRequest, requestId = 12
09:37:42.289 [RMI TCP Connection(7)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:37:42.471 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751420253937 started.
10:17:53.269 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adf454c5-cb84-4e21-9dbc-499618d349bb] Server healthy check fail, currentConnection = 1751420260960_127.0.0.1_5156
10:17:53.271 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adf454c5-cb84-4e21-9dbc-499618d349bb] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:17:54.093 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adf454c5-cb84-4e21-9dbc-499618d349bb] Success to connect a server [127.0.0.1:8848], connectionId = 1751422673863_127.0.0.1_8134
10:17:54.093 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adf454c5-cb84-4e21-9dbc-499618d349bb] Abandon prev connection, server is 127.0.0.1:8848, connectionId is 1751420260960_127.0.0.1_5156
10:17:54.093 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751420260960_127.0.0.1_5156
10:17:54.156 [nacos-grpc-client-executor-485] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751420260960_127.0.0.1_5156]Ignore complete event,isRunning:false,isAbandon=true
10:17:54.157 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adf454c5-cb84-4e21-9dbc-499618d349bb] Notify disconnected event to listeners
10:17:54.161 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adf454c5-cb84-4e21-9dbc-499618d349bb] Notify connected event to listeners.
10:17:56.859 [nacos-grpc-client-executor-488] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adf454c5-cb84-4e21-9dbc-499618d349bb] Receive server push request, request = NotifySubscriberRequest, requestId = 36
10:17:56.861 [nacos-grpc-client-executor-488] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [adf454c5-cb84-4e21-9dbc-499618d349bb] Ack server push request, request = NotifySubscriberRequest, requestId = 36
10:35:53.645 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751420253937 paused.
10:35:53.686 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:35:53.688 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:35:54.003 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:35:54.003 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@282295a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:35:54.004 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751422673863_127.0.0.1_8134
10:35:54.006 [nacos-grpc-client-executor-705] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751422673863_127.0.0.1_8134]Ignore complete event,isRunning:false,isAbandon=false
10:35:54.006 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@61159fa1[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 706]
10:35:54.135 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751420253937 shutting down.
10:35:54.135 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751420253937 paused.
10:35:54.136 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751420253937 shutdown complete.
10:35:54.136 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:35:54.140 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:35:54.147 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:35:54.147 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:36:01.559 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:36:02.112 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d5cd4fc7-10c9-485a-9419-74c788a2c150_config-0
10:36:02.179 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 34 ms to scan 1 urls, producing 3 keys and 6 values 
10:36:02.215 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 4 keys and 9 values 
10:36:02.223 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
10:36:02.232 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
10:36:02.240 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
10:36:02.245 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
10:36:02.248 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5cd4fc7-10c9-485a-9419-74c788a2c150_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:36:02.249 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5cd4fc7-10c9-485a-9419-74c788a2c150_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002489439ed38
10:36:02.249 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5cd4fc7-10c9-485a-9419-74c788a2c150_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002489439ef58
10:36:02.250 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5cd4fc7-10c9-485a-9419-74c788a2c150_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:36:02.251 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5cd4fc7-10c9-485a-9419-74c788a2c150_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:36:02.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5cd4fc7-10c9-485a-9419-74c788a2c150_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
10:36:03.109 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5cd4fc7-10c9-485a-9419-74c788a2c150_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751423762941_127.0.0.1_9837
10:36:03.112 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5cd4fc7-10c9-485a-9419-74c788a2c150_config-0] Notify connected event to listeners.
10:36:03.112 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5cd4fc7-10c9-485a-9419-74c788a2c150_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:36:03.116 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d5cd4fc7-10c9-485a-9419-74c788a2c150_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000024894518ad8
10:36:03.235 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:36:05.944 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
10:36:05.944 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:36:05.944 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:36:06.087 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:36:06.658 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:36:06.660 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:36:06.660 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:36:06.892 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:36:06.906 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:36:06.906 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:36:06.918 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751423766894'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

10:36:06.918 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
10:36:06.918 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:36:06.920 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@43255f91
10:36:09.923 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:36:14.286 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 18785b70-1795-467e-99fd-40c9cdd6db7b
10:36:14.287 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [18785b70-1795-467e-99fd-40c9cdd6db7b] RpcClient init label, labels = {module=naming, source=sdk}
10:36:14.288 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [18785b70-1795-467e-99fd-40c9cdd6db7b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:36:14.289 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [18785b70-1795-467e-99fd-40c9cdd6db7b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:36:14.289 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [18785b70-1795-467e-99fd-40c9cdd6db7b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:36:14.289 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [18785b70-1795-467e-99fd-40c9cdd6db7b] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
10:36:14.422 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [18785b70-1795-467e-99fd-40c9cdd6db7b] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751423774297_127.0.0.1_9879
10:36:14.423 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [18785b70-1795-467e-99fd-40c9cdd6db7b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:36:14.423 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [18785b70-1795-467e-99fd-40c9cdd6db7b] Notify connected event to listeners.
10:36:14.423 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [18785b70-1795-467e-99fd-40c9cdd6db7b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000024894518ad8
10:36:14.472 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
10:36:14.530 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job 192.168.2.43:9500 register finished
10:36:14.648 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 13.687 seconds (JVM running for 14.626)
10:36:14.666 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
10:36:14.674 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
10:36:14.691 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
10:36:14.986 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [18785b70-1795-467e-99fd-40c9cdd6db7b] Receive server push request, request = NotifySubscriberRequest, requestId = 39
10:36:15.003 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [18785b70-1795-467e-99fd-40c9cdd6db7b] Ack server push request, request = NotifySubscriberRequest, requestId = 39
10:36:15.192 [RMI TCP Connection(1)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:36:15.789 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751423766894 started.
10:36:16.704 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [18785b70-1795-467e-99fd-40c9cdd6db7b] Receive server push request, request = NotifySubscriberRequest, requestId = 41
10:36:16.705 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [18785b70-1795-467e-99fd-40c9cdd6db7b] Ack server push request, request = NotifySubscriberRequest, requestId = 41
10:40:47.764 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751423766894 paused.
10:40:47.856 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:40:47.866 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:40:48.180 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:40:48.181 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@63f06953[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:40:48.181 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751423774297_127.0.0.1_9879
10:40:48.183 [nacos-grpc-client-executor-77] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751423774297_127.0.0.1_9879]Ignore complete event,isRunning:false,isAbandon=false
10:40:48.188 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5ab3e062[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 78]
10:40:48.339 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751423766894 shutting down.
10:40:48.339 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751423766894 paused.
10:40:48.341 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751423766894 shutdown complete.
10:40:48.342 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:40:48.346 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:40:48.363 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:40:48.363 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
10:40:55.428 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:40:56.395 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2217c6ef-0044-4fbd-880a-4d9293dac53a_config-0
10:40:56.495 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 46 ms to scan 1 urls, producing 3 keys and 6 values 
10:40:56.574 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 4 keys and 9 values 
10:40:56.591 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 3 keys and 10 values 
10:40:56.606 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 5 values 
10:40:56.622 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
10:40:56.623 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
10:40:56.623 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2217c6ef-0044-4fbd-880a-4d9293dac53a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:40:56.638 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2217c6ef-0044-4fbd-880a-4d9293dac53a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000287383b68d8
10:40:56.640 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2217c6ef-0044-4fbd-880a-4d9293dac53a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000287383b6af8
10:40:56.641 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2217c6ef-0044-4fbd-880a-4d9293dac53a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:40:56.642 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2217c6ef-0044-4fbd-880a-4d9293dac53a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:40:56.659 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2217c6ef-0044-4fbd-880a-4d9293dac53a_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
10:40:57.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2217c6ef-0044-4fbd-880a-4d9293dac53a_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751424057617_127.0.0.1_10380
10:40:57.916 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2217c6ef-0044-4fbd-880a-4d9293dac53a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:40:57.916 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2217c6ef-0044-4fbd-880a-4d9293dac53a_config-0] Notify connected event to listeners.
10:40:57.917 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2217c6ef-0044-4fbd-880a-4d9293dac53a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000287384f0ad8
10:40:58.076 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:41:02.467 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
10:41:02.467 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:41:02.467 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:41:02.729 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:41:03.642 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
10:41:03.642 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
10:41:03.642 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
10:41:04.087 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
10:41:04.123 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
10:41:04.123 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
10:41:04.139 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751424064097'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

10:41:04.139 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
10:41:04.139 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
10:41:04.139 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@147c4523
10:41:08.569 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:41:15.291 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e0a8b8f2-713e-45c6-aa7e-10be8ba5ec6e
10:41:15.291 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0a8b8f2-713e-45c6-aa7e-10be8ba5ec6e] RpcClient init label, labels = {module=naming, source=sdk}
10:41:15.291 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0a8b8f2-713e-45c6-aa7e-10be8ba5ec6e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:41:15.291 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0a8b8f2-713e-45c6-aa7e-10be8ba5ec6e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:41:15.291 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0a8b8f2-713e-45c6-aa7e-10be8ba5ec6e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:41:15.291 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0a8b8f2-713e-45c6-aa7e-10be8ba5ec6e] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
10:41:15.429 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0a8b8f2-713e-45c6-aa7e-10be8ba5ec6e] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751424075306_127.0.0.1_10414
10:41:15.429 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0a8b8f2-713e-45c6-aa7e-10be8ba5ec6e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:41:15.429 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0a8b8f2-713e-45c6-aa7e-10be8ba5ec6e] Notify connected event to listeners.
10:41:15.430 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0a8b8f2-713e-45c6-aa7e-10be8ba5ec6e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000287384f0ad8
10:41:15.517 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
10:41:15.575 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job 192.168.2.43:9500 register finished
10:41:15.744 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 21.098 seconds (JVM running for 22.633)
10:41:15.760 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
10:41:15.776 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
10:41:15.784 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
10:41:16.049 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0a8b8f2-713e-45c6-aa7e-10be8ba5ec6e] Receive server push request, request = NotifySubscriberRequest, requestId = 43
10:41:16.074 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0a8b8f2-713e-45c6-aa7e-10be8ba5ec6e] Ack server push request, request = NotifySubscriberRequest, requestId = 43
10:41:16.782 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751424064097 started.
11:41:35.568 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751424064097 paused.
11:41:35.628 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:41:35.631 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:41:35.950 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:41:35.951 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@9d77cbd[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:41:35.952 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751424075306_127.0.0.1_10414
11:41:35.957 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6f972079[Running, pool size = 4, active threads = 1, queued tasks = 0, completed tasks = 732]
11:41:35.957 [nacos-grpc-client-executor-732] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751424075306_127.0.0.1_10414]Ignore complete event,isRunning:false,isAbandon=false
11:41:36.043 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751424064097 shutting down.
11:41:36.043 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751424064097 paused.
11:41:36.055 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751424064097 shutdown complete.
11:41:36.058 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:41:36.062 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:41:36.077 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:41:36.078 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:41:40.362 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:41:41.325 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f468977f-d78b-4c11-a7a9-4bc0a8ec4c1b_config-0
11:41:41.417 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 39 ms to scan 1 urls, producing 3 keys and 6 values 
11:41:41.483 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
11:41:41.493 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 3 keys and 10 values 
11:41:41.509 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
11:41:41.525 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
11:41:41.537 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
11:41:41.542 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f468977f-d78b-4c11-a7a9-4bc0a8ec4c1b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:41:41.544 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f468977f-d78b-4c11-a7a9-4bc0a8ec4c1b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002cb0b39ed38
11:41:41.544 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f468977f-d78b-4c11-a7a9-4bc0a8ec4c1b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002cb0b39ef58
11:41:41.545 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f468977f-d78b-4c11-a7a9-4bc0a8ec4c1b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:41:41.547 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f468977f-d78b-4c11-a7a9-4bc0a8ec4c1b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:41:41.561 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f468977f-d78b-4c11-a7a9-4bc0a8ec4c1b_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:41:42.805 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f468977f-d78b-4c11-a7a9-4bc0a8ec4c1b_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751427702579_127.0.0.1_13018
11:41:42.807 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f468977f-d78b-4c11-a7a9-4bc0a8ec4c1b_config-0] Notify connected event to listeners.
11:41:42.808 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f468977f-d78b-4c11-a7a9-4bc0a8ec4c1b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:41:42.810 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f468977f-d78b-4c11-a7a9-4bc0a8ec4c1b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002cb0b518ad8
11:41:42.998 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:41:47.668 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
11:41:47.669 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:41:47.669 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:41:47.949 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:41:48.875 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:41:48.875 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:41:48.875 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:41:49.320 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
11:41:49.347 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
11:41:49.347 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
11:41:49.373 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751427709327'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

11:41:49.374 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
11:41:49.374 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
11:41:49.375 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@4e68aede
11:41:53.740 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:42:00.221 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 31d8aac0-dac6-4ea2-b3ae-1099aed322ae
11:42:00.222 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31d8aac0-dac6-4ea2-b3ae-1099aed322ae] RpcClient init label, labels = {module=naming, source=sdk}
11:42:00.225 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31d8aac0-dac6-4ea2-b3ae-1099aed322ae] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:42:00.225 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31d8aac0-dac6-4ea2-b3ae-1099aed322ae] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:42:00.226 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31d8aac0-dac6-4ea2-b3ae-1099aed322ae] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:42:00.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31d8aac0-dac6-4ea2-b3ae-1099aed322ae] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:42:00.358 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31d8aac0-dac6-4ea2-b3ae-1099aed322ae] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751427720234_127.0.0.1_13069
11:42:00.359 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31d8aac0-dac6-4ea2-b3ae-1099aed322ae] Notify connected event to listeners.
11:42:00.359 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31d8aac0-dac6-4ea2-b3ae-1099aed322ae] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:42:00.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31d8aac0-dac6-4ea2-b3ae-1099aed322ae] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000002cb0b518ad8
11:42:00.460 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
11:42:00.513 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job 192.168.2.43:9500 register finished
11:42:00.715 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 21.198 seconds (JVM running for 22.707)
11:42:00.737 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
11:42:00.738 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
11:42:00.747 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
11:42:00.973 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31d8aac0-dac6-4ea2-b3ae-1099aed322ae] Receive server push request, request = NotifySubscriberRequest, requestId = 46
11:42:00.997 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [31d8aac0-dac6-4ea2-b3ae-1099aed322ae] Ack server push request, request = NotifySubscriberRequest, requestId = 46
11:42:01.296 [RMI TCP Connection(3)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:42:01.777 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751427709327 started.
11:51:49.902 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751427709327 paused.
11:51:49.989 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
11:51:49.997 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
11:51:50.311 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
11:51:50.312 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@306c9ade[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
11:51:50.314 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751427720234_127.0.0.1_13069
11:51:50.321 [nacos-grpc-client-executor-130] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751427720234_127.0.0.1_13069]Ignore complete event,isRunning:false,isAbandon=false
11:51:50.323 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7af5ddb0[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 131]
11:51:50.467 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751427709327 shutting down.
11:51:50.468 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751427709327 paused.
11:51:50.469 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751427709327 shutdown complete.
11:51:50.471 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
11:51:50.475 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
11:51:50.487 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
11:51:50.487 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
11:51:58.507 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:51:59.457 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of de875a40-8dc0-4c3d-b51e-dd057a3189a3_config-0
11:51:59.565 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 56 ms to scan 1 urls, producing 3 keys and 6 values 
11:51:59.614 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
11:51:59.632 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 3 keys and 10 values 
11:51:59.648 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 5 values 
11:51:59.666 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 7 values 
11:51:59.679 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
11:51:59.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de875a40-8dc0-4c3d-b51e-dd057a3189a3_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:51:59.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de875a40-8dc0-4c3d-b51e-dd057a3189a3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001dd0139e480
11:51:59.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de875a40-8dc0-4c3d-b51e-dd057a3189a3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001dd0139e6a0
11:51:59.685 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de875a40-8dc0-4c3d-b51e-dd057a3189a3_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:51:59.687 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de875a40-8dc0-4c3d-b51e-dd057a3189a3_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:51:59.702 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de875a40-8dc0-4c3d-b51e-dd057a3189a3_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:52:00.897 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de875a40-8dc0-4c3d-b51e-dd057a3189a3_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751428320664_127.0.0.1_13728
11:52:00.898 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de875a40-8dc0-4c3d-b51e-dd057a3189a3_config-0] Notify connected event to listeners.
11:52:00.900 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de875a40-8dc0-4c3d-b51e-dd057a3189a3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:52:00.902 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de875a40-8dc0-4c3d-b51e-dd057a3189a3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001dd01518228
11:52:01.090 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:52:05.528 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
11:52:05.529 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
11:52:05.530 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
11:52:05.790 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
11:52:06.843 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
11:52:06.845 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
11:52:06.846 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
11:52:07.281 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
11:52:07.314 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
11:52:07.315 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
11:52:07.346 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751428327286'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

11:52:07.347 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
11:52:07.347 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
11:52:07.351 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@75882ac2
11:52:12.036 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
11:52:18.744 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7e61a0ad-0768-4082-af4b-7529aa52e655
11:52:18.745 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e61a0ad-0768-4082-af4b-7529aa52e655] RpcClient init label, labels = {module=naming, source=sdk}
11:52:18.747 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e61a0ad-0768-4082-af4b-7529aa52e655] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:52:18.748 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e61a0ad-0768-4082-af4b-7529aa52e655] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:52:18.748 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e61a0ad-0768-4082-af4b-7529aa52e655] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:52:18.749 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e61a0ad-0768-4082-af4b-7529aa52e655] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
11:52:18.886 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e61a0ad-0768-4082-af4b-7529aa52e655] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751428338761_127.0.0.1_13755
11:52:18.887 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e61a0ad-0768-4082-af4b-7529aa52e655] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:52:18.887 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e61a0ad-0768-4082-af4b-7529aa52e655] Notify connected event to listeners.
11:52:18.887 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e61a0ad-0768-4082-af4b-7529aa52e655] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001dd01518228
11:52:18.984 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
11:52:19.044 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job 192.168.2.43:9500 register finished
11:52:19.232 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 21.592 seconds (JVM running for 23.94)
11:52:19.255 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
11:52:19.256 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
11:52:19.262 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
11:52:19.527 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e61a0ad-0768-4082-af4b-7529aa52e655] Receive server push request, request = NotifySubscriberRequest, requestId = 49
11:52:19.558 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7e61a0ad-0768-4082-af4b-7529aa52e655] Ack server push request, request = NotifySubscriberRequest, requestId = 49
11:52:19.858 [RMI TCP Connection(4)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
11:52:20.484 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751428327286 started.
12:12:44.685 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751428327286 paused.
12:12:44.767 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:12:44.773 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:12:45.083 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:12:45.084 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2b5e639f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:12:45.084 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751428338761_127.0.0.1_13755
12:12:45.088 [nacos-grpc-client-executor-254] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751428338761_127.0.0.1_13755]Ignore complete event,isRunning:false,isAbandon=false
12:12:45.090 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@249ed986[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 255]
12:12:45.238 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751428327286 shutting down.
12:12:45.238 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751428327286 paused.
12:12:45.253 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751428327286 shutdown complete.
12:12:45.253 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:12:45.262 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:12:45.281 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:12:45.281 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:12:54.727 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:12:55.702 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fb1de687-a48d-43c5-9324-b545f82d1e3c_config-0
12:12:55.802 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 50 ms to scan 1 urls, producing 3 keys and 6 values 
12:12:55.885 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 4 keys and 9 values 
12:12:55.898 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
12:12:55.912 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
12:12:55.924 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
12:12:55.935 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
12:12:55.939 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb1de687-a48d-43c5-9324-b545f82d1e3c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:12:55.940 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb1de687-a48d-43c5-9324-b545f82d1e3c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000019e4c39eaf8
12:12:55.941 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb1de687-a48d-43c5-9324-b545f82d1e3c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000019e4c39ed18
12:12:55.941 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb1de687-a48d-43c5-9324-b545f82d1e3c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:12:55.943 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb1de687-a48d-43c5-9324-b545f82d1e3c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:12:55.955 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb1de687-a48d-43c5-9324-b545f82d1e3c_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:12:57.172 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb1de687-a48d-43c5-9324-b545f82d1e3c_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751429576945_127.0.0.1_1528
12:12:57.174 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb1de687-a48d-43c5-9324-b545f82d1e3c_config-0] Notify connected event to listeners.
12:12:57.175 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb1de687-a48d-43c5-9324-b545f82d1e3c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:12:57.177 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fb1de687-a48d-43c5-9324-b545f82d1e3c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000019e4c518b60
12:12:57.404 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:13:01.941 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
12:13:01.942 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:13:01.942 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:13:02.198 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:13:03.197 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:13:03.199 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:13:03.199 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:13:03.614 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
12:13:03.641 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
12:13:03.642 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
12:13:03.665 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751429583619'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

12:13:03.665 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
12:13:03.665 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
12:13:03.668 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@2e3b4394
12:13:07.676 [main] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751429583619 shutting down.
12:13:07.676 [main] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751429583619 paused.
12:13:07.677 [main] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751429583619 shutdown complete.
12:13:07.677 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:13:07.682 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:13:07.697 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:13:07.697 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:13:07.699 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
12:14:59.941 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:15:00.856 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a2e9f863-0a78-46a9-9e84-8f01a5ec21fb_config-0
12:15:00.951 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
12:15:01.006 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 4 keys and 9 values 
12:15:01.024 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
12:15:01.044 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 5 values 
12:15:01.056 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
12:15:01.068 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
12:15:01.072 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2e9f863-0a78-46a9-9e84-8f01a5ec21fb_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:15:01.072 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2e9f863-0a78-46a9-9e84-8f01a5ec21fb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000197d238e7e8
12:15:01.073 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2e9f863-0a78-46a9-9e84-8f01a5ec21fb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$471/0x00000197d238ea08
12:15:01.073 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2e9f863-0a78-46a9-9e84-8f01a5ec21fb_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:15:01.074 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2e9f863-0a78-46a9-9e84-8f01a5ec21fb_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:15:01.083 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2e9f863-0a78-46a9-9e84-8f01a5ec21fb_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:15:02.263 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2e9f863-0a78-46a9-9e84-8f01a5ec21fb_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751429702049_127.0.0.1_1708
12:15:02.264 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2e9f863-0a78-46a9-9e84-8f01a5ec21fb_config-0] Notify connected event to listeners.
12:15:02.264 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2e9f863-0a78-46a9-9e84-8f01a5ec21fb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:15:02.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a2e9f863-0a78-46a9-9e84-8f01a5ec21fb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$485/0x00000197d24e3bc0
12:15:02.414 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:15:03.096 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:15:04.066 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9c3d3d66-e5fc-432e-aa47-993cedd575ca_config-0
12:15:04.170 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 46 ms to scan 1 urls, producing 3 keys and 6 values 
12:15:04.241 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 4 keys and 9 values 
12:15:04.255 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
12:15:04.271 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
12:15:04.285 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
12:15:04.299 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
12:15:04.303 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c3d3d66-e5fc-432e-aa47-993cedd575ca_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:15:04.304 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c3d3d66-e5fc-432e-aa47-993cedd575ca_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001cd013b68d8
12:15:04.305 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c3d3d66-e5fc-432e-aa47-993cedd575ca_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001cd013b6af8
12:15:04.305 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c3d3d66-e5fc-432e-aa47-993cedd575ca_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:15:04.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c3d3d66-e5fc-432e-aa47-993cedd575ca_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:15:04.320 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c3d3d66-e5fc-432e-aa47-993cedd575ca_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:15:05.467 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c3d3d66-e5fc-432e-aa47-993cedd575ca_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751429705229_127.0.0.1_1714
12:15:05.469 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c3d3d66-e5fc-432e-aa47-993cedd575ca_config-0] Notify connected event to listeners.
12:15:05.470 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c3d3d66-e5fc-432e-aa47-993cedd575ca_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:15:05.472 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9c3d3d66-e5fc-432e-aa47-993cedd575ca_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001cd014f0ad8
12:15:05.657 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:15:06.686 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
12:15:06.687 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:15:06.687 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:15:06.912 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:15:07.685 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:15:07.687 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:15:07.687 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:15:08.105 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
12:15:08.129 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
12:15:08.129 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
12:15:08.147 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751429708110'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

12:15:08.147 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
12:15:08.147 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
12:15:08.149 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@729c98
12:15:10.300 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
12:15:10.301 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:15:10.302 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:15:10.570 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:15:11.541 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:15:11.543 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:15:11.544 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:15:11.984 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
12:15:12.012 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
12:15:12.013 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
12:15:12.035 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751429711989'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

12:15:12.036 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
12:15:12.036 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
12:15:12.040 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@81cd90e
12:15:12.489 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:15:16.859 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:15:19.018 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ba1b5682-e6e3-45eb-9bad-12b2a8b0073a
12:15:19.018 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba1b5682-e6e3-45eb-9bad-12b2a8b0073a] RpcClient init label, labels = {module=naming, source=sdk}
12:15:19.021 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba1b5682-e6e3-45eb-9bad-12b2a8b0073a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:15:19.021 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba1b5682-e6e3-45eb-9bad-12b2a8b0073a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:15:19.022 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba1b5682-e6e3-45eb-9bad-12b2a8b0073a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:15:19.023 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba1b5682-e6e3-45eb-9bad-12b2a8b0073a] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:15:19.159 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba1b5682-e6e3-45eb-9bad-12b2a8b0073a] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751429719035_127.0.0.1_1758
12:15:19.159 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba1b5682-e6e3-45eb-9bad-12b2a8b0073a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:15:19.159 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba1b5682-e6e3-45eb-9bad-12b2a8b0073a] Notify connected event to listeners.
12:15:19.160 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba1b5682-e6e3-45eb-9bad-12b2a8b0073a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$485/0x00000197d24e3bc0
12:15:19.234 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
12:15:19.273 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job 192.168.2.43:9500 register finished
12:15:19.456 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 20.339 seconds (JVM running for 21.447)
12:15:19.474 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
12:15:19.475 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
12:15:19.480 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
12:15:19.707 [RMI TCP Connection(3)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:15:19.808 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba1b5682-e6e3-45eb-9bad-12b2a8b0073a] Receive server push request, request = NotifySubscriberRequest, requestId = 52
12:15:19.828 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba1b5682-e6e3-45eb-9bad-12b2a8b0073a] Ack server push request, request = NotifySubscriberRequest, requestId = 52
12:15:20.516 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751429708110 started.
12:15:23.605 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8466198a-bc78-4f06-b7b0-482dc911919d
12:15:23.605 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8466198a-bc78-4f06-b7b0-482dc911919d] RpcClient init label, labels = {module=naming, source=sdk}
12:15:23.608 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8466198a-bc78-4f06-b7b0-482dc911919d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:15:23.608 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8466198a-bc78-4f06-b7b0-482dc911919d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:15:23.609 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8466198a-bc78-4f06-b7b0-482dc911919d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:15:23.609 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8466198a-bc78-4f06-b7b0-482dc911919d] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:15:23.753 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8466198a-bc78-4f06-b7b0-482dc911919d] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751429723622_127.0.0.1_1781
12:15:23.753 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8466198a-bc78-4f06-b7b0-482dc911919d] Notify connected event to listeners.
12:15:23.753 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8466198a-bc78-4f06-b7b0-482dc911919d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:15:23.753 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8466198a-bc78-4f06-b7b0-482dc911919d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001cd014f0ad8
12:15:23.843 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
12:15:24.360 [nacos-grpc-client-executor-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8466198a-bc78-4f06-b7b0-482dc911919d] Receive server push request, request = NotifySubscriberRequest, requestId = 54
12:15:24.362 [nacos-grpc-client-executor-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8466198a-bc78-4f06-b7b0-482dc911919d] Ack server push request, request = NotifySubscriberRequest, requestId = 54
12:15:24.510 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:15:24.510 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2627da4c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:15:24.510 [main] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751429723622_127.0.0.1_1781
12:15:24.517 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751429723622_127.0.0.1_1781]Ignore complete event,isRunning:false,isAbandon=false
12:15:24.522 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@63100783[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 6]
12:15:24.527 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2aacf14d-b733-404c-bb83-a5bb541d519d
12:15:24.528 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2aacf14d-b733-404c-bb83-a5bb541d519d] RpcClient init label, labels = {module=naming, source=sdk}
12:15:24.528 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2aacf14d-b733-404c-bb83-a5bb541d519d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:15:24.528 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2aacf14d-b733-404c-bb83-a5bb541d519d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:15:24.529 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2aacf14d-b733-404c-bb83-a5bb541d519d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:15:24.529 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2aacf14d-b733-404c-bb83-a5bb541d519d] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:15:24.678 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2aacf14d-b733-404c-bb83-a5bb541d519d] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751429724544_127.0.0.1_1782
12:15:24.680 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2aacf14d-b733-404c-bb83-a5bb541d519d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:15:24.681 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2aacf14d-b733-404c-bb83-a5bb541d519d] Notify connected event to listeners.
12:15:24.682 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2aacf14d-b733-404c-bb83-a5bb541d519d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001cd014f0ad8
12:15:24.738 [main] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751429711989 shutting down.
12:15:24.739 [main] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751429711989 paused.
12:15:24.740 [main] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751429711989 shutdown complete.
12:15:24.742 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:15:24.746 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:15:24.759 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:15:24.759 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:15:24.761 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9500"]
12:15:24.762 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
12:15:24.790 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9500"]
12:15:24.792 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9500"]
12:16:43.836 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751429708110 paused.
12:16:43.911 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:16:43.919 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:16:44.232 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:16:44.233 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@263f72d3[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:16:44.233 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751429719035_127.0.0.1_1758
12:16:44.239 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751429719035_127.0.0.1_1758]Ignore complete event,isRunning:false,isAbandon=false
12:16:44.241 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@463e19d6[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 29]
12:16:44.393 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751429708110 shutting down.
12:16:44.394 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751429708110 paused.
12:16:44.394 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751429708110 shutdown complete.
12:16:44.396 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:16:44.401 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:16:44.410 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:16:44.410 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:16:51.252 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:16:52.264 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3e88ec17-5d46-4289-8d3c-65e46590de7c_config-0
12:16:52.364 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 45 ms to scan 1 urls, producing 3 keys and 6 values 
12:16:52.419 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
12:16:52.449 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 3 keys and 10 values 
12:16:52.468 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
12:16:52.483 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
12:16:52.497 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
12:16:52.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e88ec17-5d46-4289-8d3c-65e46590de7c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:16:52.503 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e88ec17-5d46-4289-8d3c-65e46590de7c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000024c6338b188
12:16:52.504 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e88ec17-5d46-4289-8d3c-65e46590de7c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$471/0x0000024c6338b3a8
12:16:52.505 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e88ec17-5d46-4289-8d3c-65e46590de7c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:16:52.506 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e88ec17-5d46-4289-8d3c-65e46590de7c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:16:52.518 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e88ec17-5d46-4289-8d3c-65e46590de7c_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:16:53.626 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e88ec17-5d46-4289-8d3c-65e46590de7c_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751429813394_127.0.0.1_1904
12:16:53.628 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e88ec17-5d46-4289-8d3c-65e46590de7c_config-0] Notify connected event to listeners.
12:16:53.628 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e88ec17-5d46-4289-8d3c-65e46590de7c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:16:53.630 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e88ec17-5d46-4289-8d3c-65e46590de7c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$485/0x0000024c634e38e0
12:16:53.830 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:16:57.935 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
12:16:57.936 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:16:57.936 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:16:58.169 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:16:58.954 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:16:58.957 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:16:58.958 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:16:59.395 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
12:16:59.419 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
12:16:59.419 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
12:16:59.440 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751429819400'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

12:16:59.440 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
12:16:59.440 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
12:16:59.443 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@461b38ca
12:17:03.534 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:17:09.749 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 06cbfe71-857c-48d9-9d96-06be64c6a322
12:17:09.750 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06cbfe71-857c-48d9-9d96-06be64c6a322] RpcClient init label, labels = {module=naming, source=sdk}
12:17:09.752 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06cbfe71-857c-48d9-9d96-06be64c6a322] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:17:09.753 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06cbfe71-857c-48d9-9d96-06be64c6a322] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:17:09.753 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06cbfe71-857c-48d9-9d96-06be64c6a322] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:17:09.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06cbfe71-857c-48d9-9d96-06be64c6a322] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:17:09.894 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06cbfe71-857c-48d9-9d96-06be64c6a322] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751429829773_127.0.0.1_1947
12:17:09.895 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06cbfe71-857c-48d9-9d96-06be64c6a322] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:17:09.896 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06cbfe71-857c-48d9-9d96-06be64c6a322] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$485/0x0000024c634e38e0
12:17:09.897 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06cbfe71-857c-48d9-9d96-06be64c6a322] Notify connected event to listeners.
12:17:09.977 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
12:17:10.042 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job 192.168.2.43:9500 register finished
12:17:10.239 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 19.823 seconds (JVM running for 20.903)
12:17:10.262 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
12:17:10.263 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
12:17:10.267 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
12:17:10.358 [RMI TCP Connection(1)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:17:10.550 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06cbfe71-857c-48d9-9d96-06be64c6a322] Receive server push request, request = NotifySubscriberRequest, requestId = 56
12:17:10.575 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06cbfe71-857c-48d9-9d96-06be64c6a322] Ack server push request, request = NotifySubscriberRequest, requestId = 56
12:17:11.293 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751429819400 started.
12:17:35.716 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751429819400 paused.
12:17:35.810 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:17:35.820 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:17:36.128 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:17:36.129 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2abcc216[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:17:36.129 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751429829773_127.0.0.1_1947
12:17:36.133 [nacos-grpc-client-executor-16] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751429829773_127.0.0.1_1947]Ignore complete event,isRunning:false,isAbandon=false
12:17:36.137 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@23af741d[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 17]
12:17:36.289 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751429819400 shutting down.
12:17:36.289 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751429819400 paused.
12:17:36.290 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751429819400 shutdown complete.
12:17:36.292 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:17:36.297 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:17:36.318 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:17:36.318 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:17:40.226 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:17:41.145 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 63f5a409-1ba4-4585-8b7a-a1cae2be24f7_config-0
12:17:41.239 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 40 ms to scan 1 urls, producing 3 keys and 6 values 
12:17:41.314 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 4 keys and 9 values 
12:17:41.327 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
12:17:41.342 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
12:17:41.357 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
12:17:41.370 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
12:17:41.375 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63f5a409-1ba4-4585-8b7a-a1cae2be24f7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:17:41.376 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63f5a409-1ba4-4585-8b7a-a1cae2be24f7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001c09339eaf8
12:17:41.377 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63f5a409-1ba4-4585-8b7a-a1cae2be24f7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001c09339ed18
12:17:41.377 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63f5a409-1ba4-4585-8b7a-a1cae2be24f7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:17:41.379 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63f5a409-1ba4-4585-8b7a-a1cae2be24f7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:17:41.396 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63f5a409-1ba4-4585-8b7a-a1cae2be24f7_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:17:42.716 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63f5a409-1ba4-4585-8b7a-a1cae2be24f7_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751429862476_127.0.0.1_1998
12:17:42.719 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63f5a409-1ba4-4585-8b7a-a1cae2be24f7_config-0] Notify connected event to listeners.
12:17:42.719 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63f5a409-1ba4-4585-8b7a-a1cae2be24f7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:17:42.721 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [63f5a409-1ba4-4585-8b7a-a1cae2be24f7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001c093518668
12:17:42.908 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:17:47.246 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
12:17:47.247 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:17:47.247 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:17:47.489 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:17:48.451 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:17:48.454 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:17:48.454 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:17:48.869 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
12:17:48.896 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
12:17:48.896 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
12:17:48.920 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751429868874'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

12:17:48.921 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
12:17:48.921 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
12:17:48.923 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@10660795
12:17:53.214 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:17:59.464 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4a37f122-a942-4f60-bc54-228920e7aa07
12:17:59.464 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a37f122-a942-4f60-bc54-228920e7aa07] RpcClient init label, labels = {module=naming, source=sdk}
12:17:59.468 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a37f122-a942-4f60-bc54-228920e7aa07] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:17:59.468 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a37f122-a942-4f60-bc54-228920e7aa07] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:17:59.469 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a37f122-a942-4f60-bc54-228920e7aa07] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:17:59.470 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a37f122-a942-4f60-bc54-228920e7aa07] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:17:59.609 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a37f122-a942-4f60-bc54-228920e7aa07] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751429879485_127.0.0.1_2021
12:17:59.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a37f122-a942-4f60-bc54-228920e7aa07] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:17:59.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a37f122-a942-4f60-bc54-228920e7aa07] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x000001c093518668
12:17:59.610 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a37f122-a942-4f60-bc54-228920e7aa07] Notify connected event to listeners.
12:17:59.694 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
12:17:59.760 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job 192.168.2.43:9500 register finished
12:17:59.950 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 20.55 seconds (JVM running for 21.938)
12:17:59.975 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
12:17:59.976 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
12:17:59.984 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
12:18:00.315 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a37f122-a942-4f60-bc54-228920e7aa07] Receive server push request, request = NotifySubscriberRequest, requestId = 59
12:18:00.342 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4a37f122-a942-4f60-bc54-228920e7aa07] Ack server push request, request = NotifySubscriberRequest, requestId = 59
12:18:00.402 [RMI TCP Connection(3)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:18:01.049 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751429868874 started.
12:20:05.461 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751429868874 paused.
12:20:05.538 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:20:05.546 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:20:05.867 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:20:05.868 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1bf3ce37[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:20:05.870 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751429879485_127.0.0.1_2021
12:20:05.875 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5b69e765[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 36]
12:20:06.062 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751429868874 shutting down.
12:20:06.063 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751429868874 paused.
12:20:06.064 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751429868874 shutdown complete.
12:20:06.064 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:20:06.070 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:20:06.084 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:20:06.084 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:20:20.788 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:20:21.770 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 778d807f-f229-4299-bad7-485cdf63e8b2_config-0
12:20:21.864 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 3 keys and 6 values 
12:20:21.934 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 4 keys and 9 values 
12:20:21.948 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
12:20:21.964 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
12:20:21.968 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 1 ms to scan 1 urls, producing 1 keys and 7 values 
12:20:21.991 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
12:20:21.991 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778d807f-f229-4299-bad7-485cdf63e8b2_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:20:21.998 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778d807f-f229-4299-bad7-485cdf63e8b2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000232df3b71c0
12:20:21.998 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778d807f-f229-4299-bad7-485cdf63e8b2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000232df3b73e0
12:20:22.000 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778d807f-f229-4299-bad7-485cdf63e8b2_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:20:22.001 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778d807f-f229-4299-bad7-485cdf63e8b2_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:20:22.012 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778d807f-f229-4299-bad7-485cdf63e8b2_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:20:23.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778d807f-f229-4299-bad7-485cdf63e8b2_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751430022932_127.0.0.1_2351
12:20:23.190 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778d807f-f229-4299-bad7-485cdf63e8b2_config-0] Notify connected event to listeners.
12:20:23.191 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778d807f-f229-4299-bad7-485cdf63e8b2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:20:23.192 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [778d807f-f229-4299-bad7-485cdf63e8b2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000232df4f0ad8
12:20:23.383 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:20:29.082 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
12:20:29.082 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:20:29.082 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:20:29.514 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:20:31.030 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:20:31.033 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:20:31.035 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:20:31.723 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
12:20:31.755 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
12:20:31.755 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
12:20:31.803 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751430031723'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

12:20:31.803 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
12:20:31.803 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
12:20:31.807 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@256c6f7a
12:20:38.900 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:20:49.689 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e09d4302-d86d-4fe2-bdeb-74371fb678cd
12:20:49.689 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e09d4302-d86d-4fe2-bdeb-74371fb678cd] RpcClient init label, labels = {module=naming, source=sdk}
12:20:49.691 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e09d4302-d86d-4fe2-bdeb-74371fb678cd] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:20:49.691 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e09d4302-d86d-4fe2-bdeb-74371fb678cd] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:20:49.696 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e09d4302-d86d-4fe2-bdeb-74371fb678cd] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:20:49.696 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e09d4302-d86d-4fe2-bdeb-74371fb678cd] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:20:49.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e09d4302-d86d-4fe2-bdeb-74371fb678cd] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751430049706_127.0.0.1_2435
12:20:49.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e09d4302-d86d-4fe2-bdeb-74371fb678cd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:20:49.843 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e09d4302-d86d-4fe2-bdeb-74371fb678cd] Notify connected event to listeners.
12:20:49.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e09d4302-d86d-4fe2-bdeb-74371fb678cd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000232df4f0ad8
12:20:49.948 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
12:20:50.029 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job 192.168.2.43:9500 register finished
12:20:50.405 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 30.49 seconds (JVM running for 31.953)
12:20:50.432 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
12:20:50.432 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
12:20:50.440 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
12:20:50.488 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e09d4302-d86d-4fe2-bdeb-74371fb678cd] Receive server push request, request = NotifySubscriberRequest, requestId = 62
12:20:50.508 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e09d4302-d86d-4fe2-bdeb-74371fb678cd] Ack server push request, request = NotifySubscriberRequest, requestId = 62
12:20:51.454 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751430031723 started.
12:31:25.674 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751430031723 paused.
12:31:25.765 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
12:31:25.772 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
12:31:26.085 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
12:31:26.085 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@39e1698c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
12:31:26.086 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751430049706_127.0.0.1_2435
12:31:26.092 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6c05a39c[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 135]
12:31:26.132 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751430031723 shutting down.
12:31:26.133 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751430031723 paused.
12:31:26.134 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751430031723 shutdown complete.
12:31:26.134 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
12:31:26.134 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
12:31:26.151 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
12:31:26.151 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
12:31:33.707 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
12:31:34.588 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 595817d1-c125-4f8f-8768-3fd8b3b4b493_config-0
12:31:34.679 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 39 ms to scan 1 urls, producing 3 keys and 6 values 
12:31:34.756 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 4 keys and 9 values 
12:31:34.768 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
12:31:34.782 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
12:31:34.797 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
12:31:34.810 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
12:31:34.814 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [595817d1-c125-4f8f-8768-3fd8b3b4b493_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
12:31:34.815 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [595817d1-c125-4f8f-8768-3fd8b3b4b493_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000285683bdd70
12:31:34.816 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [595817d1-c125-4f8f-8768-3fd8b3b4b493_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000285683bdf90
12:31:34.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [595817d1-c125-4f8f-8768-3fd8b3b4b493_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
12:31:34.818 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [595817d1-c125-4f8f-8768-3fd8b3b4b493_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
12:31:34.828 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [595817d1-c125-4f8f-8768-3fd8b3b4b493_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:31:35.959 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [595817d1-c125-4f8f-8768-3fd8b3b4b493_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751430695734_127.0.0.1_3457
12:31:35.961 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [595817d1-c125-4f8f-8768-3fd8b3b4b493_config-0] Notify connected event to listeners.
12:31:35.962 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [595817d1-c125-4f8f-8768-3fd8b3b4b493_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:31:35.964 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [595817d1-c125-4f8f-8768-3fd8b3b4b493_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000285684f7da0
12:31:36.146 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
12:31:40.506 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
12:31:40.507 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
12:31:40.508 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
12:31:40.766 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
12:31:41.596 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
12:31:41.598 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
12:31:41.599 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
12:31:42.005 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
12:31:42.035 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
12:31:42.035 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
12:31:42.059 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751430702012'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

12:31:42.059 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
12:31:42.060 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
12:31:42.062 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@337e573b
12:31:46.124 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
12:31:52.277 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 483974d2-29ca-4b76-9ad2-88ff24731a6d
12:31:52.278 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [483974d2-29ca-4b76-9ad2-88ff24731a6d] RpcClient init label, labels = {module=naming, source=sdk}
12:31:52.280 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [483974d2-29ca-4b76-9ad2-88ff24731a6d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
12:31:52.281 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [483974d2-29ca-4b76-9ad2-88ff24731a6d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
12:31:52.282 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [483974d2-29ca-4b76-9ad2-88ff24731a6d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
12:31:52.282 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [483974d2-29ca-4b76-9ad2-88ff24731a6d] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
12:31:52.418 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [483974d2-29ca-4b76-9ad2-88ff24731a6d] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751430712295_127.0.0.1_3475
12:31:52.419 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [483974d2-29ca-4b76-9ad2-88ff24731a6d] Notify connected event to listeners.
12:31:52.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [483974d2-29ca-4b76-9ad2-88ff24731a6d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
12:31:52.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [483974d2-29ca-4b76-9ad2-88ff24731a6d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000285684f7da0
12:31:52.529 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
12:31:52.577 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job 192.168.2.43:9500 register finished
12:31:52.760 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 19.901 seconds (JVM running for 21.42)
12:31:52.777 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
12:31:52.778 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
12:31:52.783 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
12:31:52.983 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [483974d2-29ca-4b76-9ad2-88ff24731a6d] Receive server push request, request = NotifySubscriberRequest, requestId = 65
12:31:53.012 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [483974d2-29ca-4b76-9ad2-88ff24731a6d] Ack server push request, request = NotifySubscriberRequest, requestId = 65
12:31:53.199 [RMI TCP Connection(2)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
12:31:53.815 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751430702012 started.
13:27:48.924 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751430702012 paused.
13:27:49.039 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:27:49.045 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:27:49.393 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:27:49.395 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@257eca25[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:27:49.403 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751430712295_127.0.0.1_3475
13:27:49.409 [nacos-grpc-client-executor-678] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751430712295_127.0.0.1_3475]Ignore complete event,isRunning:false,isAbandon=false
13:27:49.414 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@56f919ad[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 679]
13:27:49.572 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751430702012 shutting down.
13:27:49.573 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751430702012 paused.
13:27:49.576 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751430702012 shutdown complete.
13:27:49.577 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:27:49.584 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:27:49.597 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:27:49.597 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
13:27:57.576 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:27:58.542 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 85052b6d-8c94-45c0-ad1b-b6f8894a422d_config-0
13:27:58.639 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
13:27:58.711 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
13:27:58.724 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
13:27:58.737 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
13:27:58.752 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
13:27:58.763 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
13:27:58.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85052b6d-8c94-45c0-ad1b-b6f8894a422d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:27:58.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85052b6d-8c94-45c0-ad1b-b6f8894a422d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002042039dd70
13:27:58.770 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85052b6d-8c94-45c0-ad1b-b6f8894a422d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000002042039df90
13:27:58.772 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85052b6d-8c94-45c0-ad1b-b6f8894a422d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:27:58.773 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85052b6d-8c94-45c0-ad1b-b6f8894a422d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:27:58.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85052b6d-8c94-45c0-ad1b-b6f8894a422d_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
13:28:00.067 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85052b6d-8c94-45c0-ad1b-b6f8894a422d_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751434079839_127.0.0.1_5845
13:28:00.069 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85052b6d-8c94-45c0-ad1b-b6f8894a422d_config-0] Notify connected event to listeners.
13:28:00.071 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85052b6d-8c94-45c0-ad1b-b6f8894a422d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:28:00.072 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [85052b6d-8c94-45c0-ad1b-b6f8894a422d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020420517b78
13:28:00.249 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:28:06.079 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
13:28:06.081 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:28:06.081 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:28:06.515 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:28:07.795 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
13:28:07.798 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
13:28:07.799 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
13:28:08.432 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
13:28:08.469 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
13:28:08.470 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
13:28:08.505 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751434088439'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

13:28:08.505 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
13:28:08.506 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
13:28:08.511 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@10ffe32f
13:28:15.041 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:28:25.024 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 93518919-0b6a-4987-8d7e-66129f0fd251
13:28:25.025 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93518919-0b6a-4987-8d7e-66129f0fd251] RpcClient init label, labels = {module=naming, source=sdk}
13:28:25.027 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93518919-0b6a-4987-8d7e-66129f0fd251] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:28:25.027 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93518919-0b6a-4987-8d7e-66129f0fd251] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:28:25.028 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93518919-0b6a-4987-8d7e-66129f0fd251] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:28:25.028 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93518919-0b6a-4987-8d7e-66129f0fd251] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
13:28:25.174 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93518919-0b6a-4987-8d7e-66129f0fd251] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751434105043_127.0.0.1_5911
13:28:25.174 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93518919-0b6a-4987-8d7e-66129f0fd251] Notify connected event to listeners.
13:28:25.174 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93518919-0b6a-4987-8d7e-66129f0fd251] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:28:25.175 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93518919-0b6a-4987-8d7e-66129f0fd251] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000020420517b78
13:28:25.264 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
13:28:25.326 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job 192.168.2.43:9500 register finished
13:28:25.503 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 28.816 seconds (JVM running for 30.455)
13:28:25.527 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
13:28:25.528 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
13:28:25.534 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
13:28:25.835 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93518919-0b6a-4987-8d7e-66129f0fd251] Receive server push request, request = NotifySubscriberRequest, requestId = 68
13:28:25.862 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [93518919-0b6a-4987-8d7e-66129f0fd251] Ack server push request, request = NotifySubscriberRequest, requestId = 68
13:28:26.201 [RMI TCP Connection(5)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
13:28:26.577 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751434088439 started.
13:35:26.805 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751434088439 paused.
13:35:26.907 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:35:26.915 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:35:27.227 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:35:27.228 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7fe9ebdb[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:35:27.229 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751434105043_127.0.0.1_5911
13:35:27.234 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7eb37358[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 100]
13:35:27.236 [nacos-grpc-client-executor-100] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751434105043_127.0.0.1_5911]Ignore complete event,isRunning:false,isAbandon=false
13:35:27.376 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751434088439 shutting down.
13:35:27.377 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751434088439 paused.
13:35:27.377 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751434088439 shutdown complete.
13:35:27.380 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
13:35:27.384 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
13:35:27.395 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
13:35:27.395 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:14:56.265 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:14:57.348 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 73db72d2-07c5-4a42-a9c7-a47d3cb0f3e4_config-0
14:14:57.450 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 44 ms to scan 1 urls, producing 3 keys and 6 values 
14:14:57.517 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 4 keys and 9 values 
14:14:57.535 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 3 keys and 10 values 
14:14:57.550 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
14:14:57.566 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
14:14:57.579 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
14:14:57.584 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73db72d2-07c5-4a42-a9c7-a47d3cb0f3e4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:14:57.585 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73db72d2-07c5-4a42-a9c7-a47d3cb0f3e4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000216663b6d38
14:14:57.586 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73db72d2-07c5-4a42-a9c7-a47d3cb0f3e4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000216663b6f58
14:14:57.586 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73db72d2-07c5-4a42-a9c7-a47d3cb0f3e4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:14:57.587 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73db72d2-07c5-4a42-a9c7-a47d3cb0f3e4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:14:57.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73db72d2-07c5-4a42-a9c7-a47d3cb0f3e4_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:14:58.856 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73db72d2-07c5-4a42-a9c7-a47d3cb0f3e4_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751436898606_127.0.0.1_9439
14:14:58.857 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73db72d2-07c5-4a42-a9c7-a47d3cb0f3e4_config-0] Notify connected event to listeners.
14:14:58.858 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73db72d2-07c5-4a42-a9c7-a47d3cb0f3e4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:14:58.858 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [73db72d2-07c5-4a42-a9c7-a47d3cb0f3e4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000216664f0ad8
14:14:59.016 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:15:03.941 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
14:15:03.942 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:15:03.942 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:15:04.186 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:15:05.244 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:15:05.246 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:15:05.246 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:15:05.673 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:15:05.698 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:15:05.699 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:15:05.725 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751436905678'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

14:15:05.726 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
14:15:05.726 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:15:05.729 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@5cb6b81b
14:15:10.098 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:15:16.501 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e2564f61-7cdc-43ec-be01-de1df9f5642b
14:15:16.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2564f61-7cdc-43ec-be01-de1df9f5642b] RpcClient init label, labels = {module=naming, source=sdk}
14:15:16.505 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2564f61-7cdc-43ec-be01-de1df9f5642b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:15:16.505 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2564f61-7cdc-43ec-be01-de1df9f5642b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:15:16.506 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2564f61-7cdc-43ec-be01-de1df9f5642b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:15:16.506 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2564f61-7cdc-43ec-be01-de1df9f5642b] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:15:16.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2564f61-7cdc-43ec-be01-de1df9f5642b] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751436916522_127.0.0.1_9493
14:15:16.640 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2564f61-7cdc-43ec-be01-de1df9f5642b] Notify connected event to listeners.
14:15:16.640 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2564f61-7cdc-43ec-be01-de1df9f5642b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:15:16.640 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2564f61-7cdc-43ec-be01-de1df9f5642b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000216664f0ad8
14:15:16.704 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
14:15:16.751 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job 192.168.2.43:9500 register finished
14:15:16.947 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 21.535 seconds (JVM running for 23.069)
14:15:16.964 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
14:15:16.966 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
14:15:16.971 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
14:15:17.203 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2564f61-7cdc-43ec-be01-de1df9f5642b] Receive server push request, request = NotifySubscriberRequest, requestId = 75
14:15:17.231 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2564f61-7cdc-43ec-be01-de1df9f5642b] Ack server push request, request = NotifySubscriberRequest, requestId = 75
14:15:17.989 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751436905678 started.
14:20:06.215 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751436905678 paused.
14:20:06.305 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:20:06.310 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:20:06.630 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:20:06.631 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@66a6cd89[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:20:06.631 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751436916522_127.0.0.1_9493
14:20:06.634 [nacos-grpc-client-executor-66] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751436916522_127.0.0.1_9493]Ignore complete event,isRunning:false,isAbandon=false
14:20:06.637 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6a2f20d4[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 67]
14:20:06.684 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751436905678 shutting down.
14:20:06.684 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751436905678 paused.
14:20:06.686 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751436905678 shutdown complete.
14:20:06.687 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:20:06.690 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:20:06.699 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:20:06.700 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:24:40.579 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:24:41.480 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 373c9d81-ce40-472f-ab8b-be5d540e80ff_config-0
14:24:41.577 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 46 ms to scan 1 urls, producing 3 keys and 6 values 
14:24:41.643 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 4 keys and 9 values 
14:24:41.659 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 3 keys and 10 values 
14:24:41.672 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
14:24:41.689 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
14:24:41.700 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
14:24:41.706 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [373c9d81-ce40-472f-ab8b-be5d540e80ff_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:24:41.707 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [373c9d81-ce40-472f-ab8b-be5d540e80ff_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001af223b7410
14:24:41.708 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [373c9d81-ce40-472f-ab8b-be5d540e80ff_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001af223b7630
14:24:41.709 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [373c9d81-ce40-472f-ab8b-be5d540e80ff_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:24:41.710 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [373c9d81-ce40-472f-ab8b-be5d540e80ff_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:24:41.726 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [373c9d81-ce40-472f-ab8b-be5d540e80ff_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:24:42.927 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [373c9d81-ce40-472f-ab8b-be5d540e80ff_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751437482669_127.0.0.1_10158
14:24:42.929 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [373c9d81-ce40-472f-ab8b-be5d540e80ff_config-0] Notify connected event to listeners.
14:24:42.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [373c9d81-ce40-472f-ab8b-be5d540e80ff_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:24:42.930 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [373c9d81-ce40-472f-ab8b-be5d540e80ff_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001af224f0fb0
14:24:43.126 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:24:47.336 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
14:24:47.336 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:24:47.336 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:24:47.613 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:24:48.501 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:24:48.503 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:24:48.504 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:24:48.914 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:24:48.937 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:24:48.938 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:24:48.957 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751437488919'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

14:24:48.957 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
14:24:48.957 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:24:48.960 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@7a6b214c
14:24:53.056 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:24:59.289 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9e6fe4a1-06e7-4dfd-968c-a57b8ee772d2
14:24:59.289 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e6fe4a1-06e7-4dfd-968c-a57b8ee772d2] RpcClient init label, labels = {module=naming, source=sdk}
14:24:59.292 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e6fe4a1-06e7-4dfd-968c-a57b8ee772d2] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:24:59.292 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e6fe4a1-06e7-4dfd-968c-a57b8ee772d2] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:24:59.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e6fe4a1-06e7-4dfd-968c-a57b8ee772d2] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:24:59.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e6fe4a1-06e7-4dfd-968c-a57b8ee772d2] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:24:59.424 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e6fe4a1-06e7-4dfd-968c-a57b8ee772d2] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751437499301_127.0.0.1_10192
14:24:59.424 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e6fe4a1-06e7-4dfd-968c-a57b8ee772d2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:24:59.424 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e6fe4a1-06e7-4dfd-968c-a57b8ee772d2] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001af224f0fb0
14:24:59.426 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e6fe4a1-06e7-4dfd-968c-a57b8ee772d2] Notify connected event to listeners.
14:24:59.550 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
14:24:59.601 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job 192.168.2.43:9500 register finished
14:24:59.793 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 20.101 seconds (JVM running for 21.533)
14:24:59.814 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
14:24:59.815 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
14:24:59.824 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
14:25:00.018 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e6fe4a1-06e7-4dfd-968c-a57b8ee772d2] Receive server push request, request = NotifySubscriberRequest, requestId = 81
14:25:00.064 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e6fe4a1-06e7-4dfd-968c-a57b8ee772d2] Ack server push request, request = NotifySubscriberRequest, requestId = 81
14:25:00.850 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751437488919 started.
14:25:01.366 [http-nio-9500-exec-5] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:27:22.525 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751437488919 paused.
14:27:22.613 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:27:22.620 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:27:22.929 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:27:22.930 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@28d5d8ff[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:27:22.932 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751437499301_127.0.0.1_10192
14:27:22.940 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751437499301_127.0.0.1_10192]Ignore complete event,isRunning:false,isAbandon=false
14:27:22.941 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5336b2fe[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 37]
14:27:22.976 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751437488919 shutting down.
14:27:22.976 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751437488919 paused.
14:27:22.977 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751437488919 shutdown complete.
14:27:22.979 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:27:22.984 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:27:22.996 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:27:22.996 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:27:30.077 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:27:31.070 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c23b7b1e-ff28-4d59-9fe1-658b73e71d65_config-0
14:27:31.163 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 40 ms to scan 1 urls, producing 3 keys and 6 values 
14:27:31.240 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 4 keys and 9 values 
14:27:31.253 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
14:27:31.267 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
14:27:31.279 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
14:27:31.292 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
14:27:31.297 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c23b7b1e-ff28-4d59-9fe1-658b73e71d65_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:27:31.298 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c23b7b1e-ff28-4d59-9fe1-658b73e71d65_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002265c3b7410
14:27:31.298 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c23b7b1e-ff28-4d59-9fe1-658b73e71d65_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000002265c3b7630
14:27:31.299 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c23b7b1e-ff28-4d59-9fe1-658b73e71d65_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:27:31.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c23b7b1e-ff28-4d59-9fe1-658b73e71d65_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:27:31.313 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c23b7b1e-ff28-4d59-9fe1-658b73e71d65_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:27:32.548 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c23b7b1e-ff28-4d59-9fe1-658b73e71d65_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751437652302_127.0.0.1_10390
14:27:32.550 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c23b7b1e-ff28-4d59-9fe1-658b73e71d65_config-0] Notify connected event to listeners.
14:27:32.552 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c23b7b1e-ff28-4d59-9fe1-658b73e71d65_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:27:32.553 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c23b7b1e-ff28-4d59-9fe1-658b73e71d65_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002265c4f0fb0
14:27:32.747 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:27:37.322 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
14:27:37.323 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:27:37.324 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:27:37.582 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:27:38.466 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:27:38.469 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:27:38.469 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:27:38.888 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:27:38.918 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:27:38.919 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:27:38.943 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751437658893'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

14:27:38.944 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
14:27:38.944 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:27:38.947 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@6e53bb4f
14:27:43.218 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:27:49.854 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 75bcc460-e6c2-4814-bb6b-55b11943d8e7
14:27:49.855 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75bcc460-e6c2-4814-bb6b-55b11943d8e7] RpcClient init label, labels = {module=naming, source=sdk}
14:27:49.857 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75bcc460-e6c2-4814-bb6b-55b11943d8e7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:27:49.857 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75bcc460-e6c2-4814-bb6b-55b11943d8e7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:27:49.859 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75bcc460-e6c2-4814-bb6b-55b11943d8e7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:27:49.860 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75bcc460-e6c2-4814-bb6b-55b11943d8e7] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:27:49.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75bcc460-e6c2-4814-bb6b-55b11943d8e7] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751437669868_127.0.0.1_10424
14:27:49.993 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75bcc460-e6c2-4814-bb6b-55b11943d8e7] Notify connected event to listeners.
14:27:49.994 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75bcc460-e6c2-4814-bb6b-55b11943d8e7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:27:49.995 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75bcc460-e6c2-4814-bb6b-55b11943d8e7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000002265c4f0fb0
14:27:50.089 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
14:27:50.141 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job 192.168.2.43:9500 register finished
14:27:50.337 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 21.125 seconds (JVM running for 22.523)
14:27:50.354 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
14:27:50.356 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
14:27:50.361 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
14:27:50.618 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75bcc460-e6c2-4814-bb6b-55b11943d8e7] Receive server push request, request = NotifySubscriberRequest, requestId = 83
14:27:50.647 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [75bcc460-e6c2-4814-bb6b-55b11943d8e7] Ack server push request, request = NotifySubscriberRequest, requestId = 83
14:27:51.415 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751437658893 started.
14:29:28.181 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751437658893 paused.
14:29:28.255 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:29:28.261 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:29:28.574 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:29:28.574 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@784bb2a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:29:28.575 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751437669868_127.0.0.1_10424
14:29:28.579 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751437669868_127.0.0.1_10424]Ignore complete event,isRunning:false,isAbandon=false
14:29:28.584 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@25d95d8f[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 30]
14:29:28.619 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751437658893 shutting down.
14:29:28.620 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751437658893 paused.
14:29:28.621 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751437658893 shutdown complete.
14:29:28.623 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
14:29:28.627 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
14:29:28.636 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
14:29:28.636 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
14:35:03.333 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:35:04.256 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b97caec0-b5b9-452f-a890-b2d5ba4492c8_config-0
14:35:04.359 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 39 ms to scan 1 urls, producing 3 keys and 6 values 
14:35:04.423 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 4 keys and 9 values 
14:35:04.445 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
14:35:04.458 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
14:35:04.474 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
14:35:04.490 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
14:35:04.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b97caec0-b5b9-452f-a890-b2d5ba4492c8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:35:04.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b97caec0-b5b9-452f-a890-b2d5ba4492c8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000183e43b6480
14:35:04.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b97caec0-b5b9-452f-a890-b2d5ba4492c8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x00000183e43b66a0
14:35:04.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b97caec0-b5b9-452f-a890-b2d5ba4492c8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:35:04.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b97caec0-b5b9-452f-a890-b2d5ba4492c8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:35:04.506 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b97caec0-b5b9-452f-a890-b2d5ba4492c8_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:35:05.721 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b97caec0-b5b9-452f-a890-b2d5ba4492c8_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751438105477_127.0.0.1_11001
14:35:05.721 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b97caec0-b5b9-452f-a890-b2d5ba4492c8_config-0] Notify connected event to listeners.
14:35:05.721 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b97caec0-b5b9-452f-a890-b2d5ba4492c8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:35:05.721 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b97caec0-b5b9-452f-a890-b2d5ba4492c8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000183e44f0228
14:35:05.903 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:35:10.354 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
14:35:10.354 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:35:10.354 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:35:10.620 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:35:11.553 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
14:35:11.556 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
14:35:11.556 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
14:35:12.075 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
14:35:12.100 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
14:35:12.100 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
14:35:12.124 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751438112075'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

14:35:12.124 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
14:35:12.124 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
14:35:12.124 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@69c6e5
14:35:16.791 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:35:23.003 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 11ee267d-2b50-4e3c-bf0d-14a3249f5c6b
14:35:23.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11ee267d-2b50-4e3c-bf0d-14a3249f5c6b] RpcClient init label, labels = {module=naming, source=sdk}
14:35:23.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11ee267d-2b50-4e3c-bf0d-14a3249f5c6b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:35:23.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11ee267d-2b50-4e3c-bf0d-14a3249f5c6b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:35:23.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11ee267d-2b50-4e3c-bf0d-14a3249f5c6b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:35:23.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11ee267d-2b50-4e3c-bf0d-14a3249f5c6b] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
14:35:23.134 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11ee267d-2b50-4e3c-bf0d-14a3249f5c6b] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751438123013_127.0.0.1_11021
14:35:23.134 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11ee267d-2b50-4e3c-bf0d-14a3249f5c6b] Notify connected event to listeners.
14:35:23.134 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11ee267d-2b50-4e3c-bf0d-14a3249f5c6b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:35:23.134 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11ee267d-2b50-4e3c-bf0d-14a3249f5c6b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x00000183e44f0228
14:35:23.221 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
14:35:23.274 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job 192.168.2.43:9500 register finished
14:35:23.444 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 20.947 seconds (JVM running for 22.353)
14:35:23.463 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
14:35:23.463 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
14:35:23.463 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
14:35:23.723 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11ee267d-2b50-4e3c-bf0d-14a3249f5c6b] Receive server push request, request = NotifySubscriberRequest, requestId = 85
14:35:23.755 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [11ee267d-2b50-4e3c-bf0d-14a3249f5c6b] Ack server push request, request = NotifySubscriberRequest, requestId = 85
14:35:24.511 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751438112075 started.
15:04:35.423 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751438112075 paused.
15:04:35.532 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:04:35.536 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:04:35.856 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:04:35.856 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@569cac17[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:04:35.856 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751438123013_127.0.0.1_11021
15:04:35.860 [nacos-grpc-client-executor-359] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751438123013_127.0.0.1_11021]Ignore complete event,isRunning:false,isAbandon=false
15:04:35.864 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6bb2cd6e[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 360]
15:04:35.902 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751438112075 shutting down.
15:04:35.902 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751438112075 paused.
15:04:35.904 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751438112075 shutdown complete.
15:04:35.904 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:04:35.904 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:04:35.912 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:04:35.912 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:04:45.497 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:04:46.467 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of be4ced85-c887-47fd-b93c-7beb8d5dc3a9_config-0
15:04:46.558 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 44 ms to scan 1 urls, producing 3 keys and 6 values 
15:04:46.641 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 29 ms to scan 1 urls, producing 4 keys and 9 values 
15:04:46.655 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 3 keys and 10 values 
15:04:46.672 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
15:04:46.682 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
15:04:46.698 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
15:04:46.698 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be4ced85-c887-47fd-b93c-7beb8d5dc3a9_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:04:46.698 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be4ced85-c887-47fd-b93c-7beb8d5dc3a9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001a6813b68d8
15:04:46.698 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be4ced85-c887-47fd-b93c-7beb8d5dc3a9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001a6813b6af8
15:04:46.698 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be4ced85-c887-47fd-b93c-7beb8d5dc3a9_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:04:46.706 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be4ced85-c887-47fd-b93c-7beb8d5dc3a9_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:04:46.721 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be4ced85-c887-47fd-b93c-7beb8d5dc3a9_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
15:04:47.935 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be4ced85-c887-47fd-b93c-7beb8d5dc3a9_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751439887660_127.0.0.1_12926
15:04:47.936 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be4ced85-c887-47fd-b93c-7beb8d5dc3a9_config-0] Notify connected event to listeners.
15:04:47.936 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be4ced85-c887-47fd-b93c-7beb8d5dc3a9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:04:47.940 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [be4ced85-c887-47fd-b93c-7beb8d5dc3a9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001a6814f0ad8
15:04:48.133 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:04:52.475 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
15:04:52.475 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:04:52.475 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:04:52.714 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:04:53.640 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:04:53.644 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:04:53.644 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:04:54.052 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:04:54.075 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:04:54.075 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:04:54.098 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751439894058'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

15:04:54.098 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
15:04:54.098 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:04:54.098 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@256c6f7a
15:04:58.166 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:05:04.328 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7fb293d8-4ede-4d61-a399-c89540c9a40c
15:05:04.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb293d8-4ede-4d61-a399-c89540c9a40c] RpcClient init label, labels = {module=naming, source=sdk}
15:05:04.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb293d8-4ede-4d61-a399-c89540c9a40c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:05:04.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb293d8-4ede-4d61-a399-c89540c9a40c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:05:04.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb293d8-4ede-4d61-a399-c89540c9a40c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:05:04.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb293d8-4ede-4d61-a399-c89540c9a40c] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
15:05:04.464 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb293d8-4ede-4d61-a399-c89540c9a40c] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751439904347_127.0.0.1_12955
15:05:04.464 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb293d8-4ede-4d61-a399-c89540c9a40c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:05:04.464 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb293d8-4ede-4d61-a399-c89540c9a40c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001a6814f0ad8
15:05:04.464 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb293d8-4ede-4d61-a399-c89540c9a40c] Notify connected event to listeners.
15:05:04.544 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
15:05:04.588 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job 192.168.2.43:9500 register finished
15:05:04.755 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 20.152 seconds (JVM running for 21.575)
15:05:04.776 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
15:05:04.776 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
15:05:04.786 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
15:05:05.084 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb293d8-4ede-4d61-a399-c89540c9a40c] Receive server push request, request = NotifySubscriberRequest, requestId = 87
15:05:05.107 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7fb293d8-4ede-4d61-a399-c89540c9a40c] Ack server push request, request = NotifySubscriberRequest, requestId = 87
15:05:05.826 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751439894058 started.
15:05:11.264 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,40] - 文件回收站过期文件清理任务执行成功
15:05:21.011 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,40] - 文件回收站过期文件清理任务执行成功
15:05:31.047 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,40] - 文件回收站过期文件清理任务执行成功
15:05:41.009 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,40] - 文件回收站过期文件清理任务执行成功
15:05:51.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,40] - 文件回收站过期文件清理任务执行成功
15:06:01.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,40] - 文件回收站过期文件清理任务执行成功
15:06:11.015 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,40] - 文件回收站过期文件清理任务执行成功
15:06:21.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,40] - 文件回收站过期文件清理任务执行成功
15:06:31.033 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,40] - 文件回收站过期文件清理任务执行成功
15:06:41.028 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,40] - 文件回收站过期文件清理任务执行成功
15:06:45.898 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751439894058 paused.
15:06:45.999 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:06:46.005 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:06:46.314 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:06:46.316 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@a9f78cb[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:06:46.316 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751439904347_127.0.0.1_12955
15:06:46.322 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4a07abe8[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 30]
15:06:46.357 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751439894058 shutting down.
15:06:46.357 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751439894058 paused.
15:06:46.359 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751439894058 shutdown complete.
15:06:46.360 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:06:46.360 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:06:46.377 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:06:46.379 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:06:53.827 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:06:54.714 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f0cfd672-cd5a-4f8b-8628-cef4efb58bb0_config-0
15:06:54.802 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 35 ms to scan 1 urls, producing 3 keys and 6 values 
15:06:54.865 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 33 ms to scan 1 urls, producing 4 keys and 9 values 
15:06:54.877 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
15:06:54.877 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
15:06:54.893 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
15:06:54.913 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 2 keys and 8 values 
15:06:54.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0cfd672-cd5a-4f8b-8628-cef4efb58bb0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:06:54.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0cfd672-cd5a-4f8b-8628-cef4efb58bb0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001f4b03b6d38
15:06:54.919 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0cfd672-cd5a-4f8b-8628-cef4efb58bb0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001f4b03b6f58
15:06:54.919 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0cfd672-cd5a-4f8b-8628-cef4efb58bb0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:06:54.921 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0cfd672-cd5a-4f8b-8628-cef4efb58bb0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:06:54.932 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0cfd672-cd5a-4f8b-8628-cef4efb58bb0_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
15:06:56.053 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0cfd672-cd5a-4f8b-8628-cef4efb58bb0_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751440015800_127.0.0.1_13060
15:06:56.053 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0cfd672-cd5a-4f8b-8628-cef4efb58bb0_config-0] Notify connected event to listeners.
15:06:56.057 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0cfd672-cd5a-4f8b-8628-cef4efb58bb0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:06:56.057 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f0cfd672-cd5a-4f8b-8628-cef4efb58bb0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001f4b04f0ad8
15:06:56.230 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:07:00.450 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
15:07:00.463 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:07:00.463 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:07:00.704 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:07:01.540 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:07:01.544 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:07:01.546 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:07:01.946 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:07:01.971 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:07:01.971 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:07:01.992 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751440021946'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

15:07:01.992 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
15:07:01.992 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:07:01.992 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@7a6b214c
15:07:06.254 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:07:12.565 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5b60b4e9-754f-4e6f-9872-9a6e55852a7f
15:07:12.565 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b60b4e9-754f-4e6f-9872-9a6e55852a7f] RpcClient init label, labels = {module=naming, source=sdk}
15:07:12.581 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b60b4e9-754f-4e6f-9872-9a6e55852a7f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:07:12.581 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b60b4e9-754f-4e6f-9872-9a6e55852a7f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:07:12.581 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b60b4e9-754f-4e6f-9872-9a6e55852a7f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:07:12.581 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b60b4e9-754f-4e6f-9872-9a6e55852a7f] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
15:07:12.715 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b60b4e9-754f-4e6f-9872-9a6e55852a7f] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751440032595_127.0.0.1_13091
15:07:12.715 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b60b4e9-754f-4e6f-9872-9a6e55852a7f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:07:12.715 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b60b4e9-754f-4e6f-9872-9a6e55852a7f] Notify connected event to listeners.
15:07:12.715 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b60b4e9-754f-4e6f-9872-9a6e55852a7f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001f4b04f0ad8
15:07:12.802 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
15:07:12.851 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job 192.168.2.43:9500 register finished
15:07:13.048 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 20.064 seconds (JVM running for 21.527)
15:07:13.064 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
15:07:13.064 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
15:07:13.064 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
15:07:13.294 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b60b4e9-754f-4e6f-9872-9a6e55852a7f] Receive server push request, request = NotifySubscriberRequest, requestId = 88
15:07:13.323 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5b60b4e9-754f-4e6f-9872-9a6e55852a7f] Ack server push request, request = NotifySubscriberRequest, requestId = 88
15:07:14.106 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751440021946 started.
15:08:24.644 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751440021946 paused.
15:08:24.717 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:08:24.724 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:08:25.037 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:08:25.038 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@69699751[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:08:25.039 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751440032595_127.0.0.1_13091
15:08:25.045 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751440032595_127.0.0.1_13091]Ignore complete event,isRunning:false,isAbandon=false
15:08:25.046 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@68bee511[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 25]
15:08:25.084 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751440021946 shutting down.
15:08:25.084 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751440021946 paused.
15:08:25.086 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751440021946 shutdown complete.
15:08:25.086 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:08:25.086 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:08:25.086 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:08:25.086 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:08:31.783 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:08:32.753 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ********-5da7-4466-991b-f201e5be96f1_config-0
15:08:32.928 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 121 ms to scan 1 urls, producing 3 keys and 6 values 
15:08:33.043 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 36 ms to scan 1 urls, producing 4 keys and 9 values 
15:08:33.060 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
15:08:33.072 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
15:08:33.101 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
15:08:33.124 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 2 keys and 8 values 
15:08:33.131 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-5da7-4466-991b-f201e5be96f1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:08:33.131 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-5da7-4466-991b-f201e5be96f1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001dce93b6f80
15:08:33.131 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-5da7-4466-991b-f201e5be96f1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001dce93b71a0
15:08:33.131 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-5da7-4466-991b-f201e5be96f1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:08:33.131 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-5da7-4466-991b-f201e5be96f1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:08:33.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-5da7-4466-991b-f201e5be96f1_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
15:08:34.425 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-5da7-4466-991b-f201e5be96f1_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751440114164_127.0.0.1_13218
15:08:34.425 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-5da7-4466-991b-f201e5be96f1_config-0] Notify connected event to listeners.
15:08:34.425 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-5da7-4466-991b-f201e5be96f1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:08:34.425 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [********-5da7-4466-991b-f201e5be96f1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001dce94f0668
15:08:34.598 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:08:38.867 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
15:08:38.873 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:08:38.874 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:08:39.125 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:08:40.002 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:08:40.002 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:08:40.002 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:08:40.414 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:08:40.441 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:08:40.441 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:08:40.460 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751440120414'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

15:08:40.460 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
15:08:40.460 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:08:40.460 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@145f1f97
15:08:44.657 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:08:50.917 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d2d8b37d-775a-4da1-bf12-94273066eb1f
15:08:50.917 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d8b37d-775a-4da1-bf12-94273066eb1f] RpcClient init label, labels = {module=naming, source=sdk}
15:08:50.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d8b37d-775a-4da1-bf12-94273066eb1f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:08:50.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d8b37d-775a-4da1-bf12-94273066eb1f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:08:50.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d8b37d-775a-4da1-bf12-94273066eb1f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:08:50.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d8b37d-775a-4da1-bf12-94273066eb1f] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
15:08:51.080 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d8b37d-775a-4da1-bf12-94273066eb1f] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751440130936_127.0.0.1_13239
15:08:51.080 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d8b37d-775a-4da1-bf12-94273066eb1f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:08:51.080 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d8b37d-775a-4da1-bf12-94273066eb1f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001dce94f0668
15:08:51.080 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d8b37d-775a-4da1-bf12-94273066eb1f] Notify connected event to listeners.
15:08:51.166 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
15:08:51.215 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job 192.168.2.43:9500 register finished
15:08:51.382 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 20.482 seconds (JVM running for 21.943)
15:08:51.412 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
15:08:51.413 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
15:08:51.418 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
15:08:51.681 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d8b37d-775a-4da1-bf12-94273066eb1f] Receive server push request, request = NotifySubscriberRequest, requestId = 89
15:08:51.697 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d2d8b37d-775a-4da1-bf12-94273066eb1f] Ack server push request, request = NotifySubscriberRequest, requestId = 89
15:08:52.448 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751440120414 started.
15:11:39.874 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751440120414 paused.
15:11:39.915 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:11:39.918 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:11:40.229 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:11:40.230 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@61a6112c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:11:40.230 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751440130936_127.0.0.1_13239
15:11:40.240 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@9f10704[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 44]
15:11:40.243 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751440130936_127.0.0.1_13239]Ignore complete event,isRunning:false,isAbandon=false
15:11:40.264 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751440120414 shutting down.
15:11:40.265 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751440120414 paused.
15:11:40.265 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751440120414 shutdown complete.
15:11:40.265 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:11:40.267 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:11:40.274 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:11:40.274 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
15:11:46.116 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
15:11:46.680 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a6ec5f76-87e8-486d-858d-dcbb9680c29e_config-0
15:11:46.737 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 3 keys and 6 values 
15:11:46.767 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
15:11:46.776 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
15:11:46.782 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 1 keys and 5 values 
15:11:46.789 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 7 values 
15:11:46.795 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 4 ms to scan 1 urls, producing 2 keys and 8 values 
15:11:46.798 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6ec5f76-87e8-486d-858d-dcbb9680c29e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
15:11:46.799 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6ec5f76-87e8-486d-858d-dcbb9680c29e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000025aa339e8d8
15:11:46.800 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6ec5f76-87e8-486d-858d-dcbb9680c29e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x0000025aa339eaf8
15:11:46.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6ec5f76-87e8-486d-858d-dcbb9680c29e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
15:11:46.802 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6ec5f76-87e8-486d-858d-dcbb9680c29e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
15:11:46.809 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6ec5f76-87e8-486d-858d-dcbb9680c29e_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
15:11:47.536 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6ec5f76-87e8-486d-858d-dcbb9680c29e_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751440307362_127.0.0.1_13526
15:11:47.538 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6ec5f76-87e8-486d-858d-dcbb9680c29e_config-0] Notify connected event to listeners.
15:11:47.539 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6ec5f76-87e8-486d-858d-dcbb9680c29e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:11:47.543 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a6ec5f76-87e8-486d-858d-dcbb9680c29e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000025aa3518fb0
15:11:47.638 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
15:11:50.100 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
15:11:50.100 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
15:11:50.101 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
15:11:50.278 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
15:11:50.961 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
15:11:50.963 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
15:11:50.964 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
15:11:51.201 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
15:11:51.214 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
15:11:51.214 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
15:11:51.226 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751440311204'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

15:11:51.226 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
15:11:51.226 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
15:11:51.227 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@ec1b776
15:11:53.935 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
15:11:57.603 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 661be26e-f895-4584-aa7c-e71b2f75fec1
15:11:57.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [661be26e-f895-4584-aa7c-e71b2f75fec1] RpcClient init label, labels = {module=naming, source=sdk}
15:11:57.604 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [661be26e-f895-4584-aa7c-e71b2f75fec1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
15:11:57.604 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [661be26e-f895-4584-aa7c-e71b2f75fec1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
15:11:57.605 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [661be26e-f895-4584-aa7c-e71b2f75fec1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
15:11:57.605 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [661be26e-f895-4584-aa7c-e71b2f75fec1] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
15:11:57.723 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [661be26e-f895-4584-aa7c-e71b2f75fec1] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751440317612_127.0.0.1_13544
15:11:57.723 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [661be26e-f895-4584-aa7c-e71b2f75fec1] Notify connected event to listeners.
15:11:57.724 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [661be26e-f895-4584-aa7c-e71b2f75fec1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
15:11:57.724 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [661be26e-f895-4584-aa7c-e71b2f75fec1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000025aa3518fb0
15:11:57.775 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
15:11:57.801 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job 192.168.2.43:9500 register finished
15:11:57.911 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 12.293 seconds (JVM running for 13.047)
15:11:57.921 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
15:11:57.922 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
15:11:57.924 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
15:11:58.281 [RMI TCP Connection(1)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:11:58.376 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [661be26e-f895-4584-aa7c-e71b2f75fec1] Receive server push request, request = NotifySubscriberRequest, requestId = 90
15:11:58.417 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [661be26e-f895-4584-aa7c-e71b2f75fec1] Ack server push request, request = NotifySubscriberRequest, requestId = 90
15:11:58.976 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751440311204 started.
15:12:01.160 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,48] - 没有发现过期文件需要清理
15:12:01.160 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 文件回收站过期文件清理任务执行完成
15:12:11.008 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,48] - 没有发现过期文件需要清理
15:12:11.008 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 文件回收站过期文件清理任务执行完成
15:12:21.006 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,48] - 没有发现过期文件需要清理
15:12:21.007 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 文件回收站过期文件清理任务执行完成
15:12:31.006 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,48] - 没有发现过期文件需要清理
15:12:31.006 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 文件回收站过期文件清理任务执行完成
15:12:41.006 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,48] - 没有发现过期文件需要清理
15:12:41.006 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 文件回收站过期文件清理任务执行完成
15:12:51.007 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,48] - 没有发现过期文件需要清理
15:12:51.009 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 文件回收站过期文件清理任务执行完成
15:13:01.007 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,48] - 没有发现过期文件需要清理
15:13:01.008 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 文件回收站过期文件清理任务执行完成
15:13:04.782 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751440311204 paused.
15:13:04.817 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:13:04.822 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:13:05.130 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:13:05.130 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6389bbf8[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:13:05.130 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751440317612_127.0.0.1_13544
15:13:05.139 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@26e2846a[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 25]
15:13:05.145 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [661be26e-f895-4584-aa7c-e71b2f75fec1] Notify disconnected event to listeners
15:13:05.280 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751440311204 shutting down.
15:13:05.280 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751440311204 paused.
15:13:05.282 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751440311204 shutdown complete.
15:13:05.282 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
15:13:05.286 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
15:13:05.290 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
15:13:05.290 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
17:35:18.657 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
17:35:20.080 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 95292f6c-61d8-4678-852c-831f5b266ce4_config-0
17:35:20.252 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 70 ms to scan 1 urls, producing 3 keys and 6 values 
17:35:20.354 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 33 ms to scan 1 urls, producing 4 keys and 9 values 
17:35:20.374 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 3 keys and 10 values 
17:35:20.392 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
17:35:20.414 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 7 values 
17:35:20.428 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
17:35:20.434 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95292f6c-61d8-4678-852c-831f5b266ce4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
17:35:20.434 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95292f6c-61d8-4678-852c-831f5b266ce4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001ec243b8fc8
17:35:20.434 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95292f6c-61d8-4678-852c-831f5b266ce4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$458/0x000001ec243b91e8
17:35:20.439 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95292f6c-61d8-4678-852c-831f5b266ce4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
17:35:20.440 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95292f6c-61d8-4678-852c-831f5b266ce4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
17:35:20.458 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95292f6c-61d8-4678-852c-831f5b266ce4_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:35:22.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95292f6c-61d8-4678-852c-831f5b266ce4_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751448921743_127.0.0.1_13005
17:35:22.056 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95292f6c-61d8-4678-852c-831f5b266ce4_config-0] Notify connected event to listeners.
17:35:22.058 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95292f6c-61d8-4678-852c-831f5b266ce4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:35:22.058 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [95292f6c-61d8-4678-852c-831f5b266ce4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001ec244f0fb0
17:35:22.326 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
17:35:34.542 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
17:35:34.542 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
17:35:34.542 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
17:35:35.102 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
17:35:35.135 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
17:35:35.136 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
17:35:35.165 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751448935110'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

17:35:35.166 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
17:35:35.166 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
17:35:35.171 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@44723d95
17:35:40.424 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
17:35:48.278 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e0ea6728-3d22-44a7-b2f2-3ee0acc19ea5
17:35:48.278 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0ea6728-3d22-44a7-b2f2-3ee0acc19ea5] RpcClient init label, labels = {module=naming, source=sdk}
17:35:48.278 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0ea6728-3d22-44a7-b2f2-3ee0acc19ea5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
17:35:48.278 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0ea6728-3d22-44a7-b2f2-3ee0acc19ea5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
17:35:48.278 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0ea6728-3d22-44a7-b2f2-3ee0acc19ea5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
17:35:48.285 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0ea6728-3d22-44a7-b2f2-3ee0acc19ea5] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
17:35:48.420 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0ea6728-3d22-44a7-b2f2-3ee0acc19ea5] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751448948290_127.0.0.1_13070
17:35:48.420 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0ea6728-3d22-44a7-b2f2-3ee0acc19ea5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
17:35:48.420 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0ea6728-3d22-44a7-b2f2-3ee0acc19ea5] Notify connected event to listeners.
17:35:48.420 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0ea6728-3d22-44a7-b2f2-3ee0acc19ea5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$472/0x000001ec244f0fb0
17:35:48.576 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job 192.168.2.43:9500 register finished
17:35:48.830 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 31.415 seconds (JVM running for 34.015)
17:35:48.855 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
17:35:48.856 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
17:35:48.861 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
17:35:48.930 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:35:48.940 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:35:48.962 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0ea6728-3d22-44a7-b2f2-3ee0acc19ea5] Receive server push request, request = NotifySubscriberRequest, requestId = 97
17:35:48.988 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0ea6728-3d22-44a7-b2f2-3ee0acc19ea5] Ack server push request, request = NotifySubscriberRequest, requestId = 97
17:35:49.433 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:35:49.433 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@36b4e044[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:35:49.433 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751448948290_127.0.0.1_13070
17:35:49.438 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1926fdc8[Running, pool size = 10, active threads = 1, queued tasks = 0, completed tasks = 9]
17:35:49.438 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751448948290_127.0.0.1_13070]Ignore complete event,isRunning:false,isAbandon=false
17:35:49.476 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751448935110 shutting down.
17:35:49.476 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751448935110 paused.
17:35:49.477 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751448935110 shutdown complete.
17:35:49.477 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
17:35:49.477 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
17:35:49.489 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
17:35:49.489 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
19:09:42.323 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:09:43.330 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5fc35b99-e2e4-421c-b9cb-6306fa0ffcb0_config-0
19:09:43.458 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 71 ms to scan 1 urls, producing 3 keys and 6 values 
19:09:43.561 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 4 keys and 9 values 
19:09:43.585 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 10 values 
19:09:43.609 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 5 values 
19:09:43.632 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 1 keys and 7 values 
19:09:43.644 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
19:09:43.649 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fc35b99-e2e4-421c-b9cb-6306fa0ffcb0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:09:43.650 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fc35b99-e2e4-421c-b9cb-6306fa0ffcb0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001336639e480
19:09:43.650 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fc35b99-e2e4-421c-b9cb-6306fa0ffcb0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001336639e6a0
19:09:43.651 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fc35b99-e2e4-421c-b9cb-6306fa0ffcb0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:09:43.652 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fc35b99-e2e4-421c-b9cb-6306fa0ffcb0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:09:43.687 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fc35b99-e2e4-421c-b9cb-6306fa0ffcb0_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
19:09:45.002 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fc35b99-e2e4-421c-b9cb-6306fa0ffcb0_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751454584773_127.0.0.1_9451
19:09:45.003 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fc35b99-e2e4-421c-b9cb-6306fa0ffcb0_config-0] Notify connected event to listeners.
19:09:45.004 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fc35b99-e2e4-421c-b9cb-6306fa0ffcb0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:09:45.006 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5fc35b99-e2e4-421c-b9cb-6306fa0ffcb0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000013366518228
19:09:45.209 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:09:50.116 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
19:09:50.118 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
19:09:50.118 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
19:09:50.721 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
19:09:52.234 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
19:09:52.235 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
19:09:52.235 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
19:09:53.014 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
19:09:53.062 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
19:09:53.063 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
19:09:53.106 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751454593021'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

19:09:53.106 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
19:09:53.108 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
19:09:53.115 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@53a3fc8b
19:10:00.527 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
19:10:07.187 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e2a6cd08-6160-472f-94bd-6c9a3f70c096
19:10:07.187 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2a6cd08-6160-472f-94bd-6c9a3f70c096] RpcClient init label, labels = {module=naming, source=sdk}
19:10:07.187 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2a6cd08-6160-472f-94bd-6c9a3f70c096] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:10:07.187 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2a6cd08-6160-472f-94bd-6c9a3f70c096] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:10:07.187 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2a6cd08-6160-472f-94bd-6c9a3f70c096] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:10:07.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2a6cd08-6160-472f-94bd-6c9a3f70c096] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
19:10:07.330 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2a6cd08-6160-472f-94bd-6c9a3f70c096] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751454607203_127.0.0.1_9580
19:10:07.331 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2a6cd08-6160-472f-94bd-6c9a3f70c096] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:10:07.331 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2a6cd08-6160-472f-94bd-6c9a3f70c096] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000013366518228
19:10:07.331 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2a6cd08-6160-472f-94bd-6c9a3f70c096] Notify connected event to listeners.
19:10:07.468 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
19:10:07.530 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job 192.168.2.43:9500 register finished
19:10:07.714 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 26.172 seconds (JVM running for 27.806)
19:10:07.737 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
19:10:07.737 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
19:10:07.764 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
19:10:07.931 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2a6cd08-6160-472f-94bd-6c9a3f70c096] Receive server push request, request = NotifySubscriberRequest, requestId = 119
19:10:07.961 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e2a6cd08-6160-472f-94bd-6c9a3f70c096] Ack server push request, request = NotifySubscriberRequest, requestId = 119
19:10:08.265 [RMI TCP Connection(7)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:10:08.753 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751454593021 started.
19:11:01.257 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:11:01.258 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:12:01.008 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:12:01.009 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:13:01.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:13:01.011 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:15:01.025 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:15:01.025 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:16:01.012 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:16:01.012 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:17:01.011 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:17:01.012 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:18:01.009 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:18:01.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:19:01.014 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:19:01.015 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:20:01.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:20:01.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:21:01.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:21:01.011 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:22:01.012 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:22:01.012 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:24:01.397 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:24:01.397 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:26:01.520 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:26:01.521 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:27:01.019 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:27:01.020 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:28:01.016 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:28:01.016 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:29:01.013 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:29:01.013 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:30:01.015 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:30:01.016 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:31:01.012 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:31:01.012 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:32:01.009 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:32:01.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:33:01.011 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:33:01.012 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:34:01.011 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:34:01.012 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:35:01.014 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:35:01.014 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:36:01.011 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:36:01.012 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:37:01.012 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:37:01.012 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:38:01.014 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:38:01.014 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:39:01.013 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:39:01.014 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:40:01.457 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:40:01.458 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:41:08.142 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:41:08.142 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:43:04.503 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:43:04.503 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:44:01.021 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:44:01.021 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:45:01.012 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:45:01.012 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:46:01.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:46:01.011 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:47:01.008 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:47:01.009 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:48:01.012 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:48:01.013 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:49:01.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:49:01.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:50:01.015 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:50:01.016 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:51:01.011 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:51:01.011 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:52:01.008 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:52:01.008 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:53:01.019 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:53:01.020 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:54:01.012 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:54:01.012 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:55:01.014 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:55:01.014 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:56:01.013 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:56:01.013 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:57:01.013 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:57:01.013 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:58:01.009 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:58:01.009 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
19:59:01.011 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
19:59:01.011 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:00:01.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:00:01.011 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:01:01.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:01:01.011 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:02:01.013 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:02:01.014 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:03:01.011 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:03:01.011 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:04:01.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:04:01.011 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:05:01.011 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:05:01.011 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:06:01.009 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:06:01.009 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:07:01.013 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:07:01.013 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:08:01.012 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:08:01.012 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:09:01.013 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:09:01.014 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:10:01.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:10:01.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:11:01.032 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:11:01.033 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:12:01.014 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:12:01.014 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:13:01.017 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:13:01.017 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:14:01.012 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:14:01.012 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:15:01.014 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:15:01.014 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:16:01.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:16:01.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:17:01.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:17:01.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:18:01.014 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:18:01.014 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:19:01.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:19:01.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:20:01.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:20:01.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:21:01.012 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:21:01.012 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:22:01.012 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:22:01.012 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:23:01.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:23:01.010 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:24:01.009 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:24:01.009 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:25:01.015 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:25:01.016 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:26:01.011 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:26:01.011 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:27:01.014 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:27:01.014 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:28:01.008 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,50] - 没有发现过期文件需要清理
20:28:01.008 [scheduling-1] INFO  c.h.j.t.FileRecycleCleanTask - [cleanExpiredFiles,52] - 文件回收站过期文件清理任务执行完成
20:28:31.290 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751454593021 paused.
20:28:31.389 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:28:31.392 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:28:31.711 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:28:31.713 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4ac13943[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:28:31.714 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751454607203_127.0.0.1_9580
20:28:31.721 [nacos-grpc-client-executor-949] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751454607203_127.0.0.1_9580]Ignore complete event,isRunning:false,isAbandon=false
20:28:31.727 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@46e43c14[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 950]
20:28:31.879 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751454593021 shutting down.
20:28:31.879 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751454593021 paused.
20:28:31.882 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751454593021 shutdown complete.
20:28:31.884 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:28:31.892 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:28:31.904 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:28:31.904 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:29:59.482 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:30:00.441 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1efdea48-a2fc-4079-a92a-4c643b8e9758_config-0
20:30:00.576 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 67 ms to scan 1 urls, producing 3 keys and 6 values 
20:30:00.643 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 4 keys and 9 values 
20:30:00.651 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
20:30:00.662 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 5 values 
20:30:00.681 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 1 keys and 7 values 
20:30:00.694 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
20:30:00.697 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1efdea48-a2fc-4079-a92a-4c643b8e9758_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:30:00.702 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1efdea48-a2fc-4079-a92a-4c643b8e9758_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001728139eaf8
20:30:00.702 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1efdea48-a2fc-4079-a92a-4c643b8e9758_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x000001728139ed18
20:30:00.702 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1efdea48-a2fc-4079-a92a-4c643b8e9758_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:30:00.704 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1efdea48-a2fc-4079-a92a-4c643b8e9758_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:30:00.715 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1efdea48-a2fc-4079-a92a-4c643b8e9758_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
20:30:01.921 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1efdea48-a2fc-4079-a92a-4c643b8e9758_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751459401695_127.0.0.1_5142
20:30:01.923 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1efdea48-a2fc-4079-a92a-4c643b8e9758_config-0] Notify connected event to listeners.
20:30:01.923 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1efdea48-a2fc-4079-a92a-4c643b8e9758_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:30:01.924 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1efdea48-a2fc-4079-a92a-4c643b8e9758_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x0000017281518ad8
20:30:02.108 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:30:07.132 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
20:30:07.132 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:30:07.132 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
20:30:07.388 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:30:08.432 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
20:30:08.441 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
20:30:08.441 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
20:30:08.858 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
20:30:08.882 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
20:30:08.883 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
20:30:08.905 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751459408864'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

20:30:08.905 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
20:30:08.905 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
20:30:08.907 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@779136bf
20:30:09.236 [main] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751459408864 shutting down.
20:30:09.236 [main] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751459408864 paused.
20:30:09.237 [main] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751459408864 shutdown complete.
20:30:09.237 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:30:09.239 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:30:09.249 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:30:09.251 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:30:09.254 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
20:34:18.705 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:34:19.750 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bf19eb0b-7baa-4012-a078-3288a78c2917_config-0
20:34:19.851 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 40 ms to scan 1 urls, producing 3 keys and 6 values 
20:34:19.934 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 4 keys and 9 values 
20:34:19.949 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
20:34:19.963 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
20:34:19.979 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
20:34:19.991 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
20:34:19.995 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf19eb0b-7baa-4012-a078-3288a78c2917_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:34:19.997 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf19eb0b-7baa-4012-a078-3288a78c2917_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x00000186b539e8d8
20:34:19.998 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf19eb0b-7baa-4012-a078-3288a78c2917_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$470/0x00000186b539eaf8
20:34:19.999 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf19eb0b-7baa-4012-a078-3288a78c2917_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:34:20.000 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf19eb0b-7baa-4012-a078-3288a78c2917_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:34:20.011 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf19eb0b-7baa-4012-a078-3288a78c2917_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
20:34:21.315 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf19eb0b-7baa-4012-a078-3288a78c2917_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751459661081_127.0.0.1_5725
20:34:21.316 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf19eb0b-7baa-4012-a078-3288a78c2917_config-0] Notify connected event to listeners.
20:34:21.317 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf19eb0b-7baa-4012-a078-3288a78c2917_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:34:21.317 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf19eb0b-7baa-4012-a078-3288a78c2917_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$484/0x00000186b5518668
20:34:21.470 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:34:26.139 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
20:34:26.140 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
20:34:26.140 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
20:34:26.389 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
20:34:27.274 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
20:34:27.277 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
20:34:27.277 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
20:34:27.688 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
20:34:27.712 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
20:34:27.712 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
20:34:27.735 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691751459667693'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

20:34:27.736 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
20:34:27.736 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
20:34:27.739 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@81cd90e
20:34:30.983 [main] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751459667693 shutting down.
20:34:30.984 [main] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751459667693 paused.
20:34:30.984 [main] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691751459667693 shutdown complete.
20:34:30.985 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
20:34:30.988 [main] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
20:34:31.000 [main] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
20:34:31.001 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
20:34:31.004 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
