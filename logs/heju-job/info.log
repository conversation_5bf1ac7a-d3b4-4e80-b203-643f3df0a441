09:04:11.231 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:04:12.327 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b69347d9-4ba9-46d4-b62b-d97471583795_config-0
09:04:12.401 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 30 ms to scan 1 urls, producing 3 keys and 6 values 
09:04:12.433 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 19 ms to scan 1 urls, producing 4 keys and 9 values 
09:04:12.456 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 3 keys and 10 values 
09:04:12.471 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 5 values 
09:04:12.483 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:04:12.496 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:04:12.500 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b69347d9-4ba9-46d4-b62b-d97471583795_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:04:12.500 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b69347d9-4ba9-46d4-b62b-d97471583795_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x00000207c439cfb8
09:04:12.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b69347d9-4ba9-46d4-b62b-d97471583795_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000207c439d1d8
09:04:12.503 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b69347d9-4ba9-46d4-b62b-d97471583795_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:04:12.503 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b69347d9-4ba9-46d4-b62b-d97471583795_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:04:12.520 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b69347d9-4ba9-46d4-b62b-d97471583795_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:04:13.861 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b69347d9-4ba9-46d4-b62b-d97471583795_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753059853576_127.0.0.1_1633
09:04:13.861 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b69347d9-4ba9-46d4-b62b-d97471583795_config-0] Notify connected event to listeners.
09:04:13.861 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b69347d9-4ba9-46d4-b62b-d97471583795_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:04:13.862 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b69347d9-4ba9-46d4-b62b-d97471583795_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000207c4514f98
09:04:14.087 [main] INFO  c.h.j.HeJuJobApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:04:21.022 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9500"]
09:04:21.023 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:04:21.024 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:04:21.540 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:04:23.217 [main] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-1,master} inited
09:04:23.219 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [master] success
09:04:23.220 [main] INFO  c.b.d.d.DynamicRoutingDataSource - [afterPropertiesSet,234] - dynamic-datasource initial loaded [1] datasource,primary datasource named [master]
09:04:23.948 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1220] - Using default implementation for ThreadExecutor
09:04:24.009 [main] INFO  o.q.c.SchedulerSignalerImpl - [<init>,61] - Initialized Scheduler Signaller of type: class org.quartz.core.SchedulerSignalerImpl
09:04:24.009 [main] INFO  o.q.c.QuartzScheduler - [<init>,229] - Quartz Scheduler v.2.3.2 created.
09:04:24.051 [main] INFO  o.q.c.QuartzScheduler - [initialize,294] - Scheduler meta-data: Quartz Scheduler (v2.3.2) 'hejuScheduler' with instanceId 'LAPTOP-2S34CS691753059863958'
  Scheduler class: 'org.quartz.core.QuartzScheduler' - running locally.
  NOT STARTED.
  Currently in standby mode.
  Number of jobs executed: 0
  Using thread pool 'org.quartz.simpl.SimpleThreadPool' - with 20 threads.
  Using job-store 'org.springframework.scheduling.quartz.LocalDataSourceJobStore' - which supports persistence. and is clustered.

09:04:24.052 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1374] - Quartz scheduler 'hejuScheduler' initialized from an externally provided properties instance.
09:04:24.052 [main] INFO  o.q.i.StdSchedulerFactory - [instantiate,1378] - Quartz scheduler version: 2.3.2
09:04:24.058 [main] INFO  o.q.c.QuartzScheduler - [setJobFactory,2293] - JobFactory set to: org.springframework.scheduling.quartz.AdaptableJobFactory@4a490518
09:04:30.316 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:04:35.007 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 39be7af0-fca0-4ec9-b01d-a188ee6865e8
09:04:35.008 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39be7af0-fca0-4ec9-b01d-a188ee6865e8] RpcClient init label, labels = {module=naming, source=sdk}
09:04:35.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39be7af0-fca0-4ec9-b01d-a188ee6865e8] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:04:35.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39be7af0-fca0-4ec9-b01d-a188ee6865e8] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:04:35.012 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39be7af0-fca0-4ec9-b01d-a188ee6865e8] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:04:35.012 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39be7af0-fca0-4ec9-b01d-a188ee6865e8] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:04:35.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39be7af0-fca0-4ec9-b01d-a188ee6865e8] Success to connect to server [localhost:8848] on start up, connectionId = 1753059875026_127.0.0.1_1675
09:04:35.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39be7af0-fca0-4ec9-b01d-a188ee6865e8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:04:35.152 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39be7af0-fca0-4ec9-b01d-a188ee6865e8] Notify connected event to listeners.
09:04:35.153 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39be7af0-fca0-4ec9-b01d-a188ee6865e8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000207c4514f98
09:04:35.216 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9500"]
09:04:35.266 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-job ************:9500 register finished
09:04:35.470 [main] INFO  c.h.j.HeJuJobApplication - [logStarted,61] - Started HeJuJobApplication in 25.214 seconds (JVM running for 38.084)
09:04:35.488 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job.yml, group=DEFAULT_GROUP
09:04:35.489 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job, group=DEFAULT_GROUP
09:04:35.503 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-job-dev.yml, group=DEFAULT_GROUP
09:04:35.749 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39be7af0-fca0-4ec9-b01d-a188ee6865e8] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:04:35.764 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39be7af0-fca0-4ec9-b01d-a188ee6865e8] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:04:36.554 [Quartz Scheduler [hejuScheduler]] INFO  o.q.c.QuartzScheduler - [start,547] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691753059863958 started.
10:19:11.477 [http-nio-9500-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:19:22.699 [nacos-grpc-client-executor-907] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39be7af0-fca0-4ec9-b01d-a188ee6865e8] Receive server push request, request = NotifySubscriberRequest, requestId = 17
10:19:22.700 [nacos-grpc-client-executor-907] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39be7af0-fca0-4ec9-b01d-a188ee6865e8] Ack server push request, request = NotifySubscriberRequest, requestId = 17
10:19:31.089 [nacos-grpc-client-executor-912] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39be7af0-fca0-4ec9-b01d-a188ee6865e8] Receive server push request, request = NotifySubscriberRequest, requestId = 18
10:19:31.090 [nacos-grpc-client-executor-912] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [39be7af0-fca0-4ec9-b01d-a188ee6865e8] Ack server push request, request = NotifySubscriberRequest, requestId = 18
10:23:30.440 [http-nio-9500-exec-2] INFO  c.a.d.p.DruidDataSource - [init,996] - {dataSource-2} inited
10:23:30.441 [http-nio-9500-exec-2] INFO  c.b.d.d.DynamicRoutingDataSource - [addDataSource,154] - dynamic-datasource - add a datasource named [240b0d22cd1a48069501d000f1c8efe5] success
10:29:19.174 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691753059863958 paused.
10:29:19.284 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
10:29:19.301 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
10:29:19.615 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
10:29:19.616 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@129a8581[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
10:29:19.616 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753059875026_127.0.0.1_1675
10:29:19.630 [nacos-grpc-client-executor-1040] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753059875026_127.0.0.1_1675]Ignore complete event,isRunning:false,isAbandon=false
10:29:19.630 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@74675c90[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 1040]
10:29:19.766 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,666] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691753059863958 shutting down.
10:29:19.766 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [standby,585] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691753059863958 paused.
10:29:19.767 [SpringApplicationShutdownHook] INFO  o.q.c.QuartzScheduler - [shutdown,740] - Scheduler hejuScheduler_$_LAPTOP-2S34CS691753059863958 shutdown complete.
10:29:19.769 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,211] - dynamic-datasource start closing ....
10:29:19.778 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-2} closing ...
10:29:19.790 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-2} closed
10:29:19.794 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2138] - {dataSource-1} closing ...
10:29:19.799 [SpringApplicationShutdownHook] INFO  c.a.d.p.DruidDataSource - [close,2211] - {dataSource-1} closed
10:29:19.800 [SpringApplicationShutdownHook] INFO  c.b.d.d.DynamicRoutingDataSource - [destroy,215] - dynamic-datasource all closed success,bye
