09:04:26.029 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:04:27.338 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 556fb0b2-c63b-4c9b-b714-55e3a3cfe018_config-0
09:04:27.445 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 57 ms to scan 1 urls, producing 3 keys and 6 values 
09:04:27.492 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 23 ms to scan 1 urls, producing 4 keys and 9 values 
09:04:27.513 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 3 keys and 10 values 
09:04:27.528 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 5 values 
09:04:27.544 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 1 keys and 7 values 
09:04:27.558 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
09:04:27.563 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [556fb0b2-c63b-4c9b-b714-55e3a3cfe018_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:04:27.564 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [556fb0b2-c63b-4c9b-b714-55e3a3cfe018_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001fa343b44e8
09:04:27.564 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [556fb0b2-c63b-4c9b-b714-55e3a3cfe018_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001fa343b4708
09:04:27.565 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [556fb0b2-c63b-4c9b-b714-55e3a3cfe018_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:04:27.566 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [556fb0b2-c63b-4c9b-b714-55e3a3cfe018_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:04:27.583 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [556fb0b2-c63b-4c9b-b714-55e3a3cfe018_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:04:29.448 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [556fb0b2-c63b-4c9b-b714-55e3a3cfe018_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753059869169_127.0.0.1_1664
09:04:29.449 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [556fb0b2-c63b-4c9b-b714-55e3a3cfe018_config-0] Notify connected event to listeners.
09:04:29.449 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [556fb0b2-c63b-4c9b-b714-55e3a3cfe018_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:04:29.450 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [556fb0b2-c63b-4c9b-b714-55e3a3cfe018_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001fa344ec200
09:04:29.632 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:04:33.397 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:04:33.397 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:04:33.397 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:04:33.623 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:04:35.748 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:04:37.595 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4b717ab8-ef1e-4af3-85e3-3ee00e744d30
09:04:37.596 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b717ab8-ef1e-4af3-85e3-3ee00e744d30] RpcClient init label, labels = {module=naming, source=sdk}
09:04:37.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b717ab8-ef1e-4af3-85e3-3ee00e744d30] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:04:37.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b717ab8-ef1e-4af3-85e3-3ee00e744d30] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:04:37.615 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b717ab8-ef1e-4af3-85e3-3ee00e744d30] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:04:37.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b717ab8-ef1e-4af3-85e3-3ee00e744d30] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:04:37.804 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b717ab8-ef1e-4af3-85e3-3ee00e744d30] Success to connect to server [localhost:8848] on start up, connectionId = 1753059877684_127.0.0.1_1700
09:04:37.805 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b717ab8-ef1e-4af3-85e3-3ee00e744d30] Notify connected event to listeners.
09:04:37.805 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b717ab8-ef1e-4af3-85e3-3ee00e744d30] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:04:37.805 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b717ab8-ef1e-4af3-85e3-3ee00e744d30] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001fa344ec200
09:04:37.866 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:04:37.905 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
09:04:38.093 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 13.456 seconds (JVM running for 17.121)
09:04:38.107 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:04:38.108 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:04:38.112 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:04:38.389 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b717ab8-ef1e-4af3-85e3-3ee00e744d30] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:04:38.405 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b717ab8-ef1e-4af3-85e3-3ee00e744d30] Ack server push request, request = NotifySubscriberRequest, requestId = 5
09:06:40.537 [http-nio-9200-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:06:48.331 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b717ab8-ef1e-4af3-85e3-3ee00e744d30] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:06:48.332 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b717ab8-ef1e-4af3-85e3-3ee00e744d30] Ack server push request, request = NotifySubscriberRequest, requestId = 13
09:06:51.095 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b717ab8-ef1e-4af3-85e3-3ee00e744d30] Receive server push request, request = NotifySubscriberRequest, requestId = 14
09:06:51.101 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4b717ab8-ef1e-4af3-85e3-3ee00e744d30] Ack server push request, request = NotifySubscriberRequest, requestId = 14
17:57:14.017 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:57:14.026 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:57:14.341 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:57:14.341 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@359badf4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:57:14.341 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753059877684_127.0.0.1_1700
17:57:14.345 [nacos-grpc-client-executor-6637] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753059877684_127.0.0.1_1700]Ignore complete event,isRunning:false,isAbandon=false
17:57:14.358 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3e44f214[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 6638]
