09:17:41.355 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:17:43.623 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f9954bd5-9340-4725-9ca5-c906f2f2d9c9_config-0
09:17:43.717 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 44 ms to scan 1 urls, producing 3 keys and 6 values 
09:17:43.757 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 20 ms to scan 1 urls, producing 4 keys and 9 values 
09:17:43.775 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 3 keys and 10 values 
09:17:43.789 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 5 values 
09:17:43.802 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 7 values 
09:17:43.813 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:17:43.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9954bd5-9340-4725-9ca5-c906f2f2d9c9_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:17:43.818 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9954bd5-9340-4725-9ca5-c906f2f2d9c9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x00000198dc3b3da8
09:17:43.819 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9954bd5-9340-4725-9ca5-c906f2f2d9c9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000198dc3b3fc8
09:17:43.820 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9954bd5-9340-4725-9ca5-c906f2f2d9c9_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:17:43.821 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9954bd5-9340-4725-9ca5-c906f2f2d9c9_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:17:43.832 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9954bd5-9340-4725-9ca5-c906f2f2d9c9_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:17:45.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9954bd5-9340-4725-9ca5-c906f2f2d9c9_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755134264870_127.0.0.1_14621
09:17:45.094 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9954bd5-9340-4725-9ca5-c906f2f2d9c9_config-0] Notify connected event to listeners.
09:17:45.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9954bd5-9340-4725-9ca5-c906f2f2d9c9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:17:45.095 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f9954bd5-9340-4725-9ca5-c906f2f2d9c9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000198dc4ec200
09:17:45.250 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:17:48.604 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:17:48.605 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:17:48.606 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:17:48.890 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:17:51.825 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:17:54.013 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 357bcdc8-a47e-47a8-b647-07ccce8d3e8e
09:17:54.013 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] RpcClient init label, labels = {module=naming, source=sdk}
09:17:54.016 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:17:54.017 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:17:54.017 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:17:54.018 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:17:54.162 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Success to connect to server [localhost:8848] on start up, connectionId = 1755134274031_127.0.0.1_14684
09:17:54.163 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Notify connected event to listeners.
09:17:54.163 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:17:54.163 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000198dc4ec200
09:17:54.255 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:17:54.333 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ***********:9200 register finished
09:17:54.757 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 14.335 seconds (JVM running for 18.054)
09:17:54.774 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:17:54.775 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:17:54.779 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:17:55.272 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:17:55.273 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:19:21.382 [http-nio-9200-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:19:32.563 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Receive server push request, request = NotifySubscriberRequest, requestId = 11
09:19:32.567 [nacos-grpc-client-executor-34] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Ack server push request, request = NotifySubscriberRequest, requestId = 11
09:19:41.050 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:19:41.052 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Ack server push request, request = NotifySubscriberRequest, requestId = 12
11:39:40.586 [nacos-grpc-client-executor-1722] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Receive server push request, request = NotifySubscriberRequest, requestId = 16
11:39:40.605 [nacos-grpc-client-executor-1722] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Ack server push request, request = NotifySubscriberRequest, requestId = 16
11:40:12.019 [nacos-grpc-client-executor-1729] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Receive server push request, request = NotifySubscriberRequest, requestId = 20
11:40:12.035 [nacos-grpc-client-executor-1729] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Ack server push request, request = NotifySubscriberRequest, requestId = 20
11:43:44.346 [nacos-grpc-client-executor-1772] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Receive server push request, request = NotifySubscriberRequest, requestId = 22
11:43:44.363 [nacos-grpc-client-executor-1772] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Ack server push request, request = NotifySubscriberRequest, requestId = 22
11:44:14.007 [nacos-grpc-client-executor-1778] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Receive server push request, request = NotifySubscriberRequest, requestId = 25
11:44:14.153 [nacos-grpc-client-executor-1778] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Ack server push request, request = NotifySubscriberRequest, requestId = 25
12:20:54.083 [nacos-grpc-client-executor-2217] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Receive server push request, request = NotifySubscriberRequest, requestId = 27
12:20:54.096 [nacos-grpc-client-executor-2217] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Ack server push request, request = NotifySubscriberRequest, requestId = 27
12:21:19.832 [nacos-grpc-client-executor-2222] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Receive server push request, request = NotifySubscriberRequest, requestId = 30
12:21:19.854 [nacos-grpc-client-executor-2222] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Ack server push request, request = NotifySubscriberRequest, requestId = 30
12:28:28.278 [nacos-grpc-client-executor-2309] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Receive server push request, request = NotifySubscriberRequest, requestId = 32
12:28:28.299 [nacos-grpc-client-executor-2309] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Ack server push request, request = NotifySubscriberRequest, requestId = 32
12:29:01.335 [nacos-grpc-client-executor-2318] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Receive server push request, request = NotifySubscriberRequest, requestId = 35
12:29:01.356 [nacos-grpc-client-executor-2318] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Ack server push request, request = NotifySubscriberRequest, requestId = 35
12:38:09.846 [nacos-grpc-client-executor-2428] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Receive server push request, request = NotifySubscriberRequest, requestId = 37
12:38:09.871 [nacos-grpc-client-executor-2428] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Ack server push request, request = NotifySubscriberRequest, requestId = 37
12:38:36.960 [nacos-grpc-client-executor-2434] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Receive server push request, request = NotifySubscriberRequest, requestId = 40
12:38:36.986 [nacos-grpc-client-executor-2434] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Ack server push request, request = NotifySubscriberRequest, requestId = 40
15:07:00.722 [nacos-grpc-client-executor-4217] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Receive server push request, request = NotifySubscriberRequest, requestId = 42
15:07:00.729 [nacos-grpc-client-executor-4217] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Ack server push request, request = NotifySubscriberRequest, requestId = 42
15:07:39.145 [nacos-grpc-client-executor-4226] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Receive server push request, request = NotifySubscriberRequest, requestId = 45
15:07:39.159 [nacos-grpc-client-executor-4226] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Ack server push request, request = NotifySubscriberRequest, requestId = 45
16:45:32.077 [nacos-grpc-client-executor-5399] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Receive server push request, request = NotifySubscriberRequest, requestId = 47
16:45:32.089 [nacos-grpc-client-executor-5399] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Ack server push request, request = NotifySubscriberRequest, requestId = 47
16:45:59.039 [nacos-grpc-client-executor-5406] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Receive server push request, request = NotifySubscriberRequest, requestId = 50
16:45:59.050 [nacos-grpc-client-executor-5406] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Ack server push request, request = NotifySubscriberRequest, requestId = 50
16:51:34.617 [nacos-grpc-client-executor-5479] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Receive server push request, request = NotifySubscriberRequest, requestId = 52
16:51:34.626 [nacos-grpc-client-executor-5479] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Ack server push request, request = NotifySubscriberRequest, requestId = 52
16:51:55.050 [nacos-grpc-client-executor-5484] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Receive server push request, request = NotifySubscriberRequest, requestId = 55
16:51:55.067 [nacos-grpc-client-executor-5484] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Ack server push request, request = NotifySubscriberRequest, requestId = 55
20:00:50.749 [nacos-grpc-client-executor-7917] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Receive server push request, request = NotifySubscriberRequest, requestId = 57
20:00:50.767 [nacos-grpc-client-executor-7917] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Ack server push request, request = NotifySubscriberRequest, requestId = 57
20:01:37.892 [nacos-grpc-client-executor-7926] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Receive server push request, request = NotifySubscriberRequest, requestId = 60
20:01:37.901 [nacos-grpc-client-executor-7926] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [357bcdc8-a47e-47a8-b647-07ccce8d3e8e] Ack server push request, request = NotifySubscriberRequest, requestId = 60
20:30:12.952 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:30:12.952 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:30:13.339 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:30:13.339 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@161a201c[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:30:13.339 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755134274031_127.0.0.1_14684
20:30:13.341 [nacos-grpc-client-executor-8288] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755134274031_127.0.0.1_14684]Ignore complete event,isRunning:false,isAbandon=false
20:30:13.349 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3caaab83[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 8289]
