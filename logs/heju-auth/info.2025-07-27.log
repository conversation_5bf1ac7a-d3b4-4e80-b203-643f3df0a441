14:21:33.847 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:21:35.190 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7bcca538-2686-45d9-bb0f-a4002365a436_config-0
14:21:35.322 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 75 ms to scan 1 urls, producing 3 keys and 6 values 
14:21:35.390 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 25 ms to scan 1 urls, producing 4 keys and 9 values 
14:21:35.413 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 3 keys and 10 values 
14:21:35.438 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 19 ms to scan 1 urls, producing 1 keys and 5 values 
14:21:35.463 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 19 ms to scan 1 urls, producing 1 keys and 7 values 
14:21:35.496 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 2 keys and 8 values 
14:21:35.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bcca538-2686-45d9-bb0f-a4002365a436_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:21:35.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bcca538-2686-45d9-bb0f-a4002365a436_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x00000276213b44e8
14:21:35.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bcca538-2686-45d9-bb0f-a4002365a436_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000276213b4708
14:21:35.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bcca538-2686-45d9-bb0f-a4002365a436_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:21:35.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bcca538-2686-45d9-bb0f-a4002365a436_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:21:35.528 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bcca538-2686-45d9-bb0f-a4002365a436_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:21:37.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bcca538-2686-45d9-bb0f-a4002365a436_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753597297006_127.0.0.1_4531
14:21:37.328 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bcca538-2686-45d9-bb0f-a4002365a436_config-0] Notify connected event to listeners.
14:21:37.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bcca538-2686-45d9-bb0f-a4002365a436_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:21:37.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bcca538-2686-45d9-bb0f-a4002365a436_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000276214ec720
14:21:37.598 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:21:42.143 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
14:21:42.145 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:21:42.145 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:21:42.394 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:21:44.387 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:21:45.834 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4503dcc2-9e8e-4dda-b8bd-a3d821ba0236
14:21:45.834 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] RpcClient init label, labels = {module=naming, source=sdk}
14:21:45.835 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:21:45.835 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:21:45.837 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:21:45.838 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:21:45.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Success to connect to server [localhost:8848] on start up, connectionId = 1753597305851_127.0.0.1_4556
14:21:45.968 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Notify connected event to listeners.
14:21:45.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:21:45.968 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000276214ec720
14:21:46.022 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
14:21:46.060 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
14:21:46.226 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 14.022 seconds (JVM running for 18.892)
14:21:46.239 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
14:21:46.239 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
14:21:46.239 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
14:21:46.498 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Receive server push request, request = NotifySubscriberRequest, requestId = 2
14:21:46.530 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Ack server push request, request = NotifySubscriberRequest, requestId = 2
14:36:35.328 [http-nio-9200-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:36:52.831 [nacos-grpc-client-executor-195] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Receive server push request, request = NotifySubscriberRequest, requestId = 10
14:36:52.833 [nacos-grpc-client-executor-195] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Ack server push request, request = NotifySubscriberRequest, requestId = 10
14:36:55.013 [nacos-grpc-client-executor-198] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Receive server push request, request = NotifySubscriberRequest, requestId = 11
14:36:55.013 [nacos-grpc-client-executor-198] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Ack server push request, request = NotifySubscriberRequest, requestId = 11
16:28:35.097 [nacos-grpc-client-executor-1596] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Receive server push request, request = NotifySubscriberRequest, requestId = 14
16:28:35.120 [nacos-grpc-client-executor-1596] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Ack server push request, request = NotifySubscriberRequest, requestId = 14
16:28:37.745 [nacos-grpc-client-executor-1597] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Receive server push request, request = NotifySubscriberRequest, requestId = 17
16:28:37.761 [nacos-grpc-client-executor-1597] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Ack server push request, request = NotifySubscriberRequest, requestId = 17
17:03:24.817 [nacos-grpc-client-executor-2033] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Receive server push request, request = NotifySubscriberRequest, requestId = 22
17:03:24.839 [nacos-grpc-client-executor-2033] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Ack server push request, request = NotifySubscriberRequest, requestId = 22
17:03:42.124 [nacos-grpc-client-executor-2037] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Receive server push request, request = NotifySubscriberRequest, requestId = 25
17:03:42.140 [nacos-grpc-client-executor-2037] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Ack server push request, request = NotifySubscriberRequest, requestId = 25
17:10:15.819 [nacos-grpc-client-executor-2119] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Receive server push request, request = NotifySubscriberRequest, requestId = 29
17:10:15.835 [nacos-grpc-client-executor-2119] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Ack server push request, request = NotifySubscriberRequest, requestId = 29
17:10:31.560 [nacos-grpc-client-executor-2122] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Receive server push request, request = NotifySubscriberRequest, requestId = 33
17:10:31.574 [nacos-grpc-client-executor-2122] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Ack server push request, request = NotifySubscriberRequest, requestId = 33
17:12:07.232 [nacos-grpc-client-executor-2142] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Receive server push request, request = NotifySubscriberRequest, requestId = 36
17:12:07.247 [nacos-grpc-client-executor-2142] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Ack server push request, request = NotifySubscriberRequest, requestId = 36
17:12:27.358 [nacos-grpc-client-executor-2146] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Receive server push request, request = NotifySubscriberRequest, requestId = 39
17:12:27.375 [nacos-grpc-client-executor-2146] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Ack server push request, request = NotifySubscriberRequest, requestId = 39
17:13:31.380 [nacos-grpc-client-executor-2159] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Receive server push request, request = NotifySubscriberRequest, requestId = 43
17:13:31.399 [nacos-grpc-client-executor-2159] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Ack server push request, request = NotifySubscriberRequest, requestId = 43
17:13:48.719 [nacos-grpc-client-executor-2163] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Receive server push request, request = NotifySubscriberRequest, requestId = 46
17:13:48.734 [nacos-grpc-client-executor-2163] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Ack server push request, request = NotifySubscriberRequest, requestId = 46
17:15:52.508 [nacos-grpc-client-executor-2190] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Receive server push request, request = NotifySubscriberRequest, requestId = 50
17:15:52.530 [nacos-grpc-client-executor-2190] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Ack server push request, request = NotifySubscriberRequest, requestId = 50
17:16:10.188 [nacos-grpc-client-executor-2194] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Receive server push request, request = NotifySubscriberRequest, requestId = 53
17:16:10.202 [nacos-grpc-client-executor-2194] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Ack server push request, request = NotifySubscriberRequest, requestId = 53
19:48:19.631 [nacos-grpc-client-executor-4017] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Receive server push request, request = NotifySubscriberRequest, requestId = 57
19:48:19.651 [nacos-grpc-client-executor-4017] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Ack server push request, request = NotifySubscriberRequest, requestId = 57
19:48:38.287 [nacos-grpc-client-executor-4021] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Receive server push request, request = NotifySubscriberRequest, requestId = 60
19:48:38.301 [nacos-grpc-client-executor-4021] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Ack server push request, request = NotifySubscriberRequest, requestId = 60
19:51:57.651 [nacos-grpc-client-executor-4062] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Receive server push request, request = NotifySubscriberRequest, requestId = 64
19:51:57.668 [nacos-grpc-client-executor-4062] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Ack server push request, request = NotifySubscriberRequest, requestId = 64
19:52:16.979 [nacos-grpc-client-executor-4066] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Receive server push request, request = NotifySubscriberRequest, requestId = 67
19:52:16.995 [nacos-grpc-client-executor-4066] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Ack server push request, request = NotifySubscriberRequest, requestId = 67
19:53:46.137 [nacos-grpc-client-executor-4083] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Receive server push request, request = NotifySubscriberRequest, requestId = 71
19:53:46.153 [nacos-grpc-client-executor-4083] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Ack server push request, request = NotifySubscriberRequest, requestId = 71
19:54:02.906 [nacos-grpc-client-executor-4087] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Receive server push request, request = NotifySubscriberRequest, requestId = 74
19:54:02.921 [nacos-grpc-client-executor-4087] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Ack server push request, request = NotifySubscriberRequest, requestId = 74
19:54:27.590 [nacos-grpc-client-executor-4092] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Receive server push request, request = NotifySubscriberRequest, requestId = 78
19:54:27.612 [nacos-grpc-client-executor-4092] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Ack server push request, request = NotifySubscriberRequest, requestId = 78
19:54:47.667 [nacos-grpc-client-executor-4096] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Receive server push request, request = NotifySubscriberRequest, requestId = 82
19:54:47.683 [nacos-grpc-client-executor-4096] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4503dcc2-9e8e-4dda-b8bd-a3d821ba0236] Ack server push request, request = NotifySubscriberRequest, requestId = 82
19:59:06.193 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:59:06.196 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:59:06.536 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:59:06.536 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@50e8d9c7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:59:06.536 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753597305851_127.0.0.1_4556
19:59:06.538 [nacos-grpc-client-executor-4150] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753597305851_127.0.0.1_4556]Ignore complete event,isRunning:false,isAbandon=false
19:59:06.542 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6d0a6c3d[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 4151]
