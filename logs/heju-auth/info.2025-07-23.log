09:39:27.003 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:39:27.944 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 186efb5f-bbaf-44d6-953f-4a23a1490236_config-0
09:39:28.010 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 37 ms to scan 1 urls, producing 3 keys and 6 values 
09:39:28.045 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 4 keys and 9 values 
09:39:28.057 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:39:28.071 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 9 ms to scan 1 urls, producing 1 keys and 5 values 
09:39:28.082 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:39:28.095 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
09:39:28.100 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [186efb5f-bbaf-44d6-953f-4a23a1490236_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:39:28.100 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [186efb5f-bbaf-44d6-953f-4a23a1490236_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001ca013b42b8
09:39:28.101 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [186efb5f-bbaf-44d6-953f-4a23a1490236_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001ca013b44d8
09:39:28.101 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [186efb5f-bbaf-44d6-953f-4a23a1490236_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:39:28.102 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [186efb5f-bbaf-44d6-953f-4a23a1490236_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:39:28.112 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [186efb5f-bbaf-44d6-953f-4a23a1490236_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:39:29.245 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [186efb5f-bbaf-44d6-953f-4a23a1490236_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1753234769015_127.0.0.1_4018
09:39:29.246 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [186efb5f-bbaf-44d6-953f-4a23a1490236_config-0] Notify connected event to listeners.
09:39:29.246 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [186efb5f-bbaf-44d6-953f-4a23a1490236_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:39:29.247 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [186efb5f-bbaf-44d6-953f-4a23a1490236_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001ca014ec200
09:39:29.398 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:39:31.780 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:39:31.780 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:39:31.781 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:39:31.918 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:39:33.530 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:39:34.751 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9f163a3f-12f4-4a9f-94e1-1ff8195aac8e
09:39:34.751 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] RpcClient init label, labels = {module=naming, source=sdk}
09:39:34.753 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:39:34.753 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:39:34.753 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:39:34.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:39:34.881 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Success to connect to server [localhost:8848] on start up, connectionId = 1753234774762_127.0.0.1_4072
09:39:34.881 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Notify connected event to listeners.
09:39:34.881 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:39:34.882 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001ca014ec200
09:39:34.935 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:39:34.966 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
09:39:35.087 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 8.825 seconds (JVM running for 11.408)
09:39:35.098 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:39:35.098 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:39:35.102 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:39:35.482 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:39:35.503 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:51:59.650 [http-nio-9200-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:52:07.705 [nacos-grpc-client-executor-166] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Receive server push request, request = NotifySubscriberRequest, requestId = 19
09:52:07.707 [nacos-grpc-client-executor-166] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Ack server push request, request = NotifySubscriberRequest, requestId = 19
09:52:11.423 [nacos-grpc-client-executor-169] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Receive server push request, request = NotifySubscriberRequest, requestId = 20
09:52:11.425 [nacos-grpc-client-executor-169] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Ack server push request, request = NotifySubscriberRequest, requestId = 20
11:23:27.533 [nacos-grpc-client-executor-1293] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Receive server push request, request = NotifySubscriberRequest, requestId = 23
11:23:27.553 [nacos-grpc-client-executor-1293] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Ack server push request, request = NotifySubscriberRequest, requestId = 23
11:23:52.631 [nacos-grpc-client-executor-1298] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Receive server push request, request = NotifySubscriberRequest, requestId = 26
11:23:52.652 [nacos-grpc-client-executor-1298] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Ack server push request, request = NotifySubscriberRequest, requestId = 26
11:42:48.383 [nacos-grpc-client-executor-1531] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Receive server push request, request = NotifySubscriberRequest, requestId = 30
11:42:48.402 [nacos-grpc-client-executor-1531] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Ack server push request, request = NotifySubscriberRequest, requestId = 30
11:43:12.005 [nacos-grpc-client-executor-1536] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Receive server push request, request = NotifySubscriberRequest, requestId = 33
11:43:12.022 [nacos-grpc-client-executor-1536] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Ack server push request, request = NotifySubscriberRequest, requestId = 33
19:29:13.348 [nacos-grpc-client-executor-7240] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Receive server push request, request = NotifySubscriberRequest, requestId = 37
19:29:13.364 [nacos-grpc-client-executor-7240] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Ack server push request, request = NotifySubscriberRequest, requestId = 37
19:30:01.419 [nacos-grpc-client-executor-7250] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Receive server push request, request = NotifySubscriberRequest, requestId = 40
19:30:01.434 [nacos-grpc-client-executor-7250] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Ack server push request, request = NotifySubscriberRequest, requestId = 40
19:36:48.863 [nacos-grpc-client-executor-7331] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Receive server push request, request = NotifySubscriberRequest, requestId = 45
19:36:48.885 [nacos-grpc-client-executor-7331] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Ack server push request, request = NotifySubscriberRequest, requestId = 45
19:37:12.009 [nacos-grpc-client-executor-7336] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Receive server push request, request = NotifySubscriberRequest, requestId = 49
19:37:12.021 [nacos-grpc-client-executor-7336] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Ack server push request, request = NotifySubscriberRequest, requestId = 49
19:40:33.244 [nacos-grpc-client-executor-7376] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Receive server push request, request = NotifySubscriberRequest, requestId = 52
19:40:33.260 [nacos-grpc-client-executor-7376] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Ack server push request, request = NotifySubscriberRequest, requestId = 52
19:40:49.389 [nacos-grpc-client-executor-7379] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Receive server push request, request = NotifySubscriberRequest, requestId = 55
19:40:49.404 [nacos-grpc-client-executor-7379] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9f163a3f-12f4-4a9f-94e1-1ff8195aac8e] Ack server push request, request = NotifySubscriberRequest, requestId = 55
20:22:57.264 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:22:57.268 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:22:57.603 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:22:57.603 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5948ea6e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:22:57.603 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1753234774762_127.0.0.1_4072
20:22:57.604 [nacos-grpc-client-executor-7886] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1753234774762_127.0.0.1_4072]Ignore complete event,isRunning:false,isAbandon=false
20:22:57.609 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7f93cfdc[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 7887]
