09:06:48.934 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:06:50.084 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4aa18950-439f-496d-a9b8-6b9c8f48d6ce_config-0
09:06:50.178 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 49 ms to scan 1 urls, producing 3 keys and 6 values 
09:06:50.217 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 4 keys and 9 values 
09:06:50.232 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 3 keys and 10 values 
09:06:50.251 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 1 keys and 5 values 
09:06:50.271 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 1 keys and 7 values 
09:06:50.287 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
09:06:50.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aa18950-439f-496d-a9b8-6b9c8f48d6ce_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:06:50.294 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aa18950-439f-496d-a9b8-6b9c8f48d6ce_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001818139a328
09:06:50.295 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aa18950-439f-496d-a9b8-6b9c8f48d6ce_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001818139a548
09:06:50.296 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aa18950-439f-496d-a9b8-6b9c8f48d6ce_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:06:50.297 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aa18950-439f-496d-a9b8-6b9c8f48d6ce_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:06:50.319 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aa18950-439f-496d-a9b8-6b9c8f48d6ce_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:06:51.911 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aa18950-439f-496d-a9b8-6b9c8f48d6ce_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752800811619_127.0.0.1_6288
09:06:51.913 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aa18950-439f-496d-a9b8-6b9c8f48d6ce_config-0] Notify connected event to listeners.
09:06:51.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aa18950-439f-496d-a9b8-6b9c8f48d6ce_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:06:51.915 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4aa18950-439f-496d-a9b8-6b9c8f48d6ce_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000018181513db0
09:06:52.272 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:06:57.165 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:06:57.166 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:06:57.166 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:06:57.430 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:07:00.274 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:07:06.470 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 02acbcde-fd52-47ed-9c2f-961b3a0866db
09:07:06.472 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] RpcClient init label, labels = {module=naming, source=sdk}
09:07:06.475 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:07:06.475 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:07:06.476 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:07:06.477 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:07:06.658 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Success to connect to server [localhost:8848] on start up, connectionId = 1752800826505_127.0.0.1_6413
09:07:06.659 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Notify connected event to listeners.
09:07:06.659 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:07:06.659 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000018181513db0
09:07:06.848 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:07:06.920 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
09:07:07.343 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:07:07.365 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:07:07.463 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 19.571 seconds (JVM running for 21.142)
09:07:07.487 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:07:07.488 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:07:07.494 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:07:08.110 [RMI TCP Connection(12)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:09:16.032 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Receive server push request, request = NotifySubscriberRequest, requestId = 11
09:09:16.033 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Ack server push request, request = NotifySubscriberRequest, requestId = 11
09:09:19.961 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:09:19.965 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Ack server push request, request = NotifySubscriberRequest, requestId = 12
09:14:31.960 [nacos-grpc-client-executor-104] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Receive server push request, request = NotifySubscriberRequest, requestId = 16
09:14:31.976 [nacos-grpc-client-executor-104] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Ack server push request, request = NotifySubscriberRequest, requestId = 16
09:15:24.909 [nacos-grpc-client-executor-116] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Receive server push request, request = NotifySubscriberRequest, requestId = 20
09:15:24.924 [nacos-grpc-client-executor-116] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Ack server push request, request = NotifySubscriberRequest, requestId = 20
10:04:03.254 [nacos-grpc-client-executor-697] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Receive server push request, request = NotifySubscriberRequest, requestId = 23
10:04:03.267 [nacos-grpc-client-executor-697] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Ack server push request, request = NotifySubscriberRequest, requestId = 23
10:04:32.835 [nacos-grpc-client-executor-703] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Receive server push request, request = NotifySubscriberRequest, requestId = 27
10:04:32.848 [nacos-grpc-client-executor-703] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Ack server push request, request = NotifySubscriberRequest, requestId = 27
14:40:27.506 [nacos-grpc-client-executor-4008] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Receive server push request, request = NotifySubscriberRequest, requestId = 32
14:40:27.519 [nacos-grpc-client-executor-4008] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Ack server push request, request = NotifySubscriberRequest, requestId = 32
14:40:52.417 [nacos-grpc-client-executor-4014] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Receive server push request, request = NotifySubscriberRequest, requestId = 37
14:40:52.440 [nacos-grpc-client-executor-4014] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Ack server push request, request = NotifySubscriberRequest, requestId = 37
17:45:10.394 [nacos-grpc-client-executor-6224] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Receive server push request, request = NotifySubscriberRequest, requestId = 41
17:45:10.394 [nacos-grpc-client-executor-6224] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Ack server push request, request = NotifySubscriberRequest, requestId = 41
17:45:52.376 [nacos-grpc-client-executor-6232] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Receive server push request, request = NotifySubscriberRequest, requestId = 46
17:45:52.390 [nacos-grpc-client-executor-6232] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Ack server push request, request = NotifySubscriberRequest, requestId = 46
18:03:04.794 [nacos-grpc-client-executor-6438] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Receive server push request, request = NotifySubscriberRequest, requestId = 50
18:03:04.812 [nacos-grpc-client-executor-6438] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Ack server push request, request = NotifySubscriberRequest, requestId = 50
18:03:21.444 [nacos-grpc-client-executor-6443] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Receive server push request, request = NotifySubscriberRequest, requestId = 55
18:03:21.459 [nacos-grpc-client-executor-6443] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Ack server push request, request = NotifySubscriberRequest, requestId = 55
18:05:31.062 [nacos-grpc-client-executor-6472] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Receive server push request, request = NotifySubscriberRequest, requestId = 59
18:05:31.067 [nacos-grpc-client-executor-6472] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Ack server push request, request = NotifySubscriberRequest, requestId = 59
18:05:47.868 [nacos-grpc-client-executor-6475] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Receive server push request, request = NotifySubscriberRequest, requestId = 64
18:05:47.884 [nacos-grpc-client-executor-6475] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Ack server push request, request = NotifySubscriberRequest, requestId = 64
18:13:42.279 [nacos-grpc-client-executor-6577] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Receive server push request, request = NotifySubscriberRequest, requestId = 69
18:13:42.287 [nacos-grpc-client-executor-6577] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Ack server push request, request = NotifySubscriberRequest, requestId = 69
18:13:58.560 [nacos-grpc-client-executor-6580] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Receive server push request, request = NotifySubscriberRequest, requestId = 74
18:13:58.567 [nacos-grpc-client-executor-6580] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Ack server push request, request = NotifySubscriberRequest, requestId = 74
18:18:32.559 [nacos-grpc-client-executor-6639] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Receive server push request, request = NotifySubscriberRequest, requestId = 80
18:18:32.577 [nacos-grpc-client-executor-6639] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Ack server push request, request = NotifySubscriberRequest, requestId = 80
18:18:35.964 [nacos-grpc-client-executor-6640] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Receive server push request, request = NotifySubscriberRequest, requestId = 87
18:18:35.974 [nacos-grpc-client-executor-6640] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Ack server push request, request = NotifySubscriberRequest, requestId = 87
18:21:37.910 [nacos-grpc-client-executor-6679] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Receive server push request, request = NotifySubscriberRequest, requestId = 91
18:21:37.926 [nacos-grpc-client-executor-6679] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Ack server push request, request = NotifySubscriberRequest, requestId = 91
18:21:54.154 [nacos-grpc-client-executor-6682] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Receive server push request, request = NotifySubscriberRequest, requestId = 95
18:21:54.164 [nacos-grpc-client-executor-6682] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [02acbcde-fd52-47ed-9c2f-961b3a0866db] Ack server push request, request = NotifySubscriberRequest, requestId = 95
18:47:19.474 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:47:19.477 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:47:19.818 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:47:19.819 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2611a75e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:47:19.820 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752800826505_127.0.0.1_6413
18:47:19.823 [nacos-grpc-client-executor-7012] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752800826505_127.0.0.1_6413]Ignore complete event,isRunning:false,isAbandon=false
18:47:19.834 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2fc15912[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 7013]
