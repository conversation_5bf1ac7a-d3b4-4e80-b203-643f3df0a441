09:34:30.681 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:34:31.360 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6f72f9d8-3faa-46af-8a61-109b19d332e7_config-0
09:34:31.451 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 38 ms to scan 1 urls, producing 3 keys and 6 values 
09:34:31.506 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 21 ms to scan 1 urls, producing 4 keys and 9 values 
09:34:31.518 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:34:31.529 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:34:31.545 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 11 ms to scan 1 urls, producing 1 keys and 7 values 
09:34:31.557 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
09:34:31.561 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f72f9d8-3faa-46af-8a61-109b19d332e7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:34:31.561 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f72f9d8-3faa-46af-8a61-109b19d332e7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001894b397d80
09:34:31.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f72f9d8-3faa-46af-8a61-109b19d332e7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001894b398000
09:34:31.562 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f72f9d8-3faa-46af-8a61-109b19d332e7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:34:31.564 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f72f9d8-3faa-46af-8a61-109b19d332e7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:34:31.575 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f72f9d8-3faa-46af-8a61-109b19d332e7_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:34:32.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f72f9d8-3faa-46af-8a61-109b19d332e7_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756690472549_127.0.0.1_10468
09:34:32.846 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f72f9d8-3faa-46af-8a61-109b19d332e7_config-0] Notify connected event to listeners.
09:34:32.847 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f72f9d8-3faa-46af-8a61-109b19d332e7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:34:32.848 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f72f9d8-3faa-46af-8a61-109b19d332e7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001894b510228
09:34:33.092 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:34:35.775 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:34:35.777 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:34:35.777 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:34:35.939 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:34:37.693 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:34:38.846 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fe490975-da1d-4b26-a900-7fe5b4dace00
09:34:38.846 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] RpcClient init label, labels = {module=naming, source=sdk}
09:34:38.850 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:34:38.850 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:34:38.851 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:34:38.851 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:34:38.980 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] Success to connect to server [localhost:8848] on start up, connectionId = 1756690478862_127.0.0.1_10479
09:34:38.980 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:34:38.980 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] Notify connected event to listeners.
09:34:38.980 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001894b510228
09:34:39.053 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:34:39.089 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
09:34:39.202 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 9.212 seconds (JVM running for 18.468)
09:34:39.212 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:34:39.213 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:34:39.215 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:34:39.730 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:34:39.750 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:37:25.786 [http-nio-9200-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:37:32.869 [nacos-grpc-client-executor-48] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:37:32.871 [nacos-grpc-client-executor-48] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:37:38.071 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:37:38.075 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] Ack server push request, request = NotifySubscriberRequest, requestId = 10
17:49:17.603 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f72f9d8-3faa-46af-8a61-109b19d332e7_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:49:17.603 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] Try to reconnect to a new server, server is  not appointed, will choose a random server.
17:49:17.762 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
17:49:17.794 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f72f9d8-3faa-46af-8a61-109b19d332e7_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
17:49:17.987 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
17:49:18.016 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f72f9d8-3faa-46af-8a61-109b19d332e7_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
17:49:18.302 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
17:49:18.349 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f72f9d8-3faa-46af-8a61-109b19d332e7_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
17:49:18.761 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
17:49:18.777 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f72f9d8-3faa-46af-8a61-109b19d332e7_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
17:49:19.268 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
17:49:19.288 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f72f9d8-3faa-46af-8a61-109b19d332e7_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
17:49:19.892 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
17:49:19.919 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f72f9d8-3faa-46af-8a61-109b19d332e7_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
17:49:20.597 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
17:49:20.646 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f72f9d8-3faa-46af-8a61-109b19d332e7_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
17:49:21.424 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
17:49:21.472 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f72f9d8-3faa-46af-8a61-109b19d332e7_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
17:49:22.131 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
17:49:22.337 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
17:49:22.384 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6f72f9d8-3faa-46af-8a61-109b19d332e7_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
17:49:22.480 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
17:49:22.815 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
17:49:22.815 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@78362fa2[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
17:49:22.815 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756690478862_127.0.0.1_10479
17:49:22.815 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe490975-da1d-4b26-a900-7fe5b4dace00] Client is shutdown, stop reconnect to server
17:49:22.815 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6f02d980[Running, pool size = 22, active threads = 0, queued tasks = 0, completed tasks = 6275]
