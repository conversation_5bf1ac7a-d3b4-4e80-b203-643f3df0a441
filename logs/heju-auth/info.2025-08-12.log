09:32:59.997 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:33:00.779 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of aa69124c-9368-4952-ae54-d41ed1eff131_config-0
09:33:00.858 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 36 ms to scan 1 urls, producing 3 keys and 6 values 
09:33:00.886 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 4 ms to scan 1 urls, producing 4 keys and 9 values 
09:33:00.904 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 3 keys and 10 values 
09:33:00.913 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 5 values 
09:33:00.928 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 1 keys and 7 values 
09:33:00.935 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
09:33:00.945 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa69124c-9368-4952-ae54-d41ed1eff131_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:33:00.945 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa69124c-9368-4952-ae54-d41ed1eff131_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000021001398200
09:33:00.945 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa69124c-9368-4952-ae54-d41ed1eff131_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000021001398420
09:33:00.945 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa69124c-9368-4952-ae54-d41ed1eff131_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:33:00.945 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa69124c-9368-4952-ae54-d41ed1eff131_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:33:00.957 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa69124c-9368-4952-ae54-d41ed1eff131_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:33:02.277 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa69124c-9368-4952-ae54-d41ed1eff131_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754962382031_127.0.0.1_12688
09:33:02.278 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa69124c-9368-4952-ae54-d41ed1eff131_config-0] Notify connected event to listeners.
09:33:02.278 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa69124c-9368-4952-ae54-d41ed1eff131_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:33:02.278 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aa69124c-9368-4952-ae54-d41ed1eff131_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000021001510228
09:33:02.431 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:33:07.389 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:33:07.390 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:33:07.390 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:33:07.735 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:33:11.379 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:33:18.372 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e81f69e3-ebcb-407f-8def-02d855daba4b
09:33:18.374 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] RpcClient init label, labels = {module=naming, source=sdk}
09:33:18.379 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:33:18.381 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:33:18.382 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:33:18.382 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:33:19.766 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Success to connect to server [localhost:8848] on start up, connectionId = 1754962398825_127.0.0.1_12725
09:33:19.766 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:33:19.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000021001510228
09:33:19.913 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Notify connected event to listeners.
09:33:20.497 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:33:20.709 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ***********:9200 register finished
09:33:21.450 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 22.337 seconds (JVM running for 31.8)
09:33:21.486 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:33:21.488 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:33:21.500 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:33:21.756 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:33:21.760 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:36:51.810 [http-nio-9200-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:37:01.695 [nacos-grpc-client-executor-54] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Receive server push request, request = NotifySubscriberRequest, requestId = 11
09:37:01.695 [nacos-grpc-client-executor-54] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Ack server push request, request = NotifySubscriberRequest, requestId = 11
09:37:04.646 [nacos-grpc-client-executor-57] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:37:04.654 [nacos-grpc-client-executor-57] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Ack server push request, request = NotifySubscriberRequest, requestId = 12
12:07:49.487 [nacos-grpc-client-executor-1929] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Receive server push request, request = NotifySubscriberRequest, requestId = 14
12:07:49.577 [nacos-grpc-client-executor-1929] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Ack server push request, request = NotifySubscriberRequest, requestId = 14
12:08:35.902 [nacos-grpc-client-executor-1940] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Receive server push request, request = NotifySubscriberRequest, requestId = 16
12:08:35.912 [nacos-grpc-client-executor-1940] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Ack server push request, request = NotifySubscriberRequest, requestId = 16
13:28:51.437 [nacos-grpc-client-executor-2901] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Receive server push request, request = NotifySubscriberRequest, requestId = 19
13:28:51.453 [nacos-grpc-client-executor-2901] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Ack server push request, request = NotifySubscriberRequest, requestId = 19
13:29:20.721 [nacos-grpc-client-executor-2907] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Receive server push request, request = NotifySubscriberRequest, requestId = 22
13:29:20.733 [nacos-grpc-client-executor-2907] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Ack server push request, request = NotifySubscriberRequest, requestId = 22
13:54:08.729 [nacos-grpc-client-executor-3205] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Receive server push request, request = NotifySubscriberRequest, requestId = 24
13:54:08.741 [nacos-grpc-client-executor-3205] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Ack server push request, request = NotifySubscriberRequest, requestId = 24
13:54:40.184 [nacos-grpc-client-executor-3211] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Receive server push request, request = NotifySubscriberRequest, requestId = 26
13:54:40.204 [nacos-grpc-client-executor-3211] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Ack server push request, request = NotifySubscriberRequest, requestId = 26
14:46:59.717 [nacos-grpc-client-executor-3841] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Receive server push request, request = NotifySubscriberRequest, requestId = 29
14:46:59.753 [nacos-grpc-client-executor-3841] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Ack server push request, request = NotifySubscriberRequest, requestId = 29
14:47:36.291 [nacos-grpc-client-executor-3849] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Receive server push request, request = NotifySubscriberRequest, requestId = 32
14:47:36.309 [nacos-grpc-client-executor-3849] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Ack server push request, request = NotifySubscriberRequest, requestId = 32
14:52:11.411 [nacos-grpc-client-executor-3905] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Receive server push request, request = NotifySubscriberRequest, requestId = 34
14:52:11.427 [nacos-grpc-client-executor-3905] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Ack server push request, request = NotifySubscriberRequest, requestId = 34
14:52:38.614 [nacos-grpc-client-executor-3910] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Receive server push request, request = NotifySubscriberRequest, requestId = 37
14:52:38.641 [nacos-grpc-client-executor-3910] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e81f69e3-ebcb-407f-8def-02d855daba4b] Ack server push request, request = NotifySubscriberRequest, requestId = 37
14:52:58.303 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:52:58.307 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:52:58.659 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:52:58.659 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@26d675bf[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:52:58.659 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754962398825_127.0.0.1_12725
14:52:58.663 [nacos-grpc-client-executor-3916] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754962398825_127.0.0.1_12725]Ignore complete event,isRunning:false,isAbandon=false
14:52:58.676 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@40cafb4[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3917]
14:55:32.964 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:55:33.734 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5766b954-2ccb-4e0d-b451-b11dcdfaee81_config-0
14:55:33.809 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 38 ms to scan 1 urls, producing 3 keys and 6 values 
14:55:33.833 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 4 keys and 9 values 
14:55:33.841 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
14:55:33.852 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
14:55:33.865 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
14:55:33.884 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
14:55:33.889 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5766b954-2ccb-4e0d-b451-b11dcdfaee81_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:55:33.890 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5766b954-2ccb-4e0d-b451-b11dcdfaee81_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000020965399e68
14:55:33.890 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5766b954-2ccb-4e0d-b451-b11dcdfaee81_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002096539a088
14:55:33.891 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5766b954-2ccb-4e0d-b451-b11dcdfaee81_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:55:33.893 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5766b954-2ccb-4e0d-b451-b11dcdfaee81_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:55:33.909 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5766b954-2ccb-4e0d-b451-b11dcdfaee81_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:55:35.087 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5766b954-2ccb-4e0d-b451-b11dcdfaee81_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754981734859_127.0.0.1_6499
14:55:35.088 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5766b954-2ccb-4e0d-b451-b11dcdfaee81_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:55:35.088 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5766b954-2ccb-4e0d-b451-b11dcdfaee81_config-0] Notify connected event to listeners.
14:55:35.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5766b954-2ccb-4e0d-b451-b11dcdfaee81_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000209655144f0
14:55:35.221 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:55:39.313 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
14:55:39.314 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:55:39.314 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:55:39.692 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:55:43.643 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:55:46.596 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ef947db1-28f4-43d1-8708-d1b6825c525a
14:55:46.597 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ef947db1-28f4-43d1-8708-d1b6825c525a] RpcClient init label, labels = {module=naming, source=sdk}
14:55:46.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ef947db1-28f4-43d1-8708-d1b6825c525a] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:55:46.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ef947db1-28f4-43d1-8708-d1b6825c525a] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:55:46.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ef947db1-28f4-43d1-8708-d1b6825c525a] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:55:46.604 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ef947db1-28f4-43d1-8708-d1b6825c525a] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:55:46.748 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ef947db1-28f4-43d1-8708-d1b6825c525a] Success to connect to server [localhost:8848] on start up, connectionId = 1754981746618_127.0.0.1_6572
14:55:46.749 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ef947db1-28f4-43d1-8708-d1b6825c525a] Notify connected event to listeners.
14:55:46.748 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ef947db1-28f4-43d1-8708-d1b6825c525a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:55:46.749 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ef947db1-28f4-43d1-8708-d1b6825c525a] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x00000209655144f0
14:55:46.836 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
14:55:46.884 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ***********:9200 register finished
14:55:47.184 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 14.809 seconds (JVM running for 15.817)
14:55:47.205 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
14:55:47.206 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
14:55:47.212 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
14:55:47.368 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ef947db1-28f4-43d1-8708-d1b6825c525a] Receive server push request, request = NotifySubscriberRequest, requestId = 38
14:55:47.396 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ef947db1-28f4-43d1-8708-d1b6825c525a] Ack server push request, request = NotifySubscriberRequest, requestId = 38
14:55:47.533 [RMI TCP Connection(4)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
15:28:06.249 [lettuce-nioEventLoop-4-1] INFO  i.l.c.p.CommandHandler - [log,217] - null Unexpected exception during request: java.net.SocketException: Connection reset
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:259)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
15:28:06.384 [lettuce-eventExecutorLoop-1-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /***********00:6379
15:28:15.478 [lettuce-eventExecutorLoop-1-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
15:28:23.782 [lettuce-eventExecutorLoop-1-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
15:28:40.185 [lettuce-eventExecutorLoop-1-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
15:29:20.279 [lettuce-eventExecutorLoop-1-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
15:30:00.390 [lettuce-eventExecutorLoop-1-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
15:30:40.487 [lettuce-eventExecutorLoop-1-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
15:30:40.518 [lettuce-nioEventLoop-4-19] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to ***********00/<unresolved>:6379
19:00:32.151 [lettuce-nioEventLoop-4-19] INFO  i.l.c.p.CommandHandler - [log,217] - null Unexpected exception during request: java.net.SocketException: Connection reset
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:259)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
19:00:32.178 [lettuce-eventExecutorLoop-1-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /***********00:6379
19:00:43.276 [lettuce-eventExecutorLoop-1-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:00:53.878 [lettuce-eventExecutorLoop-1-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:01:04.979 [lettuce-eventExecutorLoop-1-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:01:17.091 [lettuce-eventExecutorLoop-1-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:01:31.277 [lettuce-eventExecutorLoop-1-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:01:49.576 [lettuce-eventExecutorLoop-1-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:02:15.979 [lettuce-eventExecutorLoop-1-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:02:56.084 [lettuce-eventExecutorLoop-1-22] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:03:36.187 [lettuce-eventExecutorLoop-1-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:04:16.277 [lettuce-eventExecutorLoop-1-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:04:56.391 [lettuce-eventExecutorLoop-1-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:05:36.477 [lettuce-eventExecutorLoop-1-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:06:16.581 [lettuce-eventExecutorLoop-1-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:06:46.677 [lettuce-eventExecutorLoop-1-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:06:46.687 [lettuce-nioEventLoop-4-19] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to ***********00/<unresolved>:6379
19:43:18.165 [lettuce-nioEventLoop-4-19] INFO  i.l.c.p.CommandHandler - [log,217] - null Unexpected exception during request: java.net.SocketException: Connection reset
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:259)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
19:43:18.181 [lettuce-eventExecutorLoop-1-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /***********00:6379
19:43:28.477 [lettuce-eventExecutorLoop-1-17] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:43:38.591 [lettuce-eventExecutorLoop-1-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:43:48.691 [lettuce-eventExecutorLoop-1-19] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:43:58.777 [lettuce-eventExecutorLoop-1-20] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:44:08.990 [lettuce-eventExecutorLoop-1-21] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:44:19.277 [lettuce-eventExecutorLoop-1-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:44:29.877 [lettuce-eventExecutorLoop-1-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:44:40.978 [lettuce-eventExecutorLoop-1-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:44:53.080 [lettuce-eventExecutorLoop-1-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:45:07.278 [lettuce-eventExecutorLoop-1-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:45:25.577 [lettuce-eventExecutorLoop-1-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:45:52.078 [lettuce-eventExecutorLoop-1-17] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:46:32.188 [lettuce-eventExecutorLoop-1-21] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:47:12.288 [lettuce-eventExecutorLoop-1-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:47:52.387 [lettuce-eventExecutorLoop-1-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:48:32.489 [lettuce-eventExecutorLoop-1-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:49:12.590 [lettuce-eventExecutorLoop-1-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:49:52.686 [lettuce-eventExecutorLoop-1-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:50:32.790 [lettuce-eventExecutorLoop-1-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:51:12.877 [lettuce-eventExecutorLoop-1-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:51:52.991 [lettuce-eventExecutorLoop-1-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:52:33.097 [lettuce-eventExecutorLoop-1-17] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:53:13.180 [lettuce-eventExecutorLoop-1-19] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:53:53.278 [lettuce-eventExecutorLoop-1-21] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:54:33.391 [lettuce-eventExecutorLoop-1-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:55:13.479 [lettuce-eventExecutorLoop-1-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:55:53.577 [lettuce-eventExecutorLoop-1-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:56:33.677 [lettuce-eventExecutorLoop-1-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:57:13.787 [lettuce-eventExecutorLoop-1-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:57:53.882 [lettuce-eventExecutorLoop-1-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:58:33.979 [lettuce-eventExecutorLoop-1-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:59:14.082 [lettuce-eventExecutorLoop-1-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
19:59:54.184 [lettuce-eventExecutorLoop-1-17] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:00:34.286 [lettuce-eventExecutorLoop-1-19] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:01:14.388 [lettuce-eventExecutorLoop-1-21] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:01:54.487 [lettuce-eventExecutorLoop-1-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:02:34.591 [lettuce-eventExecutorLoop-1-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:03:14.687 [lettuce-eventExecutorLoop-1-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:03:54.777 [lettuce-eventExecutorLoop-1-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:04:34.877 [lettuce-eventExecutorLoop-1-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:05:14.977 [lettuce-eventExecutorLoop-1-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:05:55.090 [lettuce-eventExecutorLoop-1-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:06:35.177 [lettuce-eventExecutorLoop-1-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:07:15.277 [lettuce-eventExecutorLoop-1-17] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:07:55.389 [lettuce-eventExecutorLoop-1-19] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:08:35.490 [lettuce-eventExecutorLoop-1-21] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:09:15.592 [lettuce-eventExecutorLoop-1-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:09:55.689 [lettuce-eventExecutorLoop-1-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:10:35.788 [lettuce-eventExecutorLoop-1-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:11:15.889 [lettuce-eventExecutorLoop-1-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:11:55.993 [lettuce-eventExecutorLoop-1-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:12:36.078 [lettuce-eventExecutorLoop-1-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:13:16.191 [lettuce-eventExecutorLoop-1-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:13:56.277 [lettuce-eventExecutorLoop-1-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:14:36.389 [lettuce-eventExecutorLoop-1-17] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:15:16.480 [lettuce-eventExecutorLoop-1-19] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:15:56.580 [lettuce-eventExecutorLoop-1-21] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:16:36.678 [lettuce-eventExecutorLoop-1-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:17:16.780 [lettuce-eventExecutorLoop-1-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:17:56.895 [lettuce-eventExecutorLoop-1-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:18:36.991 [lettuce-eventExecutorLoop-1-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:19:17.087 [lettuce-eventExecutorLoop-1-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:19:57.177 [lettuce-eventExecutorLoop-1-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:20:37.285 [lettuce-eventExecutorLoop-1-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:21:17.385 [lettuce-eventExecutorLoop-1-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:21:57.478 [lettuce-eventExecutorLoop-1-17] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:22:37.591 [lettuce-eventExecutorLoop-1-19] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:23:17.680 [lettuce-eventExecutorLoop-1-21] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:23:57.792 [lettuce-eventExecutorLoop-1-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:24:37.887 [lettuce-eventExecutorLoop-1-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:25:17.976 [lettuce-eventExecutorLoop-1-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:25:58.091 [lettuce-eventExecutorLoop-1-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:26:38.177 [lettuce-eventExecutorLoop-1-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:27:18.292 [lettuce-eventExecutorLoop-1-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:27:58.377 [lettuce-eventExecutorLoop-1-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:28:38.477 [lettuce-eventExecutorLoop-1-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:29:18.577 [lettuce-eventExecutorLoop-1-17] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:29:58.687 [lettuce-eventExecutorLoop-1-19] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:30:38.790 [lettuce-eventExecutorLoop-1-21] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:31:18.892 [lettuce-eventExecutorLoop-1-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:31:58.982 [lettuce-eventExecutorLoop-1-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:32:39.086 [lettuce-eventExecutorLoop-1-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:33:19.193 [lettuce-eventExecutorLoop-1-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:33:59.281 [lettuce-eventExecutorLoop-1-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:34:39.390 [lettuce-eventExecutorLoop-1-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was ***********00/<unresolved>:6379
20:35:11.329 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:35:11.334 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:35:11.666 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:35:11.667 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@55324c54[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:35:11.667 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754981746618_127.0.0.1_6572
20:35:11.671 [nacos-grpc-client-executor-4078] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754981746618_127.0.0.1_6572]Ignore complete event,isRunning:false,isAbandon=false
20:35:11.682 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7dafd0d[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 4079]
