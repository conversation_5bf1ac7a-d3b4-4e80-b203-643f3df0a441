09:23:43.815 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:23:44.315 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a74e21cb-9bc6-4fec-9861-2b2da7039665_config-0
09:23:44.379 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 27 ms to scan 1 urls, producing 3 keys and 6 values 
09:23:44.394 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 4 keys and 9 values 
09:23:44.418 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 3 keys and 10 values 
09:23:44.418 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 0 ms to scan 1 urls, producing 1 keys and 5 values 
09:23:44.434 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 1 keys and 7 values 
09:23:44.450 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
09:23:44.450 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a74e21cb-9bc6-4fec-9861-2b2da7039665_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:23:44.450 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a74e21cb-9bc6-4fec-9861-2b2da7039665_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x00000203b83b7d80
09:23:44.450 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a74e21cb-9bc6-4fec-9861-2b2da7039665_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000203b83b8000
09:23:44.450 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a74e21cb-9bc6-4fec-9861-2b2da7039665_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:23:44.450 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a74e21cb-9bc6-4fec-9861-2b2da7039665_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:23:44.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a74e21cb-9bc6-4fec-9861-2b2da7039665_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:23:45.353 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a74e21cb-9bc6-4fec-9861-2b2da7039665_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755825825157_127.0.0.1_7935
09:23:45.353 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a74e21cb-9bc6-4fec-9861-2b2da7039665_config-0] Notify connected event to listeners.
09:23:45.353 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a74e21cb-9bc6-4fec-9861-2b2da7039665_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:23:45.353 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a74e21cb-9bc6-4fec-9861-2b2da7039665_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000203b84f0228
09:23:45.476 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:23:47.680 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:23:47.680 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:23:47.680 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:23:47.836 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:23:49.711 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:23:52.096 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 22751062-21ec-4444-889c-5b3dc1bbf7b1
09:23:52.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22751062-21ec-4444-889c-5b3dc1bbf7b1] RpcClient init label, labels = {module=naming, source=sdk}
09:23:52.111 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22751062-21ec-4444-889c-5b3dc1bbf7b1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:23:52.111 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22751062-21ec-4444-889c-5b3dc1bbf7b1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:23:52.111 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22751062-21ec-4444-889c-5b3dc1bbf7b1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:23:52.113 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22751062-21ec-4444-889c-5b3dc1bbf7b1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:23:52.245 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22751062-21ec-4444-889c-5b3dc1bbf7b1] Success to connect to server [localhost:8848] on start up, connectionId = 1755825832119_127.0.0.1_7940
09:23:52.248 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22751062-21ec-4444-889c-5b3dc1bbf7b1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:23:52.248 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22751062-21ec-4444-889c-5b3dc1bbf7b1] Notify connected event to listeners.
09:23:52.250 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22751062-21ec-4444-889c-5b3dc1bbf7b1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000203b84f0228
09:23:52.346 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:23:52.391 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
09:23:52.606 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 9.399 seconds (JVM running for 13.326)
09:23:52.618 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:23:52.618 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:23:52.618 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:23:53.108 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22751062-21ec-4444-889c-5b3dc1bbf7b1] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:23:53.126 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22751062-21ec-4444-889c-5b3dc1bbf7b1] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:24:35.813 [http-nio-9200-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:26:44.720 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22751062-21ec-4444-889c-5b3dc1bbf7b1] Receive server push request, request = NotifySubscriberRequest, requestId = 11
09:26:44.720 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22751062-21ec-4444-889c-5b3dc1bbf7b1] Ack server push request, request = NotifySubscriberRequest, requestId = 11
09:26:46.711 [nacos-grpc-client-executor-54] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22751062-21ec-4444-889c-5b3dc1bbf7b1] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:26:46.712 [nacos-grpc-client-executor-54] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [22751062-21ec-4444-889c-5b3dc1bbf7b1] Ack server push request, request = NotifySubscriberRequest, requestId = 12
13:58:16.797 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:58:16.824 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:58:17.168 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:58:17.168 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@208f2555[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:58:17.169 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755825832119_127.0.0.1_7940
13:58:17.173 [nacos-grpc-client-executor-3473] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755825832119_127.0.0.1_7940]Ignore complete event,isRunning:false,isAbandon=false
13:58:17.180 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@519ba3e1[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3474]
13:58:46.439 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:58:47.422 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e760f83f-28c3-4e71-b1ca-a5129474d6ee_config-0
13:58:47.539 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 83 ms to scan 1 urls, producing 3 keys and 6 values 
13:58:47.591 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 4 keys and 9 values 
13:58:47.612 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 3 keys and 10 values 
13:58:47.633 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 5 values 
13:58:47.649 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 7 values 
13:58:47.673 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
13:58:47.673 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e760f83f-28c3-4e71-b1ca-a5129474d6ee_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:58:47.680 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e760f83f-28c3-4e71-b1ca-a5129474d6ee_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001b69f3b7d80
13:58:47.680 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e760f83f-28c3-4e71-b1ca-a5129474d6ee_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001b69f3b8000
13:58:47.680 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e760f83f-28c3-4e71-b1ca-a5129474d6ee_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:58:47.680 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e760f83f-28c3-4e71-b1ca-a5129474d6ee_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:58:47.703 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e760f83f-28c3-4e71-b1ca-a5129474d6ee_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:58:49.838 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e760f83f-28c3-4e71-b1ca-a5129474d6ee_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755842329471_127.0.0.1_1240
13:58:49.839 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e760f83f-28c3-4e71-b1ca-a5129474d6ee_config-0] Notify connected event to listeners.
13:58:49.839 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e760f83f-28c3-4e71-b1ca-a5129474d6ee_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:58:49.839 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e760f83f-28c3-4e71-b1ca-a5129474d6ee_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001b69f4f0228
13:58:50.147 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:58:55.141 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
13:58:55.141 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:58:55.141 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:58:55.506 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:58:58.556 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:59:01.430 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f7192c80-70c2-4d52-8a75-730ffa25b460
13:59:01.431 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7192c80-70c2-4d52-8a75-730ffa25b460] RpcClient init label, labels = {module=naming, source=sdk}
13:59:01.433 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7192c80-70c2-4d52-8a75-730ffa25b460] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:59:01.433 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7192c80-70c2-4d52-8a75-730ffa25b460] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:59:01.434 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7192c80-70c2-4d52-8a75-730ffa25b460] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:59:01.435 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7192c80-70c2-4d52-8a75-730ffa25b460] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:59:01.572 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7192c80-70c2-4d52-8a75-730ffa25b460] Success to connect to server [localhost:8848] on start up, connectionId = 1755842341451_127.0.0.1_1245
13:59:01.572 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7192c80-70c2-4d52-8a75-730ffa25b460] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:59:01.572 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7192c80-70c2-4d52-8a75-730ffa25b460] Notify connected event to listeners.
13:59:01.572 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7192c80-70c2-4d52-8a75-730ffa25b460] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001b69f4f0228
13:59:01.689 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
13:59:01.756 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
13:59:02.025 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 16.244 seconds (JVM running for 22.777)
13:59:02.049 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
13:59:02.052 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
13:59:02.063 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
13:59:02.709 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7192c80-70c2-4d52-8a75-730ffa25b460] Receive server push request, request = NotifySubscriberRequest, requestId = 1
13:59:02.710 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7192c80-70c2-4d52-8a75-730ffa25b460] Ack server push request, request = NotifySubscriberRequest, requestId = 1
16:38:07.912 [http-nio-9200-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
16:38:09.363 [nacos-grpc-client-executor-1922] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7192c80-70c2-4d52-8a75-730ffa25b460] Receive server push request, request = NotifySubscriberRequest, requestId = 12
16:38:09.363 [nacos-grpc-client-executor-1922] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7192c80-70c2-4d52-8a75-730ffa25b460] Ack server push request, request = NotifySubscriberRequest, requestId = 12
16:39:32.076 [nacos-grpc-client-executor-1940] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7192c80-70c2-4d52-8a75-730ffa25b460] Receive server push request, request = NotifySubscriberRequest, requestId = 13
16:39:32.076 [nacos-grpc-client-executor-1940] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7192c80-70c2-4d52-8a75-730ffa25b460] Ack server push request, request = NotifySubscriberRequest, requestId = 13
17:05:24.424 [nacos-grpc-client-executor-2252] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7192c80-70c2-4d52-8a75-730ffa25b460] Receive server push request, request = NotifySubscriberRequest, requestId = 14
17:05:24.441 [nacos-grpc-client-executor-2252] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7192c80-70c2-4d52-8a75-730ffa25b460] Ack server push request, request = NotifySubscriberRequest, requestId = 14
17:05:44.201 [nacos-grpc-client-executor-2257] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7192c80-70c2-4d52-8a75-730ffa25b460] Receive server push request, request = NotifySubscriberRequest, requestId = 16
17:05:44.218 [nacos-grpc-client-executor-2257] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7192c80-70c2-4d52-8a75-730ffa25b460] Ack server push request, request = NotifySubscriberRequest, requestId = 16
17:26:56.243 [nacos-grpc-client-executor-2511] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7192c80-70c2-4d52-8a75-730ffa25b460] Receive server push request, request = NotifySubscriberRequest, requestId = 19
17:26:56.266 [nacos-grpc-client-executor-2511] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7192c80-70c2-4d52-8a75-730ffa25b460] Ack server push request, request = NotifySubscriberRequest, requestId = 19
17:27:18.918 [nacos-grpc-client-executor-2516] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7192c80-70c2-4d52-8a75-730ffa25b460] Receive server push request, request = NotifySubscriberRequest, requestId = 21
17:27:18.941 [nacos-grpc-client-executor-2516] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f7192c80-70c2-4d52-8a75-730ffa25b460] Ack server push request, request = NotifySubscriberRequest, requestId = 21
18:03:53.140 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:03:53.143 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:03:53.474 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:03:53.474 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@29897a5e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:03:53.474 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755842341451_127.0.0.1_1245
18:03:53.476 [nacos-grpc-client-executor-2956] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755842341451_127.0.0.1_1245]Ignore complete event,isRunning:false,isAbandon=false
18:03:53.479 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1e22634b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2957]
