09:19:01.163 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:19:01.888 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3216d51c-0db0-4b00-8109-4d5f67c78117_config-0
09:19:01.964 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 38 ms to scan 1 urls, producing 3 keys and 6 values 
09:19:02.005 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 4 keys and 9 values 
09:19:02.015 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 3 keys and 10 values 
09:19:02.015 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 0 ms to scan 1 urls, producing 1 keys and 5 values 
09:19:02.034 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 3 ms to scan 1 urls, producing 1 keys and 7 values 
09:19:02.047 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
09:19:02.051 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3216d51c-0db0-4b00-8109-4d5f67c78117_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:19:02.054 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3216d51c-0db0-4b00-8109-4d5f67c78117_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000019e043b3da8
09:19:02.054 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3216d51c-0db0-4b00-8109-4d5f67c78117_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000019e043b3fc8
09:19:02.054 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3216d51c-0db0-4b00-8109-4d5f67c78117_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:19:02.054 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3216d51c-0db0-4b00-8109-4d5f67c78117_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:19:02.065 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3216d51c-0db0-4b00-8109-4d5f67c78117_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:03.214 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3216d51c-0db0-4b00-8109-4d5f67c78117_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754615942972_127.0.0.1_12134
09:19:03.216 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3216d51c-0db0-4b00-8109-4d5f67c78117_config-0] Notify connected event to listeners.
09:19:03.216 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3216d51c-0db0-4b00-8109-4d5f67c78117_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:19:03.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3216d51c-0db0-4b00-8109-4d5f67c78117_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000019e044ec480
09:19:03.376 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:19:05.980 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:19:05.980 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:19:05.980 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:19:06.149 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:19:07.841 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:19:09.134 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8f9dac44-f979-461c-a403-6a0f6f5752ae
09:19:09.134 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] RpcClient init label, labels = {module=naming, source=sdk}
09:19:09.136 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:19:09.136 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:19:09.137 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:19:09.138 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:09.276 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] Success to connect to server [localhost:8848] on start up, connectionId = 1754615949149_127.0.0.1_12155
09:19:09.276 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:19:09.276 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] Notify connected event to listeners.
09:19:09.277 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000019e044ec480
09:19:09.330 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:19:09.364 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
09:19:09.518 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 9.136 seconds (JVM running for 11.676)
09:19:09.533 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:19:09.534 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:19:09.538 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:19:09.856 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:19:09.873 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:20:49.149 [http-nio-9200-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:21:13.847 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] Receive server push request, request = NotifySubscriberRequest, requestId = 11
09:21:13.847 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] Ack server push request, request = NotifySubscriberRequest, requestId = 11
09:21:16.517 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:21:16.523 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] Ack server push request, request = NotifySubscriberRequest, requestId = 12
15:00:18.204 [nacos-grpc-client-executor-4324] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] Receive server push request, request = NotifySubscriberRequest, requestId = 17
15:00:18.225 [nacos-grpc-client-executor-4324] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] Ack server push request, request = NotifySubscriberRequest, requestId = 17
15:01:01.627 [nacos-grpc-client-executor-4334] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] Receive server push request, request = NotifySubscriberRequest, requestId = 21
15:01:01.642 [nacos-grpc-client-executor-4334] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] Ack server push request, request = NotifySubscriberRequest, requestId = 21
15:18:37.276 [nacos-grpc-client-executor-4551] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] Receive server push request, request = NotifySubscriberRequest, requestId = 26
15:18:37.287 [nacos-grpc-client-executor-4551] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] Ack server push request, request = NotifySubscriberRequest, requestId = 26
15:19:14.545 [nacos-grpc-client-executor-4558] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] Receive server push request, request = NotifySubscriberRequest, requestId = 30
15:19:14.560 [nacos-grpc-client-executor-4558] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] Ack server push request, request = NotifySubscriberRequest, requestId = 30
17:09:24.032 [nacos-grpc-client-executor-5912] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] Receive server push request, request = NotifySubscriberRequest, requestId = 35
17:09:24.046 [nacos-grpc-client-executor-5912] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] Ack server push request, request = NotifySubscriberRequest, requestId = 35
17:09:52.140 [nacos-grpc-client-executor-5918] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] Receive server push request, request = NotifySubscriberRequest, requestId = 39
17:09:52.156 [nacos-grpc-client-executor-5918] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f9dac44-f979-461c-a403-6a0f6f5752ae] Ack server push request, request = NotifySubscriberRequest, requestId = 39
18:08:24.212 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:08:24.214 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:08:24.553 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:08:24.553 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@b700f59[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:08:24.553 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754615949149_127.0.0.1_12155
18:08:24.557 [nacos-grpc-client-executor-6632] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754615949149_127.0.0.1_12155]Ignore complete event,isRunning:false,isAbandon=false
18:08:24.557 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5b0a2dd6[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 6633]
18:09:16.645 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:09:17.613 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bf51df97-9eae-4bdf-bdde-7a47b908116b_config-0
18:09:17.705 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 48 ms to scan 1 urls, producing 3 keys and 6 values 
18:09:17.748 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 4 keys and 9 values 
18:09:17.763 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 3 keys and 10 values 
18:09:17.780 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
18:09:17.795 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
18:09:17.807 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
18:09:17.810 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf51df97-9eae-4bdf-bdde-7a47b908116b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:09:17.810 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf51df97-9eae-4bdf-bdde-7a47b908116b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000002cd993b3b48
18:09:17.810 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf51df97-9eae-4bdf-bdde-7a47b908116b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002cd993b3d68
18:09:17.810 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf51df97-9eae-4bdf-bdde-7a47b908116b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:09:17.810 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf51df97-9eae-4bdf-bdde-7a47b908116b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:09:17.832 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf51df97-9eae-4bdf-bdde-7a47b908116b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:09:19.633 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf51df97-9eae-4bdf-bdde-7a47b908116b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754647759296_127.0.0.1_6763
18:09:19.635 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf51df97-9eae-4bdf-bdde-7a47b908116b_config-0] Notify connected event to listeners.
18:09:19.636 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf51df97-9eae-4bdf-bdde-7a47b908116b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:09:19.637 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf51df97-9eae-4bdf-bdde-7a47b908116b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002cd994ebdb0
18:09:19.917 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:09:26.552 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
18:09:26.555 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:09:26.557 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
18:09:27.417 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:09:31.697 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:09:33.878 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 78481192-b8af-4dc3-b544-3108bb6f4d43
18:09:33.878 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78481192-b8af-4dc3-b544-3108bb6f4d43] RpcClient init label, labels = {module=naming, source=sdk}
18:09:33.881 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78481192-b8af-4dc3-b544-3108bb6f4d43] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:09:33.881 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78481192-b8af-4dc3-b544-3108bb6f4d43] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:09:33.882 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78481192-b8af-4dc3-b544-3108bb6f4d43] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:09:33.882 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78481192-b8af-4dc3-b544-3108bb6f4d43] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:09:34.016 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78481192-b8af-4dc3-b544-3108bb6f4d43] Success to connect to server [localhost:8848] on start up, connectionId = 1754647773895_127.0.0.1_6944
18:09:34.017 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78481192-b8af-4dc3-b544-3108bb6f4d43] Notify connected event to listeners.
18:09:34.017 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78481192-b8af-4dc3-b544-3108bb6f4d43] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:09:34.017 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78481192-b8af-4dc3-b544-3108bb6f4d43] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002cd994ebdb0
18:09:34.070 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
18:09:34.106 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
18:09:34.314 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 18.584 seconds (JVM running for 23.747)
18:09:34.327 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
18:09:34.327 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
18:09:34.327 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
18:09:34.638 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78481192-b8af-4dc3-b544-3108bb6f4d43] Receive server push request, request = NotifySubscriberRequest, requestId = 52
18:09:34.653 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [78481192-b8af-4dc3-b544-3108bb6f4d43] Ack server push request, request = NotifySubscriberRequest, requestId = 52
18:15:36.772 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:15:36.775 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:15:37.101 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:15:37.102 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7c0d2a49[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:15:37.102 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754647773895_127.0.0.1_6944
18:15:37.105 [nacos-grpc-client-executor-81] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754647773895_127.0.0.1_6944]Ignore complete event,isRunning:false,isAbandon=false
18:15:37.107 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@164dd127[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 82]
