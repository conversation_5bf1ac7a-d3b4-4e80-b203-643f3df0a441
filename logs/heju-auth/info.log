10:40:45.086 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:40:47.394 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c9f4870e-5406-44f1-98a3-64e1e1330c00_config-0
10:40:47.595 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 91 ms to scan 1 urls, producing 3 keys and 6 values 
10:40:47.671 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 32 ms to scan 1 urls, producing 4 keys and 9 values 
10:40:47.697 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 26 ms to scan 1 urls, producing 3 keys and 10 values 
10:40:47.726 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 23 ms to scan 1 urls, producing 1 keys and 5 values 
10:40:47.762 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 28 ms to scan 1 urls, producing 1 keys and 7 values 
10:40:47.791 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 2 keys and 8 values 
10:40:47.799 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9f4870e-5406-44f1-98a3-64e1e1330c00_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:40:47.800 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9f4870e-5406-44f1-98a3-64e1e1330c00_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x00000262e73b8638
10:40:47.800 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9f4870e-5406-44f1-98a3-64e1e1330c00_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000262e73b8858
10:40:47.803 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9f4870e-5406-44f1-98a3-64e1e1330c00_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:40:47.803 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9f4870e-5406-44f1-98a3-64e1e1330c00_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:40:47.824 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9f4870e-5406-44f1-98a3-64e1e1330c00_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:40:50.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9f4870e-5406-44f1-98a3-64e1e1330c00_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754793649800_127.0.0.1_4595
10:40:50.173 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9f4870e-5406-44f1-98a3-64e1e1330c00_config-0] Notify connected event to listeners.
10:40:50.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9f4870e-5406-44f1-98a3-64e1e1330c00_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:40:50.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c9f4870e-5406-44f1-98a3-64e1e1330c00_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000262e74f0228
10:40:50.432 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:40:56.681 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
10:40:56.682 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:40:56.683 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:40:57.042 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:41:02.467 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:41:07.784 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a4b15bd8-64d8-4eae-a556-f74147b229b5
10:41:07.784 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4b15bd8-64d8-4eae-a556-f74147b229b5] RpcClient init label, labels = {module=naming, source=sdk}
10:41:07.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4b15bd8-64d8-4eae-a556-f74147b229b5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:41:07.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4b15bd8-64d8-4eae-a556-f74147b229b5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:41:07.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4b15bd8-64d8-4eae-a556-f74147b229b5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:41:07.791 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4b15bd8-64d8-4eae-a556-f74147b229b5] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:41:07.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4b15bd8-64d8-4eae-a556-f74147b229b5] Success to connect to server [localhost:8848] on start up, connectionId = 1754793667818_127.0.0.1_4798
10:41:07.951 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4b15bd8-64d8-4eae-a556-f74147b229b5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:41:07.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4b15bd8-64d8-4eae-a556-f74147b229b5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000262e74f0228
10:41:07.952 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4b15bd8-64d8-4eae-a556-f74147b229b5] Notify connected event to listeners.
10:41:08.081 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
10:41:08.187 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
10:41:08.641 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 25.476 seconds (JVM running for 49.387)
10:41:08.674 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
10:41:08.674 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
10:41:08.678 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
10:41:10.213 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4b15bd8-64d8-4eae-a556-f74147b229b5] Receive server push request, request = NotifySubscriberRequest, requestId = 1
10:41:10.222 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4b15bd8-64d8-4eae-a556-f74147b229b5] Ack server push request, request = NotifySubscriberRequest, requestId = 1
10:44:35.521 [http-nio-9200-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:45:04.795 [nacos-grpc-client-executor-56] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4b15bd8-64d8-4eae-a556-f74147b229b5] Receive server push request, request = NotifySubscriberRequest, requestId = 11
10:45:04.795 [nacos-grpc-client-executor-56] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4b15bd8-64d8-4eae-a556-f74147b229b5] Ack server push request, request = NotifySubscriberRequest, requestId = 11
10:45:07.691 [nacos-grpc-client-executor-59] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4b15bd8-64d8-4eae-a556-f74147b229b5] Receive server push request, request = NotifySubscriberRequest, requestId = 12
10:45:07.693 [nacos-grpc-client-executor-59] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4b15bd8-64d8-4eae-a556-f74147b229b5] Ack server push request, request = NotifySubscriberRequest, requestId = 12
14:08:20.066 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:08:20.071 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:08:20.415 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:08:20.415 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@792524a7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:08:20.417 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754793667818_127.0.0.1_4798
14:08:20.419 [nacos-grpc-client-executor-2721] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754793667818_127.0.0.1_4798]Ignore complete event,isRunning:false,isAbandon=false
14:08:20.425 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1d805c7b[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 2722]
