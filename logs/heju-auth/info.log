09:02:13.385 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:02:13.391 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:02:13.728 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:02:13.729 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@512199b9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:02:13.730 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756257844792_127.0.0.1_14697
09:02:13.737 [nacos-grpc-client-executor-17319] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756257844792_127.0.0.1_14697]Ignore complete event,isRunning:false,isAbandon=false
09:02:13.744 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@637cf11a[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 17320]
09:35:35.278 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:35:36.084 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 96e66b2f-b616-464d-8f14-b91ca1732652_config-0
09:35:36.154 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 3 keys and 6 values 
09:35:36.195 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 23 ms to scan 1 urls, producing 4 keys and 9 values 
09:35:36.207 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:35:36.220 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
09:35:36.232 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
09:35:36.244 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
09:35:36.248 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [96e66b2f-b616-464d-8f14-b91ca1732652_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:35:36.248 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [96e66b2f-b616-464d-8f14-b91ca1732652_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001e3093b3da8
09:35:36.249 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [96e66b2f-b616-464d-8f14-b91ca1732652_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001e3093b3fc8
09:35:36.249 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [96e66b2f-b616-464d-8f14-b91ca1732652_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:35:36.250 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [96e66b2f-b616-464d-8f14-b91ca1732652_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:35:36.261 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [96e66b2f-b616-464d-8f14-b91ca1732652_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:35:37.490 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [96e66b2f-b616-464d-8f14-b91ca1732652_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756344937251_127.0.0.1_9647
09:35:37.491 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [96e66b2f-b616-464d-8f14-b91ca1732652_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:35:37.491 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [96e66b2f-b616-464d-8f14-b91ca1732652_config-0] Notify connected event to listeners.
09:35:37.492 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [96e66b2f-b616-464d-8f14-b91ca1732652_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001e3094ebdb0
09:35:37.642 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:35:40.199 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:35:40.200 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:35:40.200 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:35:40.355 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:35:41.776 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:35:42.826 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e0f9504c-f2e5-4708-bf1f-32de0ef0adf7
09:35:42.827 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0f9504c-f2e5-4708-bf1f-32de0ef0adf7] RpcClient init label, labels = {module=naming, source=sdk}
09:35:42.828 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0f9504c-f2e5-4708-bf1f-32de0ef0adf7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:35:42.828 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0f9504c-f2e5-4708-bf1f-32de0ef0adf7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:35:42.828 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0f9504c-f2e5-4708-bf1f-32de0ef0adf7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:35:42.829 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0f9504c-f2e5-4708-bf1f-32de0ef0adf7] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:35:42.957 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0f9504c-f2e5-4708-bf1f-32de0ef0adf7] Success to connect to server [localhost:8848] on start up, connectionId = 1756344942839_127.0.0.1_9669
09:35:42.957 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0f9504c-f2e5-4708-bf1f-32de0ef0adf7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:35:42.957 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0f9504c-f2e5-4708-bf1f-32de0ef0adf7] Notify connected event to listeners.
09:35:42.957 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0f9504c-f2e5-4708-bf1f-32de0ef0adf7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001e3094ebdb0
09:35:43.002 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:35:43.034 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
09:35:43.142 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 8.622 seconds (JVM running for 11.056)
09:35:43.151 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:35:43.151 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:35:43.154 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:35:43.502 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0f9504c-f2e5-4708-bf1f-32de0ef0adf7] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:35:43.519 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0f9504c-f2e5-4708-bf1f-32de0ef0adf7] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:41:00.880 [http-nio-9200-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:41:11.803 [nacos-grpc-client-executor-75] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0f9504c-f2e5-4708-bf1f-32de0ef0adf7] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:41:11.805 [nacos-grpc-client-executor-75] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0f9504c-f2e5-4708-bf1f-32de0ef0adf7] Ack server push request, request = NotifySubscriberRequest, requestId = 10
09:41:19.378 [nacos-grpc-client-executor-78] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0f9504c-f2e5-4708-bf1f-32de0ef0adf7] Receive server push request, request = NotifySubscriberRequest, requestId = 11
09:41:19.378 [nacos-grpc-client-executor-78] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e0f9504c-f2e5-4708-bf1f-32de0ef0adf7] Ack server push request, request = NotifySubscriberRequest, requestId = 11
