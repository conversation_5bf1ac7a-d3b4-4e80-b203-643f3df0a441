09:06:00.539 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:06:01.297 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b2967e85-b5ca-4bcf-afcd-84a6e667d1a1_config-0
09:06:01.360 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 32 ms to scan 1 urls, producing 3 keys and 6 values 
09:06:01.375 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 0 ms to scan 1 urls, producing 4 keys and 9 values 
09:06:01.393 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 2 ms to scan 1 urls, producing 3 keys and 10 values 
09:06:01.407 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 1 keys and 5 values 
09:06:01.407 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 0 ms to scan 1 urls, producing 1 keys and 7 values 
09:06:01.423 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 2 keys and 8 values 
09:06:01.426 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2967e85-b5ca-4bcf-afcd-84a6e667d1a1_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:06:01.426 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2967e85-b5ca-4bcf-afcd-84a6e667d1a1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x00000219153b4958
09:06:01.426 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2967e85-b5ca-4bcf-afcd-84a6e667d1a1_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x00000219153b4b78
09:06:01.426 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2967e85-b5ca-4bcf-afcd-84a6e667d1a1_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:06:01.426 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2967e85-b5ca-4bcf-afcd-84a6e667d1a1_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:06:01.437 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2967e85-b5ca-4bcf-afcd-84a6e667d1a1_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:06:03.269 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2967e85-b5ca-4bcf-afcd-84a6e667d1a1_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754528762951_127.0.0.1_1751
09:06:03.271 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2967e85-b5ca-4bcf-afcd-84a6e667d1a1_config-0] Notify connected event to listeners.
09:06:03.271 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2967e85-b5ca-4bcf-afcd-84a6e667d1a1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:06:03.271 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b2967e85-b5ca-4bcf-afcd-84a6e667d1a1_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000219154ecb18
09:06:03.433 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:06:07.213 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:06:07.214 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:06:07.214 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:06:07.518 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:06:09.396 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:06:10.598 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of aade3351-ff68-49e6-90df-49a04d0d85c5
09:06:10.598 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] RpcClient init label, labels = {module=naming, source=sdk}
09:06:10.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:06:10.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:06:10.601 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:06:10.602 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:06:10.724 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Success to connect to server [localhost:8848] on start up, connectionId = 1754528770612_127.0.0.1_1852
09:06:10.724 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Notify connected event to listeners.
09:06:10.724 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:06:10.725 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x00000219154ecb18
09:06:10.768 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:06:10.801 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
09:06:10.960 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 11.261 seconds (JVM running for 14.118)
09:06:10.971 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:06:10.971 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:06:10.975 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:06:11.290 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:06:11.307 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Ack server push request, request = NotifySubscriberRequest, requestId = 5
09:14:50.262 [http-nio-9200-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:15:00.132 [nacos-grpc-client-executor-119] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:15:00.134 [nacos-grpc-client-executor-119] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Ack server push request, request = NotifySubscriberRequest, requestId = 10
09:15:04.500 [nacos-grpc-client-executor-122] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Receive server push request, request = NotifySubscriberRequest, requestId = 11
09:15:04.507 [nacos-grpc-client-executor-122] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Ack server push request, request = NotifySubscriberRequest, requestId = 11
10:08:32.366 [nacos-grpc-client-executor-769] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Receive server push request, request = NotifySubscriberRequest, requestId = 14
10:08:32.401 [nacos-grpc-client-executor-769] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Ack server push request, request = NotifySubscriberRequest, requestId = 14
10:09:21.885 [nacos-grpc-client-executor-780] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Receive server push request, request = NotifySubscriberRequest, requestId = 17
10:09:21.902 [nacos-grpc-client-executor-780] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Ack server push request, request = NotifySubscriberRequest, requestId = 17
10:11:30.949 [nacos-grpc-client-executor-806] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Receive server push request, request = NotifySubscriberRequest, requestId = 21
10:11:30.959 [nacos-grpc-client-executor-806] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Ack server push request, request = NotifySubscriberRequest, requestId = 21
10:11:55.252 [nacos-grpc-client-executor-810] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Receive server push request, request = NotifySubscriberRequest, requestId = 24
10:11:55.268 [nacos-grpc-client-executor-810] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Ack server push request, request = NotifySubscriberRequest, requestId = 24
11:36:32.754 [nacos-grpc-client-executor-1837] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Receive server push request, request = NotifySubscriberRequest, requestId = 28
11:36:32.770 [nacos-grpc-client-executor-1837] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Ack server push request, request = NotifySubscriberRequest, requestId = 28
11:36:34.287 [nacos-grpc-client-executor-1838] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Receive server push request, request = NotifySubscriberRequest, requestId = 31
11:36:34.318 [nacos-grpc-client-executor-1838] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Ack server push request, request = NotifySubscriberRequest, requestId = 31
11:44:15.734 [nacos-grpc-client-executor-1931] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Receive server push request, request = NotifySubscriberRequest, requestId = 111
11:44:15.755 [nacos-grpc-client-executor-1931] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Ack server push request, request = NotifySubscriberRequest, requestId = 111
11:44:18.579 [nacos-grpc-client-executor-1932] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Receive server push request, request = NotifySubscriberRequest, requestId = 115
11:44:18.598 [nacos-grpc-client-executor-1932] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Ack server push request, request = NotifySubscriberRequest, requestId = 115
11:55:33.406 [nacos-grpc-client-executor-2071] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Receive server push request, request = NotifySubscriberRequest, requestId = 118
11:55:33.422 [nacos-grpc-client-executor-2071] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Ack server push request, request = NotifySubscriberRequest, requestId = 118
11:56:11.500 [nacos-grpc-client-executor-2080] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Receive server push request, request = NotifySubscriberRequest, requestId = 122
11:56:11.515 [nacos-grpc-client-executor-2080] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Ack server push request, request = NotifySubscriberRequest, requestId = 122
14:12:07.684 [nacos-grpc-client-executor-3823] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Receive server push request, request = NotifySubscriberRequest, requestId = 125
14:12:07.703 [nacos-grpc-client-executor-3823] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Ack server push request, request = NotifySubscriberRequest, requestId = 125
14:12:32.783 [nacos-grpc-client-executor-3829] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Receive server push request, request = NotifySubscriberRequest, requestId = 128
14:12:32.797 [nacos-grpc-client-executor-3829] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Ack server push request, request = NotifySubscriberRequest, requestId = 128
14:33:40.491 [nacos-grpc-client-executor-4082] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Receive server push request, request = NotifySubscriberRequest, requestId = 132
14:33:40.500 [nacos-grpc-client-executor-4082] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Ack server push request, request = NotifySubscriberRequest, requestId = 132
14:34:16.367 [nacos-grpc-client-executor-4091] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Receive server push request, request = NotifySubscriberRequest, requestId = 135
14:34:16.376 [nacos-grpc-client-executor-4091] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Ack server push request, request = NotifySubscriberRequest, requestId = 135
14:36:07.929 [nacos-grpc-client-executor-4114] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Receive server push request, request = NotifySubscriberRequest, requestId = 139
14:36:07.930 [nacos-grpc-client-executor-4114] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Ack server push request, request = NotifySubscriberRequest, requestId = 139
14:38:54.704 [nacos-grpc-client-executor-4148] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Receive server push request, request = NotifySubscriberRequest, requestId = 143
14:38:54.719 [nacos-grpc-client-executor-4148] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Ack server push request, request = NotifySubscriberRequest, requestId = 143
14:39:17.651 [nacos-grpc-client-executor-4153] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Receive server push request, request = NotifySubscriberRequest, requestId = 146
14:39:17.665 [nacos-grpc-client-executor-4153] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Ack server push request, request = NotifySubscriberRequest, requestId = 146
14:42:54.935 [nacos-grpc-client-executor-4197] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Receive server push request, request = NotifySubscriberRequest, requestId = 150
14:42:54.952 [nacos-grpc-client-executor-4197] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Ack server push request, request = NotifySubscriberRequest, requestId = 150
14:42:57.417 [nacos-grpc-client-executor-4198] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Receive server push request, request = NotifySubscriberRequest, requestId = 153
14:42:57.432 [nacos-grpc-client-executor-4198] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aade3351-ff68-49e6-90df-49a04d0d85c5] Ack server push request, request = NotifySubscriberRequest, requestId = 153
16:37:59.780 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:37:59.780 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:38:00.096 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:38:00.096 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@427b1e8e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:38:00.096 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754528770612_127.0.0.1_1852
16:38:00.098 [nacos-grpc-client-executor-5579] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754528770612_127.0.0.1_1852]Ignore complete event,isRunning:false,isAbandon=false
16:38:00.106 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@23050a46[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 5580]
