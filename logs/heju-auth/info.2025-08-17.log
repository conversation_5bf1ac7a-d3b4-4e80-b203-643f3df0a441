10:17:17.233 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:17:18.121 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6814ab2e-57de-41b2-8e11-45f5df3ebf6c_config-0
10:17:18.227 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 49 ms to scan 1 urls, producing 3 keys and 6 values 
10:17:18.258 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 4 keys and 9 values 
10:17:18.269 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 3 keys and 10 values 
10:17:18.279 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 1 keys and 5 values 
10:17:18.289 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 7 values 
10:17:18.300 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
10:17:18.304 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6814ab2e-57de-41b2-8e11-45f5df3ebf6c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:17:18.305 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6814ab2e-57de-41b2-8e11-45f5df3ebf6c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000025f163b9ed0
10:17:18.305 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6814ab2e-57de-41b2-8e11-45f5df3ebf6c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000025f163ba0f0
10:17:18.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6814ab2e-57de-41b2-8e11-45f5df3ebf6c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:17:18.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6814ab2e-57de-41b2-8e11-45f5df3ebf6c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:17:18.322 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6814ab2e-57de-41b2-8e11-45f5df3ebf6c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:17:19.603 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6814ab2e-57de-41b2-8e11-45f5df3ebf6c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755397039346_127.0.0.1_10582
10:17:19.604 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6814ab2e-57de-41b2-8e11-45f5df3ebf6c_config-0] Notify connected event to listeners.
10:17:19.604 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6814ab2e-57de-41b2-8e11-45f5df3ebf6c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:17:19.605 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6814ab2e-57de-41b2-8e11-45f5df3ebf6c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000025f164f3db0
10:17:19.836 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:17:22.745 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
10:17:22.746 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:17:22.746 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:17:22.959 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:17:25.007 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:17:26.798 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8e642c53-8255-4a52-a549-a709336f75a6
10:17:26.798 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] RpcClient init label, labels = {module=naming, source=sdk}
10:17:26.800 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:17:26.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:17:26.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:17:26.802 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:17:26.933 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Success to connect to server [localhost:8848] on start up, connectionId = 1755397046814_127.0.0.1_10613
10:17:26.935 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Notify connected event to listeners.
10:17:26.935 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:17:26.936 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000025f164f3db0
10:17:27.001 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
10:17:27.053 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
10:17:27.306 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 10.827 seconds (JVM running for 13.742)
10:17:27.320 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
10:17:27.321 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
10:17:27.324 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
10:17:27.633 [RMI TCP Connection(6)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:17:27.864 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Receive server push request, request = NotifySubscriberRequest, requestId = 1
10:17:27.920 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Ack server push request, request = NotifySubscriberRequest, requestId = 1
10:19:14.928 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Receive server push request, request = NotifySubscriberRequest, requestId = 11
10:19:14.929 [nacos-grpc-client-executor-35] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Ack server push request, request = NotifySubscriberRequest, requestId = 11
10:19:17.658 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Receive server push request, request = NotifySubscriberRequest, requestId = 12
10:19:17.659 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Ack server push request, request = NotifySubscriberRequest, requestId = 12
13:03:04.538 [nacos-grpc-client-executor-2077] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Receive server push request, request = NotifySubscriberRequest, requestId = 16
13:03:04.576 [nacos-grpc-client-executor-2077] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Ack server push request, request = NotifySubscriberRequest, requestId = 16
13:03:33.540 [nacos-grpc-client-executor-2083] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Receive server push request, request = NotifySubscriberRequest, requestId = 19
13:03:33.565 [nacos-grpc-client-executor-2083] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Ack server push request, request = NotifySubscriberRequest, requestId = 19
14:23:13.262 [nacos-grpc-client-executor-3052] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Receive server push request, request = NotifySubscriberRequest, requestId = 23
14:23:13.295 [nacos-grpc-client-executor-3052] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Ack server push request, request = NotifySubscriberRequest, requestId = 23
14:23:40.494 [nacos-grpc-client-executor-3058] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Receive server push request, request = NotifySubscriberRequest, requestId = 26
14:23:40.513 [nacos-grpc-client-executor-3058] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Ack server push request, request = NotifySubscriberRequest, requestId = 26
14:25:18.528 [nacos-grpc-client-executor-3078] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Receive server push request, request = NotifySubscriberRequest, requestId = 30
14:25:18.543 [nacos-grpc-client-executor-3078] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Ack server push request, request = NotifySubscriberRequest, requestId = 30
14:25:44.537 [nacos-grpc-client-executor-3084] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Receive server push request, request = NotifySubscriberRequest, requestId = 33
14:25:44.563 [nacos-grpc-client-executor-3084] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Ack server push request, request = NotifySubscriberRequest, requestId = 33
15:29:29.672 [nacos-grpc-client-executor-3849] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Receive server push request, request = NotifySubscriberRequest, requestId = 38
15:29:29.696 [nacos-grpc-client-executor-3849] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Ack server push request, request = NotifySubscriberRequest, requestId = 38
15:29:54.963 [nacos-grpc-client-executor-3856] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Receive server push request, request = NotifySubscriberRequest, requestId = 42
15:29:54.977 [nacos-grpc-client-executor-3856] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Ack server push request, request = NotifySubscriberRequest, requestId = 42
15:50:15.128 [nacos-grpc-client-executor-4099] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Receive server push request, request = NotifySubscriberRequest, requestId = 47
15:50:15.144 [nacos-grpc-client-executor-4099] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Ack server push request, request = NotifySubscriberRequest, requestId = 47
15:50:32.179 [nacos-grpc-client-executor-4103] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Receive server push request, request = NotifySubscriberRequest, requestId = 51
15:50:32.196 [nacos-grpc-client-executor-4103] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8e642c53-8255-4a52-a549-a709336f75a6] Ack server push request, request = NotifySubscriberRequest, requestId = 51
15:58:50.753 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:58:50.757 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:58:51.112 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:58:51.112 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@10796f8e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:58:51.112 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755397046814_127.0.0.1_10613
15:58:51.114 [nacos-grpc-client-executor-4205] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755397046814_127.0.0.1_10613]Ignore complete event,isRunning:false,isAbandon=false
15:58:51.118 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@23811cc8[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 4206]
16:00:37.538 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:00:38.652 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7f7f95b4-7d5a-49ae-8ff9-e0b12e9e62bb_config-0
16:00:38.779 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 68 ms to scan 1 urls, producing 3 keys and 6 values 
16:00:38.822 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
16:00:38.839 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 3 keys and 10 values 
16:00:38.853 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
16:00:38.869 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 7 values 
16:00:38.897 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 2 keys and 8 values 
16:00:38.902 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f7f95b4-7d5a-49ae-8ff9-e0b12e9e62bb_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:00:38.903 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f7f95b4-7d5a-49ae-8ff9-e0b12e9e62bb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002189239b650
16:00:38.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f7f95b4-7d5a-49ae-8ff9-e0b12e9e62bb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002189239b870
16:00:38.905 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f7f95b4-7d5a-49ae-8ff9-e0b12e9e62bb_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:00:38.906 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f7f95b4-7d5a-49ae-8ff9-e0b12e9e62bb_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:00:38.923 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f7f95b4-7d5a-49ae-8ff9-e0b12e9e62bb_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:00:40.619 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f7f95b4-7d5a-49ae-8ff9-e0b12e9e62bb_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755417640365_127.0.0.1_4247
16:00:40.620 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f7f95b4-7d5a-49ae-8ff9-e0b12e9e62bb_config-0] Notify connected event to listeners.
16:00:40.621 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f7f95b4-7d5a-49ae-8ff9-e0b12e9e62bb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:00:40.622 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7f7f95b4-7d5a-49ae-8ff9-e0b12e9e62bb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000021892514d90
16:00:40.799 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:00:44.841 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
16:00:44.842 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:00:44.842 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:00:45.107 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:00:48.103 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:00:50.620 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7477e73c-e10a-4658-876b-a46ed562cc16
16:00:50.620 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7477e73c-e10a-4658-876b-a46ed562cc16] RpcClient init label, labels = {module=naming, source=sdk}
16:00:50.623 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7477e73c-e10a-4658-876b-a46ed562cc16] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:00:50.624 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7477e73c-e10a-4658-876b-a46ed562cc16] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:00:50.624 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7477e73c-e10a-4658-876b-a46ed562cc16] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:00:50.625 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7477e73c-e10a-4658-876b-a46ed562cc16] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:00:50.758 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7477e73c-e10a-4658-876b-a46ed562cc16] Success to connect to server [localhost:8848] on start up, connectionId = 1755417650639_127.0.0.1_4263
16:00:50.759 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7477e73c-e10a-4658-876b-a46ed562cc16] Notify connected event to listeners.
16:00:50.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7477e73c-e10a-4658-876b-a46ed562cc16] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:00:50.760 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7477e73c-e10a-4658-876b-a46ed562cc16] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000021892514d90
16:00:50.864 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
16:00:50.916 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
16:00:51.239 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 14.587 seconds (JVM running for 15.905)
16:00:51.263 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
16:00:51.265 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
16:00:51.270 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
16:00:51.366 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7477e73c-e10a-4658-876b-a46ed562cc16] Receive server push request, request = NotifySubscriberRequest, requestId = 55
16:00:51.402 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7477e73c-e10a-4658-876b-a46ed562cc16] Ack server push request, request = NotifySubscriberRequest, requestId = 55
16:00:51.464 [RMI TCP Connection(5)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
19:41:50.470 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:41:50.474 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:41:50.809 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:41:50.811 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7c4181c9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:41:50.811 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755417650639_127.0.0.1_4263
19:41:50.811 [nacos-grpc-client-executor-2660] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755417650639_127.0.0.1_4263]Ignore complete event,isRunning:false,isAbandon=false
19:41:50.817 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1b1f84f4[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 2661]
