13:27:05.783 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:27:08.247 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 633d9436-73eb-4cec-b23a-d4dffb28d7d9_config-0
13:27:08.413 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 88 ms to scan 1 urls, producing 3 keys and 6 values 
13:27:08.569 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 58 ms to scan 1 urls, producing 4 keys and 9 values 
13:27:08.616 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 39 ms to scan 1 urls, producing 3 keys and 10 values 
13:27:08.663 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 28 ms to scan 1 urls, producing 1 keys and 5 values 
13:27:08.693 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 22 ms to scan 1 urls, producing 1 keys and 7 values 
13:27:08.738 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 35 ms to scan 1 urls, producing 2 keys and 8 values 
13:27:08.748 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [633d9436-73eb-4cec-b23a-d4dffb28d7d9_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:27:08.750 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [633d9436-73eb-4cec-b23a-d4dffb28d7d9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000019e5339a328
13:27:08.752 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [633d9436-73eb-4cec-b23a-d4dffb28d7d9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000019e5339a548
13:27:08.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [633d9436-73eb-4cec-b23a-d4dffb28d7d9_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:27:08.758 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [633d9436-73eb-4cec-b23a-d4dffb28d7d9_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:27:08.795 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [633d9436-73eb-4cec-b23a-d4dffb28d7d9_config-0] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
13:27:12.109 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [633d9436-73eb-4cec-b23a-d4dffb28d7d9_config-0] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751347631775_127.0.0.1_11393
13:27:12.112 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [633d9436-73eb-4cec-b23a-d4dffb28d7d9_config-0] Notify connected event to listeners.
13:27:12.113 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [633d9436-73eb-4cec-b23a-d4dffb28d7d9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:27:12.115 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [633d9436-73eb-4cec-b23a-d4dffb28d7d9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000019e535169a8
13:27:12.352 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:27:16.088 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
13:27:16.089 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:27:16.089 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:27:16.302 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:27:18.631 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:27:20.066 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8efc5844-6890-4d4c-bdaa-5ac2b160e34e
13:27:20.067 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8efc5844-6890-4d4c-bdaa-5ac2b160e34e] RpcClient init label, labels = {module=naming, source=sdk}
13:27:20.069 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8efc5844-6890-4d4c-bdaa-5ac2b160e34e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:27:20.069 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8efc5844-6890-4d4c-bdaa-5ac2b160e34e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:27:20.070 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8efc5844-6890-4d4c-bdaa-5ac2b160e34e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:27:20.070 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8efc5844-6890-4d4c-bdaa-5ac2b160e34e] Try to connect to server on start up, server: {serverIp = '127.0.0.1', server main port = 8848}
13:27:20.199 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8efc5844-6890-4d4c-bdaa-5ac2b160e34e] Success to connect to server [127.0.0.1:8848] on start up, connectionId = 1751347640081_127.0.0.1_11403
13:27:20.199 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8efc5844-6890-4d4c-bdaa-5ac2b160e34e] Notify connected event to listeners.
13:27:20.199 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8efc5844-6890-4d4c-bdaa-5ac2b160e34e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:27:20.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8efc5844-6890-4d4c-bdaa-5ac2b160e34e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000019e535169a8
13:27:20.274 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
13:27:20.326 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth 192.168.2.43:9200 register finished
13:27:20.506 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 16.852 seconds (JVM running for 20.546)
13:27:20.520 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
13:27:20.521 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
13:27:20.528 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
13:27:20.807 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8efc5844-6890-4d4c-bdaa-5ac2b160e34e] Receive server push request, request = NotifySubscriberRequest, requestId = 6
13:27:20.834 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8efc5844-6890-4d4c-bdaa-5ac2b160e34e] Ack server push request, request = NotifySubscriberRequest, requestId = 6
13:27:20.910 [RMI TCP Connection(4)-192.168.2.43] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
21:14:32.546 [nacos-grpc-client-executor-5609] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8efc5844-6890-4d4c-bdaa-5ac2b160e34e] Receive server push request, request = NotifySubscriberRequest, requestId = 48
21:14:32.548 [nacos-grpc-client-executor-5609] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8efc5844-6890-4d4c-bdaa-5ac2b160e34e] Ack server push request, request = NotifySubscriberRequest, requestId = 48
21:14:36.353 [nacos-grpc-client-executor-5612] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8efc5844-6890-4d4c-bdaa-5ac2b160e34e] Receive server push request, request = NotifySubscriberRequest, requestId = 50
21:14:36.382 [nacos-grpc-client-executor-5612] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8efc5844-6890-4d4c-bdaa-5ac2b160e34e] Ack server push request, request = NotifySubscriberRequest, requestId = 50
21:14:49.097 [nacos-grpc-client-executor-5616] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8efc5844-6890-4d4c-bdaa-5ac2b160e34e] Receive server push request, request = NotifySubscriberRequest, requestId = 52
21:14:49.098 [nacos-grpc-client-executor-5616] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8efc5844-6890-4d4c-bdaa-5ac2b160e34e] Ack server push request, request = NotifySubscriberRequest, requestId = 52
21:23:31.510 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
21:23:31.515 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
21:23:31.852 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
21:23:31.852 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1e3fe015[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
21:23:31.852 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751347640081_127.0.0.1_11403
21:23:31.855 [nacos-grpc-client-executor-5723] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751347640081_127.0.0.1_11403]Ignore complete event,isRunning:false,isAbandon=false
21:23:31.855 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@76d1b4b4[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 5723]
