09:02:27.996 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:02:29.189 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 626632e0-62c1-4fdf-a48c-f9930c9f9aa3_config-0
09:02:29.281 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 49 ms to scan 1 urls, producing 3 keys and 6 values 
09:02:29.343 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 46 ms to scan 1 urls, producing 4 keys and 9 values 
09:02:29.361 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 15 ms to scan 1 urls, producing 3 keys and 10 values 
09:02:29.378 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 5 values 
09:02:29.395 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 1 keys and 7 values 
09:02:29.409 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
09:02:29.415 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [626632e0-62c1-4fdf-a48c-f9930c9f9aa3_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:02:29.416 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [626632e0-62c1-4fdf-a48c-f9930c9f9aa3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000002372c3978e0
09:02:29.416 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [626632e0-62c1-4fdf-a48c-f9930c9f9aa3_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002372c397b00
09:02:29.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [626632e0-62c1-4fdf-a48c-f9930c9f9aa3_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:02:29.419 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [626632e0-62c1-4fdf-a48c-f9930c9f9aa3_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:02:29.435 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [626632e0-62c1-4fdf-a48c-f9930c9f9aa3_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:02:31.357 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [626632e0-62c1-4fdf-a48c-f9930c9f9aa3_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754269351057_127.0.0.1_8839
09:02:31.358 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [626632e0-62c1-4fdf-a48c-f9930c9f9aa3_config-0] Notify connected event to listeners.
09:02:31.359 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [626632e0-62c1-4fdf-a48c-f9930c9f9aa3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:02:31.359 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [626632e0-62c1-4fdf-a48c-f9930c9f9aa3_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002372c50fb88
09:02:31.635 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:02:35.896 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:02:35.897 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:02:35.897 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:02:36.256 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:02:39.180 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:02:43.556 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3e0239be-d857-48d6-832c-61b4931878f7
09:02:43.556 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] RpcClient init label, labels = {module=naming, source=sdk}
09:02:43.560 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:02:43.563 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:02:43.564 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:02:43.565 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:02:43.706 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Success to connect to server [localhost:8848] on start up, connectionId = 1754269363581_127.0.0.1_9092
09:02:43.707 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Notify connected event to listeners.
09:02:43.709 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:02:43.711 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002372c50fb88
09:02:43.841 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:02:43.907 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
09:02:44.222 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 17.258 seconds (JVM running for 24.844)
09:02:44.241 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:02:44.242 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:02:44.248 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:02:44.788 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:02:44.789 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:13:10.568 [http-nio-9200-exec-2] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:13:49.460 [nacos-grpc-client-executor-148] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Receive server push request, request = NotifySubscriberRequest, requestId = 11
09:13:49.464 [nacos-grpc-client-executor-148] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Ack server push request, request = NotifySubscriberRequest, requestId = 11
09:13:52.224 [nacos-grpc-client-executor-151] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:13:52.224 [nacos-grpc-client-executor-151] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Ack server push request, request = NotifySubscriberRequest, requestId = 12
10:09:06.104 [nacos-grpc-client-executor-832] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Receive server push request, request = NotifySubscriberRequest, requestId = 16
10:09:06.119 [nacos-grpc-client-executor-832] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Ack server push request, request = NotifySubscriberRequest, requestId = 16
10:25:04.730 [nacos-grpc-client-executor-1024] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Receive server push request, request = NotifySubscriberRequest, requestId = 19
10:25:04.744 [nacos-grpc-client-executor-1024] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Ack server push request, request = NotifySubscriberRequest, requestId = 19
10:43:24.631 [nacos-grpc-client-executor-1247] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Receive server push request, request = NotifySubscriberRequest, requestId = 23
10:43:24.646 [nacos-grpc-client-executor-1247] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Ack server push request, request = NotifySubscriberRequest, requestId = 23
10:43:42.426 [nacos-grpc-client-executor-1251] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Receive server push request, request = NotifySubscriberRequest, requestId = 26
10:43:42.439 [nacos-grpc-client-executor-1251] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Ack server push request, request = NotifySubscriberRequest, requestId = 26
11:43:53.006 [nacos-grpc-client-executor-1976] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Receive server push request, request = NotifySubscriberRequest, requestId = 30
11:43:53.021 [nacos-grpc-client-executor-1976] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Ack server push request, request = NotifySubscriberRequest, requestId = 30
11:44:14.410 [nacos-grpc-client-executor-1981] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Receive server push request, request = NotifySubscriberRequest, requestId = 34
11:44:14.424 [nacos-grpc-client-executor-1981] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Ack server push request, request = NotifySubscriberRequest, requestId = 34
13:35:14.319 [nacos-grpc-client-executor-3311] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Receive server push request, request = NotifySubscriberRequest, requestId = 39
13:35:14.339 [nacos-grpc-client-executor-3311] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Ack server push request, request = NotifySubscriberRequest, requestId = 39
13:35:47.530 [nacos-grpc-client-executor-3317] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Receive server push request, request = NotifySubscriberRequest, requestId = 44
13:35:47.543 [nacos-grpc-client-executor-3317] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3e0239be-d857-48d6-832c-61b4931878f7] Ack server push request, request = NotifySubscriberRequest, requestId = 44
18:50:00.008 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:50:00.026 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:50:00.360 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:50:00.360 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5395ff04[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:50:00.361 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754269363581_127.0.0.1_9092
18:50:00.363 [nacos-grpc-client-executor-7096] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754269363581_127.0.0.1_9092]Ignore complete event,isRunning:false,isAbandon=false
18:50:00.367 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@54161e1d[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 7097]
