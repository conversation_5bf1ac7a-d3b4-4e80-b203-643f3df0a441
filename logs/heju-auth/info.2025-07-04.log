09:24:00.026 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:24:00.951 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0
09:24:01.084 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 57 ms to scan 1 urls, producing 3 keys and 6 values 
09:24:01.144 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 28 ms to scan 1 urls, producing 4 keys and 9 values 
09:24:01.157 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 3 keys and 10 values 
09:24:01.168 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:24:01.183 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 7 values 
09:24:01.195 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
09:24:01.199 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:24:01.200 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000002b13e3b8200
09:24:01.201 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002b13e3b8420
09:24:01.202 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:24:01.203 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:24:01.214 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:02.887 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:02.910 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:02.939 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:02.939 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:24:02.939 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002b13e4c5f88
09:24:03.113 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:03.360 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:03.701 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:04.166 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:04.705 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:04.887 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:24:05.626 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:06.441 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:07.275 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:08.211 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:09.245 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:10.527 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:11.991 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:13.561 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:16.128 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:24:16.133 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:24:16.135 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:24:16.982 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:24:18.330 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:20.038 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:21.703 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:24:21.799 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:23.737 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:24.249 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b07142c3-e87d-459b-8b0e-b4c6d9273ab1
09:24:24.249 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07142c3-e87d-459b-8b0e-b4c6d9273ab1] RpcClient init label, labels = {module=naming, source=sdk}
09:24:24.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07142c3-e87d-459b-8b0e-b4c6d9273ab1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:24:24.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07142c3-e87d-459b-8b0e-b4c6d9273ab1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:24:24.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07142c3-e87d-459b-8b0e-b4c6d9273ab1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:24:24.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07142c3-e87d-459b-8b0e-b4c6d9273ab1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:24.267 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07142c3-e87d-459b-8b0e-b4c6d9273ab1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:24.283 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07142c3-e87d-459b-8b0e-b4c6d9273ab1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:24.283 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07142c3-e87d-459b-8b0e-b4c6d9273ab1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:24.283 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07142c3-e87d-459b-8b0e-b4c6d9273ab1] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:24:24.283 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07142c3-e87d-459b-8b0e-b4c6d9273ab1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002b13e4c5f88
09:24:24.421 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07142c3-e87d-459b-8b0e-b4c6d9273ab1] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:24.638 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:24:24.645 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07142c3-e87d-459b-8b0e-b4c6d9273ab1] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:24.978 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07142c3-e87d-459b-8b0e-b4c6d9273ab1] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:25.430 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07142c3-e87d-459b-8b0e-b4c6d9273ab1] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:25.550 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [aef23238-a01b-46f4-8dfe-60b6aa3dbd38_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:25.622 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:24:25.622 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3857c5d5[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:24:25.622 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@237824b0[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
09:24:25.622 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07142c3-e87d-459b-8b0e-b4c6d9273ab1] Client is shutdown, stop reconnect to server
09:24:25.625 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2ec9b031-4cb9-45a7-b334-3d5839d4a169
09:24:25.626 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec9b031-4cb9-45a7-b334-3d5839d4a169] RpcClient init label, labels = {module=naming, source=sdk}
09:24:25.626 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec9b031-4cb9-45a7-b334-3d5839d4a169] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:24:25.626 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec9b031-4cb9-45a7-b334-3d5839d4a169] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:24:25.626 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec9b031-4cb9-45a7-b334-3d5839d4a169] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:24:25.627 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec9b031-4cb9-45a7-b334-3d5839d4a169] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:25.639 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec9b031-4cb9-45a7-b334-3d5839d4a169] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:25.645 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec9b031-4cb9-45a7-b334-3d5839d4a169] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:25.658 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec9b031-4cb9-45a7-b334-3d5839d4a169] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:24:25.658 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec9b031-4cb9-45a7-b334-3d5839d4a169] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:25.659 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec9b031-4cb9-45a7-b334-3d5839d4a169] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002b13e4c5f88
09:24:25.780 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec9b031-4cb9-45a7-b334-3d5839d4a169] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:25.991 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec9b031-4cb9-45a7-b334-3d5839d4a169] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:26.036 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9200"]
09:24:26.036 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:24:26.041 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9200"]
09:24:26.050 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9200"]
09:24:26.297 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec9b031-4cb9-45a7-b334-3d5839d4a169] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:24:26.712 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2ec9b031-4cb9-45a7-b334-3d5839d4a169] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:25:11.923 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:25:12.778 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b07d3b01-535d-4637-b47b-e3fb298b8105_config-0
09:25:12.858 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 38 ms to scan 1 urls, producing 3 keys and 6 values 
09:25:12.895 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 4 keys and 9 values 
09:25:12.907 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 3 keys and 10 values 
09:25:12.921 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 1 keys and 5 values 
09:25:12.936 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
09:25:12.947 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 2 keys and 8 values 
09:25:12.951 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07d3b01-535d-4637-b47b-e3fb298b8105_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:25:12.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07d3b01-535d-4637-b47b-e3fb298b8105_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000022c91399ed0
09:25:12.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07d3b01-535d-4637-b47b-e3fb298b8105_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000022c9139a0f0
09:25:12.953 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07d3b01-535d-4637-b47b-e3fb298b8105_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:25:12.953 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07d3b01-535d-4637-b47b-e3fb298b8105_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:25:12.965 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07d3b01-535d-4637-b47b-e3fb298b8105_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:25:14.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07d3b01-535d-4637-b47b-e3fb298b8105_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1751592314044_127.0.0.1_9428
09:25:14.310 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07d3b01-535d-4637-b47b-e3fb298b8105_config-0] Notify connected event to listeners.
09:25:14.311 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07d3b01-535d-4637-b47b-e3fb298b8105_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:25:14.311 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07d3b01-535d-4637-b47b-e3fb298b8105_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000022c91513db0
09:25:14.480 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:25:17.503 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:25:17.503 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:25:17.504 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:25:17.784 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:25:20.187 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:25:22.410 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3db80b86-8121-4b54-80ab-dfb542bba8af
09:25:22.411 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] RpcClient init label, labels = {module=naming, source=sdk}
09:25:22.424 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:25:22.425 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:25:22.426 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:25:22.426 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:25:22.646 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Success to connect to server [localhost:8848] on start up, connectionId = 1751592322466_127.0.0.1_9647
09:25:22.647 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Notify connected event to listeners.
09:25:22.647 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:25:22.647 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000022c91513db0
09:25:22.706 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:25:22.747 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
09:25:22.989 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 11.866 seconds (JVM running for 13.207)
09:25:23.016 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:25:23.016 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:25:23.020 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:25:23.128 [RMI TCP Connection(7)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:25:23.197 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:25:23.216 [nacos-grpc-client-executor-7] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:50:01.051 [nacos-grpc-client-executor-307] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:50:01.053 [nacos-grpc-client-executor-307] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 12
09:50:07.682 [nacos-grpc-client-executor-311] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 13
09:50:07.684 [nacos-grpc-client-executor-311] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 13
11:05:16.830 [nacos-grpc-client-executor-1214] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 15
11:05:16.855 [nacos-grpc-client-executor-1214] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 15
11:05:37.395 [nacos-grpc-client-executor-1219] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 17
11:05:37.443 [nacos-grpc-client-executor-1219] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 17
11:41:47.065 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07d3b01-535d-4637-b47b-e3fb298b8105_config-0] Server healthy check fail, currentConnection = 1751592314044_127.0.0.1_9428
11:41:47.065 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07d3b01-535d-4637-b47b-e3fb298b8105_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
11:41:47.771 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07d3b01-535d-4637-b47b-e3fb298b8105_config-0] Success to connect a server [localhost:8848], connectionId = 1751600507657_127.0.0.1_13705
11:41:47.772 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07d3b01-535d-4637-b47b-e3fb298b8105_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1751592314044_127.0.0.1_9428
11:41:47.772 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751592314044_127.0.0.1_9428
11:41:47.783 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07d3b01-535d-4637-b47b-e3fb298b8105_config-0] Notify disconnected event to listeners
11:41:47.786 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07d3b01-535d-4637-b47b-e3fb298b8105_config-0] Notify connected event to listeners.
11:41:47.799 [nacos-grpc-client-executor-1653] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751592314044_127.0.0.1_9428]Ignore complete event,isRunning:true,isAbandon=true
11:41:48.541 [nacos-grpc-client-executor-1649] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 22
11:41:48.669 [nacos-grpc-client-executor-1649] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 22
11:41:51.314 [nacos-grpc-client-executor-1650] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 28
11:41:51.344 [nacos-grpc-client-executor-1650] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 28
11:42:04.205 [nacos-grpc-client-executor-1653] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 31
11:42:04.238 [nacos-grpc-client-executor-1653] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 31
11:43:00.781 [nacos-grpc-client-executor-1665] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 34
11:43:00.810 [nacos-grpc-client-executor-1665] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 34
11:55:24.956 [nacos-grpc-client-executor-1815] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 37
11:55:24.992 [nacos-grpc-client-executor-1815] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 37
11:55:48.401 [nacos-grpc-client-executor-1820] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 39
11:55:48.427 [nacos-grpc-client-executor-1820] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 39
11:57:39.026 [nacos-grpc-client-executor-1842] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 42
11:57:39.047 [nacos-grpc-client-executor-1842] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 42
11:58:07.951 [nacos-grpc-client-executor-1848] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 45
11:58:07.972 [nacos-grpc-client-executor-1848] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 45
11:59:27.943 [nacos-grpc-client-executor-1864] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 47
11:59:27.960 [nacos-grpc-client-executor-1864] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 47
11:59:54.711 [nacos-grpc-client-executor-1869] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 50
11:59:54.736 [nacos-grpc-client-executor-1869] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 50
12:01:59.247 [nacos-grpc-client-executor-1895] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 52
12:01:59.261 [nacos-grpc-client-executor-1895] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 52
12:02:22.442 [nacos-grpc-client-executor-1900] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 54
12:02:22.472 [nacos-grpc-client-executor-1900] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 54
12:28:39.850 [nacos-grpc-client-executor-2235] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 57
12:28:39.874 [nacos-grpc-client-executor-2235] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 57
12:29:00.052 [nacos-grpc-client-executor-2239] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 59
12:29:00.106 [nacos-grpc-client-executor-2239] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 59
12:34:11.572 [nacos-grpc-client-executor-2305] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 62
12:34:11.591 [nacos-grpc-client-executor-2305] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 62
12:34:30.505 [nacos-grpc-client-executor-2310] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 65
12:34:30.519 [nacos-grpc-client-executor-2310] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 65
13:04:24.560 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Server healthy check fail, currentConnection = 1751592322466_127.0.0.1_9647
13:04:24.562 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Try to reconnect to a new server, server is  not appointed, will choose a random server.
13:04:31.488 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07d3b01-535d-4637-b47b-e3fb298b8105_config-0] Server healthy check fail, currentConnection = 1751600507657_127.0.0.1_13705
13:04:31.488 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07d3b01-535d-4637-b47b-e3fb298b8105_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
13:04:54.248 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07d3b01-535d-4637-b47b-e3fb298b8105_config-0] Success to connect a server [localhost:8848], connectionId = 1751605494093_127.0.0.1_6541
13:04:54.248 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07d3b01-535d-4637-b47b-e3fb298b8105_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1751600507657_127.0.0.1_13705
13:04:54.248 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751600507657_127.0.0.1_13705
13:04:54.248 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07d3b01-535d-4637-b47b-e3fb298b8105_config-0] Notify disconnected event to listeners
13:04:54.248 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b07d3b01-535d-4637-b47b-e3fb298b8105_config-0] Notify connected event to listeners.
13:04:54.268 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Success to connect a server [localhost:8848], connectionId = 1751605494129_127.0.0.1_6551
13:04:54.268 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Abandon prev connection, server is localhost:8848, connectionId is 1751592322466_127.0.0.1_9647
13:04:54.268 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751592322466_127.0.0.1_9647
13:04:54.268 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Notify disconnected event to listeners
13:04:54.281 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Notify connected event to listeners.
13:04:55.887 [nacos-grpc-client-executor-2677] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 66
13:04:55.887 [nacos-grpc-client-executor-2677] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 66
13:04:55.887 [nacos-grpc-client-executor-2677] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 67
13:04:56.044 [nacos-grpc-client-executor-2677] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 67
13:04:56.422 [nacos-grpc-client-executor-2678] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 68
13:04:56.436 [nacos-grpc-client-executor-2678] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 68
13:04:58.896 [nacos-grpc-client-executor-2680] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 69
13:04:58.967 [nacos-grpc-client-executor-2680] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 69
13:04:59.610 [nacos-grpc-client-executor-2681] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 77
13:04:59.848 [nacos-grpc-client-executor-2681] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 77
13:21:17.310 [nacos-grpc-client-executor-2892] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 80
13:21:17.326 [nacos-grpc-client-executor-2892] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 80
13:21:34.958 [nacos-grpc-client-executor-2895] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Receive server push request, request = NotifySubscriberRequest, requestId = 82
13:21:35.064 [nacos-grpc-client-executor-2895] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3db80b86-8121-4b54-80ab-dfb542bba8af] Ack server push request, request = NotifySubscriberRequest, requestId = 82
15:09:03.364 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:09:03.373 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:09:03.738 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:09:03.738 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@375bd6e5[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:09:03.740 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1751605494129_127.0.0.1_6551
15:09:03.742 [nacos-grpc-client-executor-4239] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1751605494129_127.0.0.1_6551]Ignore complete event,isRunning:false,isAbandon=false
15:09:03.742 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@45d70072[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 4240]
