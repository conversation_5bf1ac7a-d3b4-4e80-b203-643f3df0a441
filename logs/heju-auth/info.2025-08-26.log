09:44:41.979 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:44:43.606 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ac4649c8-a8bc-4ca8-8d47-2fd2391680f2_config-0
09:44:43.748 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 75 ms to scan 1 urls, producing 3 keys and 6 values 
09:44:43.823 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 38 ms to scan 1 urls, producing 4 keys and 9 values 
09:44:43.845 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 20 ms to scan 1 urls, producing 3 keys and 10 values 
09:44:43.871 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 20 ms to scan 1 urls, producing 1 keys and 5 values 
09:44:43.871 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 0 ms to scan 1 urls, producing 1 keys and 7 values 
09:44:43.899 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
09:44:43.899 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac4649c8-a8bc-4ca8-8d47-2fd2391680f2_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:44:43.899 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac4649c8-a8bc-4ca8-8d47-2fd2391680f2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001d8813b7d80
09:44:43.899 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac4649c8-a8bc-4ca8-8d47-2fd2391680f2_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001d8813b8000
09:44:43.907 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac4649c8-a8bc-4ca8-8d47-2fd2391680f2_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:44:43.907 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac4649c8-a8bc-4ca8-8d47-2fd2391680f2_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:44:43.925 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac4649c8-a8bc-4ca8-8d47-2fd2391680f2_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:44:46.051 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac4649c8-a8bc-4ca8-8d47-2fd2391680f2_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756172685659_127.0.0.1_2320
09:44:46.051 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac4649c8-a8bc-4ca8-8d47-2fd2391680f2_config-0] Notify connected event to listeners.
09:44:46.051 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac4649c8-a8bc-4ca8-8d47-2fd2391680f2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:44:46.051 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ac4649c8-a8bc-4ca8-8d47-2fd2391680f2_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001d8814f0440
09:44:46.388 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:44:50.514 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:44:50.514 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:44:50.514 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:44:50.777 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:44:53.307 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:44:56.215 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6778ddf5-e360-44c2-9d89-fb9924e60444
09:44:56.215 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6778ddf5-e360-44c2-9d89-fb9924e60444] RpcClient init label, labels = {module=naming, source=sdk}
09:44:56.215 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6778ddf5-e360-44c2-9d89-fb9924e60444] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:44:56.215 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6778ddf5-e360-44c2-9d89-fb9924e60444] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:44:56.225 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6778ddf5-e360-44c2-9d89-fb9924e60444] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:44:56.225 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6778ddf5-e360-44c2-9d89-fb9924e60444] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:44:56.355 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6778ddf5-e360-44c2-9d89-fb9924e60444] Success to connect to server [localhost:8848] on start up, connectionId = 1756172696225_127.0.0.1_2331
09:44:56.355 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6778ddf5-e360-44c2-9d89-fb9924e60444] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:44:56.355 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6778ddf5-e360-44c2-9d89-fb9924e60444] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001d8814f0440
09:44:56.355 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6778ddf5-e360-44c2-9d89-fb9924e60444] Notify connected event to listeners.
09:44:56.474 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:44:56.551 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
09:44:56.848 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 16.215 seconds (JVM running for 24.035)
09:44:56.867 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:44:56.869 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:44:56.872 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:44:57.341 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6778ddf5-e360-44c2-9d89-fb9924e60444] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:44:57.360 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6778ddf5-e360-44c2-9d89-fb9924e60444] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:46:56.619 [http-nio-9200-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:47:05.564 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6778ddf5-e360-44c2-9d89-fb9924e60444] Receive server push request, request = NotifySubscriberRequest, requestId = 11
09:47:05.565 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6778ddf5-e360-44c2-9d89-fb9924e60444] Ack server push request, request = NotifySubscriberRequest, requestId = 11
09:47:11.432 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6778ddf5-e360-44c2-9d89-fb9924e60444] Receive server push request, request = NotifySubscriberRequest, requestId = 12
09:47:11.436 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6778ddf5-e360-44c2-9d89-fb9924e60444] Ack server push request, request = NotifySubscriberRequest, requestId = 12
16:34:09.982 [nacos-grpc-client-executor-4928] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6778ddf5-e360-44c2-9d89-fb9924e60444] Receive server push request, request = NotifySubscriberRequest, requestId = 16
16:34:10.000 [nacos-grpc-client-executor-4928] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6778ddf5-e360-44c2-9d89-fb9924e60444] Ack server push request, request = NotifySubscriberRequest, requestId = 16
16:34:49.001 [nacos-grpc-client-executor-4937] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6778ddf5-e360-44c2-9d89-fb9924e60444] Receive server push request, request = NotifySubscriberRequest, requestId = 19
16:34:49.005 [nacos-grpc-client-executor-4937] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6778ddf5-e360-44c2-9d89-fb9924e60444] Ack server push request, request = NotifySubscriberRequest, requestId = 19
20:31:06.361 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:31:06.369 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:31:06.694 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:31:06.694 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@713748dc[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:31:06.694 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756172696225_127.0.0.1_2331
20:31:06.696 [nacos-grpc-client-executor-7772] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756172696225_127.0.0.1_2331]Ignore complete event,isRunning:false,isAbandon=false
20:31:06.699 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@323bb114[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 7773]
