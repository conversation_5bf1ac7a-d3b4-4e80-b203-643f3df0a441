10:16:35.111 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:16:35.764 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4bd3f5f8-e7c8-4c00-b665-7851751af741_config-0
10:16:35.817 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 25 ms to scan 1 urls, producing 3 keys and 6 values 
10:16:35.843 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 4 keys and 9 values 
10:16:35.851 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 3 keys and 10 values 
10:16:35.862 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 1 keys and 5 values 
10:16:35.870 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 1 keys and 7 values 
10:16:35.879 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
10:16:35.882 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4bd3f5f8-e7c8-4c00-b665-7851751af741_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:16:35.882 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4bd3f5f8-e7c8-4c00-b665-7851751af741_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000002048d3b7d80
10:16:35.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4bd3f5f8-e7c8-4c00-b665-7851751af741_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000002048d3b8000
10:16:35.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4bd3f5f8-e7c8-4c00-b665-7851751af741_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:16:35.884 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4bd3f5f8-e7c8-4c00-b665-7851751af741_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:16:35.892 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4bd3f5f8-e7c8-4c00-b665-7851751af741_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:16:37.814 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4bd3f5f8-e7c8-4c00-b665-7851751af741_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756001797298_127.0.0.1_11185
10:16:37.815 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4bd3f5f8-e7c8-4c00-b665-7851751af741_config-0] Notify connected event to listeners.
10:16:37.816 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4bd3f5f8-e7c8-4c00-b665-7851751af741_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:16:37.817 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4bd3f5f8-e7c8-4c00-b665-7851751af741_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002048d4f0228
10:16:38.016 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:16:42.162 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
10:16:42.163 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
10:16:42.164 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
10:16:42.409 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
10:16:45.896 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
10:16:49.053 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 06722363-7454-42ea-8b3e-506ef0557419
10:16:49.054 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06722363-7454-42ea-8b3e-506ef0557419] RpcClient init label, labels = {module=naming, source=sdk}
10:16:49.058 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06722363-7454-42ea-8b3e-506ef0557419] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:16:49.058 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06722363-7454-42ea-8b3e-506ef0557419] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:16:49.060 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06722363-7454-42ea-8b3e-506ef0557419] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:16:49.062 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06722363-7454-42ea-8b3e-506ef0557419] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:16:49.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06722363-7454-42ea-8b3e-506ef0557419] Success to connect to server [localhost:8848] on start up, connectionId = 1756001809076_127.0.0.1_11217
10:16:49.194 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06722363-7454-42ea-8b3e-506ef0557419] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:16:49.195 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06722363-7454-42ea-8b3e-506ef0557419] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000002048d4f0228
10:16:49.195 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06722363-7454-42ea-8b3e-506ef0557419] Notify connected event to listeners.
10:16:49.275 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
10:16:49.329 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
10:16:49.654 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 15.218 seconds (JVM running for 21.361)
10:16:49.677 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
10:16:49.679 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
10:16:49.683 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
10:16:49.783 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06722363-7454-42ea-8b3e-506ef0557419] Receive server push request, request = NotifySubscriberRequest, requestId = 3
10:16:49.806 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06722363-7454-42ea-8b3e-506ef0557419] Ack server push request, request = NotifySubscriberRequest, requestId = 3
10:20:29.729 [http-nio-9200-exec-1] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
10:20:49.449 [nacos-grpc-client-executor-57] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06722363-7454-42ea-8b3e-506ef0557419] Receive server push request, request = NotifySubscriberRequest, requestId = 11
10:20:49.450 [nacos-grpc-client-executor-57] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06722363-7454-42ea-8b3e-506ef0557419] Ack server push request, request = NotifySubscriberRequest, requestId = 11
10:20:52.232 [nacos-grpc-client-executor-61] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06722363-7454-42ea-8b3e-506ef0557419] Receive server push request, request = NotifySubscriberRequest, requestId = 12
10:20:52.234 [nacos-grpc-client-executor-61] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [06722363-7454-42ea-8b3e-506ef0557419] Ack server push request, request = NotifySubscriberRequest, requestId = 12
18:54:36.313 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:54:36.318 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:54:36.659 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:54:36.659 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@466be3d2[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:54:36.660 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756001809076_127.0.0.1_11217
18:54:36.661 [nacos-grpc-client-executor-6951] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756001809076_127.0.0.1_11217]Ignore complete event,isRunning:false,isAbandon=false
18:54:36.671 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4301c478[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 6952]
