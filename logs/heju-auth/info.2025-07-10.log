09:15:57.946 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:15:59.530 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1bcd2f91-6540-42ba-84d4-f71c97997296_config-0
09:15:59.661 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 72 ms to scan 1 urls, producing 3 keys and 6 values 
09:15:59.721 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 27 ms to scan 1 urls, producing 4 keys and 9 values 
09:15:59.738 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 13 ms to scan 1 urls, producing 3 keys and 10 values 
09:15:59.754 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 1 keys and 5 values 
09:15:59.774 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 20 ms to scan 1 urls, producing 1 keys and 7 values 
09:15:59.789 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
09:15:59.798 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:15:59.800 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000001e33739a328
09:15:59.800 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000001e33739a548
09:15:59.802 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:15:59.804 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:15:59.818 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:01.617 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:01.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:01.649 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:16:01.649 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:16:01.651 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001e3374ea228
09:16:01.816 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:02.028 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:02.348 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:02.765 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:03.282 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:03.440 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:16:03.904 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:04.621 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:05.436 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:06.352 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:07.369 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:07.853 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:16:07.854 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:16:07.855 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:16:08.166 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:16:08.609 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:09.952 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:10.922 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:16:11.355 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:12.864 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:13.400 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8814ceb7-1ade-4a06-972c-608239f8f9b1
09:16:13.400 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8814ceb7-1ade-4a06-972c-608239f8f9b1] RpcClient init label, labels = {module=naming, source=sdk}
09:16:13.414 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8814ceb7-1ade-4a06-972c-608239f8f9b1] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:16:13.415 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8814ceb7-1ade-4a06-972c-608239f8f9b1] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:16:13.416 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8814ceb7-1ade-4a06-972c-608239f8f9b1] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:16:13.417 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8814ceb7-1ade-4a06-972c-608239f8f9b1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:13.443 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8814ceb7-1ade-4a06-972c-608239f8f9b1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:13.462 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8814ceb7-1ade-4a06-972c-608239f8f9b1] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:13.472 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8814ceb7-1ade-4a06-972c-608239f8f9b1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:16:13.472 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8814ceb7-1ade-4a06-972c-608239f8f9b1] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:16:13.472 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8814ceb7-1ade-4a06-972c-608239f8f9b1] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001e3374ea228
09:16:13.643 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8814ceb7-1ade-4a06-972c-608239f8f9b1] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:13.817 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:16:13.852 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8814ceb7-1ade-4a06-972c-608239f8f9b1] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:14.173 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8814ceb7-1ade-4a06-972c-608239f8f9b1] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:14.393 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:14.590 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8814ceb7-1ade-4a06-972c-608239f8f9b1] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:14.830 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:16:14.831 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@28b286c0[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:16:14.832 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8814ceb7-1ade-4a06-972c-608239f8f9b1] Client is shutdown, stop reconnect to server
09:16:14.832 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@718e700c[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
09:16:14.842 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 116671d8-4571-49c5-bc11-1879b3ca99f8
09:16:14.842 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [116671d8-4571-49c5-bc11-1879b3ca99f8] RpcClient init label, labels = {module=naming, source=sdk}
09:16:14.842 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [116671d8-4571-49c5-bc11-1879b3ca99f8] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:16:14.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [116671d8-4571-49c5-bc11-1879b3ca99f8] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:16:14.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [116671d8-4571-49c5-bc11-1879b3ca99f8] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:16:14.843 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [116671d8-4571-49c5-bc11-1879b3ca99f8] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:14.967 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [116671d8-4571-49c5-bc11-1879b3ca99f8] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:14.977 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [116671d8-4571-49c5-bc11-1879b3ca99f8] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:16:14.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [116671d8-4571-49c5-bc11-1879b3ca99f8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:16:14.985 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [116671d8-4571-49c5-bc11-1879b3ca99f8] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000001e3374ea228
09:16:14.988 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [116671d8-4571-49c5-bc11-1879b3ca99f8] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:16:15.114 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [116671d8-4571-49c5-bc11-1879b3ca99f8] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:15.330 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [116671d8-4571-49c5-bc11-1879b3ca99f8] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:15.365 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9200"]
09:16:15.365 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:16:15.375 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9200"]
09:16:15.381 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9200"]
09:16:15.639 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [116671d8-4571-49c5-bc11-1879b3ca99f8] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:16.007 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1bcd2f91-6540-42ba-84d4-f71c97997296_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:16:16.055 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [116671d8-4571-49c5-bc11-1879b3ca99f8] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:18:39.669 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:18:40.367 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 65b7531b-1079-452a-8a11-cfa84217cd38_config-0
09:18:40.449 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 42 ms to scan 1 urls, producing 3 keys and 6 values 
09:18:40.474 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
09:18:40.488 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 3 keys and 10 values 
09:18:40.500 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 5 values 
09:18:40.511 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:18:40.519 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
09:18:40.524 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65b7531b-1079-452a-8a11-cfa84217cd38_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:18:40.525 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65b7531b-1079-452a-8a11-cfa84217cd38_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x0000010e9939b188
09:18:40.525 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65b7531b-1079-452a-8a11-cfa84217cd38_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x0000010e9939b3a8
09:18:40.526 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65b7531b-1079-452a-8a11-cfa84217cd38_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:18:40.527 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65b7531b-1079-452a-8a11-cfa84217cd38_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:18:40.540 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65b7531b-1079-452a-8a11-cfa84217cd38_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:18:41.766 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65b7531b-1079-452a-8a11-cfa84217cd38_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752110321399_127.0.0.1_4262
09:18:41.768 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65b7531b-1079-452a-8a11-cfa84217cd38_config-0] Notify connected event to listeners.
09:18:41.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65b7531b-1079-452a-8a11-cfa84217cd38_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:41.770 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [65b7531b-1079-452a-8a11-cfa84217cd38_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000010e99515288
09:18:42.066 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:18:45.635 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:18:45.639 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:18:45.641 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:18:46.050 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:18:50.223 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:18:52.688 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9e4643f7-c19a-40ad-b5b7-11ab404d2605
09:18:52.688 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e4643f7-c19a-40ad-b5b7-11ab404d2605] RpcClient init label, labels = {module=naming, source=sdk}
09:18:52.691 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e4643f7-c19a-40ad-b5b7-11ab404d2605] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:18:52.691 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e4643f7-c19a-40ad-b5b7-11ab404d2605] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:18:52.693 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e4643f7-c19a-40ad-b5b7-11ab404d2605] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:18:52.693 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e4643f7-c19a-40ad-b5b7-11ab404d2605] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:18:52.827 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e4643f7-c19a-40ad-b5b7-11ab404d2605] Success to connect to server [localhost:8848] on start up, connectionId = 1752110332707_127.0.0.1_4364
09:18:52.827 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e4643f7-c19a-40ad-b5b7-11ab404d2605] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:52.827 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e4643f7-c19a-40ad-b5b7-11ab404d2605] Notify connected event to listeners.
09:18:52.827 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e4643f7-c19a-40ad-b5b7-11ab404d2605] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000010e99515288
09:18:52.896 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:18:52.936 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ***********:9200 register finished
09:18:53.263 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 14.446 seconds (JVM running for 15.936)
09:18:53.280 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:18:53.281 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:18:53.283 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:18:53.513 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e4643f7-c19a-40ad-b5b7-11ab404d2605] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:18:53.567 [nacos-grpc-client-executor-8] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e4643f7-c19a-40ad-b5b7-11ab404d2605] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:18:53.848 [RMI TCP Connection(4)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:35:47.352 [nacos-grpc-client-executor-220] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e4643f7-c19a-40ad-b5b7-11ab404d2605] Receive server push request, request = NotifySubscriberRequest, requestId = 11
09:35:47.354 [nacos-grpc-client-executor-220] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9e4643f7-c19a-40ad-b5b7-11ab404d2605] Ack server push request, request = NotifySubscriberRequest, requestId = 11
13:38:02.163 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:38:02.170 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:38:02.507 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:38:02.508 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1115264[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:38:02.509 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752110332707_127.0.0.1_4364
13:38:02.513 [nacos-grpc-client-executor-3127] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752110332707_127.0.0.1_4364]Ignore complete event,isRunning:false,isAbandon=false
13:38:02.521 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5a634fe4[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 3128]
13:44:55.431 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:44:56.077 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 809251bf-5289-43c7-9423-38848def8c58_config-0
13:44:56.155 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 42 ms to scan 1 urls, producing 3 keys and 6 values 
13:44:56.175 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 4 keys and 9 values 
13:44:56.189 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 3 keys and 10 values 
13:44:56.198 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
13:44:56.206 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
13:44:56.218 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 2 keys and 8 values 
13:44:56.218 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [809251bf-5289-43c7-9423-38848def8c58_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:44:56.224 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [809251bf-5289-43c7-9423-38848def8c58_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002139339af18
13:44:56.224 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [809251bf-5289-43c7-9423-38848def8c58_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002139339b138
13:44:56.224 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [809251bf-5289-43c7-9423-38848def8c58_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:44:56.224 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [809251bf-5289-43c7-9423-38848def8c58_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:44:56.232 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [809251bf-5289-43c7-9423-38848def8c58_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:44:57.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [809251bf-5289-43c7-9423-38848def8c58_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752126297039_127.0.0.1_10383
13:44:57.260 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [809251bf-5289-43c7-9423-38848def8c58_config-0] Notify connected event to listeners.
13:44:57.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [809251bf-5289-43c7-9423-38848def8c58_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:44:57.260 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [809251bf-5289-43c7-9423-38848def8c58_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000021393515418
13:44:57.383 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:45:00.581 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
13:45:00.583 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
13:45:00.583 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
13:45:00.802 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
13:45:04.530 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
13:45:07.911 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ed7faf54-645f-47e7-9b6c-122fde6731ff
13:45:07.913 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7faf54-645f-47e7-9b6c-122fde6731ff] RpcClient init label, labels = {module=naming, source=sdk}
13:45:07.922 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7faf54-645f-47e7-9b6c-122fde6731ff] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:45:07.922 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7faf54-645f-47e7-9b6c-122fde6731ff] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:45:07.924 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7faf54-645f-47e7-9b6c-122fde6731ff] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:45:07.927 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7faf54-645f-47e7-9b6c-122fde6731ff] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:45:08.088 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7faf54-645f-47e7-9b6c-122fde6731ff] Success to connect to server [localhost:8848] on start up, connectionId = 1752126307950_127.0.0.1_10402
13:45:08.088 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7faf54-645f-47e7-9b6c-122fde6731ff] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:45:08.089 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7faf54-645f-47e7-9b6c-122fde6731ff] Notify connected event to listeners.
13:45:08.089 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7faf54-645f-47e7-9b6c-122fde6731ff] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000021393515418
13:45:08.220 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
13:45:08.308 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ***********:9200 register finished
13:45:08.743 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7faf54-645f-47e7-9b6c-122fde6731ff] Receive server push request, request = NotifySubscriberRequest, requestId = 23
13:45:08.778 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7faf54-645f-47e7-9b6c-122fde6731ff] Ack server push request, request = NotifySubscriberRequest, requestId = 23
13:45:08.841 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 14.034 seconds (JVM running for 14.955)
13:45:08.866 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
13:45:08.872 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
13:45:08.895 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
13:45:09.245 [RMI TCP Connection(4)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:20:37.905 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:20:37.908 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:20:38.230 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:20:38.230 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@72f57d87[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:20:38.230 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752126307950_127.0.0.1_10402
14:20:38.231 [nacos-grpc-client-executor-442] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752126307950_127.0.0.1_10402]Ignore complete event,isRunning:false,isAbandon=false
14:20:38.233 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6f417f05[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 443]
14:31:45.244 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:31:46.104 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1b659243-63f3-449c-94a1-952745ce5e09_config-0
14:31:46.184 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 41 ms to scan 1 urls, producing 3 keys and 6 values 
14:31:46.201 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 2 ms to scan 1 urls, producing 4 keys and 9 values 
14:31:46.221 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 3 keys and 10 values 
14:31:46.233 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 5 values 
14:31:46.247 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
14:31:46.265 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 2 keys and 8 values 
14:31:46.270 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b659243-63f3-449c-94a1-952745ce5e09_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:31:46.271 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b659243-63f3-449c-94a1-952745ce5e09_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002324a39b3d8
14:31:46.271 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b659243-63f3-449c-94a1-952745ce5e09_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002324a39b5f8
14:31:46.272 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b659243-63f3-449c-94a1-952745ce5e09_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:31:46.272 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b659243-63f3-449c-94a1-952745ce5e09_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:31:46.334 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b659243-63f3-449c-94a1-952745ce5e09_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:31:47.410 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b659243-63f3-449c-94a1-952745ce5e09_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752129107194_127.0.0.1_1677
14:31:47.411 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b659243-63f3-449c-94a1-952745ce5e09_config-0] Notify connected event to listeners.
14:31:47.411 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b659243-63f3-449c-94a1-952745ce5e09_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:31:47.413 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b659243-63f3-449c-94a1-952745ce5e09_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002324a514f98
14:31:47.503 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:31:49.751 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
14:31:49.752 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
14:31:49.752 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
14:31:49.915 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
14:31:51.637 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
14:31:52.859 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9d392280-6e5f-45f8-b940-6c5c016b929f
14:31:52.860 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d392280-6e5f-45f8-b940-6c5c016b929f] RpcClient init label, labels = {module=naming, source=sdk}
14:31:52.862 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d392280-6e5f-45f8-b940-6c5c016b929f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:31:52.862 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d392280-6e5f-45f8-b940-6c5c016b929f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:31:52.863 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d392280-6e5f-45f8-b940-6c5c016b929f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:31:52.863 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d392280-6e5f-45f8-b940-6c5c016b929f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:31:52.992 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d392280-6e5f-45f8-b940-6c5c016b929f] Success to connect to server [localhost:8848] on start up, connectionId = 1752129112875_127.0.0.1_1700
14:31:52.993 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d392280-6e5f-45f8-b940-6c5c016b929f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:31:52.993 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d392280-6e5f-45f8-b940-6c5c016b929f] Notify connected event to listeners.
14:31:52.993 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d392280-6e5f-45f8-b940-6c5c016b929f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002324a514f98
14:31:53.046 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
14:31:53.068 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ***********:9200 register finished
14:31:53.299 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 8.919 seconds (JVM running for 10.393)
14:31:53.322 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
14:31:53.323 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
14:31:53.343 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
14:31:53.580 [RMI TCP Connection(6)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
14:31:53.620 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d392280-6e5f-45f8-b940-6c5c016b929f] Receive server push request, request = NotifySubscriberRequest, requestId = 40
14:31:53.637 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9d392280-6e5f-45f8-b940-6c5c016b929f] Ack server push request, request = NotifySubscriberRequest, requestId = 40
14:58:53.141 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:58:53.145 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:58:53.481 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:58:53.481 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@17e64ab6[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:58:53.481 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752129112875_127.0.0.1_1700
14:58:53.484 [nacos-grpc-client-executor-337] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752129112875_127.0.0.1_1700]Ignore complete event,isRunning:false,isAbandon=false
14:58:53.487 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@293d2bc3[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 338]
16:21:20.659 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:21:21.095 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e337f900-974a-4b2f-a8eb-bf7b4be6f6db_config-0
16:21:21.139 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 3 keys and 6 values 
16:21:21.160 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
16:21:21.166 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
16:21:21.173 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
16:21:21.181 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 1 keys and 7 values 
16:21:21.187 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 2 keys and 8 values 
16:21:21.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e337f900-974a-4b2f-a8eb-bf7b4be6f6db_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:21:21.190 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e337f900-974a-4b2f-a8eb-bf7b4be6f6db_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x000001fa483b42b8
16:21:21.190 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e337f900-974a-4b2f-a8eb-bf7b4be6f6db_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x000001fa483b44d8
16:21:21.191 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e337f900-974a-4b2f-a8eb-bf7b4be6f6db_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:21:21.191 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e337f900-974a-4b2f-a8eb-bf7b4be6f6db_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:21:21.196 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e337f900-974a-4b2f-a8eb-bf7b4be6f6db_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:21:21.895 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e337f900-974a-4b2f-a8eb-bf7b4be6f6db_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752135681711_127.0.0.1_9908
16:21:21.895 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e337f900-974a-4b2f-a8eb-bf7b4be6f6db_config-0] Notify connected event to listeners.
16:21:21.896 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e337f900-974a-4b2f-a8eb-bf7b4be6f6db_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:21:21.896 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e337f900-974a-4b2f-a8eb-bf7b4be6f6db_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001fa484ec200
16:21:21.983 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:21:23.286 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
16:21:23.286 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
16:21:23.286 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
16:21:23.376 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
16:21:24.353 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
16:21:25.290 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2e80d00f-eaae-4c60-87a8-f3e64472339f
16:21:25.290 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e80d00f-eaae-4c60-87a8-f3e64472339f] RpcClient init label, labels = {module=naming, source=sdk}
16:21:25.292 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e80d00f-eaae-4c60-87a8-f3e64472339f] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:21:25.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e80d00f-eaae-4c60-87a8-f3e64472339f] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:21:25.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e80d00f-eaae-4c60-87a8-f3e64472339f] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:21:25.293 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e80d00f-eaae-4c60-87a8-f3e64472339f] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:21:25.423 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e80d00f-eaae-4c60-87a8-f3e64472339f] Success to connect to server [localhost:8848] on start up, connectionId = 1752135685301_127.0.0.1_9916
16:21:25.423 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e80d00f-eaae-4c60-87a8-f3e64472339f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:21:25.423 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e80d00f-eaae-4c60-87a8-f3e64472339f] Notify connected event to listeners.
16:21:25.423 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e80d00f-eaae-4c60-87a8-f3e64472339f] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x000001fa484ec200
16:21:25.460 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
16:21:25.478 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ***********:9200 register finished
16:21:25.601 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 5.348 seconds (JVM running for 7.525)
16:21:25.609 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
16:21:25.609 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
16:21:25.611 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
16:21:26.000 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e80d00f-eaae-4c60-87a8-f3e64472339f] Receive server push request, request = NotifySubscriberRequest, requestId = 55
16:21:26.013 [nacos-grpc-client-executor-5] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2e80d00f-eaae-4c60-87a8-f3e64472339f] Ack server push request, request = NotifySubscriberRequest, requestId = 55
16:40:01.600 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:40:01.600 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:40:01.957 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:40:01.957 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@13776edf[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:40:01.957 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752135685301_127.0.0.1_9916
16:40:01.962 [nacos-grpc-client-executor-231] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752135685301_127.0.0.1_9916]Ignore complete event,isRunning:false,isAbandon=false
16:40:01.966 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@935595f[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 232]
18:20:53.867 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:20:54.430 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 681ad7e7-6bbe-4b1c-8d81-f228e551edb6_config-0
18:20:54.499 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 43 ms to scan 1 urls, producing 3 keys and 6 values 
18:20:54.523 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
18:20:54.531 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 3 keys and 10 values 
18:20:54.540 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 5 values 
18:20:54.550 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 1 keys and 7 values 
18:20:54.562 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
18:20:54.565 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [681ad7e7-6bbe-4b1c-8d81-f228e551edb6_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:20:54.566 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [681ad7e7-6bbe-4b1c-8d81-f228e551edb6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002f1243c9ed0
18:20:54.566 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [681ad7e7-6bbe-4b1c-8d81-f228e551edb6_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002f1243ca0f0
18:20:54.568 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [681ad7e7-6bbe-4b1c-8d81-f228e551edb6_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:20:54.569 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [681ad7e7-6bbe-4b1c-8d81-f228e551edb6_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:20:54.575 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [681ad7e7-6bbe-4b1c-8d81-f228e551edb6_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:20:55.519 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [681ad7e7-6bbe-4b1c-8d81-f228e551edb6_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752142855298_127.0.0.1_8959
18:20:55.521 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [681ad7e7-6bbe-4b1c-8d81-f228e551edb6_config-0] Notify connected event to listeners.
18:20:55.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [681ad7e7-6bbe-4b1c-8d81-f228e551edb6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:20:55.522 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [681ad7e7-6bbe-4b1c-8d81-f228e551edb6_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002f1245042a8
18:20:55.666 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:20:57.743 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
18:20:57.745 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
18:20:57.745 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
18:20:57.871 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
18:20:59.280 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
18:21:00.471 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d4e1a5fd-d7cf-4263-85de-e2ae4f81436d
18:21:00.471 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4e1a5fd-d7cf-4263-85de-e2ae4f81436d] RpcClient init label, labels = {module=naming, source=sdk}
18:21:00.473 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4e1a5fd-d7cf-4263-85de-e2ae4f81436d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:21:00.473 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4e1a5fd-d7cf-4263-85de-e2ae4f81436d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:21:00.473 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4e1a5fd-d7cf-4263-85de-e2ae4f81436d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:21:00.474 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4e1a5fd-d7cf-4263-85de-e2ae4f81436d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:21:00.608 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4e1a5fd-d7cf-4263-85de-e2ae4f81436d] Success to connect to server [localhost:8848] on start up, connectionId = 1752142860484_127.0.0.1_8970
18:21:00.608 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4e1a5fd-d7cf-4263-85de-e2ae4f81436d] Notify connected event to listeners.
18:21:00.609 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4e1a5fd-d7cf-4263-85de-e2ae4f81436d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:21:00.610 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4e1a5fd-d7cf-4263-85de-e2ae4f81436d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x000002f1245042a8
18:21:00.661 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
18:21:00.692 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ***********:9200 register finished
18:21:00.820 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 7.52 seconds (JVM running for 8.577)
18:21:00.831 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
18:21:00.831 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
18:21:00.835 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
18:21:01.188 [RMI TCP Connection(4)-***********] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
18:21:01.237 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4e1a5fd-d7cf-4263-85de-e2ae4f81436d] Receive server push request, request = NotifySubscriberRequest, requestId = 75
18:21:01.254 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d4e1a5fd-d7cf-4263-85de-e2ae4f81436d] Ack server push request, request = NotifySubscriberRequest, requestId = 75
20:41:31.026 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:41:31.031 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:41:31.386 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:41:31.387 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@58acf237[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:41:31.388 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752142860484_127.0.0.1_8970
20:41:31.395 [nacos-grpc-client-executor-1695] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752142860484_127.0.0.1_8970]Ignore complete event,isRunning:false,isAbandon=false
20:41:31.401 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4d7197e4[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 1696]
