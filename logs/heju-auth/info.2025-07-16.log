09:18:52.164 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:18:55.224 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 58bc980c-365d-462b-9e04-c941f353643c_config-0
09:18:55.461 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 125 ms to scan 1 urls, producing 3 keys and 6 values 
09:18:55.554 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 34 ms to scan 1 urls, producing 4 keys and 9 values 
09:18:55.583 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 24 ms to scan 1 urls, producing 3 keys and 10 values 
09:18:55.638 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 44 ms to scan 1 urls, producing 1 keys and 5 values 
09:18:55.671 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 27 ms to scan 1 urls, producing 1 keys and 7 values 
09:18:55.708 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 2 keys and 8 values 
09:18:55.718 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:18:55.720 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$456/0x0000022b3e3b8200
09:18:55.722 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$457/0x0000022b3e3b8420
09:18:55.723 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:18:55.726 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:18:55.757 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:18:59.092 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:18:59.112 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:18:59.127 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:18:59.127 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:18:59.127 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000022b3e4c5f88
09:18:59.311 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:18:59.539 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:18:59.878 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:00.305 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:00.848 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Fail to connect server, after trying 5 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:01.118 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:19:01.544 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Fail to connect server, after trying 6 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:02.327 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Fail to connect server, after trying 7 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:03.162 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Fail to connect server, after trying 8 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:04.244 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Fail to connect server, after trying 9 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:05.570 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Fail to connect server, after trying 10 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:06.968 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Fail to connect server, after trying 11 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:08.430 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Fail to connect server, after trying 12 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:09.975 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Fail to connect server, after trying 13 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:10.227 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:19:10.230 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:19:10.230 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:19:10.800 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:19:12.075 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Fail to connect server, after trying 14 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:14.155 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Fail to connect server, after trying 15 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:16.208 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Fail to connect server, after trying 16 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:18.544 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Fail to connect server, after trying 17 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:20.342 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:19:20.795 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Fail to connect server, after trying 18 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:23.338 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Fail to connect server, after trying 19 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:25.882 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Fail to connect server, after trying 20 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:27.530 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 779569aa-a514-4f25-87af-7aede18f34bd
09:19:27.535 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [779569aa-a514-4f25-87af-7aede18f34bd] RpcClient init label, labels = {module=naming, source=sdk}
09:19:27.535 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [779569aa-a514-4f25-87af-7aede18f34bd] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:19:27.544 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [779569aa-a514-4f25-87af-7aede18f34bd] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:19:27.544 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [779569aa-a514-4f25-87af-7aede18f34bd] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:19:27.544 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [779569aa-a514-4f25-87af-7aede18f34bd] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:27.815 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [779569aa-a514-4f25-87af-7aede18f34bd] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:27.847 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [779569aa-a514-4f25-87af-7aede18f34bd] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:27.879 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [779569aa-a514-4f25-87af-7aede18f34bd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:19:27.879 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [779569aa-a514-4f25-87af-7aede18f34bd] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:19:27.879 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [779569aa-a514-4f25-87af-7aede18f34bd] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000022b3e4c5f88
09:19:28.042 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Fail to connect server, after trying 21 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:28.050 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [779569aa-a514-4f25-87af-7aede18f34bd] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:28.236 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:19:28.307 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [779569aa-a514-4f25-87af-7aede18f34bd] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:28.649 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [779569aa-a514-4f25-87af-7aede18f34bd] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:29.082 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [779569aa-a514-4f25-87af-7aede18f34bd] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:29.241 [main] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:19:29.241 [main] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@4c51fc9a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:19:29.241 [main] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@425376cc[Running, pool size = 16, active threads = 0, queued tasks = 0, completed tasks = 16]
09:19:29.241 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [779569aa-a514-4f25-87af-7aede18f34bd] Client is shutdown, stop reconnect to server
09:19:29.241 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b160974f-9a98-4755-b368-27b6334f676e
09:19:29.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b160974f-9a98-4755-b368-27b6334f676e] RpcClient init label, labels = {module=naming, source=sdk}
09:19:29.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b160974f-9a98-4755-b368-27b6334f676e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:19:29.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b160974f-9a98-4755-b368-27b6334f676e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:19:29.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b160974f-9a98-4755-b368-27b6334f676e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:19:29.241 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b160974f-9a98-4755-b368-27b6334f676e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:29.272 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b160974f-9a98-4755-b368-27b6334f676e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:29.288 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b160974f-9a98-4755-b368-27b6334f676e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:29.304 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b160974f-9a98-4755-b368-27b6334f676e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:19:29.304 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b160974f-9a98-4755-b368-27b6334f676e] Try to reconnect to a new server, server is  not appointed, will choose a random server.
09:19:29.304 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b160974f-9a98-4755-b368-27b6334f676e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$471/0x0000022b3e4c5f88
09:19:29.438 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b160974f-9a98-4755-b368-27b6334f676e] Fail to connect server, after trying 1 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:29.667 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b160974f-9a98-4755-b368-27b6334f676e] Fail to connect server, after trying 2 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:29.794 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Pausing ProtocolHandler ["http-nio-9200"]
09:19:29.794 [main] INFO  o.a.c.c.StandardService - [log,173] - Stopping service [Tomcat]
09:19:29.811 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Stopping ProtocolHandler ["http-nio-9200"]
09:19:29.825 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Destroying ProtocolHandler ["http-nio-9200"]
09:19:29.983 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b160974f-9a98-4755-b368-27b6334f676e] Fail to connect server, after trying 3 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:30.258 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [58bc980c-365d-462b-9e04-c941f353643c_config-0] Fail to connect server, after trying 22 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:19:30.416 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b160974f-9a98-4755-b368-27b6334f676e] Fail to connect server, after trying 4 times, last try server is {serverIp = 'localhost', server main port = 8848}, error = unknown
09:26:51.175 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:26:51.931 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of eb0ed8e8-ab1a-49ee-ac65-128324a0258d_config-0
09:26:51.995 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 46 ms to scan 1 urls, producing 3 keys and 6 values 
09:26:52.026 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 4 keys and 9 values 
09:26:52.041 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 3 keys and 10 values 
09:26:52.049 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
09:26:52.051 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 1 keys and 7 values 
09:26:52.068 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 2 keys and 8 values 
09:26:52.071 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0ed8e8-ab1a-49ee-ac65-128324a0258d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:26:52.071 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0ed8e8-ab1a-49ee-ac65-128324a0258d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$468/0x000002935939af18
09:26:52.071 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0ed8e8-ab1a-49ee-ac65-128324a0258d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$469/0x000002935939b138
09:26:52.071 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0ed8e8-ab1a-49ee-ac65-128324a0258d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:26:52.071 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0ed8e8-ab1a-49ee-ac65-128324a0258d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:26:52.082 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0ed8e8-ab1a-49ee-ac65-128324a0258d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:26:53.107 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0ed8e8-ab1a-49ee-ac65-128324a0258d_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1752629212882_127.0.0.1_12417
09:26:53.108 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0ed8e8-ab1a-49ee-ac65-128324a0258d_config-0] Notify connected event to listeners.
09:26:53.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0ed8e8-ab1a-49ee-ac65-128324a0258d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:26:53.108 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0ed8e8-ab1a-49ee-ac65-128324a0258d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000029359514d90
09:26:53.216 [main] INFO  c.h.a.HeJuAuthApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:26:55.393 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Initializing ProtocolHandler ["http-nio-9200"]
09:26:55.394 [main] INFO  o.a.c.c.StandardService - [log,173] - Starting service [Tomcat]
09:26:55.394 [main] INFO  o.a.c.c.StandardEngine - [log,173] - Starting Servlet engine: [Apache Tomcat/9.0.70]
09:26:55.514 [main] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring embedded WebApplicationContext
09:26:56.897 [main] INFO  c.a.c.s.SentinelWebMvcConfigurer - [addInterceptors,52] - [Sentinel Starter] register SentinelWebInterceptor with urlPatterns: [/**].
09:26:57.923 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c4c96167-cc58-4dab-a909-f4a4983850c4
09:26:57.923 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] RpcClient init label, labels = {module=naming, source=sdk}
09:26:57.924 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:26:57.924 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:26:57.925 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:26:57.925 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:26:58.051 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Success to connect to server [localhost:8848] on start up, connectionId = 1752629217933_127.0.0.1_12452
09:26:58.051 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Notify connected event to listeners.
09:26:58.051 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:26:58.052 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$483/0x0000029359514d90
09:26:58.094 [main] INFO  o.a.c.h.Http11NioProtocol - [log,173] - Starting ProtocolHandler ["http-nio-9200"]
09:26:58.120 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-auth ************:9200 register finished
09:26:58.242 [main] INFO  c.h.a.HeJuAuthApplication - [logStarted,61] - Started HeJuAuthApplication in 7.6 seconds (JVM running for 8.563)
09:26:58.253 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth, group=DEFAULT_GROUP
09:26:58.253 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth.yml, group=DEFAULT_GROUP
09:26:58.256 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-auth-dev.yml, group=DEFAULT_GROUP
09:26:58.569 [RMI TCP Connection(9)-************] INFO  o.a.c.c.C.[.[.[/] - [log,173] - Initializing Spring DispatcherServlet 'dispatcherServlet'
09:26:58.613 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:26:58.625 [nacos-grpc-client-executor-6] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:33:21.737 [nacos-grpc-client-executor-92] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Receive server push request, request = NotifySubscriberRequest, requestId = 11
09:33:21.738 [nacos-grpc-client-executor-92] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Ack server push request, request = NotifySubscriberRequest, requestId = 11
09:36:35.679 [nacos-grpc-client-executor-133] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Receive server push request, request = NotifySubscriberRequest, requestId = 15
09:36:35.679 [nacos-grpc-client-executor-133] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Ack server push request, request = NotifySubscriberRequest, requestId = 15
10:18:16.913 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0ed8e8-ab1a-49ee-ac65-128324a0258d_config-0] Server healthy check fail, currentConnection = 1752629212882_127.0.0.1_12417
10:18:16.913 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0ed8e8-ab1a-49ee-ac65-128324a0258d_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
10:18:23.818 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0ed8e8-ab1a-49ee-ac65-128324a0258d_config-0] Success to connect a server [localhost:8848], connectionId = 1752632303700_127.0.0.1_3109
10:18:23.819 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0ed8e8-ab1a-49ee-ac65-128324a0258d_config-0] Abandon prev connection, server is localhost:8848, connectionId is 1752629212882_127.0.0.1_12417
10:18:23.819 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752629212882_127.0.0.1_12417
10:18:23.821 [nacos-grpc-client-executor-638] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752629212882_127.0.0.1_12417]Ignore complete event,isRunning:false,isAbandon=true
10:18:23.822 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0ed8e8-ab1a-49ee-ac65-128324a0258d_config-0] Notify disconnected event to listeners
10:18:23.823 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [eb0ed8e8-ab1a-49ee-ac65-128324a0258d_config-0] Notify connected event to listeners.
14:01:00.082 [nacos-grpc-client-executor-3301] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Receive server push request, request = NotifySubscriberRequest, requestId = 19
14:01:00.113 [nacos-grpc-client-executor-3301] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Ack server push request, request = NotifySubscriberRequest, requestId = 19
14:01:52.490 [nacos-grpc-client-executor-3314] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Receive server push request, request = NotifySubscriberRequest, requestId = 23
14:01:52.511 [nacos-grpc-client-executor-3314] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Ack server push request, request = NotifySubscriberRequest, requestId = 23
15:31:23.071 [nacos-grpc-client-executor-4387] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Receive server push request, request = NotifySubscriberRequest, requestId = 26
15:31:23.101 [nacos-grpc-client-executor-4387] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Ack server push request, request = NotifySubscriberRequest, requestId = 26
15:31:49.726 [nacos-grpc-client-executor-4392] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Receive server push request, request = NotifySubscriberRequest, requestId = 30
15:31:49.753 [nacos-grpc-client-executor-4392] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Ack server push request, request = NotifySubscriberRequest, requestId = 30
15:40:37.266 [nacos-grpc-client-executor-4497] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Receive server push request, request = NotifySubscriberRequest, requestId = 33
15:40:37.269 [nacos-grpc-client-executor-4497] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Ack server push request, request = NotifySubscriberRequest, requestId = 33
15:41:03.132 [nacos-grpc-client-executor-4503] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Receive server push request, request = NotifySubscriberRequest, requestId = 36
15:41:03.154 [nacos-grpc-client-executor-4503] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Ack server push request, request = NotifySubscriberRequest, requestId = 36
17:02:03.984 [nacos-grpc-client-executor-5555] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Receive server push request, request = NotifySubscriberRequest, requestId = 40
17:02:03.997 [nacos-grpc-client-executor-5555] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Ack server push request, request = NotifySubscriberRequest, requestId = 40
17:02:21.049 [nacos-grpc-client-executor-5561] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Receive server push request, request = NotifySubscriberRequest, requestId = 44
17:02:21.069 [nacos-grpc-client-executor-5561] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Ack server push request, request = NotifySubscriberRequest, requestId = 44
17:43:21.286 [nacos-grpc-client-executor-6053] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Receive server push request, request = NotifySubscriberRequest, requestId = 47
17:43:21.302 [nacos-grpc-client-executor-6053] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Ack server push request, request = NotifySubscriberRequest, requestId = 47
17:44:29.210 [nacos-grpc-client-executor-6070] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Receive server push request, request = NotifySubscriberRequest, requestId = 51
17:44:29.236 [nacos-grpc-client-executor-6070] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Ack server push request, request = NotifySubscriberRequest, requestId = 51
20:01:10.786 [nacos-grpc-client-executor-7720] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Receive server push request, request = NotifySubscriberRequest, requestId = 54
20:01:10.795 [nacos-grpc-client-executor-7720] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Ack server push request, request = NotifySubscriberRequest, requestId = 54
20:01:43.811 [nacos-grpc-client-executor-7728] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Receive server push request, request = NotifySubscriberRequest, requestId = 57
20:01:43.829 [nacos-grpc-client-executor-7728] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c4c96167-cc58-4dab-a909-f4a4983850c4] Ack server push request, request = NotifySubscriberRequest, requestId = 57
20:13:13.466 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:13:13.466 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:13:13.804 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:13:13.804 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6897896f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:13:13.804 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1752629217933_127.0.0.1_12452
20:13:13.804 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4f41babe[Running, pool size = 5, active threads = 1, queued tasks = 0, completed tasks = 7872]
20:13:13.804 [nacos-grpc-client-executor-7872] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1752629217933_127.0.0.1_12452]Ignore complete event,isRunning:false,isAbandon=false
