09:28:15.102 [reactor-http-nio-3] ERROR c.h.g.f.Auth<PERSON>ilter - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/user/getInfo
09:28:15.101 [reactor-http-nio-2] ERROR c.h.g.f.AuthFilter - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/enterprise/getInfo
09:33:01.687 [reactor-http-nio-8] ERROR c.h.g.f.AuthFilter - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/universal
11:06:07.313 [reactor-http-nio-16] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/list,异常信息:Connection prematurely closed BEFORE response
11:06:07.313 [reactor-http-nio-13] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/searchFieldList,异常信息:Connection prematurely closed BEFORE response
11:52:22.895 [boundedElastic-124] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/kkfileview/onlinePreview,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for kkfileview"
14:37:34.802 [reactor-http-nio-5] ERROR c.h.g.f.AuthFilter - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/universal/getInfo
19:22:35.020 [boundedElastic-471] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/searchFieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
19:22:35.131 [boundedElastic-471] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/list,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
19:22:35.290 [boundedElastic-471] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/fieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
