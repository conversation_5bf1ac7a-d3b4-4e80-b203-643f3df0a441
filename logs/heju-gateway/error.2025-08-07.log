09:48:51.616 [reactor-http-nio-22] ERROR c.h.g.f.<PERSON>th<PERSON>er - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/universal
09:51:05.077 [reactor-http-nio-1] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/enterprise/getInfo,异常信息:Connection prematurely closed BEFORE response
09:51:05.089 [reactor-http-nio-1] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/user/getInfo,异常信息:Connection prematurely closed BEFORE response
10:05:50.437 [reactor-http-nio-18] ERROR c.h.g.f.AuthFilter - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/universal
10:09:18.910 [boundedElastic-52] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/customerinfo/list,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
10:09:21.307 [boundedElastic-52] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/searchFieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
10:09:21.619 [boundedElastic-52] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/list,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
10:09:21.760 [boundedElastic-52] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/fieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
10:21:03.749 [reactor-http-nio-7] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/getInfo,异常信息:Connection prematurely closed BEFORE response
