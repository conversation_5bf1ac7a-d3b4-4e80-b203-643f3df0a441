09:24:01.632 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:24:02.545 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c8c8de68-ba2e-40fb-8ae9-61ce7609f981_config-0
09:24:02.644 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 53 ms to scan 1 urls, producing 3 keys and 6 values 
09:24:02.678 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 4 keys and 9 values 
09:24:02.695 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 3 keys and 10 values 
09:24:02.714 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 1 keys and 5 values 
09:24:02.734 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 1 keys and 7 values 
09:24:02.749 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
09:24:02.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c8c8de68-ba2e-40fb-8ae9-61ce7609f981_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:24:02.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c8c8de68-ba2e-40fb-8ae9-61ce7609f981_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001d2163bc4e8
09:24:02.755 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c8c8de68-ba2e-40fb-8ae9-61ce7609f981_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001d2163bc708
09:24:02.756 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c8c8de68-ba2e-40fb-8ae9-61ce7609f981_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:24:02.756 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c8c8de68-ba2e-40fb-8ae9-61ce7609f981_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:24:02.772 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c8c8de68-ba2e-40fb-8ae9-61ce7609f981_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:04.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c8c8de68-ba2e-40fb-8ae9-61ce7609f981_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755566643855_127.0.0.1_14645
09:24:04.095 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c8c8de68-ba2e-40fb-8ae9-61ce7609f981_config-0] Notify connected event to listeners.
09:24:04.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c8c8de68-ba2e-40fb-8ae9-61ce7609f981_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:04.097 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c8c8de68-ba2e-40fb-8ae9-61ce7609f981_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001d2164f6350
09:24:04.318 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:24:09.246 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:24:11.666 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:24:12.356 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fadeb454-11b8-49dc-b678-04af968cc5ee_config-0
09:24:12.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fadeb454-11b8-49dc-b678-04af968cc5ee_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:24:12.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fadeb454-11b8-49dc-b678-04af968cc5ee_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001d2163bc4e8
09:24:12.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fadeb454-11b8-49dc-b678-04af968cc5ee_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001d2163bc708
09:24:12.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fadeb454-11b8-49dc-b678-04af968cc5ee_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:24:12.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fadeb454-11b8-49dc-b678-04af968cc5ee_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:24:12.357 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fadeb454-11b8-49dc-b678-04af968cc5ee_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:12.479 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fadeb454-11b8-49dc-b678-04af968cc5ee_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755566652367_127.0.0.1_14696
09:24:12.480 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fadeb454-11b8-49dc-b678-04af968cc5ee_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:12.480 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fadeb454-11b8-49dc-b678-04af968cc5ee_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001d2164f6350
09:24:12.481 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fadeb454-11b8-49dc-b678-04af968cc5ee_config-0] Notify connected event to listeners.
09:24:12.608 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 790275bf-ac62-43fe-9d84-2f60e728704d
09:24:12.608 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] RpcClient init label, labels = {module=naming, source=sdk}
09:24:12.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:24:12.611 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:24:12.622 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:24:12.623 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:24:12.767 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Success to connect to server [localhost:8848] on start up, connectionId = 1755566652632_127.0.0.1_14697
09:24:12.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:24:12.768 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Notify connected event to listeners.
09:24:12.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001d2164f6350
09:24:13.208 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ************:8081 register finished
09:24:13.251 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 12.342 seconds (JVM running for 15.953)
09:24:13.260 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:24:13.261 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:24:13.262 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:24:13.398 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:24:13.399 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 5
09:24:13.490 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:24:13.491 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:24:13.499 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:24:13.499 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:24:13.507 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:24:13.508 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:24:43.491 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:24:43.492 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 10
10:15:15.493 [nacos-grpc-client-executor-1057] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 16
10:15:15.509 [nacos-grpc-client-executor-1057] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 16
10:16:21.646 [nacos-grpc-client-executor-1079] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 19
10:16:21.668 [nacos-grpc-client-executor-1079] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 19
10:44:26.127 [nacos-grpc-client-executor-1633] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 23
10:44:26.145 [nacos-grpc-client-executor-1633] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 23
10:44:51.494 [nacos-grpc-client-executor-1639] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 27
10:44:51.510 [nacos-grpc-client-executor-1639] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 27
10:55:51.085 [nacos-grpc-client-executor-1851] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 30
10:55:51.112 [nacos-grpc-client-executor-1851] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 30
10:56:17.220 [nacos-grpc-client-executor-1863] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 33
10:56:17.243 [nacos-grpc-client-executor-1863] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 33
11:17:25.033 [nacos-grpc-client-executor-2266] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 37
11:17:25.054 [nacos-grpc-client-executor-2266] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 37
11:17:54.901 [nacos-grpc-client-executor-2273] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 40
11:17:54.929 [nacos-grpc-client-executor-2273] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 40
11:18:05.166 [nacos-grpc-client-executor-2275] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 44
11:18:05.180 [nacos-grpc-client-executor-2275] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 44
11:18:29.528 [nacos-grpc-client-executor-2287] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 48
11:18:29.547 [nacos-grpc-client-executor-2287] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 48
11:20:31.837 [nacos-grpc-client-executor-2325] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 51
11:20:31.854 [nacos-grpc-client-executor-2325] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 51
11:21:03.017 [nacos-grpc-client-executor-2332] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 55
11:21:03.037 [nacos-grpc-client-executor-2332] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 55
11:23:14.189 [nacos-grpc-client-executor-2370] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 58
11:23:14.219 [nacos-grpc-client-executor-2370] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 58
11:23:38.815 [nacos-grpc-client-executor-2383] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 62
11:23:38.838 [nacos-grpc-client-executor-2383] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 62
13:18:52.621 [nacos-grpc-client-executor-4626] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 65
13:18:52.637 [nacos-grpc-client-executor-4626] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 65
13:19:10.078 [nacos-grpc-client-executor-4630] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 69
13:19:10.091 [nacos-grpc-client-executor-4630] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 69
16:42:51.634 [nacos-grpc-client-executor-8516] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 72
16:42:51.634 [nacos-grpc-client-executor-8516] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 72
16:43:12.514 [nacos-grpc-client-executor-8522] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 76
16:43:12.528 [nacos-grpc-client-executor-8522] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 76
16:46:32.776 [nacos-grpc-client-executor-8590] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 79
16:46:32.805 [nacos-grpc-client-executor-8590] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 79
16:46:59.017 [nacos-grpc-client-executor-8599] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 83
16:46:59.026 [nacos-grpc-client-executor-8599] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 83
16:48:03.920 [nacos-grpc-client-executor-8619] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 86
16:48:03.949 [nacos-grpc-client-executor-8619] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 86
16:48:29.889 [nacos-grpc-client-executor-8633] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 90
16:48:29.893 [nacos-grpc-client-executor-8633] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 90
16:51:47.364 [nacos-grpc-client-executor-8693] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 93
16:51:47.380 [nacos-grpc-client-executor-8693] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 93
16:52:11.523 [nacos-grpc-client-executor-8699] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 96
16:52:11.540 [nacos-grpc-client-executor-8699] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 96
18:18:30.472 [nacos-grpc-client-executor-10319] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 102
18:18:30.472 [nacos-grpc-client-executor-10319] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 102
18:19:17.090 [nacos-grpc-client-executor-10329] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 106
18:19:17.099 [nacos-grpc-client-executor-10329] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 106
18:25:14.887 [nacos-grpc-client-executor-10442] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 111
18:25:14.899 [nacos-grpc-client-executor-10442] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 111
18:25:37.270 [nacos-grpc-client-executor-10455] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 115
18:25:37.285 [nacos-grpc-client-executor-10455] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 115
18:34:14.087 [nacos-grpc-client-executor-10607] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 121
18:34:14.103 [nacos-grpc-client-executor-10607] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 121
18:35:21.424 [nacos-grpc-client-executor-10633] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 126
18:35:21.438 [nacos-grpc-client-executor-10633] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 126
18:39:58.655 [nacos-grpc-client-executor-10715] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 131
18:39:58.672 [nacos-grpc-client-executor-10715] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 131
18:40:16.546 [nacos-grpc-client-executor-10718] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 136
18:40:16.558 [nacos-grpc-client-executor-10718] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 136
19:30:28.170 [nacos-grpc-client-executor-11659] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 140
19:30:28.185 [nacos-grpc-client-executor-11659] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 140
19:30:46.214 [nacos-grpc-client-executor-11663] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 144
19:30:46.232 [nacos-grpc-client-executor-11663] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 144
19:44:37.747 [nacos-grpc-client-executor-11913] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 149
19:44:37.765 [nacos-grpc-client-executor-11913] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 149
19:44:58.824 [nacos-grpc-client-executor-11920] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 153
19:44:58.839 [nacos-grpc-client-executor-11920] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 153
19:59:27.219 [nacos-grpc-client-executor-12193] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 158
19:59:27.235 [nacos-grpc-client-executor-12193] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 158
19:59:45.601 [nacos-grpc-client-executor-12197] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 163
19:59:45.609 [nacos-grpc-client-executor-12197] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 163
20:12:47.822 [nacos-grpc-client-executor-12432] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Receive server push request, request = NotifySubscriberRequest, requestId = 167
20:12:47.829 [nacos-grpc-client-executor-12432] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [790275bf-ac62-43fe-9d84-2f60e728704d] Ack server push request, request = NotifySubscriberRequest, requestId = 167
20:12:50.304 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:12:50.304 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:12:50.640 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:12:50.640 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@29ac9a0e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:12:50.640 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755566652632_127.0.0.1_14697
20:12:50.643 [nacos-grpc-client-executor-12435] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755566652632_127.0.0.1_14697]Ignore complete event,isRunning:false,isAbandon=false
20:12:50.645 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5e77d434[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 12436]
20:15:22.725 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
20:15:24.078 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b20f2a1c-ba1a-4060-be7a-d64dac1a40cb_config-0
20:15:24.206 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 74 ms to scan 1 urls, producing 3 keys and 6 values 
20:15:24.285 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 31 ms to scan 1 urls, producing 4 keys and 9 values 
20:15:24.313 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 3 keys and 10 values 
20:15:24.328 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
20:15:24.344 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 12 ms to scan 1 urls, producing 1 keys and 7 values 
20:15:24.361 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
20:15:24.365 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b20f2a1c-ba1a-4060-be7a-d64dac1a40cb_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:15:24.366 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b20f2a1c-ba1a-4060-be7a-d64dac1a40cb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x00000210813b4fb8
20:15:24.366 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b20f2a1c-ba1a-4060-be7a-d64dac1a40cb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x00000210813b51d8
20:15:24.367 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b20f2a1c-ba1a-4060-be7a-d64dac1a40cb_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:15:24.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b20f2a1c-ba1a-4060-be7a-d64dac1a40cb_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:15:24.388 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b20f2a1c-ba1a-4060-be7a-d64dac1a40cb_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:15:27.501 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b20f2a1c-ba1a-4060-be7a-d64dac1a40cb_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755605727128_127.0.0.1_14230
20:15:27.504 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b20f2a1c-ba1a-4060-be7a-d64dac1a40cb_config-0] Notify connected event to listeners.
20:15:27.505 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b20f2a1c-ba1a-4060-be7a-d64dac1a40cb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:15:27.507 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b20f2a1c-ba1a-4060-be7a-d64dac1a40cb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000210814efb88
20:15:27.876 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
20:15:34.723 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
20:15:36.214 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
20:15:36.889 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3a172808-7bf5-4b51-9c60-1aec65710106_config-0
20:15:36.889 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a172808-7bf5-4b51-9c60-1aec65710106_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
20:15:36.889 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a172808-7bf5-4b51-9c60-1aec65710106_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x00000210813b4fb8
20:15:36.900 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a172808-7bf5-4b51-9c60-1aec65710106_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x00000210813b51d8
20:15:36.900 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a172808-7bf5-4b51-9c60-1aec65710106_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
20:15:36.900 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a172808-7bf5-4b51-9c60-1aec65710106_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
20:15:36.901 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a172808-7bf5-4b51-9c60-1aec65710106_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:15:37.027 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a172808-7bf5-4b51-9c60-1aec65710106_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755605736914_127.0.0.1_14255
20:15:37.027 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a172808-7bf5-4b51-9c60-1aec65710106_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:15:37.027 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a172808-7bf5-4b51-9c60-1aec65710106_config-0] Notify connected event to listeners.
20:15:37.028 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3a172808-7bf5-4b51-9c60-1aec65710106_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000210814efb88
20:15:37.147 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3075aa4f-e5f9-4b1e-a0a1-797a43bfe690
20:15:37.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3075aa4f-e5f9-4b1e-a0a1-797a43bfe690] RpcClient init label, labels = {module=naming, source=sdk}
20:15:37.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3075aa4f-e5f9-4b1e-a0a1-797a43bfe690] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
20:15:37.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3075aa4f-e5f9-4b1e-a0a1-797a43bfe690] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
20:15:37.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3075aa4f-e5f9-4b1e-a0a1-797a43bfe690] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
20:15:37.147 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3075aa4f-e5f9-4b1e-a0a1-797a43bfe690] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
20:15:37.285 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3075aa4f-e5f9-4b1e-a0a1-797a43bfe690] Success to connect to server [localhost:8848] on start up, connectionId = 1755605737160_127.0.0.1_14256
20:15:37.285 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3075aa4f-e5f9-4b1e-a0a1-797a43bfe690] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
20:15:37.285 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3075aa4f-e5f9-4b1e-a0a1-797a43bfe690] Notify connected event to listeners.
20:15:37.285 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3075aa4f-e5f9-4b1e-a0a1-797a43bfe690] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000210814efb88
20:15:37.834 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3075aa4f-e5f9-4b1e-a0a1-797a43bfe690] Receive server push request, request = NotifySubscriberRequest, requestId = 171
20:15:37.834 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3075aa4f-e5f9-4b1e-a0a1-797a43bfe690] Ack server push request, request = NotifySubscriberRequest, requestId = 171
20:15:38.018 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3075aa4f-e5f9-4b1e-a0a1-797a43bfe690] Receive server push request, request = NotifySubscriberRequest, requestId = 173
20:15:38.018 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3075aa4f-e5f9-4b1e-a0a1-797a43bfe690] Ack server push request, request = NotifySubscriberRequest, requestId = 173
20:15:38.030 [nacos-grpc-client-executor-14] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3075aa4f-e5f9-4b1e-a0a1-797a43bfe690] Receive server push request, request = NotifySubscriberRequest, requestId = 172
20:15:38.033 [nacos-grpc-client-executor-14] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3075aa4f-e5f9-4b1e-a0a1-797a43bfe690] Ack server push request, request = NotifySubscriberRequest, requestId = 172
20:15:38.172 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ************:8081 register finished
20:15:38.230 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 16.904 seconds (JVM running for 19.492)
20:15:38.249 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
20:15:38.255 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
20:15:38.255 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
20:15:38.783 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3075aa4f-e5f9-4b1e-a0a1-797a43bfe690] Receive server push request, request = NotifySubscriberRequest, requestId = 174
20:15:38.783 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3075aa4f-e5f9-4b1e-a0a1-797a43bfe690] Ack server push request, request = NotifySubscriberRequest, requestId = 174
20:15:57.414 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3075aa4f-e5f9-4b1e-a0a1-797a43bfe690] Receive server push request, request = NotifySubscriberRequest, requestId = 177
20:15:57.415 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3075aa4f-e5f9-4b1e-a0a1-797a43bfe690] Ack server push request, request = NotifySubscriberRequest, requestId = 177
20:15:57.423 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3075aa4f-e5f9-4b1e-a0a1-797a43bfe690] Receive server push request, request = NotifySubscriberRequest, requestId = 178
20:15:57.424 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3075aa4f-e5f9-4b1e-a0a1-797a43bfe690] Ack server push request, request = NotifySubscriberRequest, requestId = 178
20:21:05.422 [nacos-grpc-client-executor-137] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3075aa4f-e5f9-4b1e-a0a1-797a43bfe690] Receive server push request, request = NotifySubscriberRequest, requestId = 179
20:21:05.422 [nacos-grpc-client-executor-137] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3075aa4f-e5f9-4b1e-a0a1-797a43bfe690] Ack server push request, request = NotifySubscriberRequest, requestId = 179
20:21:22.154 [nacos-grpc-client-executor-142] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3075aa4f-e5f9-4b1e-a0a1-797a43bfe690] Receive server push request, request = NotifySubscriberRequest, requestId = 181
20:21:22.170 [nacos-grpc-client-executor-142] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3075aa4f-e5f9-4b1e-a0a1-797a43bfe690] Ack server push request, request = NotifySubscriberRequest, requestId = 181
20:41:07.057 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:41:07.057 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:41:07.389 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:41:07.389 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7f3844c9[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:41:07.389 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755605737160_127.0.0.1_14256
20:41:07.391 [nacos-grpc-client-executor-506] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755605737160_127.0.0.1_14256]Ignore complete event,isRunning:false,isAbandon=false
20:41:07.393 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@17f945e9[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 507]
