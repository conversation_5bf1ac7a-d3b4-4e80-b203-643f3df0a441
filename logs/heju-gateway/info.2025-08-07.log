09:05:32.580 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:05:34.038 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0b34524d-74dc-4103-b3e7-3aaa2a8ebef4_config-0
09:05:34.221 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 87 ms to scan 1 urls, producing 3 keys and 6 values 
09:05:34.284 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 21 ms to scan 1 urls, producing 4 keys and 9 values 
09:05:34.306 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 3 keys and 10 values 
09:05:34.330 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 16 ms to scan 1 urls, producing 1 keys and 5 values 
09:05:34.355 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 20 ms to scan 1 urls, producing 1 keys and 7 values 
09:05:34.384 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 2 keys and 8 values 
09:05:34.394 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b34524d-74dc-4103-b3e7-3aaa2a8ebef4_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:05:34.396 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b34524d-74dc-4103-b3e7-3aaa2a8ebef4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001e7093b8d60
09:05:34.396 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b34524d-74dc-4103-b3e7-3aaa2a8ebef4_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001e7093b8f80
09:05:34.398 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b34524d-74dc-4103-b3e7-3aaa2a8ebef4_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:05:34.400 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b34524d-74dc-4103-b3e7-3aaa2a8ebef4_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:05:34.420 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b34524d-74dc-4103-b3e7-3aaa2a8ebef4_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:05:36.764 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b34524d-74dc-4103-b3e7-3aaa2a8ebef4_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754528736340_127.0.0.1_1679
09:05:36.764 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b34524d-74dc-4103-b3e7-3aaa2a8ebef4_config-0] Notify connected event to listeners.
09:05:36.764 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b34524d-74dc-4103-b3e7-3aaa2a8ebef4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:05:36.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b34524d-74dc-4103-b3e7-3aaa2a8ebef4_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001e7094f0ad8
09:05:37.062 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:05:49.249 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:05:53.038 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:05:53.908 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 016d1159-08c4-4cb3-a255-34ea77b1425e_config-0
09:05:53.910 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [016d1159-08c4-4cb3-a255-34ea77b1425e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:05:53.910 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [016d1159-08c4-4cb3-a255-34ea77b1425e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001e7093b8d60
09:05:53.911 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [016d1159-08c4-4cb3-a255-34ea77b1425e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001e7093b8f80
09:05:53.911 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [016d1159-08c4-4cb3-a255-34ea77b1425e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:05:53.911 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [016d1159-08c4-4cb3-a255-34ea77b1425e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:05:53.912 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [016d1159-08c4-4cb3-a255-34ea77b1425e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:05:54.036 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [016d1159-08c4-4cb3-a255-34ea77b1425e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754528753919_127.0.0.1_1716
09:05:54.036 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [016d1159-08c4-4cb3-a255-34ea77b1425e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:05:54.036 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [016d1159-08c4-4cb3-a255-34ea77b1425e_config-0] Notify connected event to listeners.
09:05:54.036 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [016d1159-08c4-4cb3-a255-34ea77b1425e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001e7094f0ad8
09:05:54.193 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5af79227-7b0d-4a4b-9ecc-8eae28b19c58
09:05:54.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] RpcClient init label, labels = {module=naming, source=sdk}
09:05:54.195 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:05:54.195 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:05:54.195 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:05:54.195 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:05:54.323 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Success to connect to server [localhost:8848] on start up, connectionId = 1754528754206_127.0.0.1_1721
09:05:54.323 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:05:54.325 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Notify connected event to listeners.
09:05:54.325 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001e7094f0ad8
09:05:54.911 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ************:8081 register finished
09:05:54.955 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 23.393 seconds (JVM running for 32.676)
09:05:54.963 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:05:54.965 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:05:54.965 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:05:55.110 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:05:55.113 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:05:55.131 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:05:55.132 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:06:24.966 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:06:24.966 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:06:25.066 [nacos-grpc-client-executor-45] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:06:25.066 [nacos-grpc-client-executor-45] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:06:25.080 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:06:25.080 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Ack server push request, request = NotifySubscriberRequest, requestId = 9
10:08:32.364 [nacos-grpc-client-executor-1268] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Receive server push request, request = NotifySubscriberRequest, requestId = 15
10:08:32.388 [nacos-grpc-client-executor-1268] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Ack server push request, request = NotifySubscriberRequest, requestId = 15
10:09:21.885 [nacos-grpc-client-executor-1282] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Receive server push request, request = NotifySubscriberRequest, requestId = 18
10:09:21.902 [nacos-grpc-client-executor-1282] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Ack server push request, request = NotifySubscriberRequest, requestId = 18
10:11:30.952 [nacos-grpc-client-executor-1328] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Receive server push request, request = NotifySubscriberRequest, requestId = 22
10:11:30.959 [nacos-grpc-client-executor-1328] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Ack server push request, request = NotifySubscriberRequest, requestId = 22
10:11:55.252 [nacos-grpc-client-executor-1332] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Receive server push request, request = NotifySubscriberRequest, requestId = 25
10:11:55.268 [nacos-grpc-client-executor-1332] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Ack server push request, request = NotifySubscriberRequest, requestId = 25
11:36:32.754 [nacos-grpc-client-executor-3005] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Receive server push request, request = NotifySubscriberRequest, requestId = 29
11:36:32.770 [nacos-grpc-client-executor-3005] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Ack server push request, request = NotifySubscriberRequest, requestId = 29
11:36:34.287 [nacos-grpc-client-executor-3006] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Receive server push request, request = NotifySubscriberRequest, requestId = 32
11:36:34.303 [nacos-grpc-client-executor-3006] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Ack server push request, request = NotifySubscriberRequest, requestId = 32
11:44:15.734 [nacos-grpc-client-executor-3157] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Receive server push request, request = NotifySubscriberRequest, requestId = 112
11:44:15.755 [nacos-grpc-client-executor-3157] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Ack server push request, request = NotifySubscriberRequest, requestId = 112
11:44:18.579 [nacos-grpc-client-executor-3158] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Receive server push request, request = NotifySubscriberRequest, requestId = 116
11:44:18.598 [nacos-grpc-client-executor-3158] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Ack server push request, request = NotifySubscriberRequest, requestId = 116
11:55:33.406 [nacos-grpc-client-executor-3383] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Receive server push request, request = NotifySubscriberRequest, requestId = 119
11:55:33.422 [nacos-grpc-client-executor-3383] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Ack server push request, request = NotifySubscriberRequest, requestId = 119
11:56:11.500 [nacos-grpc-client-executor-3393] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Receive server push request, request = NotifySubscriberRequest, requestId = 123
11:56:11.514 [nacos-grpc-client-executor-3393] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Ack server push request, request = NotifySubscriberRequest, requestId = 123
14:12:07.687 [nacos-grpc-client-executor-6068] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Receive server push request, request = NotifySubscriberRequest, requestId = 126
14:12:07.687 [nacos-grpc-client-executor-6068] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Ack server push request, request = NotifySubscriberRequest, requestId = 126
14:12:32.784 [nacos-grpc-client-executor-6077] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Receive server push request, request = NotifySubscriberRequest, requestId = 129
14:12:32.797 [nacos-grpc-client-executor-6077] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Ack server push request, request = NotifySubscriberRequest, requestId = 129
14:33:40.485 [nacos-grpc-client-executor-6472] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Receive server push request, request = NotifySubscriberRequest, requestId = 133
14:33:40.500 [nacos-grpc-client-executor-6472] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Ack server push request, request = NotifySubscriberRequest, requestId = 133
14:34:16.366 [nacos-grpc-client-executor-6483] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Receive server push request, request = NotifySubscriberRequest, requestId = 136
14:34:16.375 [nacos-grpc-client-executor-6483] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Ack server push request, request = NotifySubscriberRequest, requestId = 136
14:36:07.930 [nacos-grpc-client-executor-6520] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Receive server push request, request = NotifySubscriberRequest, requestId = 141
14:36:07.930 [nacos-grpc-client-executor-6520] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Ack server push request, request = NotifySubscriberRequest, requestId = 141
14:38:54.704 [nacos-grpc-client-executor-6570] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Receive server push request, request = NotifySubscriberRequest, requestId = 144
14:38:54.719 [nacos-grpc-client-executor-6570] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Ack server push request, request = NotifySubscriberRequest, requestId = 144
14:39:17.651 [nacos-grpc-client-executor-6578] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Receive server push request, request = NotifySubscriberRequest, requestId = 148
14:39:17.665 [nacos-grpc-client-executor-6578] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Ack server push request, request = NotifySubscriberRequest, requestId = 148
14:42:54.935 [nacos-grpc-client-executor-6647] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Receive server push request, request = NotifySubscriberRequest, requestId = 151
14:42:54.952 [nacos-grpc-client-executor-6647] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Ack server push request, request = NotifySubscriberRequest, requestId = 151
14:42:57.417 [nacos-grpc-client-executor-6648] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Receive server push request, request = NotifySubscriberRequest, requestId = 154
14:42:57.432 [nacos-grpc-client-executor-6648] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5af79227-7b0d-4a4b-9ecc-8eae28b19c58] Ack server push request, request = NotifySubscriberRequest, requestId = 154
16:37:59.687 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
16:37:59.690 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
16:38:00.015 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
16:38:00.016 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6722c68b[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
16:38:00.016 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754528754206_127.0.0.1_1721
16:38:00.018 [nacos-grpc-client-executor-8756] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754528754206_127.0.0.1_1721]Ignore complete event,isRunning:false,isAbandon=false
16:38:00.018 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@2aefd4d[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 8757]
16:58:01.016 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:58:02.621 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 673b7d9c-14ea-491d-a241-1ec266e2ef00_config-0
16:58:02.779 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 83 ms to scan 1 urls, producing 3 keys and 6 values 
16:58:02.869 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 42 ms to scan 1 urls, producing 4 keys and 9 values 
16:58:02.895 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 3 keys and 10 values 
16:58:02.939 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 1 keys and 5 values 
16:58:02.955 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 1 keys and 7 values 
16:58:02.977 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
16:58:02.977 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [673b7d9c-14ea-491d-a241-1ec266e2ef00_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:58:02.990 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [673b7d9c-14ea-491d-a241-1ec266e2ef00_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x00000214813b5248
16:58:02.990 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [673b7d9c-14ea-491d-a241-1ec266e2ef00_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x00000214813b5468
16:58:02.990 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [673b7d9c-14ea-491d-a241-1ec266e2ef00_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:58:02.993 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [673b7d9c-14ea-491d-a241-1ec266e2ef00_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:58:03.010 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [673b7d9c-14ea-491d-a241-1ec266e2ef00_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:58:05.225 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [673b7d9c-14ea-491d-a241-1ec266e2ef00_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754557084877_127.0.0.1_13880
16:58:05.227 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [673b7d9c-14ea-491d-a241-1ec266e2ef00_config-0] Notify connected event to listeners.
16:58:05.227 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [673b7d9c-14ea-491d-a241-1ec266e2ef00_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:58:05.229 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [673b7d9c-14ea-491d-a241-1ec266e2ef00_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000214814ecf98
16:58:05.524 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:58:14.203 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
16:58:16.798 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
16:58:18.169 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e89f54b1-e91d-4b8e-9a7d-a454bc58f0da_config-0
16:58:18.169 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e89f54b1-e91d-4b8e-9a7d-a454bc58f0da_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:58:18.169 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e89f54b1-e91d-4b8e-9a7d-a454bc58f0da_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x00000214813b5248
16:58:18.169 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e89f54b1-e91d-4b8e-9a7d-a454bc58f0da_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x00000214813b5468
16:58:18.169 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e89f54b1-e91d-4b8e-9a7d-a454bc58f0da_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:58:18.169 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e89f54b1-e91d-4b8e-9a7d-a454bc58f0da_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:58:18.173 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e89f54b1-e91d-4b8e-9a7d-a454bc58f0da_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:58:18.306 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e89f54b1-e91d-4b8e-9a7d-a454bc58f0da_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754557098190_127.0.0.1_13908
16:58:18.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e89f54b1-e91d-4b8e-9a7d-a454bc58f0da_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:58:18.307 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e89f54b1-e91d-4b8e-9a7d-a454bc58f0da_config-0] Notify connected event to listeners.
16:58:18.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e89f54b1-e91d-4b8e-9a7d-a454bc58f0da_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000214814ecf98
16:58:18.577 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of bf084b48-3302-4f02-91a3-31959c527877
16:58:18.577 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] RpcClient init label, labels = {module=naming, source=sdk}
16:58:18.580 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:58:18.580 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:58:18.585 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:58:18.585 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:58:18.720 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Success to connect to server [localhost:8848] on start up, connectionId = 1754557098601_127.0.0.1_13909
16:58:18.721 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:58:18.721 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Notify connected event to listeners.
16:58:18.721 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000214814ecf98
16:58:19.332 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Receive server push request, request = NotifySubscriberRequest, requestId = 157
16:58:19.335 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Ack server push request, request = NotifySubscriberRequest, requestId = 157
16:58:19.887 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ************:8081 register finished
16:58:19.973 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 20.557 seconds (JVM running for 26.179)
16:58:19.989 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
16:58:19.991 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
16:58:19.994 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
16:58:20.455 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Receive server push request, request = NotifySubscriberRequest, requestId = 158
16:58:20.456 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Ack server push request, request = NotifySubscriberRequest, requestId = 158
16:58:49.605 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Receive server push request, request = NotifySubscriberRequest, requestId = 163
16:58:49.606 [nacos-grpc-client-executor-36] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Ack server push request, request = NotifySubscriberRequest, requestId = 163
16:58:49.622 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Receive server push request, request = NotifySubscriberRequest, requestId = 164
16:58:49.622 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Ack server push request, request = NotifySubscriberRequest, requestId = 164
16:58:49.644 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Receive server push request, request = NotifySubscriberRequest, requestId = 165
16:58:49.644 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Ack server push request, request = NotifySubscriberRequest, requestId = 165
16:58:49.710 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Receive server push request, request = NotifySubscriberRequest, requestId = 166
16:58:49.714 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Ack server push request, request = NotifySubscriberRequest, requestId = 166
19:05:10.839 [nacos-grpc-client-executor-2514] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Receive server push request, request = NotifySubscriberRequest, requestId = 169
19:05:10.856 [nacos-grpc-client-executor-2514] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Ack server push request, request = NotifySubscriberRequest, requestId = 169
19:06:09.471 [nacos-grpc-client-executor-2533] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Receive server push request, request = NotifySubscriberRequest, requestId = 171
19:06:09.488 [nacos-grpc-client-executor-2533] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Ack server push request, request = NotifySubscriberRequest, requestId = 171
19:23:27.537 [nacos-grpc-client-executor-2866] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Receive server push request, request = NotifySubscriberRequest, requestId = 176
19:23:27.569 [nacos-grpc-client-executor-2866] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Ack server push request, request = NotifySubscriberRequest, requestId = 176
19:23:57.614 [nacos-grpc-client-executor-2876] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Receive server push request, request = NotifySubscriberRequest, requestId = 179
19:23:57.631 [nacos-grpc-client-executor-2876] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Ack server push request, request = NotifySubscriberRequest, requestId = 179
19:28:31.015 [nacos-grpc-client-executor-2967] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Receive server push request, request = NotifySubscriberRequest, requestId = 183
19:28:31.034 [nacos-grpc-client-executor-2967] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Ack server push request, request = NotifySubscriberRequest, requestId = 183
19:29:02.247 [nacos-grpc-client-executor-2977] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Receive server push request, request = NotifySubscriberRequest, requestId = 186
19:29:02.265 [nacos-grpc-client-executor-2977] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Ack server push request, request = NotifySubscriberRequest, requestId = 186
19:35:00.514 [nacos-grpc-client-executor-3096] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Receive server push request, request = NotifySubscriberRequest, requestId = 190
19:35:00.530 [nacos-grpc-client-executor-3096] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Ack server push request, request = NotifySubscriberRequest, requestId = 190
19:35:35.927 [nacos-grpc-client-executor-3107] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Receive server push request, request = NotifySubscriberRequest, requestId = 193
19:35:35.948 [nacos-grpc-client-executor-3107] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Ack server push request, request = NotifySubscriberRequest, requestId = 193
19:49:32.552 [nacos-grpc-client-executor-3367] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Receive server push request, request = NotifySubscriberRequest, requestId = 197
19:49:32.577 [nacos-grpc-client-executor-3367] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Ack server push request, request = NotifySubscriberRequest, requestId = 197
19:50:14.499 [nacos-grpc-client-executor-3382] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Receive server push request, request = NotifySubscriberRequest, requestId = 200
19:50:14.508 [nacos-grpc-client-executor-3382] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [bf084b48-3302-4f02-91a3-31959c527877] Ack server push request, request = NotifySubscriberRequest, requestId = 200
19:55:50.978 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:55:50.983 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:55:51.308 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:55:51.308 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@28b015bc[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:55:51.308 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754557098601_127.0.0.1_13909
19:55:51.313 [nacos-grpc-client-executor-3484] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754557098601_127.0.0.1_13909]Ignore complete event,isRunning:false,isAbandon=false
19:55:51.322 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@508f2134[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 3485]
