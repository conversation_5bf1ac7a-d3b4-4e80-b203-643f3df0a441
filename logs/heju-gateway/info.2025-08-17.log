10:17:17.233 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:17:18.133 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ab0b97f9-3635-42bf-b033-52a98d1045d9_config-0
10:17:18.229 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 51 ms to scan 1 urls, producing 3 keys and 6 values 
10:17:18.259 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 4 keys and 9 values 
10:17:18.268 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 3 keys and 10 values 
10:17:18.278 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 1 keys and 5 values 
10:17:18.288 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 1 keys and 7 values 
10:17:18.304 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 2 keys and 8 values 
10:17:18.308 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab0b97f9-3635-42bf-b033-52a98d1045d9_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:17:18.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab0b97f9-3635-42bf-b033-52a98d1045d9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000002a0813bc2b8
10:17:18.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab0b97f9-3635-42bf-b033-52a98d1045d9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000002a0813bc4d8
10:17:18.310 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab0b97f9-3635-42bf-b033-52a98d1045d9_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:17:18.311 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab0b97f9-3635-42bf-b033-52a98d1045d9_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:17:18.321 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab0b97f9-3635-42bf-b033-52a98d1045d9_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:17:19.605 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab0b97f9-3635-42bf-b033-52a98d1045d9_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755397039346_127.0.0.1_10581
10:17:19.606 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab0b97f9-3635-42bf-b033-52a98d1045d9_config-0] Notify connected event to listeners.
10:17:19.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab0b97f9-3635-42bf-b033-52a98d1045d9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:17:19.607 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ab0b97f9-3635-42bf-b033-52a98d1045d9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000002a0814f6370
10:17:19.841 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:17:24.935 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
10:17:26.435 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
10:17:27.234 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a342403d-a422-4dc7-9407-8b27bf0d8379_config-0
10:17:27.235 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a342403d-a422-4dc7-9407-8b27bf0d8379_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:17:27.236 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a342403d-a422-4dc7-9407-8b27bf0d8379_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000002a0813bc2b8
10:17:27.236 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a342403d-a422-4dc7-9407-8b27bf0d8379_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000002a0813bc4d8
10:17:27.236 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a342403d-a422-4dc7-9407-8b27bf0d8379_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:17:27.236 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a342403d-a422-4dc7-9407-8b27bf0d8379_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:17:27.238 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a342403d-a422-4dc7-9407-8b27bf0d8379_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:17:27.391 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a342403d-a422-4dc7-9407-8b27bf0d8379_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755397047255_127.0.0.1_10618
10:17:27.398 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a342403d-a422-4dc7-9407-8b27bf0d8379_config-0] Notify connected event to listeners.
10:17:27.398 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a342403d-a422-4dc7-9407-8b27bf0d8379_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:17:27.399 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a342403d-a422-4dc7-9407-8b27bf0d8379_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000002a0814f6370
10:17:27.676 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of c63f9171-9f09-4441-8a07-c465014290f4
10:17:27.676 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] RpcClient init label, labels = {module=naming, source=sdk}
10:17:27.679 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:17:27.679 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:17:27.680 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:17:27.681 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:17:27.812 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Success to connect to server [localhost:8848] on start up, connectionId = 1755397047696_127.0.0.1_10622
10:17:27.813 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Notify connected event to listeners.
10:17:27.813 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:17:27.814 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000002a0814f6370
10:17:28.377 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Receive server push request, request = NotifySubscriberRequest, requestId = 2
10:17:28.378 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Ack server push request, request = NotifySubscriberRequest, requestId = 2
10:17:28.525 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.21:8081 register finished
10:17:28.569 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 12.036 seconds (JVM running for 13.307)
10:17:28.577 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Receive server push request, request = NotifySubscriberRequest, requestId = 3
10:17:28.578 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Ack server push request, request = NotifySubscriberRequest, requestId = 3
10:17:28.578 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
10:17:28.580 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
10:17:28.581 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
10:17:29.125 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Receive server push request, request = NotifySubscriberRequest, requestId = 4
10:17:29.125 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Ack server push request, request = NotifySubscriberRequest, requestId = 4
10:17:58.725 [nacos-grpc-client-executor-53] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Receive server push request, request = NotifySubscriberRequest, requestId = 8
10:17:58.729 [nacos-grpc-client-executor-53] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Ack server push request, request = NotifySubscriberRequest, requestId = 8
10:17:58.739 [nacos-grpc-client-executor-54] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Receive server push request, request = NotifySubscriberRequest, requestId = 10
10:17:58.739 [nacos-grpc-client-executor-54] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Ack server push request, request = NotifySubscriberRequest, requestId = 10
10:17:58.747 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Receive server push request, request = NotifySubscriberRequest, requestId = 9
10:17:58.747 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Ack server push request, request = NotifySubscriberRequest, requestId = 9
13:03:04.535 [nacos-grpc-client-executor-3315] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Receive server push request, request = NotifySubscriberRequest, requestId = 17
13:03:04.554 [nacos-grpc-client-executor-3315] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Ack server push request, request = NotifySubscriberRequest, requestId = 17
13:03:33.544 [nacos-grpc-client-executor-3326] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Receive server push request, request = NotifySubscriberRequest, requestId = 21
13:03:33.558 [nacos-grpc-client-executor-3326] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Ack server push request, request = NotifySubscriberRequest, requestId = 21
14:23:13.262 [nacos-grpc-client-executor-4912] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Receive server push request, request = NotifySubscriberRequest, requestId = 24
14:23:13.293 [nacos-grpc-client-executor-4912] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Ack server push request, request = NotifySubscriberRequest, requestId = 24
14:23:40.495 [nacos-grpc-client-executor-4921] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Receive server push request, request = NotifySubscriberRequest, requestId = 28
14:23:40.513 [nacos-grpc-client-executor-4921] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Ack server push request, request = NotifySubscriberRequest, requestId = 28
14:25:18.519 [nacos-grpc-client-executor-4952] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Receive server push request, request = NotifySubscriberRequest, requestId = 31
14:25:18.543 [nacos-grpc-client-executor-4952] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Ack server push request, request = NotifySubscriberRequest, requestId = 31
14:25:44.538 [nacos-grpc-client-executor-4961] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Receive server push request, request = NotifySubscriberRequest, requestId = 35
14:25:44.563 [nacos-grpc-client-executor-4961] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Ack server push request, request = NotifySubscriberRequest, requestId = 35
15:29:29.669 [nacos-grpc-client-executor-6202] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Receive server push request, request = NotifySubscriberRequest, requestId = 40
15:29:29.696 [nacos-grpc-client-executor-6202] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Ack server push request, request = NotifySubscriberRequest, requestId = 40
15:29:54.963 [nacos-grpc-client-executor-6212] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Receive server push request, request = NotifySubscriberRequest, requestId = 44
15:29:54.977 [nacos-grpc-client-executor-6212] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Ack server push request, request = NotifySubscriberRequest, requestId = 44
15:50:15.128 [nacos-grpc-client-executor-6618] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Receive server push request, request = NotifySubscriberRequest, requestId = 49
15:50:15.144 [nacos-grpc-client-executor-6618] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Ack server push request, request = NotifySubscriberRequest, requestId = 49
15:50:32.184 [nacos-grpc-client-executor-6621] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Receive server push request, request = NotifySubscriberRequest, requestId = 54
15:50:32.195 [nacos-grpc-client-executor-6621] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [c63f9171-9f09-4441-8a07-c465014290f4] Ack server push request, request = NotifySubscriberRequest, requestId = 54
15:58:50.657 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
15:58:50.662 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
15:58:50.994 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
15:58:50.994 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3984259a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
15:58:50.994 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755397047696_127.0.0.1_10622
15:58:50.996 [nacos-grpc-client-executor-6787] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755397047696_127.0.0.1_10622]Ignore complete event,isRunning:false,isAbandon=false
15:58:50.996 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7c0dc014[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 6788]
16:00:45.215 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:00:46.451 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 41bb9734-95ef-4b4d-9901-60a5917e9fa8_config-0
16:00:46.588 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 77 ms to scan 1 urls, producing 3 keys and 6 values 
16:00:46.634 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 4 keys and 9 values 
16:00:46.654 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 3 keys and 10 values 
16:00:46.677 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 18 ms to scan 1 urls, producing 1 keys and 5 values 
16:00:46.698 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
16:00:46.713 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
16:00:46.717 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41bb9734-95ef-4b4d-9901-60a5917e9fa8_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:00:46.718 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41bb9734-95ef-4b4d-9901-60a5917e9fa8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x00000270813b42b8
16:00:46.719 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41bb9734-95ef-4b4d-9901-60a5917e9fa8_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x00000270813b44d8
16:00:46.720 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41bb9734-95ef-4b4d-9901-60a5917e9fa8_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:00:46.721 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41bb9734-95ef-4b4d-9901-60a5917e9fa8_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:00:46.741 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41bb9734-95ef-4b4d-9901-60a5917e9fa8_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:00:48.708 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41bb9734-95ef-4b4d-9901-60a5917e9fa8_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755417648410_127.0.0.1_4260
16:00:48.709 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41bb9734-95ef-4b4d-9901-60a5917e9fa8_config-0] Notify connected event to listeners.
16:00:48.710 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41bb9734-95ef-4b4d-9901-60a5917e9fa8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:00:48.711 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41bb9734-95ef-4b4d-9901-60a5917e9fa8_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000270814ee350
16:00:48.945 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:00:57.374 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
16:00:59.396 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
16:01:00.300 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2930dd3f-8dce-410b-b899-bb5de1072ce5_config-0
16:01:00.301 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2930dd3f-8dce-410b-b899-bb5de1072ce5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:01:00.302 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2930dd3f-8dce-410b-b899-bb5de1072ce5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x00000270813b42b8
16:01:00.302 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2930dd3f-8dce-410b-b899-bb5de1072ce5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x00000270813b44d8
16:01:00.302 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2930dd3f-8dce-410b-b899-bb5de1072ce5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:01:00.303 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2930dd3f-8dce-410b-b899-bb5de1072ce5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:01:00.303 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2930dd3f-8dce-410b-b899-bb5de1072ce5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:01:00.446 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2930dd3f-8dce-410b-b899-bb5de1072ce5_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755417660321_127.0.0.1_4278
16:01:00.446 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2930dd3f-8dce-410b-b899-bb5de1072ce5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:01:00.446 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2930dd3f-8dce-410b-b899-bb5de1072ce5_config-0] Notify connected event to listeners.
16:01:00.447 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2930dd3f-8dce-410b-b899-bb5de1072ce5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000270814ee350
16:01:00.623 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 142df605-3695-411e-9470-6e6ba4e6a1bf
16:01:00.624 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142df605-3695-411e-9470-6e6ba4e6a1bf] RpcClient init label, labels = {module=naming, source=sdk}
16:01:00.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142df605-3695-411e-9470-6e6ba4e6a1bf] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:01:00.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142df605-3695-411e-9470-6e6ba4e6a1bf] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:01:00.634 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142df605-3695-411e-9470-6e6ba4e6a1bf] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:01:00.635 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142df605-3695-411e-9470-6e6ba4e6a1bf] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:01:00.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142df605-3695-411e-9470-6e6ba4e6a1bf] Success to connect to server [localhost:8848] on start up, connectionId = 1755417660655_127.0.0.1_4282
16:01:00.787 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142df605-3695-411e-9470-6e6ba4e6a1bf] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:01:00.787 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142df605-3695-411e-9470-6e6ba4e6a1bf] Notify connected event to listeners.
16:01:00.788 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142df605-3695-411e-9470-6e6ba4e6a1bf] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000270814ee350
16:01:01.337 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142df605-3695-411e-9470-6e6ba4e6a1bf] Receive server push request, request = NotifySubscriberRequest, requestId = 57
16:01:01.338 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142df605-3695-411e-9470-6e6ba4e6a1bf] Ack server push request, request = NotifySubscriberRequest, requestId = 57
16:01:01.520 [nacos-grpc-client-executor-16] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142df605-3695-411e-9470-6e6ba4e6a1bf] Receive server push request, request = NotifySubscriberRequest, requestId = 59
16:01:01.521 [nacos-grpc-client-executor-16] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142df605-3695-411e-9470-6e6ba4e6a1bf] Ack server push request, request = NotifySubscriberRequest, requestId = 59
16:01:01.533 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142df605-3695-411e-9470-6e6ba4e6a1bf] Receive server push request, request = NotifySubscriberRequest, requestId = 58
16:01:01.534 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142df605-3695-411e-9470-6e6ba4e6a1bf] Ack server push request, request = NotifySubscriberRequest, requestId = 58
16:01:01.568 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.21:8081 register finished
16:01:01.620 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 17.592 seconds (JVM running for 19.702)
16:01:01.649 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
16:01:01.650 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
16:01:01.651 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
16:01:02.123 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142df605-3695-411e-9470-6e6ba4e6a1bf] Receive server push request, request = NotifySubscriberRequest, requestId = 60
16:01:02.123 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142df605-3695-411e-9470-6e6ba4e6a1bf] Ack server push request, request = NotifySubscriberRequest, requestId = 60
16:01:31.677 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142df605-3695-411e-9470-6e6ba4e6a1bf] Receive server push request, request = NotifySubscriberRequest, requestId = 64
16:01:31.678 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142df605-3695-411e-9470-6e6ba4e6a1bf] Ack server push request, request = NotifySubscriberRequest, requestId = 64
16:01:31.681 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142df605-3695-411e-9470-6e6ba4e6a1bf] Receive server push request, request = NotifySubscriberRequest, requestId = 63
16:01:31.681 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [142df605-3695-411e-9470-6e6ba4e6a1bf] Ack server push request, request = NotifySubscriberRequest, requestId = 63
19:41:50.414 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:41:50.419 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:41:50.742 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:41:50.742 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2e49bd12[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:41:50.744 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755417660655_127.0.0.1_4282
19:41:50.746 [nacos-grpc-client-executor-4399] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755417660655_127.0.0.1_4282]Ignore complete event,isRunning:false,isAbandon=false
19:41:50.757 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@48396fc[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 4400]
