09:24:33.759 [reactor-http-nio-3] ERROR c.h.g.f.Auth<PERSON>ilter - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/universal/fieldList
09:24:33.759 [reactor-http-nio-2] ERROR c.h.g.f.AuthFilter - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/universal/list
09:24:34.073 [reactor-http-nio-5] ERROR c.h.g.f.AuthFilter - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/universal/list
09:24:34.073 [reactor-http-nio-4] ERROR c.h.g.f.AuthFilter - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/universal/searchFieldList
09:24:34.129 [reactor-http-nio-6] ERROR c.h.g.f.AuthFilter - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/universal/searchFieldList
09:24:34.382 [reactor-http-nio-7] ERROR c.h.g.f.AuthFilter - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/universal/fieldList
13:59:04.497 [boundedElastic-2] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/searchFieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
13:59:04.497 [boundedElastic-4] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/fieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
13:59:04.497 [boundedElastic-3] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/list,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
17:08:31.486 [reactor-http-nio-1] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal,异常信息:Connection prematurely closed BEFORE response
17:08:31.486 [reactor-http-nio-2] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/searchFieldList,异常信息:Connection prematurely closed BEFORE response
17:26:53.299 [reactor-http-nio-21] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/customerinfo/list,异常信息:Connection prematurely closed BEFORE response
