10:16:34.843 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:16:35.566 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 446c1c4a-48cd-41d3-8677-28b020978e4c_config-0
10:16:35.652 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 44 ms to scan 1 urls, producing 3 keys and 6 values 
10:16:35.680 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 4 keys and 9 values 
10:16:35.691 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 3 keys and 10 values 
10:16:35.704 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 8 ms to scan 1 urls, producing 1 keys and 5 values 
10:16:35.715 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 9 ms to scan 1 urls, producing 1 keys and 7 values 
10:16:35.726 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 9 ms to scan 1 urls, producing 2 keys and 8 values 
10:16:35.731 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [446c1c4a-48cd-41d3-8677-28b020978e4c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:16:35.731 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [446c1c4a-48cd-41d3-8677-28b020978e4c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000027a813b9bf8
10:16:35.731 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [446c1c4a-48cd-41d3-8677-28b020978e4c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000027a813b9e18
10:16:35.732 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [446c1c4a-48cd-41d3-8677-28b020978e4c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:16:35.733 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [446c1c4a-48cd-41d3-8677-28b020978e4c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:16:35.741 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [446c1c4a-48cd-41d3-8677-28b020978e4c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:16:37.192 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [446c1c4a-48cd-41d3-8677-28b020978e4c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756001796871_127.0.0.1_11176
10:16:37.192 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [446c1c4a-48cd-41d3-8677-28b020978e4c_config-0] Notify connected event to listeners.
10:16:37.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [446c1c4a-48cd-41d3-8677-28b020978e4c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:16:37.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [446c1c4a-48cd-41d3-8677-28b020978e4c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000027a814f1450
10:16:37.682 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:16:42.840 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
10:16:44.518 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
10:16:45.186 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 317c4ff8-d7c6-4c6b-bd7e-9be5b38befc5_config-0
10:16:45.186 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [317c4ff8-d7c6-4c6b-bd7e-9be5b38befc5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:16:45.186 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [317c4ff8-d7c6-4c6b-bd7e-9be5b38befc5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000027a813b9bf8
10:16:45.187 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [317c4ff8-d7c6-4c6b-bd7e-9be5b38befc5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000027a813b9e18
10:16:45.187 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [317c4ff8-d7c6-4c6b-bd7e-9be5b38befc5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:16:45.187 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [317c4ff8-d7c6-4c6b-bd7e-9be5b38befc5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:16:45.187 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [317c4ff8-d7c6-4c6b-bd7e-9be5b38befc5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:16:45.311 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [317c4ff8-d7c6-4c6b-bd7e-9be5b38befc5_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756001805198_127.0.0.1_11203
10:16:45.311 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [317c4ff8-d7c6-4c6b-bd7e-9be5b38befc5_config-0] Notify connected event to listeners.
10:16:45.311 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [317c4ff8-d7c6-4c6b-bd7e-9be5b38befc5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:16:45.311 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [317c4ff8-d7c6-4c6b-bd7e-9be5b38befc5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000027a814f1450
10:16:45.429 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e8a354e5-d862-4782-b780-bec2303989d4
10:16:45.430 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8a354e5-d862-4782-b780-bec2303989d4] RpcClient init label, labels = {module=naming, source=sdk}
10:16:45.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8a354e5-d862-4782-b780-bec2303989d4] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:16:45.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8a354e5-d862-4782-b780-bec2303989d4] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:16:45.432 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8a354e5-d862-4782-b780-bec2303989d4] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:16:45.434 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8a354e5-d862-4782-b780-bec2303989d4] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:16:45.559 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8a354e5-d862-4782-b780-bec2303989d4] Success to connect to server [localhost:8848] on start up, connectionId = 1756001805444_127.0.0.1_11204
10:16:45.559 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8a354e5-d862-4782-b780-bec2303989d4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:16:45.559 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8a354e5-d862-4782-b780-bec2303989d4] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000027a814f1450
10:16:45.559 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8a354e5-d862-4782-b780-bec2303989d4] Notify connected event to listeners.
10:16:46.331 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ************:8081 register finished
10:16:46.383 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 12.43 seconds (JVM running for 15.513)
10:16:46.396 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
10:16:46.396 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
10:16:46.396 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
10:16:46.538 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8a354e5-d862-4782-b780-bec2303989d4] Receive server push request, request = NotifySubscriberRequest, requestId = 1
10:16:46.539 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8a354e5-d862-4782-b780-bec2303989d4] Ack server push request, request = NotifySubscriberRequest, requestId = 1
10:16:46.879 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8a354e5-d862-4782-b780-bec2303989d4] Receive server push request, request = NotifySubscriberRequest, requestId = 2
10:16:46.879 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8a354e5-d862-4782-b780-bec2303989d4] Ack server push request, request = NotifySubscriberRequest, requestId = 2
10:17:16.404 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8a354e5-d862-4782-b780-bec2303989d4] Receive server push request, request = NotifySubscriberRequest, requestId = 8
10:17:16.405 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8a354e5-d862-4782-b780-bec2303989d4] Ack server push request, request = NotifySubscriberRequest, requestId = 8
10:17:16.417 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8a354e5-d862-4782-b780-bec2303989d4] Receive server push request, request = NotifySubscriberRequest, requestId = 7
10:17:16.417 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8a354e5-d862-4782-b780-bec2303989d4] Ack server push request, request = NotifySubscriberRequest, requestId = 7
10:17:16.429 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8a354e5-d862-4782-b780-bec2303989d4] Receive server push request, request = NotifySubscriberRequest, requestId = 6
10:17:16.434 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8a354e5-d862-4782-b780-bec2303989d4] Ack server push request, request = NotifySubscriberRequest, requestId = 6
10:17:46.356 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8a354e5-d862-4782-b780-bec2303989d4] Receive server push request, request = NotifySubscriberRequest, requestId = 10
10:17:46.357 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e8a354e5-d862-4782-b780-bec2303989d4] Ack server push request, request = NotifySubscriberRequest, requestId = 10
18:54:36.214 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:54:36.222 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:54:36.562 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:54:36.562 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3dd8b21f[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:54:36.562 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756001805444_127.0.0.1_11204
18:54:36.565 [nacos-grpc-client-executor-10085] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756001805444_127.0.0.1_11204]Ignore complete event,isRunning:false,isAbandon=false
18:54:36.572 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@d54a6b7[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 10086]
