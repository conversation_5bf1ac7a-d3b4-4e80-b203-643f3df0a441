09:01:07.316 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:01:07.325 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:01:07.663 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:01:07.663 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@647c0f08[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:01:07.664 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755653293773_127.0.0.1_8680
09:01:07.670 [nacos-grpc-client-executor-15622] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755653293773_127.0.0.1_8680]Ignore complete event,isRunning:false,isAbandon=false
09:01:07.680 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@1c8b78a3[Running, pool size = 6, active threads = 0, queued tasks = 0, completed tasks = 15623]
09:32:00.438 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:32:01.005 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of fe32c338-1955-42ff-921d-61d33408a66f_config-0
09:32:01.081 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 32 ms to scan 1 urls, producing 3 keys and 6 values 
09:32:01.109 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 4 keys and 9 values 
09:32:01.116 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 3 keys and 10 values 
09:32:01.123 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 5 ms to scan 1 urls, producing 1 keys and 5 values 
09:32:01.131 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
09:32:01.140 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
09:32:01.144 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe32c338-1955-42ff-921d-61d33408a66f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:32:01.144 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe32c338-1955-42ff-921d-61d33408a66f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x00000272313b8d60
09:32:01.145 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe32c338-1955-42ff-921d-61d33408a66f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x00000272313b8f80
09:32:01.145 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe32c338-1955-42ff-921d-61d33408a66f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:32:01.146 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe32c338-1955-42ff-921d-61d33408a66f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:32:01.152 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe32c338-1955-42ff-921d-61d33408a66f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:32:02.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe32c338-1955-42ff-921d-61d33408a66f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755739921889_127.0.0.1_13224
09:32:02.105 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe32c338-1955-42ff-921d-61d33408a66f_config-0] Notify connected event to listeners.
09:32:02.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe32c338-1955-42ff-921d-61d33408a66f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:32:02.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [fe32c338-1955-42ff-921d-61d33408a66f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000272314f0668
09:32:02.234 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:32:05.851 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:32:07.234 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:32:07.754 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 80597377-a790-44d4-a543-f1affeb6c713_config-0
09:32:07.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80597377-a790-44d4-a543-f1affeb6c713_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:32:07.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80597377-a790-44d4-a543-f1affeb6c713_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x00000272313b8d60
09:32:07.754 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80597377-a790-44d4-a543-f1affeb6c713_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x00000272313b8f80
09:32:07.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80597377-a790-44d4-a543-f1affeb6c713_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:32:07.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80597377-a790-44d4-a543-f1affeb6c713_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:32:07.759 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80597377-a790-44d4-a543-f1affeb6c713_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:32:07.877 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80597377-a790-44d4-a543-f1affeb6c713_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755739927768_127.0.0.1_13231
09:32:07.877 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80597377-a790-44d4-a543-f1affeb6c713_config-0] Notify connected event to listeners.
09:32:07.877 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80597377-a790-44d4-a543-f1affeb6c713_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:32:07.877 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [80597377-a790-44d4-a543-f1affeb6c713_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000272314f0668
09:32:07.978 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of da458ee5-cbb0-450c-bf3e-f25c1af99d9e
09:32:07.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] RpcClient init label, labels = {module=naming, source=sdk}
09:32:07.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:32:07.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:32:07.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:32:07.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:32:08.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Success to connect to server [localhost:8848] on start up, connectionId = 1755739927985_127.0.0.1_13232
09:32:08.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:32:08.106 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000272314f0668
09:32:08.106 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Notify connected event to listeners.
09:32:08.225 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Receive server push request, request = NotifySubscriberRequest, requestId = 91
09:32:08.225 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Ack server push request, request = NotifySubscriberRequest, requestId = 91
09:32:08.525 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.21:8081 register finished
09:32:08.557 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 8.656 seconds (JVM running for 14.74)
09:32:08.560 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:32:08.560 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:32:08.560 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:32:08.642 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Receive server push request, request = NotifySubscriberRequest, requestId = 93
09:32:08.642 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Ack server push request, request = NotifySubscriberRequest, requestId = 93
09:32:38.811 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Receive server push request, request = NotifySubscriberRequest, requestId = 97
09:32:38.811 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Ack server push request, request = NotifySubscriberRequest, requestId = 97
09:32:38.819 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Receive server push request, request = NotifySubscriberRequest, requestId = 98
09:32:38.819 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Ack server push request, request = NotifySubscriberRequest, requestId = 98
09:32:38.823 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Receive server push request, request = NotifySubscriberRequest, requestId = 99
09:32:38.823 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Ack server push request, request = NotifySubscriberRequest, requestId = 99
11:00:36.378 [nacos-grpc-client-executor-1770] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Receive server push request, request = NotifySubscriberRequest, requestId = 102
11:00:36.392 [nacos-grpc-client-executor-1770] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Ack server push request, request = NotifySubscriberRequest, requestId = 102
11:00:54.000 [nacos-grpc-client-executor-1777] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Receive server push request, request = NotifySubscriberRequest, requestId = 104
11:00:54.022 [nacos-grpc-client-executor-1777] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Ack server push request, request = NotifySubscriberRequest, requestId = 104
11:33:39.432 [nacos-grpc-client-executor-2394] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Receive server push request, request = NotifySubscriberRequest, requestId = 107
11:33:39.451 [nacos-grpc-client-executor-2394] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Ack server push request, request = NotifySubscriberRequest, requestId = 107
11:34:27.356 [nacos-grpc-client-executor-2413] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Receive server push request, request = NotifySubscriberRequest, requestId = 109
11:34:27.370 [nacos-grpc-client-executor-2413] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Ack server push request, request = NotifySubscriberRequest, requestId = 109
11:48:43.630 [nacos-grpc-client-executor-2688] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Receive server push request, request = NotifySubscriberRequest, requestId = 114
11:48:43.651 [nacos-grpc-client-executor-2688] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Ack server push request, request = NotifySubscriberRequest, requestId = 114
11:49:09.053 [nacos-grpc-client-executor-2693] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Receive server push request, request = NotifySubscriberRequest, requestId = 117
11:49:09.070 [nacos-grpc-client-executor-2693] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Ack server push request, request = NotifySubscriberRequest, requestId = 117
12:15:09.591 [nacos-grpc-client-executor-3205] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Receive server push request, request = NotifySubscriberRequest, requestId = 121
12:15:09.591 [nacos-grpc-client-executor-3205] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Ack server push request, request = NotifySubscriberRequest, requestId = 121
12:32:09.270 [nacos-grpc-client-executor-3544] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Receive server push request, request = NotifySubscriberRequest, requestId = 125
12:32:09.282 [nacos-grpc-client-executor-3544] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Ack server push request, request = NotifySubscriberRequest, requestId = 125
12:36:21.637 [nacos-grpc-client-executor-3633] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Receive server push request, request = NotifySubscriberRequest, requestId = 128
12:36:21.645 [nacos-grpc-client-executor-3633] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Ack server push request, request = NotifySubscriberRequest, requestId = 128
12:36:26.073 [nacos-grpc-client-executor-3634] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Receive server push request, request = NotifySubscriberRequest, requestId = 132
12:36:26.089 [nacos-grpc-client-executor-3634] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Ack server push request, request = NotifySubscriberRequest, requestId = 132
12:36:42.689 [nacos-grpc-client-executor-3641] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Receive server push request, request = NotifySubscriberRequest, requestId = 135
12:36:42.707 [nacos-grpc-client-executor-3641] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [da458ee5-cbb0-450c-bf3e-f25c1af99d9e] Ack server push request, request = NotifySubscriberRequest, requestId = 135
14:07:45.707 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:07:45.714 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:07:46.032 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:07:46.032 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@6cf7e41e[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:07:46.032 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755739927985_127.0.0.1_13232
14:07:46.032 [nacos-grpc-client-executor-5408] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755739927985_127.0.0.1_13232]Ignore complete event,isRunning:false,isAbandon=false
14:07:46.035 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@72c1c56d[Running, pool size = 8, active threads = 0, queued tasks = 0, completed tasks = 5409]
14:10:26.077 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:10:27.638 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2fb14c24-10d7-44ba-b127-81fb0a7f2e46_config-0
14:10:27.836 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 111 ms to scan 1 urls, producing 3 keys and 6 values 
14:10:27.909 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 4 keys and 9 values 
14:10:27.933 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 3 keys and 10 values 
14:10:27.961 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 1 keys and 5 values 
14:10:27.992 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 1 keys and 7 values 
14:10:28.022 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 2 keys and 8 values 
14:10:28.028 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fb14c24-10d7-44ba-b127-81fb0a7f2e46_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:10:28.029 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fb14c24-10d7-44ba-b127-81fb0a7f2e46_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001db3a3b4948
14:10:28.030 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fb14c24-10d7-44ba-b127-81fb0a7f2e46_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001db3a3b4b68
14:10:28.031 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fb14c24-10d7-44ba-b127-81fb0a7f2e46_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:10:28.033 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fb14c24-10d7-44ba-b127-81fb0a7f2e46_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:10:28.059 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fb14c24-10d7-44ba-b127-81fb0a7f2e46_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:30.508 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fb14c24-10d7-44ba-b127-81fb0a7f2e46_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755756630091_127.0.0.1_6222
14:10:30.509 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fb14c24-10d7-44ba-b127-81fb0a7f2e46_config-0] Notify connected event to listeners.
14:10:30.510 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fb14c24-10d7-44ba-b127-81fb0a7f2e46_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:10:30.511 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2fb14c24-10d7-44ba-b127-81fb0a7f2e46_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001db3a4ee780
14:10:30.808 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:10:38.748 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
14:10:41.496 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
14:10:42.579 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8a17a6e3-f9f3-42ab-93a3-7365a9d97e71_config-0
14:10:42.580 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a17a6e3-f9f3-42ab-93a3-7365a9d97e71_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:10:42.581 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a17a6e3-f9f3-42ab-93a3-7365a9d97e71_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001db3a3b4948
14:10:42.581 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a17a6e3-f9f3-42ab-93a3-7365a9d97e71_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001db3a3b4b68
14:10:42.581 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a17a6e3-f9f3-42ab-93a3-7365a9d97e71_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:10:42.582 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a17a6e3-f9f3-42ab-93a3-7365a9d97e71_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:10:42.583 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a17a6e3-f9f3-42ab-93a3-7365a9d97e71_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:42.720 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a17a6e3-f9f3-42ab-93a3-7365a9d97e71_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755756642601_127.0.0.1_6248
14:10:42.720 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a17a6e3-f9f3-42ab-93a3-7365a9d97e71_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:10:42.720 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a17a6e3-f9f3-42ab-93a3-7365a9d97e71_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001db3a4ee780
14:10:42.720 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8a17a6e3-f9f3-42ab-93a3-7365a9d97e71_config-0] Notify connected event to listeners.
14:10:42.885 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7bb63c10-fa6e-4bc7-af9d-214a975d867b
14:10:42.886 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] RpcClient init label, labels = {module=naming, source=sdk}
14:10:42.890 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:10:42.890 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:10:42.891 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:10:42.892 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:10:43.035 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Success to connect to server [localhost:8848] on start up, connectionId = 1755756642914_127.0.0.1_6249
14:10:43.035 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:10:43.035 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001db3a4ee780
14:10:43.035 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Notify connected event to listeners.
14:10:43.603 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Receive server push request, request = NotifySubscriberRequest, requestId = 139
14:10:43.604 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Ack server push request, request = NotifySubscriberRequest, requestId = 139
14:10:43.687 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Receive server push request, request = NotifySubscriberRequest, requestId = 140
14:10:43.688 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Ack server push request, request = NotifySubscriberRequest, requestId = 140
14:10:43.751 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.21:8081 register finished
14:10:43.804 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 19.491 seconds (JVM running for 22.708)
14:10:43.813 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
14:10:43.815 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
14:10:43.815 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
14:10:44.293 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Receive server push request, request = NotifySubscriberRequest, requestId = 141
14:10:44.293 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Ack server push request, request = NotifySubscriberRequest, requestId = 141
14:10:56.123 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Receive server push request, request = NotifySubscriberRequest, requestId = 145
14:10:56.124 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Ack server push request, request = NotifySubscriberRequest, requestId = 145
14:10:56.130 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Receive server push request, request = NotifySubscriberRequest, requestId = 146
14:10:56.131 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Ack server push request, request = NotifySubscriberRequest, requestId = 146
14:10:56.138 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Receive server push request, request = NotifySubscriberRequest, requestId = 147
14:10:56.139 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Ack server push request, request = NotifySubscriberRequest, requestId = 147
14:17:59.218 [nacos-grpc-client-executor-175] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Receive server push request, request = NotifySubscriberRequest, requestId = 148
14:17:59.234 [nacos-grpc-client-executor-175] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Ack server push request, request = NotifySubscriberRequest, requestId = 148
14:18:16.214 [nacos-grpc-client-executor-179] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Receive server push request, request = NotifySubscriberRequest, requestId = 149
14:18:16.231 [nacos-grpc-client-executor-179] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Ack server push request, request = NotifySubscriberRequest, requestId = 149
14:36:16.825 [nacos-grpc-client-executor-513] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Receive server push request, request = NotifySubscriberRequest, requestId = 153
14:36:16.845 [nacos-grpc-client-executor-513] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Ack server push request, request = NotifySubscriberRequest, requestId = 153
14:36:34.516 [nacos-grpc-client-executor-516] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Receive server push request, request = NotifySubscriberRequest, requestId = 155
14:36:34.533 [nacos-grpc-client-executor-516] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Ack server push request, request = NotifySubscriberRequest, requestId = 155
16:21:03.603 [nacos-grpc-client-executor-2492] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Receive server push request, request = NotifySubscriberRequest, requestId = 158
16:21:03.619 [nacos-grpc-client-executor-2492] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Ack server push request, request = NotifySubscriberRequest, requestId = 158
16:21:48.997 [nacos-grpc-client-executor-2507] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Receive server push request, request = NotifySubscriberRequest, requestId = 160
16:21:49.012 [nacos-grpc-client-executor-2507] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Ack server push request, request = NotifySubscriberRequest, requestId = 160
16:25:20.795 [nacos-grpc-client-executor-2573] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Receive server push request, request = NotifySubscriberRequest, requestId = 163
16:25:20.808 [nacos-grpc-client-executor-2573] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Ack server push request, request = NotifySubscriberRequest, requestId = 163
16:25:51.766 [nacos-grpc-client-executor-2583] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Receive server push request, request = NotifySubscriberRequest, requestId = 165
16:25:51.779 [nacos-grpc-client-executor-2583] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Ack server push request, request = NotifySubscriberRequest, requestId = 165
17:14:45.872 [nacos-grpc-client-executor-3500] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Receive server push request, request = NotifySubscriberRequest, requestId = 168
17:14:45.885 [nacos-grpc-client-executor-3500] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Ack server push request, request = NotifySubscriberRequest, requestId = 168
17:15:24.984 [nacos-grpc-client-executor-3515] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Receive server push request, request = NotifySubscriberRequest, requestId = 170
17:15:24.999 [nacos-grpc-client-executor-3515] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Ack server push request, request = NotifySubscriberRequest, requestId = 170
17:35:16.001 [nacos-grpc-client-executor-3862] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Receive server push request, request = NotifySubscriberRequest, requestId = 173
17:35:16.018 [nacos-grpc-client-executor-3862] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Ack server push request, request = NotifySubscriberRequest, requestId = 173
17:35:35.580 [nacos-grpc-client-executor-3869] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Receive server push request, request = NotifySubscriberRequest, requestId = 175
17:35:35.596 [nacos-grpc-client-executor-3869] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Ack server push request, request = NotifySubscriberRequest, requestId = 175
19:51:45.374 [nacos-grpc-client-executor-6310] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Receive server push request, request = NotifySubscriberRequest, requestId = 178
19:51:45.399 [nacos-grpc-client-executor-6310] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7bb63c10-fa6e-4bc7-af9d-214a975d867b] Ack server push request, request = NotifySubscriberRequest, requestId = 178
19:57:38.972 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
19:57:38.988 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
19:57:39.323 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
19:57:39.323 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@62835587[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
19:57:39.323 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755756642914_127.0.0.1_6249
19:57:39.324 [nacos-grpc-client-executor-6421] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755756642914_127.0.0.1_6249]Ignore complete event,isRunning:false,isAbandon=false
19:57:39.329 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3ebf0a06[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 6422]
19:58:42.890 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
19:58:45.107 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 7ca44cff-0190-42c1-8805-e3863c65804e_config-0
19:58:45.306 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 109 ms to scan 1 urls, producing 3 keys and 6 values 
19:58:45.399 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 59 ms to scan 1 urls, producing 4 keys and 9 values 
19:58:45.426 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 22 ms to scan 1 urls, producing 3 keys and 10 values 
19:58:45.458 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 21 ms to scan 1 urls, producing 1 keys and 5 values 
19:58:45.490 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 1 keys and 7 values 
19:58:45.522 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 25 ms to scan 1 urls, producing 2 keys and 8 values 
19:58:45.532 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ca44cff-0190-42c1-8805-e3863c65804e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:58:45.534 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ca44cff-0190-42c1-8805-e3863c65804e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001ae363b94f0
19:58:45.535 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ca44cff-0190-42c1-8805-e3863c65804e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001ae363b9710
19:58:45.537 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ca44cff-0190-42c1-8805-e3863c65804e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:58:45.540 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ca44cff-0190-42c1-8805-e3863c65804e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:58:45.573 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ca44cff-0190-42c1-8805-e3863c65804e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:58:48.304 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ca44cff-0190-42c1-8805-e3863c65804e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755777527871_127.0.0.1_9181
19:58:48.306 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ca44cff-0190-42c1-8805-e3863c65804e_config-0] Notify connected event to listeners.
19:58:48.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ca44cff-0190-42c1-8805-e3863c65804e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:58:48.309 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [7ca44cff-0190-42c1-8805-e3863c65804e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001ae364f1210
19:58:48.634 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
19:58:58.092 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
19:59:00.263 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
19:59:01.457 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 43ae9394-84fb-4d4e-ae06-bfd4ff9b5072_config-0
19:59:01.458 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [43ae9394-84fb-4d4e-ae06-bfd4ff9b5072_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
19:59:01.458 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [43ae9394-84fb-4d4e-ae06-bfd4ff9b5072_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001ae363b94f0
19:59:01.458 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [43ae9394-84fb-4d4e-ae06-bfd4ff9b5072_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001ae363b9710
19:59:01.458 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [43ae9394-84fb-4d4e-ae06-bfd4ff9b5072_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
19:59:01.459 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [43ae9394-84fb-4d4e-ae06-bfd4ff9b5072_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
19:59:01.459 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [43ae9394-84fb-4d4e-ae06-bfd4ff9b5072_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:59:01.592 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [43ae9394-84fb-4d4e-ae06-bfd4ff9b5072_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755777541473_127.0.0.1_9231
19:59:01.592 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [43ae9394-84fb-4d4e-ae06-bfd4ff9b5072_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:59:01.592 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [43ae9394-84fb-4d4e-ae06-bfd4ff9b5072_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001ae364f1210
19:59:01.592 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [43ae9394-84fb-4d4e-ae06-bfd4ff9b5072_config-0] Notify connected event to listeners.
19:59:01.762 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 8f093537-812e-4bec-b193-9177e610ff41
19:59:01.763 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f093537-812e-4bec-b193-9177e610ff41] RpcClient init label, labels = {module=naming, source=sdk}
19:59:01.766 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f093537-812e-4bec-b193-9177e610ff41] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
19:59:01.766 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f093537-812e-4bec-b193-9177e610ff41] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
19:59:01.768 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f093537-812e-4bec-b193-9177e610ff41] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
19:59:01.769 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f093537-812e-4bec-b193-9177e610ff41] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
19:59:01.903 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f093537-812e-4bec-b193-9177e610ff41] Success to connect to server [localhost:8848] on start up, connectionId = 1755777541777_127.0.0.1_9232
19:59:01.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f093537-812e-4bec-b193-9177e610ff41] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
19:59:01.904 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f093537-812e-4bec-b193-9177e610ff41] Notify connected event to listeners.
19:59:01.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f093537-812e-4bec-b193-9177e610ff41] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001ae364f1210
19:59:02.444 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f093537-812e-4bec-b193-9177e610ff41] Receive server push request, request = NotifySubscriberRequest, requestId = 181
19:59:02.445 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f093537-812e-4bec-b193-9177e610ff41] Ack server push request, request = NotifySubscriberRequest, requestId = 181
19:59:02.622 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.21:8081 register finished
19:59:02.641 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f093537-812e-4bec-b193-9177e610ff41] Receive server push request, request = NotifySubscriberRequest, requestId = 182
19:59:02.642 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f093537-812e-4bec-b193-9177e610ff41] Ack server push request, request = NotifySubscriberRequest, requestId = 182
19:59:02.657 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f093537-812e-4bec-b193-9177e610ff41] Receive server push request, request = NotifySubscriberRequest, requestId = 183
19:59:02.657 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f093537-812e-4bec-b193-9177e610ff41] Ack server push request, request = NotifySubscriberRequest, requestId = 183
19:59:02.669 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f093537-812e-4bec-b193-9177e610ff41] Receive server push request, request = NotifySubscriberRequest, requestId = 184
19:59:02.671 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f093537-812e-4bec-b193-9177e610ff41] Ack server push request, request = NotifySubscriberRequest, requestId = 184
19:59:02.692 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 21.49 seconds (JVM running for 24.737)
19:59:02.700 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
19:59:02.702 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
19:59:02.703 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
19:59:03.174 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f093537-812e-4bec-b193-9177e610ff41] Receive server push request, request = NotifySubscriberRequest, requestId = 185
19:59:03.188 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f093537-812e-4bec-b193-9177e610ff41] Ack server push request, request = NotifySubscriberRequest, requestId = 185
19:59:03.824 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f093537-812e-4bec-b193-9177e610ff41] Receive server push request, request = NotifySubscriberRequest, requestId = 187
19:59:03.840 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f093537-812e-4bec-b193-9177e610ff41] Ack server push request, request = NotifySubscriberRequest, requestId = 187
19:59:04.674 [nacos-grpc-client-executor-45] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f093537-812e-4bec-b193-9177e610ff41] Receive server push request, request = NotifySubscriberRequest, requestId = 189
19:59:04.691 [nacos-grpc-client-executor-45] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f093537-812e-4bec-b193-9177e610ff41] Ack server push request, request = NotifySubscriberRequest, requestId = 189
19:59:32.691 [nacos-grpc-client-executor-59] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f093537-812e-4bec-b193-9177e610ff41] Receive server push request, request = NotifySubscriberRequest, requestId = 191
19:59:32.692 [nacos-grpc-client-executor-59] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [8f093537-812e-4bec-b193-9177e610ff41] Ack server push request, request = NotifySubscriberRequest, requestId = 191
