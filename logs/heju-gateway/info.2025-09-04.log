16:41:24.496 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
16:41:25.087 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of de1186d3-1bd7-4e4c-bca7-cfbbeb68db3c_config-0
16:41:25.142 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 28 ms to scan 1 urls, producing 3 keys and 6 values 
16:41:25.159 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 4 keys and 9 values 
16:41:25.166 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 5 ms to scan 1 urls, producing 3 keys and 10 values 
16:41:25.172 [main] INFO  o.r.Reflections - [scan,232] - Re<PERSON> took 4 ms to scan 1 urls, producing 1 keys and 5 values 
16:41:25.179 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 5 ms to scan 1 urls, producing 1 keys and 7 values 
16:41:25.188 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 7 ms to scan 1 urls, producing 2 keys and 8 values 
16:41:25.191 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de1186d3-1bd7-4e4c-bca7-cfbbeb68db3c_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:41:25.191 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de1186d3-1bd7-4e4c-bca7-cfbbeb68db3c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000016fc13b94f0
16:41:25.191 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de1186d3-1bd7-4e4c-bca7-cfbbeb68db3c_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000016fc13b9710
16:41:25.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de1186d3-1bd7-4e4c-bca7-cfbbeb68db3c_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:41:25.193 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de1186d3-1bd7-4e4c-bca7-cfbbeb68db3c_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:41:25.201 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de1186d3-1bd7-4e4c-bca7-cfbbeb68db3c_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:41:26.134 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de1186d3-1bd7-4e4c-bca7-cfbbeb68db3c_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756975285921_127.0.0.1_3464
16:41:26.134 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de1186d3-1bd7-4e4c-bca7-cfbbeb68db3c_config-0] Notify connected event to listeners.
16:41:26.135 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de1186d3-1bd7-4e4c-bca7-cfbbeb68db3c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:41:26.135 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [de1186d3-1bd7-4e4c-bca7-cfbbeb68db3c_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000016fc14f1210
16:41:26.275 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
16:41:29.712 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
16:41:30.742 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
16:41:31.258 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 86321f0e-af0e-405e-910a-f9e8e3965fca_config-0
16:41:31.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86321f0e-af0e-405e-910a-f9e8e3965fca_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
16:41:31.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86321f0e-af0e-405e-910a-f9e8e3965fca_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000016fc13b94f0
16:41:31.258 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86321f0e-af0e-405e-910a-f9e8e3965fca_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000016fc13b9710
16:41:31.259 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86321f0e-af0e-405e-910a-f9e8e3965fca_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
16:41:31.259 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86321f0e-af0e-405e-910a-f9e8e3965fca_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
16:41:31.259 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86321f0e-af0e-405e-910a-f9e8e3965fca_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:41:31.390 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86321f0e-af0e-405e-910a-f9e8e3965fca_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756975291276_127.0.0.1_3477
16:41:31.390 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86321f0e-af0e-405e-910a-f9e8e3965fca_config-0] Notify connected event to listeners.
16:41:31.391 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86321f0e-af0e-405e-910a-f9e8e3965fca_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:41:31.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [86321f0e-af0e-405e-910a-f9e8e3965fca_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000016fc14f1210
16:41:31.484 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of db0ebc14-58cd-40ec-938d-a0a1de2d6126
16:41:31.484 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db0ebc14-58cd-40ec-938d-a0a1de2d6126] RpcClient init label, labels = {module=naming, source=sdk}
16:41:31.486 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db0ebc14-58cd-40ec-938d-a0a1de2d6126] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
16:41:31.486 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db0ebc14-58cd-40ec-938d-a0a1de2d6126] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
16:41:31.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db0ebc14-58cd-40ec-938d-a0a1de2d6126] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
16:41:31.487 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db0ebc14-58cd-40ec-938d-a0a1de2d6126] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
16:41:31.621 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db0ebc14-58cd-40ec-938d-a0a1de2d6126] Success to connect to server [localhost:8848] on start up, connectionId = 1756975291497_127.0.0.1_3479
16:41:31.622 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db0ebc14-58cd-40ec-938d-a0a1de2d6126] Notify connected event to listeners.
16:41:31.622 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db0ebc14-58cd-40ec-938d-a0a1de2d6126] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
16:41:31.622 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db0ebc14-58cd-40ec-938d-a0a1de2d6126] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000016fc14f1210
16:41:32.023 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ************:8081 register finished
16:41:32.058 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 8.202 seconds (JVM running for 10.752)
16:41:32.066 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
16:41:32.067 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
16:41:32.067 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
16:41:32.418 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db0ebc14-58cd-40ec-938d-a0a1de2d6126] Receive server push request, request = NotifySubscriberRequest, requestId = 1
16:41:32.418 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db0ebc14-58cd-40ec-938d-a0a1de2d6126] Ack server push request, request = NotifySubscriberRequest, requestId = 1
16:42:02.370 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db0ebc14-58cd-40ec-938d-a0a1de2d6126] Receive server push request, request = NotifySubscriberRequest, requestId = 6
16:42:02.370 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db0ebc14-58cd-40ec-938d-a0a1de2d6126] Ack server push request, request = NotifySubscriberRequest, requestId = 6
16:42:02.377 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db0ebc14-58cd-40ec-938d-a0a1de2d6126] Receive server push request, request = NotifySubscriberRequest, requestId = 5
16:42:02.377 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db0ebc14-58cd-40ec-938d-a0a1de2d6126] Ack server push request, request = NotifySubscriberRequest, requestId = 5
16:42:02.385 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db0ebc14-58cd-40ec-938d-a0a1de2d6126] Receive server push request, request = NotifySubscriberRequest, requestId = 7
16:42:02.385 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [db0ebc14-58cd-40ec-938d-a0a1de2d6126] Ack server push request, request = NotifySubscriberRequest, requestId = 7
20:10:41.331 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:10:41.331 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:10:41.696 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:10:41.696 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@7827a113[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:10:41.697 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756975291497_127.0.0.1_3479
20:10:41.704 [nacos-grpc-client-executor-3973] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756975291497_127.0.0.1_3479]Ignore complete event,isRunning:false,isAbandon=false
20:10:41.715 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@f601147[Running, pool size = 8, active threads = 0, queued tasks = 0, completed tasks = 3974]
