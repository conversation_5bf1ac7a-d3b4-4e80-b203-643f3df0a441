09:45:59.011 [reactor-http-nio-3] ERROR c.h.g.f.<PERSON><PERSON><PERSON>er - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/enterprise/getInfo
09:45:59.011 [reactor-http-nio-2] ERROR c.h.g.f.AuthFilter - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/user/getInfo
10:30:18.057 [reactor-http-nio-10] ERROR c.h.g.f.AuthFilter - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/enterprise/getInfo
10:30:18.058 [reactor-http-nio-9] ERROR c.h.g.f.AuthFilter - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/user/getInfo
10:52:57.728 [reactor-http-nio-13] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/fieldList,异常信息:Connection prematurely closed BEFORE response
12:40:31.781 [reactor-http-nio-6] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/customerinfo/pass,异常信息:Connection prematurely closed BEFORE response
16:52:00.518 [reactor-http-nio-6] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/list,异常信息:Connection prematurely closed BEFORE response
17:14:50.148 [boundedElastic-165] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/getInfo,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
17:15:24.714 [boundedElastic-145] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/searchFieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
17:35:19.717 [boundedElastic-153] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/getInfo,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
19:54:23.761 [boundedElastic-269] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/fieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
19:54:27.797 [reactor-http-nio-2] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/searchFieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
19:54:28.099 [boundedElastic-269] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/list,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
19:54:28.101 [boundedElastic-269] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/fieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
19:54:30.467 [boundedElastic-269] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/searchFieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
19:54:30.503 [boundedElastic-269] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/list,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
19:54:30.790 [boundedElastic-269] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/fieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
19:54:32.076 [boundedElastic-269] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/searchFieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
19:54:32.389 [boundedElastic-269] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/list,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
19:54:32.391 [boundedElastic-269] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/fieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
19:54:33.709 [boundedElastic-269] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/searchFieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
19:54:33.765 [boundedElastic-269] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/list,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
19:54:34.036 [boundedElastic-269] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/fieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
19:54:36.027 [boundedElastic-269] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/searchFieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
19:54:36.351 [boundedElastic-269] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/list,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
19:54:36.351 [boundedElastic-265] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/fieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
19:55:15.612 [boundedElastic-274] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/searchFieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
19:55:15.924 [boundedElastic-274] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/list,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
19:55:15.932 [boundedElastic-274] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/fieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
19:55:16.908 [boundedElastic-274] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/searchFieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
19:55:16.963 [boundedElastic-274] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/list,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
19:55:17.206 [boundedElastic-274] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/fieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
