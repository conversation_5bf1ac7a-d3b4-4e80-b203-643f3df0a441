09:32:59.314 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:33:00.172 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6e4a9346-8c19-4165-a4a5-94ac31418515_config-0
09:33:00.281 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 51 ms to scan 1 urls, producing 3 keys and 6 values 
09:33:00.328 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 19 ms to scan 1 urls, producing 4 keys and 9 values 
09:33:00.342 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 3 keys and 10 values 
09:33:00.358 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 1 keys and 5 values 
09:33:00.372 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 1 keys and 7 values 
09:33:00.386 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
09:33:00.391 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e4a9346-8c19-4165-a4a5-94ac31418515_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:33:00.391 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e4a9346-8c19-4165-a4a5-94ac31418515_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000028f013b8638
09:33:00.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e4a9346-8c19-4165-a4a5-94ac31418515_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000028f013b8858
09:33:00.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e4a9346-8c19-4165-a4a5-94ac31418515_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:33:00.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e4a9346-8c19-4165-a4a5-94ac31418515_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:33:00.411 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e4a9346-8c19-4165-a4a5-94ac31418515_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:33:01.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e4a9346-8c19-4165-a4a5-94ac31418515_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754962381628_127.0.0.1_12685
09:33:01.951 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e4a9346-8c19-4165-a4a5-94ac31418515_config-0] Notify connected event to listeners.
09:33:01.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e4a9346-8c19-4165-a4a5-94ac31418515_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:33:01.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e4a9346-8c19-4165-a4a5-94ac31418515_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000028f014f0228
09:33:02.177 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:33:11.588 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:33:17.992 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:33:19.881 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 26f900d1-6af9-4349-a472-4c2d58ef83df_config-0
09:33:19.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26f900d1-6af9-4349-a472-4c2d58ef83df_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:33:19.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26f900d1-6af9-4349-a472-4c2d58ef83df_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000028f013b8638
09:33:19.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26f900d1-6af9-4349-a472-4c2d58ef83df_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000028f013b8858
09:33:19.885 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26f900d1-6af9-4349-a472-4c2d58ef83df_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:33:19.885 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26f900d1-6af9-4349-a472-4c2d58ef83df_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:33:19.887 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26f900d1-6af9-4349-a472-4c2d58ef83df_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:33:20.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26f900d1-6af9-4349-a472-4c2d58ef83df_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754962399929_127.0.0.1_12726
09:33:20.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26f900d1-6af9-4349-a472-4c2d58ef83df_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:33:20.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26f900d1-6af9-4349-a472-4c2d58ef83df_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000028f014f0228
09:33:20.483 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26f900d1-6af9-4349-a472-4c2d58ef83df_config-0] Notify connected event to listeners.
09:33:20.898 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 00a7056f-660f-4955-9e52-fc026d0137c5
09:33:20.902 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] RpcClient init label, labels = {module=naming, source=sdk}
09:33:20.906 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:33:20.908 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:33:20.910 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:33:20.910 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:33:21.187 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Success to connect to server [localhost:8848] on start up, connectionId = 1754962400950_127.0.0.1_12729
09:33:21.187 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:33:21.187 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Notify connected event to listeners.
09:33:21.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000028f014f0228
09:33:21.797 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:33:21.799 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:33:22.090 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:33:22.092 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:33:22.644 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.2:8081 register finished
09:33:22.733 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 24.162 seconds (JVM running for 29.831)
09:33:22.749 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:33:22.751 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:33:22.753 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:33:23.169 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:33:23.171 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:33:52.235 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:33:52.236 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:33:52.243 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:33:52.244 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:34:22.328 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:34:22.330 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Ack server push request, request = NotifySubscriberRequest, requestId = 10
12:07:49.487 [nacos-grpc-client-executor-3054] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Receive server push request, request = NotifySubscriberRequest, requestId = 13
12:07:49.575 [nacos-grpc-client-executor-3054] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Ack server push request, request = NotifySubscriberRequest, requestId = 13
12:08:35.904 [nacos-grpc-client-executor-3071] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Receive server push request, request = NotifySubscriberRequest, requestId = 15
12:08:35.912 [nacos-grpc-client-executor-3071] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Ack server push request, request = NotifySubscriberRequest, requestId = 15
13:28:51.437 [nacos-grpc-client-executor-4565] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Receive server push request, request = NotifySubscriberRequest, requestId = 18
13:28:51.454 [nacos-grpc-client-executor-4565] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Ack server push request, request = NotifySubscriberRequest, requestId = 18
13:29:20.721 [nacos-grpc-client-executor-4573] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Receive server push request, request = NotifySubscriberRequest, requestId = 20
13:29:20.733 [nacos-grpc-client-executor-4573] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Ack server push request, request = NotifySubscriberRequest, requestId = 20
13:54:08.724 [nacos-grpc-client-executor-5023] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Receive server push request, request = NotifySubscriberRequest, requestId = 23
13:54:08.741 [nacos-grpc-client-executor-5023] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Ack server push request, request = NotifySubscriberRequest, requestId = 23
13:54:40.184 [nacos-grpc-client-executor-5034] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Receive server push request, request = NotifySubscriberRequest, requestId = 25
13:54:40.204 [nacos-grpc-client-executor-5034] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Ack server push request, request = NotifySubscriberRequest, requestId = 25
14:46:59.713 [nacos-grpc-client-executor-6000] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Receive server push request, request = NotifySubscriberRequest, requestId = 28
14:46:59.753 [nacos-grpc-client-executor-6000] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Ack server push request, request = NotifySubscriberRequest, requestId = 28
14:47:36.291 [nacos-grpc-client-executor-6012] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Receive server push request, request = NotifySubscriberRequest, requestId = 30
14:47:36.310 [nacos-grpc-client-executor-6012] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Ack server push request, request = NotifySubscriberRequest, requestId = 30
14:52:11.411 [nacos-grpc-client-executor-6098] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Receive server push request, request = NotifySubscriberRequest, requestId = 33
14:52:11.427 [nacos-grpc-client-executor-6098] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Ack server push request, request = NotifySubscriberRequest, requestId = 33
14:52:38.625 [nacos-grpc-client-executor-6107] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Receive server push request, request = NotifySubscriberRequest, requestId = 35
14:52:38.641 [nacos-grpc-client-executor-6107] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Ack server push request, request = NotifySubscriberRequest, requestId = 35
14:52:58.191 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:52:58.210 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:52:58.558 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:52:58.563 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@135ccf14[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:52:58.563 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754962400950_127.0.0.1_12729
14:52:58.567 [nacos-grpc-client-executor-6115] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754962400950_127.0.0.1_12729]Ignore complete event,isRunning:false,isAbandon=false
14:52:58.580 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@f499c55[Running, pool size = 7, active threads = 0, queued tasks = 0, completed tasks = 6116]
14:55:39.525 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
14:55:41.119 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 60068b33-4088-443e-89f5-f4394e20f4db_config-0
14:55:41.290 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 83 ms to scan 1 urls, producing 3 keys and 6 values 
14:55:41.403 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 68 ms to scan 1 urls, producing 4 keys and 9 values 
14:55:41.436 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 27 ms to scan 1 urls, producing 3 keys and 10 values 
14:55:41.469 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 24 ms to scan 1 urls, producing 1 keys and 5 values 
14:55:41.494 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 20 ms to scan 1 urls, producing 1 keys and 7 values 
14:55:41.526 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 30 ms to scan 1 urls, producing 2 keys and 8 values 
14:55:41.534 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60068b33-4088-443e-89f5-f4394e20f4db_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:55:41.536 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60068b33-4088-443e-89f5-f4394e20f4db_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001e0013cc2b8
14:55:41.536 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60068b33-4088-443e-89f5-f4394e20f4db_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001e0013cc4d8
14:55:41.536 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60068b33-4088-443e-89f5-f4394e20f4db_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:55:41.540 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60068b33-4088-443e-89f5-f4394e20f4db_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:55:41.558 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60068b33-4088-443e-89f5-f4394e20f4db_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:55:44.159 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60068b33-4088-443e-89f5-f4394e20f4db_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754981743788_127.0.0.1_6553
14:55:44.161 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60068b33-4088-443e-89f5-f4394e20f4db_config-0] Notify connected event to listeners.
14:55:44.162 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60068b33-4088-443e-89f5-f4394e20f4db_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:55:44.163 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [60068b33-4088-443e-89f5-f4394e20f4db_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001e001506780
14:55:44.444 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
14:55:54.921 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
14:55:57.128 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
14:55:58.737 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 387f73f8-63dc-4bc4-a0f2-bf90c3b3ee5a_config-0
14:55:58.737 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [387f73f8-63dc-4bc4-a0f2-bf90c3b3ee5a_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
14:55:58.738 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [387f73f8-63dc-4bc4-a0f2-bf90c3b3ee5a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$463/0x000001e0013cc2b8
14:55:58.738 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [387f73f8-63dc-4bc4-a0f2-bf90c3b3ee5a_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$464/0x000001e0013cc4d8
14:55:58.739 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [387f73f8-63dc-4bc4-a0f2-bf90c3b3ee5a_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
14:55:58.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [387f73f8-63dc-4bc4-a0f2-bf90c3b3ee5a_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
14:55:58.740 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [387f73f8-63dc-4bc4-a0f2-bf90c3b3ee5a_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:55:58.887 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [387f73f8-63dc-4bc4-a0f2-bf90c3b3ee5a_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754981758761_127.0.0.1_6611
14:55:58.888 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [387f73f8-63dc-4bc4-a0f2-bf90c3b3ee5a_config-0] Notify connected event to listeners.
14:55:58.888 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [387f73f8-63dc-4bc4-a0f2-bf90c3b3ee5a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:55:58.888 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [387f73f8-63dc-4bc4-a0f2-bf90c3b3ee5a_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001e001506780
14:55:59.089 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1c0842e5-33e8-450c-98c5-4dd74eac9a4b
14:55:59.090 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c0842e5-33e8-450c-98c5-4dd74eac9a4b] RpcClient init label, labels = {module=naming, source=sdk}
14:55:59.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c0842e5-33e8-450c-98c5-4dd74eac9a4b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
14:55:59.094 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c0842e5-33e8-450c-98c5-4dd74eac9a4b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
14:55:59.095 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c0842e5-33e8-450c-98c5-4dd74eac9a4b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
14:55:59.096 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c0842e5-33e8-450c-98c5-4dd74eac9a4b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
14:55:59.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c0842e5-33e8-450c-98c5-4dd74eac9a4b] Success to connect to server [localhost:8848] on start up, connectionId = 1754981759115_127.0.0.1_6613
14:55:59.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c0842e5-33e8-450c-98c5-4dd74eac9a4b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
14:55:59.243 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c0842e5-33e8-450c-98c5-4dd74eac9a4b] Notify connected event to listeners.
14:55:59.243 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c0842e5-33e8-450c-98c5-4dd74eac9a4b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$478/0x000001e001506780
14:55:59.828 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c0842e5-33e8-450c-98c5-4dd74eac9a4b] Receive server push request, request = NotifySubscriberRequest, requestId = 40
14:55:59.829 [nacos-grpc-client-executor-11] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c0842e5-33e8-450c-98c5-4dd74eac9a4b] Ack server push request, request = NotifySubscriberRequest, requestId = 40
14:55:59.920 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c0842e5-33e8-450c-98c5-4dd74eac9a4b] Receive server push request, request = NotifySubscriberRequest, requestId = 42
14:55:59.920 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c0842e5-33e8-450c-98c5-4dd74eac9a4b] Ack server push request, request = NotifySubscriberRequest, requestId = 42
14:55:59.934 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c0842e5-33e8-450c-98c5-4dd74eac9a4b] Receive server push request, request = NotifySubscriberRequest, requestId = 41
14:55:59.934 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c0842e5-33e8-450c-98c5-4dd74eac9a4b] Ack server push request, request = NotifySubscriberRequest, requestId = 41
14:55:59.977 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.2:8081 register finished
14:56:00.034 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 21.942 seconds (JVM running for 24.052)
14:56:00.045 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
14:56:00.047 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
14:56:00.048 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
14:56:00.596 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c0842e5-33e8-450c-98c5-4dd74eac9a4b] Receive server push request, request = NotifySubscriberRequest, requestId = 43
14:56:00.598 [nacos-grpc-client-executor-25] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c0842e5-33e8-450c-98c5-4dd74eac9a4b] Ack server push request, request = NotifySubscriberRequest, requestId = 43
14:56:25.460 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c0842e5-33e8-450c-98c5-4dd74eac9a4b] Receive server push request, request = NotifySubscriberRequest, requestId = 46
14:56:25.462 [nacos-grpc-client-executor-37] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c0842e5-33e8-450c-98c5-4dd74eac9a4b] Ack server push request, request = NotifySubscriberRequest, requestId = 46
14:56:25.469 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c0842e5-33e8-450c-98c5-4dd74eac9a4b] Receive server push request, request = NotifySubscriberRequest, requestId = 47
14:56:25.469 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c0842e5-33e8-450c-98c5-4dd74eac9a4b] Ack server push request, request = NotifySubscriberRequest, requestId = 47
15:17:32.474 [nacos-grpc-client-executor-443] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c0842e5-33e8-450c-98c5-4dd74eac9a4b] Receive server push request, request = NotifySubscriberRequest, requestId = 48
15:17:32.487 [nacos-grpc-client-executor-443] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c0842e5-33e8-450c-98c5-4dd74eac9a4b] Ack server push request, request = NotifySubscriberRequest, requestId = 48
15:18:14.312 [nacos-grpc-client-executor-456] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c0842e5-33e8-450c-98c5-4dd74eac9a4b] Receive server push request, request = NotifySubscriberRequest, requestId = 49
15:18:14.327 [nacos-grpc-client-executor-456] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1c0842e5-33e8-450c-98c5-4dd74eac9a4b] Ack server push request, request = NotifySubscriberRequest, requestId = 49
15:28:06.242 [lettuce-nioEventLoop-5-1] INFO  i.l.c.p.CommandHandler - [log,217] - null Unexpected exception during request: java.net.SocketException: Connection reset
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:259)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
15:28:06.242 [lettuce-nioEventLoop-5-2] INFO  i.l.c.p.CommandHandler - [log,217] - null Unexpected exception during request: java.net.SocketException: Connection reset
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:259)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
15:28:06.304 [lettuce-eventExecutorLoop-3-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /192.168.1.200:6379
15:28:06.304 [lettuce-eventExecutorLoop-3-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /192.168.1.200:6379
15:28:15.380 [lettuce-eventExecutorLoop-3-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
15:28:15.380 [lettuce-eventExecutorLoop-3-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
15:28:23.687 [lettuce-eventExecutorLoop-3-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
15:28:23.687 [lettuce-eventExecutorLoop-3-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
15:28:40.185 [lettuce-eventExecutorLoop-3-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
15:28:40.185 [lettuce-eventExecutorLoop-3-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
15:29:20.279 [lettuce-eventExecutorLoop-3-19] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
15:29:20.279 [lettuce-eventExecutorLoop-3-20] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
15:30:00.375 [lettuce-eventExecutorLoop-3-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
15:30:00.375 [lettuce-eventExecutorLoop-3-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
15:30:40.487 [lettuce-eventExecutorLoop-3-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
15:30:40.487 [lettuce-eventExecutorLoop-3-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
15:30:40.503 [lettuce-nioEventLoop-5-15] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to 192.168.1.200/<unresolved>:6379
15:30:40.503 [lettuce-nioEventLoop-5-16] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to 192.168.1.200/<unresolved>:6379
19:00:32.152 [lettuce-nioEventLoop-5-16] INFO  i.l.c.p.CommandHandler - [log,217] - null Unexpected exception during request: java.net.SocketException: Connection reset
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:259)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
19:00:32.152 [lettuce-nioEventLoop-5-15] INFO  i.l.c.p.CommandHandler - [log,217] - null Unexpected exception during request: java.net.SocketException: Connection reset
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:259)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.buffer.WrappedByteBuf.writeBytes(WrappedByteBuf.java:821)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
19:00:32.176 [lettuce-eventExecutorLoop-3-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /192.168.1.200:6379
19:00:32.176 [lettuce-eventExecutorLoop-3-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /192.168.1.200:6379
19:00:43.275 [lettuce-eventExecutorLoop-3-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:00:43.275 [lettuce-eventExecutorLoop-3-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:00:53.874 [lettuce-eventExecutorLoop-3-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:00:53.874 [lettuce-eventExecutorLoop-3-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:01:04.978 [lettuce-eventExecutorLoop-3-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:01:04.979 [lettuce-eventExecutorLoop-3-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:01:17.074 [lettuce-eventExecutorLoop-3-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:01:17.074 [lettuce-eventExecutorLoop-3-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:01:31.175 [lettuce-eventExecutorLoop-3-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:01:31.175 [lettuce-eventExecutorLoop-3-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:01:49.482 [lettuce-eventExecutorLoop-3-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:01:49.486 [lettuce-eventExecutorLoop-3-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:02:15.975 [lettuce-eventExecutorLoop-3-17] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:02:15.979 [lettuce-eventExecutorLoop-3-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:02:56.084 [lettuce-eventExecutorLoop-3-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:02:56.084 [lettuce-eventExecutorLoop-3-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:03:36.175 [lettuce-eventExecutorLoop-3-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:03:36.175 [lettuce-eventExecutorLoop-3-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:04:16.274 [lettuce-eventExecutorLoop-3-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:04:16.274 [lettuce-eventExecutorLoop-3-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:04:56.375 [lettuce-eventExecutorLoop-3-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:04:56.375 [lettuce-eventExecutorLoop-3-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:05:36.477 [lettuce-eventExecutorLoop-3-19] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:05:36.477 [lettuce-eventExecutorLoop-3-20] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:06:16.581 [lettuce-eventExecutorLoop-3-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:06:16.581 [lettuce-eventExecutorLoop-3-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:06:46.677 [lettuce-eventExecutorLoop-3-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:06:46.677 [lettuce-eventExecutorLoop-3-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:06:46.685 [lettuce-nioEventLoop-5-16] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to 192.168.1.200/<unresolved>:6379
19:06:46.685 [lettuce-nioEventLoop-5-15] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to 192.168.1.200/<unresolved>:6379
19:43:18.164 [lettuce-nioEventLoop-5-15] INFO  i.l.c.p.CommandHandler - [log,217] - null Unexpected exception during request: java.net.SocketException: Connection reset
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:259)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
19:43:18.164 [lettuce-nioEventLoop-5-16] INFO  i.l.c.p.CommandHandler - [log,217] - null Unexpected exception during request: java.net.SocketException: Connection reset
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:259)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
19:43:18.180 [lettuce-eventExecutorLoop-3-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /192.168.1.200:6379
19:43:18.180 [lettuce-eventExecutorLoop-3-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /192.168.1.200:6379
19:43:28.475 [lettuce-eventExecutorLoop-3-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:43:28.475 [lettuce-eventExecutorLoop-3-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:43:38.575 [lettuce-eventExecutorLoop-3-17] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:43:38.575 [lettuce-eventExecutorLoop-3-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:43:48.675 [lettuce-eventExecutorLoop-3-20] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:43:48.675 [lettuce-eventExecutorLoop-3-19] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:43:58.777 [lettuce-eventExecutorLoop-3-22] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:43:58.777 [lettuce-eventExecutorLoop-3-21] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:44:08.975 [lettuce-eventExecutorLoop-3-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:44:08.975 [lettuce-eventExecutorLoop-3-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:44:19.275 [lettuce-eventExecutorLoop-3-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:44:19.275 [lettuce-eventExecutorLoop-3-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:44:29.877 [lettuce-eventExecutorLoop-3-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:44:29.877 [lettuce-eventExecutorLoop-3-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:44:40.976 [lettuce-eventExecutorLoop-3-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:44:40.976 [lettuce-eventExecutorLoop-3-17] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:44:53.076 [lettuce-eventExecutorLoop-3-21] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:44:53.076 [lettuce-eventExecutorLoop-3-22] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:45:07.278 [lettuce-eventExecutorLoop-3-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:45:07.278 [lettuce-eventExecutorLoop-3-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:45:25.575 [lettuce-eventExecutorLoop-3-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:45:25.575 [lettuce-eventExecutorLoop-3-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:45:51.975 [lettuce-eventExecutorLoop-3-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:45:51.975 [lettuce-eventExecutorLoop-3-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:46:32.088 [lettuce-eventExecutorLoop-3-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:46:32.088 [lettuce-eventExecutorLoop-3-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:47:12.175 [lettuce-eventExecutorLoop-3-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:47:12.175 [lettuce-eventExecutorLoop-3-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:47:52.288 [lettuce-eventExecutorLoop-3-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:47:52.289 [lettuce-eventExecutorLoop-3-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:48:32.387 [lettuce-eventExecutorLoop-3-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:48:32.387 [lettuce-eventExecutorLoop-3-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:49:12.476 [lettuce-eventExecutorLoop-3-17] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:49:12.476 [lettuce-eventExecutorLoop-3-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:49:52.575 [lettuce-eventExecutorLoop-3-21] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:49:52.575 [lettuce-eventExecutorLoop-3-22] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:50:32.675 [lettuce-eventExecutorLoop-3-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:50:32.675 [lettuce-eventExecutorLoop-3-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:51:12.775 [lettuce-eventExecutorLoop-3-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:51:12.775 [lettuce-eventExecutorLoop-3-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:51:52.880 [lettuce-eventExecutorLoop-3-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:51:52.880 [lettuce-eventExecutorLoop-3-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:52:32.976 [lettuce-eventExecutorLoop-3-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:52:32.976 [lettuce-eventExecutorLoop-3-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:53:13.082 [lettuce-eventExecutorLoop-3-20] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:53:13.082 [lettuce-eventExecutorLoop-3-19] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:53:53.181 [lettuce-eventExecutorLoop-3-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:53:53.181 [lettuce-eventExecutorLoop-3-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:54:33.277 [lettuce-eventExecutorLoop-3-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:54:33.277 [lettuce-eventExecutorLoop-3-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:55:13.385 [lettuce-eventExecutorLoop-3-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:55:13.384 [lettuce-eventExecutorLoop-3-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:55:53.475 [lettuce-eventExecutorLoop-3-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:55:53.475 [lettuce-eventExecutorLoop-3-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:56:33.576 [lettuce-eventExecutorLoop-3-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:56:33.576 [lettuce-eventExecutorLoop-3-17] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:57:13.687 [lettuce-eventExecutorLoop-3-21] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:57:13.687 [lettuce-eventExecutorLoop-3-22] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:57:53.777 [lettuce-eventExecutorLoop-3-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:57:53.777 [lettuce-eventExecutorLoop-3-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:58:33.888 [lettuce-eventExecutorLoop-3-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:58:33.888 [lettuce-eventExecutorLoop-3-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:59:13.976 [lettuce-eventExecutorLoop-3-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:59:13.976 [lettuce-eventExecutorLoop-3-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:59:54.075 [lettuce-eventExecutorLoop-3-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
19:59:54.075 [lettuce-eventExecutorLoop-3-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:00:34.187 [lettuce-eventExecutorLoop-3-20] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:00:34.188 [lettuce-eventExecutorLoop-3-19] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:01:14.288 [lettuce-eventExecutorLoop-3-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:01:14.288 [lettuce-eventExecutorLoop-3-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:01:54.382 [lettuce-eventExecutorLoop-3-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:01:54.382 [lettuce-eventExecutorLoop-3-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:02:34.480 [lettuce-eventExecutorLoop-3-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:02:34.480 [lettuce-eventExecutorLoop-3-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:03:14.575 [lettuce-eventExecutorLoop-3-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:03:14.575 [lettuce-eventExecutorLoop-3-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:03:54.676 [lettuce-eventExecutorLoop-3-17] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:03:54.676 [lettuce-eventExecutorLoop-3-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:04:34.776 [lettuce-eventExecutorLoop-3-21] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:04:34.776 [lettuce-eventExecutorLoop-3-22] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:05:14.875 [lettuce-eventExecutorLoop-3-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:05:14.875 [lettuce-eventExecutorLoop-3-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:05:54.977 [lettuce-eventExecutorLoop-3-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:05:54.977 [lettuce-eventExecutorLoop-3-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:06:35.076 [lettuce-eventExecutorLoop-3-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:06:35.076 [lettuce-eventExecutorLoop-3-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:07:15.187 [lettuce-eventExecutorLoop-3-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:07:15.187 [lettuce-eventExecutorLoop-3-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:07:55.276 [lettuce-eventExecutorLoop-3-19] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:07:55.276 [lettuce-eventExecutorLoop-3-20] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:08:35.388 [lettuce-eventExecutorLoop-3-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:08:35.388 [lettuce-eventExecutorLoop-3-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:09:15.475 [lettuce-eventExecutorLoop-3-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:09:15.475 [lettuce-eventExecutorLoop-3-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:09:55.577 [lettuce-eventExecutorLoop-3-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:09:55.577 [lettuce-eventExecutorLoop-3-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:10:35.675 [lettuce-eventExecutorLoop-3-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:10:35.675 [lettuce-eventExecutorLoop-3-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:11:15.777 [lettuce-eventExecutorLoop-3-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:11:15.777 [lettuce-eventExecutorLoop-3-19] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:11:55.877 [lettuce-eventExecutorLoop-3-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:11:55.877 [lettuce-eventExecutorLoop-3-22] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:12:35.987 [lettuce-eventExecutorLoop-3-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:12:35.987 [lettuce-eventExecutorLoop-3-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:13:16.076 [lettuce-eventExecutorLoop-3-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:13:16.076 [lettuce-eventExecutorLoop-3-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:13:56.180 [lettuce-eventExecutorLoop-3-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:13:56.182 [lettuce-eventExecutorLoop-3-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:14:36.278 [lettuce-eventExecutorLoop-3-17] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:14:36.278 [lettuce-eventExecutorLoop-3-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:15:16.386 [lettuce-eventExecutorLoop-3-20] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:15:16.386 [lettuce-eventExecutorLoop-3-21] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:15:56.481 [lettuce-eventExecutorLoop-3-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:15:56.481 [lettuce-eventExecutorLoop-3-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:16:36.576 [lettuce-eventExecutorLoop-3-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:16:36.576 [lettuce-eventExecutorLoop-3-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:17:16.675 [lettuce-eventExecutorLoop-3-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:17:16.675 [lettuce-eventExecutorLoop-3-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:17:56.777 [lettuce-eventExecutorLoop-3-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:17:56.777 [lettuce-eventExecutorLoop-3-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:18:36.877 [lettuce-eventExecutorLoop-3-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:18:36.877 [lettuce-eventExecutorLoop-3-19] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:19:16.976 [lettuce-eventExecutorLoop-3-22] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:19:16.978 [lettuce-eventExecutorLoop-3-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:19:57.075 [lettuce-eventExecutorLoop-3-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:19:57.075 [lettuce-eventExecutorLoop-3-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:20:37.176 [lettuce-eventExecutorLoop-3-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:20:37.176 [lettuce-eventExecutorLoop-3-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:21:17.287 [lettuce-eventExecutorLoop-3-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:21:17.287 [lettuce-eventExecutorLoop-3-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:21:57.376 [lettuce-eventExecutorLoop-3-17] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:21:57.376 [lettuce-eventExecutorLoop-3-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:22:37.477 [lettuce-eventExecutorLoop-3-20] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:22:37.477 [lettuce-eventExecutorLoop-3-21] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:23:17.575 [lettuce-eventExecutorLoop-3-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:23:17.575 [lettuce-eventExecutorLoop-3-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:23:57.675 [lettuce-eventExecutorLoop-3-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:23:57.675 [lettuce-eventExecutorLoop-3-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:24:37.783 [lettuce-eventExecutorLoop-3-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:24:37.783 [lettuce-eventExecutorLoop-3-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:25:17.875 [lettuce-eventExecutorLoop-3-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:25:17.875 [lettuce-eventExecutorLoop-3-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:25:57.977 [lettuce-eventExecutorLoop-3-19] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:25:57.977 [lettuce-eventExecutorLoop-3-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:26:38.074 [lettuce-eventExecutorLoop-3-22] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:26:38.074 [lettuce-eventExecutorLoop-3-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:27:18.175 [lettuce-eventExecutorLoop-3-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:27:18.175 [lettuce-eventExecutorLoop-3-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:27:58.275 [lettuce-eventExecutorLoop-3-9] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:27:58.275 [lettuce-eventExecutorLoop-3-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:28:38.379 [lettuce-eventExecutorLoop-3-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:28:38.379 [lettuce-eventExecutorLoop-3-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:29:18.475 [lettuce-eventExecutorLoop-3-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:29:18.475 [lettuce-eventExecutorLoop-3-17] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:29:58.575 [lettuce-eventExecutorLoop-3-20] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:29:58.575 [lettuce-eventExecutorLoop-3-21] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:30:38.676 [lettuce-eventExecutorLoop-3-3] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:30:38.676 [lettuce-eventExecutorLoop-3-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:31:18.777 [lettuce-eventExecutorLoop-3-7] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:31:18.777 [lettuce-eventExecutorLoop-3-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:31:58.885 [lettuce-eventExecutorLoop-3-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:31:58.885 [lettuce-eventExecutorLoop-3-11] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:32:38.977 [lettuce-eventExecutorLoop-3-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:32:38.977 [lettuce-eventExecutorLoop-3-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:33:19.077 [lettuce-eventExecutorLoop-3-19] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:33:19.077 [lettuce-eventExecutorLoop-3-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:33:59.182 [lettuce-eventExecutorLoop-3-1] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:33:59.182 [lettuce-eventExecutorLoop-3-22] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:34:39.275 [lettuce-eventExecutorLoop-3-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:34:39.275 [lettuce-eventExecutorLoop-3-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
20:35:11.265 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:35:11.273 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:35:11.597 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:35:11.597 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1f8ed7f4[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:35:11.598 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754981759115_127.0.0.1_6613
20:35:11.600 [nacos-grpc-client-executor-6448] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754981759115_127.0.0.1_6613]Ignore complete event,isRunning:false,isAbandon=false
20:35:11.604 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@114d017d[Running, pool size = 10, active threads = 0, queued tasks = 0, completed tasks = 6449]
