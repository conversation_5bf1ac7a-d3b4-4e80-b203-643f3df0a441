09:11:18.803 [reactor-http-nio-9] ERROR c.h.g.f.<PERSON>th<PERSON><PERSON> - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/user/getInfo
09:11:18.803 [reactor-http-nio-8] ERROR c.h.g.f.AuthFilter - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/enterprise/getInfo
11:35:06.714 [reactor-http-nio-3] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/getInfo,异常信息:Connection prematurely closed BEFORE response
11:35:06.714 [reactor-http-nio-4] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/getInfo,异常信息:Connection prematurely closed BEFORE response
15:18:15.427 [boundedElastic-295] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/kkfileview/onlinePreview,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for kkfileview"
16:00:44.020 [boundedElastic-348] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/kkfileview/onlinePreview,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for kkfileview"
