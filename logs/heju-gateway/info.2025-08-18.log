09:07:41.991 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:07:43.924 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6de9d87e-9388-4ea5-86d9-e898bf2d8d5b_config-0
09:07:44.124 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 86 ms to scan 1 urls, producing 3 keys and 6 values 
09:07:44.206 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 36 ms to scan 1 urls, producing 4 keys and 9 values 
09:07:44.228 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 3 keys and 10 values 
09:07:44.259 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 19 ms to scan 1 urls, producing 1 keys and 5 values 
09:07:44.279 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 18 ms to scan 1 urls, producing 1 keys and 7 values 
09:07:44.296 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
09:07:44.302 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6de9d87e-9388-4ea5-86d9-e898bf2d8d5b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:07:44.304 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6de9d87e-9388-4ea5-86d9-e898bf2d8d5b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000020781398d60
09:07:44.305 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6de9d87e-9388-4ea5-86d9-e898bf2d8d5b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000020781398f80
09:07:44.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6de9d87e-9388-4ea5-86d9-e898bf2d8d5b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:07:44.307 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6de9d87e-9388-4ea5-86d9-e898bf2d8d5b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:07:44.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6de9d87e-9388-4ea5-86d9-e898bf2d8d5b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:07:46.916 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6de9d87e-9388-4ea5-86d9-e898bf2d8d5b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755479266480_127.0.0.1_1669
09:07:46.916 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6de9d87e-9388-4ea5-86d9-e898bf2d8d5b_config-0] Notify connected event to listeners.
09:07:46.919 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6de9d87e-9388-4ea5-86d9-e898bf2d8d5b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:07:46.921 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6de9d87e-9388-4ea5-86d9-e898bf2d8d5b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000207814d0ad8
09:07:47.270 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:08:07.941 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:08:11.557 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:08:13.562 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a4e09555-e0a5-4cff-b30f-bd1838e01a0b_config-0
09:08:13.564 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4e09555-e0a5-4cff-b30f-bd1838e01a0b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:08:13.565 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4e09555-e0a5-4cff-b30f-bd1838e01a0b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000020781398d60
09:08:13.568 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4e09555-e0a5-4cff-b30f-bd1838e01a0b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000020781398f80
09:08:13.568 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4e09555-e0a5-4cff-b30f-bd1838e01a0b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:08:13.568 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4e09555-e0a5-4cff-b30f-bd1838e01a0b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:08:13.571 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4e09555-e0a5-4cff-b30f-bd1838e01a0b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:08:14.178 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4e09555-e0a5-4cff-b30f-bd1838e01a0b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755479293805_127.0.0.1_2079
09:08:14.178 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4e09555-e0a5-4cff-b30f-bd1838e01a0b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:08:14.178 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4e09555-e0a5-4cff-b30f-bd1838e01a0b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000207814d0ad8
09:08:14.178 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4e09555-e0a5-4cff-b30f-bd1838e01a0b_config-0] Notify connected event to listeners.
09:08:15.050 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b8420142-49fa-4d6e-8252-6683befdb27b
09:08:15.050 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] RpcClient init label, labels = {module=naming, source=sdk}
09:08:15.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:08:15.056 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:08:15.058 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:08:15.062 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:08:15.538 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Success to connect to server [localhost:8848] on start up, connectionId = 1755479295287_127.0.0.1_2080
09:08:15.538 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:08:15.538 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Notify connected event to listeners.
09:08:15.538 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000207814d0ad8
09:08:16.431 [nacos-grpc-client-executor-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:08:16.445 [nacos-grpc-client-executor-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:08:17.376 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:08:17.378 [nacos-grpc-client-executor-12] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:08:18.145 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.21:8081 register finished
09:08:18.217 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 37.894 seconds (JVM running for 48.348)
09:08:18.233 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:08:18.236 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:08:18.237 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:08:18.713 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:08:18.723 [nacos-grpc-client-executor-26] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:08:18.738 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:08:18.740 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Ack server push request, request = NotifySubscriberRequest, requestId = 5
09:08:47.517 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:08:47.517 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:09:17.584 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:09:17.584 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Ack server push request, request = NotifySubscriberRequest, requestId = 10
09:27:35.361 [nacos-grpc-client-executor-417] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Receive server push request, request = NotifySubscriberRequest, requestId = 17
09:27:35.375 [nacos-grpc-client-executor-417] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Ack server push request, request = NotifySubscriberRequest, requestId = 17
09:28:38.087 [nacos-grpc-client-executor-435] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Receive server push request, request = NotifySubscriberRequest, requestId = 21
09:28:38.105 [nacos-grpc-client-executor-435] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Ack server push request, request = NotifySubscriberRequest, requestId = 21
10:06:47.255 [nacos-grpc-client-executor-1181] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Receive server push request, request = NotifySubscriberRequest, requestId = 24
10:06:47.271 [nacos-grpc-client-executor-1181] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Ack server push request, request = NotifySubscriberRequest, requestId = 24
10:07:15.254 [nacos-grpc-client-executor-1190] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Receive server push request, request = NotifySubscriberRequest, requestId = 28
10:07:15.275 [nacos-grpc-client-executor-1190] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Ack server push request, request = NotifySubscriberRequest, requestId = 28
10:13:14.675 [nacos-grpc-client-executor-1303] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Receive server push request, request = NotifySubscriberRequest, requestId = 31
10:13:14.700 [nacos-grpc-client-executor-1303] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Ack server push request, request = NotifySubscriberRequest, requestId = 31
10:13:37.502 [nacos-grpc-client-executor-1313] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Receive server push request, request = NotifySubscriberRequest, requestId = 34
10:13:37.514 [nacos-grpc-client-executor-1313] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Ack server push request, request = NotifySubscriberRequest, requestId = 34
10:55:25.424 [nacos-grpc-client-executor-2128] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Receive server push request, request = NotifySubscriberRequest, requestId = 38
10:55:25.434 [nacos-grpc-client-executor-2128] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Ack server push request, request = NotifySubscriberRequest, requestId = 38
10:55:52.629 [nacos-grpc-client-executor-2137] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Receive server push request, request = NotifySubscriberRequest, requestId = 42
10:55:52.653 [nacos-grpc-client-executor-2137] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Ack server push request, request = NotifySubscriberRequest, requestId = 42
15:18:15.895 [nacos-grpc-client-executor-6992] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Receive server push request, request = NotifySubscriberRequest, requestId = 43
15:18:15.895 [nacos-grpc-client-executor-6992] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b8420142-49fa-4d6e-8252-6683befdb27b] Ack server push request, request = NotifySubscriberRequest, requestId = 43
18:00:52.521 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:00:52.528 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:00:52.892 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:00:52.893 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3b3dd2bb[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:00:52.894 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755479295287_127.0.0.1_2080
18:00:52.897 [nacos-grpc-client-executor-9990] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755479295287_127.0.0.1_2080]Ignore complete event,isRunning:false,isAbandon=false
18:00:52.905 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@68a64c3b[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 9991]
