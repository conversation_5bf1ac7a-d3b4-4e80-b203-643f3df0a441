10:42:02.524 [reactor-http-nio-22] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/fieldList,异常信息:Connection prematurely closed BEFORE response
10:42:02.524 [reactor-http-nio-21] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/searchFieldList,异常信息:Connection prematurely closed BEFORE response
10:42:08.604 [reactor-http-nio-8] ERROR c.h.g.f.AuthFilter - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/universal/fieldList
10:45:55.853 [reactor-http-nio-21] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/customerinfo/list,异常信息:Connection prematurely closed BEFORE response
10:45:55.853 [reactor-http-nio-15] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/list,异常信息:Connection prematurely closed BEFORE response
10:45:55.853 [reactor-http-nio-19] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/fieldList,异常信息:Connection prematurely closed BEFORE response
10:45:55.853 [reactor-http-nio-22] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/searchFieldList,异常信息:Connection prematurely closed BEFORE response
15:02:53.643 [reactor-http-nio-6] ERROR c.h.g.f.AuthFilter - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/universal/fieldList
