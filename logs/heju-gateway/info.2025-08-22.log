09:23:43.357 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:23:43.979 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 5c2735bc-9b14-4b0f-ae1a-dbd9a65bf64f_config-0
09:23:44.071 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 49 ms to scan 1 urls, producing 3 keys and 6 values 
09:23:44.090 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 0 ms to scan 1 urls, producing 4 keys and 9 values 
09:23:44.110 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 20 ms to scan 1 urls, producing 3 keys and 10 values 
09:23:44.110 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 0 ms to scan 1 urls, producing 1 keys and 5 values 
09:23:44.110 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 0 ms to scan 1 urls, producing 1 keys and 7 values 
09:23:44.126 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 0 ms to scan 1 urls, producing 2 keys and 8 values 
09:23:44.126 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c2735bc-9b14-4b0f-ae1a-dbd9a65bf64f_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:23:44.126 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c2735bc-9b14-4b0f-ae1a-dbd9a65bf64f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000015e013b9220
09:23:44.126 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c2735bc-9b14-4b0f-ae1a-dbd9a65bf64f_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000015e013b9440
09:23:44.126 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c2735bc-9b14-4b0f-ae1a-dbd9a65bf64f_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:23:44.139 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c2735bc-9b14-4b0f-ae1a-dbd9a65bf64f_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:23:44.145 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c2735bc-9b14-4b0f-ae1a-dbd9a65bf64f_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:23:45.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c2735bc-9b14-4b0f-ae1a-dbd9a65bf64f_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755825824966_127.0.0.1_7932
09:23:45.212 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c2735bc-9b14-4b0f-ae1a-dbd9a65bf64f_config-0] Notify connected event to listeners.
09:23:45.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c2735bc-9b14-4b0f-ae1a-dbd9a65bf64f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:23:45.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [5c2735bc-9b14-4b0f-ae1a-dbd9a65bf64f_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000015e014f0fb0
09:23:45.395 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:23:49.945 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:23:52.052 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:23:52.951 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f3b68f85-1861-4d32-958d-c97ddfb2a442_config-0
09:23:52.951 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3b68f85-1861-4d32-958d-c97ddfb2a442_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:23:52.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3b68f85-1861-4d32-958d-c97ddfb2a442_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000015e013b9220
09:23:52.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3b68f85-1861-4d32-958d-c97ddfb2a442_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000015e013b9440
09:23:52.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3b68f85-1861-4d32-958d-c97ddfb2a442_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:23:52.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3b68f85-1861-4d32-958d-c97ddfb2a442_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:23:52.953 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3b68f85-1861-4d32-958d-c97ddfb2a442_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:23:53.092 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3b68f85-1861-4d32-958d-c97ddfb2a442_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755825832964_127.0.0.1_7943
09:23:53.095 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3b68f85-1861-4d32-958d-c97ddfb2a442_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:23:53.096 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3b68f85-1861-4d32-958d-c97ddfb2a442_config-0] Notify connected event to listeners.
09:23:53.097 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f3b68f85-1861-4d32-958d-c97ddfb2a442_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000015e014f0fb0
09:23:53.274 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ed7e0d06-eced-40f0-aaf7-02ff2f295d96
09:23:53.274 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7e0d06-eced-40f0-aaf7-02ff2f295d96] RpcClient init label, labels = {module=naming, source=sdk}
09:23:53.277 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7e0d06-eced-40f0-aaf7-02ff2f295d96] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:23:53.278 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7e0d06-eced-40f0-aaf7-02ff2f295d96] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:23:53.279 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7e0d06-eced-40f0-aaf7-02ff2f295d96] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:23:53.279 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7e0d06-eced-40f0-aaf7-02ff2f295d96] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:23:53.405 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7e0d06-eced-40f0-aaf7-02ff2f295d96] Success to connect to server [localhost:8848] on start up, connectionId = 1755825833290_127.0.0.1_7944
09:23:53.405 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7e0d06-eced-40f0-aaf7-02ff2f295d96] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:23:53.405 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7e0d06-eced-40f0-aaf7-02ff2f295d96] Notify connected event to listeners.
09:23:53.405 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7e0d06-eced-40f0-aaf7-02ff2f295d96] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000015e014f0fb0
09:23:53.945 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7e0d06-eced-40f0-aaf7-02ff2f295d96] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:23:53.946 [nacos-grpc-client-executor-13] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7e0d06-eced-40f0-aaf7-02ff2f295d96] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:23:53.985 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ************:8081 register finished
09:23:54.035 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 11.297 seconds (JVM running for 18.459)
09:23:54.044 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:23:54.045 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:23:54.046 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:23:54.149 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7e0d06-eced-40f0-aaf7-02ff2f295d96] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:23:54.150 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7e0d06-eced-40f0-aaf7-02ff2f295d96] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:23:54.368 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7e0d06-eced-40f0-aaf7-02ff2f295d96] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:23:54.369 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7e0d06-eced-40f0-aaf7-02ff2f295d96] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:23:54.591 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7e0d06-eced-40f0-aaf7-02ff2f295d96] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:23:54.592 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7e0d06-eced-40f0-aaf7-02ff2f295d96] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:24:24.152 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7e0d06-eced-40f0-aaf7-02ff2f295d96] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:24:24.153 [nacos-grpc-client-executor-55] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7e0d06-eced-40f0-aaf7-02ff2f295d96] Ack server push request, request = NotifySubscriberRequest, requestId = 10
09:24:24.158 [nacos-grpc-client-executor-56] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7e0d06-eced-40f0-aaf7-02ff2f295d96] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:24:24.159 [nacos-grpc-client-executor-56] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ed7e0d06-eced-40f0-aaf7-02ff2f295d96] Ack server push request, request = NotifySubscriberRequest, requestId = 9
13:58:16.683 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
13:58:16.691 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
13:58:17.023 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
13:58:17.023 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@234b9b8a[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
13:58:17.023 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755825833290_127.0.0.1_7944
13:58:17.025 [nacos-grpc-client-executor-5458] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755825833290_127.0.0.1_7944]Ignore complete event,isRunning:false,isAbandon=false
13:58:17.032 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@54d66a59[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 5459]
13:58:45.950 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
13:58:46.626 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 0b873e9f-fa87-4ae2-8d38-69887380d7a5_config-0
13:58:46.739 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 54 ms to scan 1 urls, producing 3 keys and 6 values 
13:58:46.789 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 4 keys and 9 values 
13:58:46.806 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 3 keys and 10 values 
13:58:46.822 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 1 keys and 5 values 
13:58:46.838 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 1 keys and 7 values 
13:58:46.855 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 13 ms to scan 1 urls, producing 2 keys and 8 values 
13:58:46.861 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b873e9f-fa87-4ae2-8d38-69887380d7a5_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:58:46.862 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b873e9f-fa87-4ae2-8d38-69887380d7a5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x00000168ae3b8fc8
13:58:46.862 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b873e9f-fa87-4ae2-8d38-69887380d7a5_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x00000168ae3b91e8
13:58:46.862 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b873e9f-fa87-4ae2-8d38-69887380d7a5_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:58:46.865 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b873e9f-fa87-4ae2-8d38-69887380d7a5_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:58:46.872 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b873e9f-fa87-4ae2-8d38-69887380d7a5_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:58:48.604 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b873e9f-fa87-4ae2-8d38-69887380d7a5_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755842328270_127.0.0.1_1239
13:58:48.605 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b873e9f-fa87-4ae2-8d38-69887380d7a5_config-0] Notify connected event to listeners.
13:58:48.605 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b873e9f-fa87-4ae2-8d38-69887380d7a5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:58:48.605 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [0b873e9f-fa87-4ae2-8d38-69887380d7a5_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000168ae4f0fb0
13:58:48.929 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
13:58:57.706 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
13:59:00.405 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
13:59:01.356 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of ba90fede-a51d-4722-93c1-82de97cbcb64_config-0
13:59:01.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba90fede-a51d-4722-93c1-82de97cbcb64_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
13:59:01.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba90fede-a51d-4722-93c1-82de97cbcb64_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x00000168ae3b8fc8
13:59:01.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba90fede-a51d-4722-93c1-82de97cbcb64_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x00000168ae3b91e8
13:59:01.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba90fede-a51d-4722-93c1-82de97cbcb64_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
13:59:01.356 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba90fede-a51d-4722-93c1-82de97cbcb64_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
13:59:01.361 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba90fede-a51d-4722-93c1-82de97cbcb64_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:59:01.500 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba90fede-a51d-4722-93c1-82de97cbcb64_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755842341377_127.0.0.1_1244
13:59:01.500 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba90fede-a51d-4722-93c1-82de97cbcb64_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:59:01.500 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba90fede-a51d-4722-93c1-82de97cbcb64_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000168ae4f0fb0
13:59:01.500 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [ba90fede-a51d-4722-93c1-82de97cbcb64_config-0] Notify connected event to listeners.
13:59:01.672 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 1b83c638-a7b6-4399-94a5-f2ae6398b727
13:59:01.672 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] RpcClient init label, labels = {module=naming, source=sdk}
13:59:01.672 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
13:59:01.688 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
13:59:01.689 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
13:59:01.689 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
13:59:01.838 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Success to connect to server [localhost:8848] on start up, connectionId = 1755842341706_127.0.0.1_1246
13:59:01.838 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
13:59:01.838 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000168ae4f0fb0
13:59:01.838 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Notify connected event to listeners.
13:59:02.766 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ************:8081 register finished
13:59:02.824 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 17.475 seconds (JVM running for 21.887)
13:59:02.838 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
13:59:02.840 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
13:59:02.842 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
13:59:02.894 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Receive server push request, request = NotifySubscriberRequest, requestId = 2
13:59:02.898 [nacos-grpc-client-executor-27] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Ack server push request, request = NotifySubscriberRequest, requestId = 2
13:59:02.906 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Receive server push request, request = NotifySubscriberRequest, requestId = 3
13:59:02.906 [nacos-grpc-client-executor-28] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Ack server push request, request = NotifySubscriberRequest, requestId = 3
13:59:03.358 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Receive server push request, request = NotifySubscriberRequest, requestId = 4
13:59:03.358 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Ack server push request, request = NotifySubscriberRequest, requestId = 4
13:59:04.956 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Receive server push request, request = NotifySubscriberRequest, requestId = 6
13:59:04.956 [nacos-grpc-client-executor-44] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Ack server push request, request = NotifySubscriberRequest, requestId = 6
13:59:05.056 [nacos-grpc-client-executor-45] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Receive server push request, request = NotifySubscriberRequest, requestId = 7
13:59:05.056 [nacos-grpc-client-executor-45] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Ack server push request, request = NotifySubscriberRequest, requestId = 7
13:59:16.856 [nacos-grpc-client-executor-52] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Receive server push request, request = NotifySubscriberRequest, requestId = 9
13:59:16.872 [nacos-grpc-client-executor-52] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Ack server push request, request = NotifySubscriberRequest, requestId = 9
13:59:32.791 [nacos-grpc-client-executor-59] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Receive server push request, request = NotifySubscriberRequest, requestId = 11
13:59:32.791 [nacos-grpc-client-executor-59] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Ack server push request, request = NotifySubscriberRequest, requestId = 11
17:05:24.424 [nacos-grpc-client-executor-3472] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Receive server push request, request = NotifySubscriberRequest, requestId = 15
17:05:24.441 [nacos-grpc-client-executor-3472] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Ack server push request, request = NotifySubscriberRequest, requestId = 15
17:05:44.201 [nacos-grpc-client-executor-3477] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Receive server push request, request = NotifySubscriberRequest, requestId = 18
17:05:44.218 [nacos-grpc-client-executor-3477] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Ack server push request, request = NotifySubscriberRequest, requestId = 18
17:26:56.243 [nacos-grpc-client-executor-3860] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Receive server push request, request = NotifySubscriberRequest, requestId = 20
17:26:56.266 [nacos-grpc-client-executor-3860] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Ack server push request, request = NotifySubscriberRequest, requestId = 20
17:27:18.918 [nacos-grpc-client-executor-3869] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Receive server push request, request = NotifySubscriberRequest, requestId = 23
17:27:18.941 [nacos-grpc-client-executor-3869] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [1b83c638-a7b6-4399-94a5-f2ae6398b727] Ack server push request, request = NotifySubscriberRequest, requestId = 23
18:03:53.118 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:03:53.118 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:03:53.456 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:03:53.456 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1c804e67[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:03:53.456 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755842341706_127.0.0.1_1246
18:03:53.456 [nacos-grpc-client-executor-4527] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755842341706_127.0.0.1_1246]Ignore complete event,isRunning:false,isAbandon=false
18:03:53.464 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7a476399[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 4528]
