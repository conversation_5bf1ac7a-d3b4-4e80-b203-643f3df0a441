11:00:17.263 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
11:00:19.628 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 71192d01-527b-46b2-85ef-6497be56b47b_config-0
11:00:19.931 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 168 ms to scan 1 urls, producing 3 keys and 6 values 
11:00:20.021 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 46 ms to scan 1 urls, producing 4 keys and 9 values 
11:00:20.066 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 37 ms to scan 1 urls, producing 3 keys and 10 values 
11:00:20.150 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 65 ms to scan 1 urls, producing 1 keys and 5 values 
11:00:20.246 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 1 keys and 7 values 
11:00:20.278 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 2 keys and 8 values 
11:00:20.284 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71192d01-527b-46b2-85ef-6497be56b47b_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:00:20.285 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71192d01-527b-46b2-85ef-6497be56b47b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000018a013b6448
11:00:20.286 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71192d01-527b-46b2-85ef-6497be56b47b_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000018a013b6668
11:00:20.288 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71192d01-527b-46b2-85ef-6497be56b47b_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:00:20.289 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71192d01-527b-46b2-85ef-6497be56b47b_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:00:20.320 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71192d01-527b-46b2-85ef-6497be56b47b_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:00:23.263 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71192d01-527b-46b2-85ef-6497be56b47b_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756868422918_127.0.0.1_3821
11:00:23.264 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71192d01-527b-46b2-85ef-6497be56b47b_config-0] Notify connected event to listeners.
11:00:23.265 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71192d01-527b-46b2-85ef-6497be56b47b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:00:23.266 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [71192d01-527b-46b2-85ef-6497be56b47b_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000018a014ee848
11:00:23.506 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
11:00:29.749 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
11:00:31.099 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
11:00:31.673 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4d23f8e6-bbc2-499d-93a7-c916d0e07990_config-0
11:00:31.673 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d23f8e6-bbc2-499d-93a7-c916d0e07990_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
11:00:31.673 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d23f8e6-bbc2-499d-93a7-c916d0e07990_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000018a013b6448
11:00:31.674 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d23f8e6-bbc2-499d-93a7-c916d0e07990_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000018a013b6668
11:00:31.674 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d23f8e6-bbc2-499d-93a7-c916d0e07990_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
11:00:31.674 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d23f8e6-bbc2-499d-93a7-c916d0e07990_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
11:00:31.674 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d23f8e6-bbc2-499d-93a7-c916d0e07990_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:00:31.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d23f8e6-bbc2-499d-93a7-c916d0e07990_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756868431685_127.0.0.1_3831
11:00:31.801 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d23f8e6-bbc2-499d-93a7-c916d0e07990_config-0] Notify connected event to listeners.
11:00:31.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d23f8e6-bbc2-499d-93a7-c916d0e07990_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:00:31.801 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4d23f8e6-bbc2-499d-93a7-c916d0e07990_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000018a014ee848
11:00:31.901 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3204a9cb-dea3-438a-b574-ca087a515391
11:00:31.902 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3204a9cb-dea3-438a-b574-ca087a515391] RpcClient init label, labels = {module=naming, source=sdk}
11:00:31.904 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3204a9cb-dea3-438a-b574-ca087a515391] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
11:00:31.905 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3204a9cb-dea3-438a-b574-ca087a515391] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
11:00:31.905 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3204a9cb-dea3-438a-b574-ca087a515391] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
11:00:31.906 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3204a9cb-dea3-438a-b574-ca087a515391] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
11:00:32.034 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3204a9cb-dea3-438a-b574-ca087a515391] Success to connect to server [localhost:8848] on start up, connectionId = 1756868431917_127.0.0.1_3832
11:00:32.035 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3204a9cb-dea3-438a-b574-ca087a515391] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
11:00:32.035 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3204a9cb-dea3-438a-b574-ca087a515391] Notify connected event to listeners.
11:00:32.035 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3204a9cb-dea3-438a-b574-ca087a515391] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000018a014ee848
11:00:32.613 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3204a9cb-dea3-438a-b574-ca087a515391] Receive server push request, request = NotifySubscriberRequest, requestId = 3
11:00:32.613 [nacos-grpc-client-executor-17] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3204a9cb-dea3-438a-b574-ca087a515391] Ack server push request, request = NotifySubscriberRequest, requestId = 3
11:00:32.623 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.52:8081 register finished
11:00:32.663 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 17.135 seconds (JVM running for 24.266)
11:00:32.672 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
11:00:32.673 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
11:00:32.673 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
11:00:32.709 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3204a9cb-dea3-438a-b574-ca087a515391] Receive server push request, request = NotifySubscriberRequest, requestId = 4
11:00:32.709 [nacos-grpc-client-executor-29] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3204a9cb-dea3-438a-b574-ca087a515391] Ack server push request, request = NotifySubscriberRequest, requestId = 4
11:00:32.717 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3204a9cb-dea3-438a-b574-ca087a515391] Receive server push request, request = NotifySubscriberRequest, requestId = 5
11:00:32.717 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3204a9cb-dea3-438a-b574-ca087a515391] Ack server push request, request = NotifySubscriberRequest, requestId = 5
11:00:33.162 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3204a9cb-dea3-438a-b574-ca087a515391] Receive server push request, request = NotifySubscriberRequest, requestId = 6
11:00:33.173 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3204a9cb-dea3-438a-b574-ca087a515391] Ack server push request, request = NotifySubscriberRequest, requestId = 6
11:04:32.868 [nacos-grpc-client-executor-109] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3204a9cb-dea3-438a-b574-ca087a515391] Receive server push request, request = NotifySubscriberRequest, requestId = 8
11:04:32.869 [nacos-grpc-client-executor-109] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3204a9cb-dea3-438a-b574-ca087a515391] Ack server push request, request = NotifySubscriberRequest, requestId = 8
20:29:24.709 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:29:24.734 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:29:25.055 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:29:25.055 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3b315bb7[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:29:25.055 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756868431917_127.0.0.1_3832
20:29:25.060 [nacos-grpc-client-executor-10611] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756868431917_127.0.0.1_3832]Ignore complete event,isRunning:false,isAbandon=false
20:29:25.060 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@5d59fc0e[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 10612]
