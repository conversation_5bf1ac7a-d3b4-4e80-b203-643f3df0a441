09:14:16.470 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:14:18.069 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 3c76a15c-732d-41b0-89cd-5a7d13ff7cd7_config-0
09:14:18.328 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 145 ms to scan 1 urls, producing 3 keys and 6 values 
09:14:18.422 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 37 ms to scan 1 urls, producing 4 keys and 9 values 
09:14:18.458 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 27 ms to scan 1 urls, producing 3 keys and 10 values 
09:14:18.491 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 24 ms to scan 1 urls, producing 1 keys and 5 values 
09:14:18.524 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 26 ms to scan 1 urls, producing 1 keys and 7 values 
09:14:18.557 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 26 ms to scan 1 urls, producing 2 keys and 8 values 
09:14:18.565 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c76a15c-732d-41b0-89cd-5a7d13ff7cd7_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:14:18.567 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c76a15c-732d-41b0-89cd-5a7d13ff7cd7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001f4813b7d80
09:14:18.568 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c76a15c-732d-41b0-89cd-5a7d13ff7cd7_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001f4813b8000
09:14:18.569 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c76a15c-732d-41b0-89cd-5a7d13ff7cd7_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:14:18.571 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c76a15c-732d-41b0-89cd-5a7d13ff7cd7_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:14:18.605 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c76a15c-732d-41b0-89cd-5a7d13ff7cd7_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:14:21.844 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c76a15c-732d-41b0-89cd-5a7d13ff7cd7_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755047661368_127.0.0.1_4102
09:14:21.845 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c76a15c-732d-41b0-89cd-5a7d13ff7cd7_config-0] Notify connected event to listeners.
09:14:21.846 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c76a15c-732d-41b0-89cd-5a7d13ff7cd7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:14:21.847 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [3c76a15c-732d-41b0-89cd-5a7d13ff7cd7_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001f4814f0228
09:14:22.187 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:14:37.856 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:14:44.105 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:14:45.627 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of b100573d-d08a-4d0a-a817-916b8c2bb588_config-0
09:14:45.628 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b100573d-d08a-4d0a-a817-916b8c2bb588_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:14:45.628 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b100573d-d08a-4d0a-a817-916b8c2bb588_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001f4813b7d80
09:14:45.629 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b100573d-d08a-4d0a-a817-916b8c2bb588_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001f4813b8000
09:14:45.629 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b100573d-d08a-4d0a-a817-916b8c2bb588_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:14:45.630 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b100573d-d08a-4d0a-a817-916b8c2bb588_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:14:45.631 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b100573d-d08a-4d0a-a817-916b8c2bb588_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:14:45.776 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b100573d-d08a-4d0a-a817-916b8c2bb588_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1755047685652_127.0.0.1_4245
09:14:45.777 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b100573d-d08a-4d0a-a817-916b8c2bb588_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:14:45.777 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b100573d-d08a-4d0a-a817-916b8c2bb588_config-0] Notify connected event to listeners.
09:14:45.777 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [b100573d-d08a-4d0a-a817-916b8c2bb588_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001f4814f0228
09:14:46.056 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9a7dda09-39a5-4d72-b2e4-8e502998a74d
09:14:46.057 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] RpcClient init label, labels = {module=naming, source=sdk}
09:14:46.062 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:14:46.064 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:14:46.065 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:14:46.067 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:14:46.211 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Success to connect to server [localhost:8848] on start up, connectionId = 1755047686088_127.0.0.1_4251
09:14:46.211 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:14:46.211 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Notify connected event to listeners.
09:14:46.212 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001f4814f0228
09:14:46.869 [nacos-grpc-client-executor-14] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:14:46.871 [nacos-grpc-client-executor-14] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:14:46.956 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:14:46.958 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:14:47.498 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.2:8081 register finished
09:14:47.591 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 32.45 seconds (JVM running for 45.977)
09:14:47.609 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:14:47.611 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:14:47.614 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:14:47.932 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 5
09:14:47.944 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 5
09:14:48.036 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:14:48.038 [nacos-grpc-client-executor-33] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:15:17.089 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:15:17.090 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:15:47.099 [nacos-grpc-client-executor-62] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:15:47.100 [nacos-grpc-client-executor-62] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 10
10:11:18.175 [nacos-grpc-client-executor-1137] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 17
10:11:18.212 [nacos-grpc-client-executor-1137] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 17
10:12:00.494 [nacos-grpc-client-executor-1152] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 21
10:12:00.505 [nacos-grpc-client-executor-1152] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 21
10:17:48.344 [nacos-grpc-client-executor-1257] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 24
10:17:48.375 [nacos-grpc-client-executor-1257] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 24
10:18:15.844 [nacos-grpc-client-executor-1266] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 28
10:18:15.861 [nacos-grpc-client-executor-1266] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 28
10:31:06.704 [nacos-grpc-client-executor-1517] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 31
10:31:06.728 [nacos-grpc-client-executor-1517] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 31
10:31:39.304 [nacos-grpc-client-executor-1528] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 35
10:31:39.320 [nacos-grpc-client-executor-1528] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 35
10:33:24.529 [nacos-grpc-client-executor-1564] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 38
10:33:24.557 [nacos-grpc-client-executor-1564] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 38
10:33:52.096 [nacos-grpc-client-executor-1576] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 42
10:33:52.112 [nacos-grpc-client-executor-1576] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 42
11:49:35.576 [nacos-grpc-client-executor-3018] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 45
11:49:35.617 [nacos-grpc-client-executor-3018] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 45
11:50:34.781 [nacos-grpc-client-executor-3037] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 49
11:50:34.795 [nacos-grpc-client-executor-3037] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 49
12:01:58.913 [nacos-grpc-client-executor-3257] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 52
12:01:58.932 [nacos-grpc-client-executor-3257] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 52
12:02:46.211 [nacos-grpc-client-executor-3268] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 56
12:02:46.223 [nacos-grpc-client-executor-3268] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 56
12:14:33.125 [nacos-grpc-client-executor-3491] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 59
12:14:33.145 [nacos-grpc-client-executor-3491] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 59
12:15:17.346 [nacos-grpc-client-executor-3504] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 63
12:15:17.361 [nacos-grpc-client-executor-3504] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 63
12:31:25.246 [nacos-grpc-client-executor-3803] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 66
12:31:25.315 [nacos-grpc-client-executor-3803] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 66
12:31:59.313 [nacos-grpc-client-executor-3816] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 70
12:31:59.329 [nacos-grpc-client-executor-3816] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 70
13:51:27.237 [lettuce-nioEventLoop-5-1] INFO  i.l.c.p.CommandHandler - [log,217] - null Unexpected exception during request: java.net.SocketException: Connection reset
java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)
	at java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)
	at io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:259)
	at io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)
	at io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)
	at io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)
	at io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)
	at io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)
	at io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)
	at io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)
	at io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)
	at io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)
	at java.base/java.lang.Thread.run(Thread.java:840)
13:51:27.277 [lettuce-eventExecutorLoop-3-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was /192.168.1.200:6379
13:51:38.360 [lettuce-eventExecutorLoop-3-13] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
13:51:48.965 [lettuce-eventExecutorLoop-3-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
13:52:00.059 [lettuce-eventExecutorLoop-3-15] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
13:52:12.159 [lettuce-eventExecutorLoop-3-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
13:52:26.371 [lettuce-eventExecutorLoop-3-17] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
13:52:44.662 [lettuce-eventExecutorLoop-3-5] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
13:53:11.060 [lettuce-eventExecutorLoop-3-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
13:53:51.158 [lettuce-eventExecutorLoop-3-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
13:54:31.279 [lettuce-eventExecutorLoop-3-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
13:55:11.358 [lettuce-eventExecutorLoop-3-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
13:55:51.470 [lettuce-eventExecutorLoop-3-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
13:56:31.569 [lettuce-eventExecutorLoop-3-20] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
13:57:11.658 [lettuce-eventExecutorLoop-3-22] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
13:57:51.759 [lettuce-eventExecutorLoop-3-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
13:58:31.858 [lettuce-eventExecutorLoop-3-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
13:59:11.962 [lettuce-eventExecutorLoop-3-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
13:59:52.070 [lettuce-eventExecutorLoop-3-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
14:00:32.168 [lettuce-eventExecutorLoop-3-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
14:01:12.268 [lettuce-eventExecutorLoop-3-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
14:01:52.368 [lettuce-eventExecutorLoop-3-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
14:02:32.469 [lettuce-eventExecutorLoop-3-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
14:03:12.568 [lettuce-eventExecutorLoop-3-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
14:03:52.668 [lettuce-eventExecutorLoop-3-20] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
14:04:32.759 [lettuce-eventExecutorLoop-3-22] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
14:05:12.868 [lettuce-eventExecutorLoop-3-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
14:05:52.968 [lettuce-eventExecutorLoop-3-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
14:06:33.072 [lettuce-eventExecutorLoop-3-6] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
14:07:13.164 [lettuce-eventExecutorLoop-3-8] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
14:07:53.259 [lettuce-eventExecutorLoop-3-10] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
14:08:33.369 [lettuce-eventExecutorLoop-3-12] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
14:09:13.468 [lettuce-eventExecutorLoop-3-14] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
14:09:53.568 [lettuce-eventExecutorLoop-3-16] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
14:10:33.666 [lettuce-eventExecutorLoop-3-18] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
14:11:13.761 [lettuce-eventExecutorLoop-3-20] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
14:11:53.862 [lettuce-eventExecutorLoop-3-22] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
14:12:33.967 [lettuce-eventExecutorLoop-3-2] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
14:13:09.458 [lettuce-eventExecutorLoop-3-4] INFO  i.l.c.p.ConnectionWatchdog - [log,171] - Reconnecting, last destination was 192.168.1.200/<unresolved>:6379
14:13:09.476 [lettuce-nioEventLoop-5-2] INFO  i.l.c.p.ReconnectionHandler - [lambda$null$3,174] - Reconnected to 192.168.1.200/<unresolved>:6379
14:17:57.687 [nacos-grpc-client-executor-5780] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 73
14:17:57.708 [nacos-grpc-client-executor-5780] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 73
14:18:44.554 [nacos-grpc-client-executor-5794] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 77
14:18:44.569 [nacos-grpc-client-executor-5794] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 77
14:51:26.600 [nacos-grpc-client-executor-6402] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 80
14:51:26.616 [nacos-grpc-client-executor-6402] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 80
14:52:02.926 [nacos-grpc-client-executor-6414] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 84
14:52:02.951 [nacos-grpc-client-executor-6414] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 84
15:59:40.289 [nacos-grpc-client-executor-7655] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 87
15:59:40.432 [nacos-grpc-client-executor-7655] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 87
16:00:22.867 [nacos-grpc-client-executor-7669] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 91
16:00:22.887 [nacos-grpc-client-executor-7669] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 91
16:21:43.568 [nacos-grpc-client-executor-8042] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 94
16:21:43.583 [nacos-grpc-client-executor-8042] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 94
16:22:17.433 [nacos-grpc-client-executor-8053] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 98
16:22:17.450 [nacos-grpc-client-executor-8053] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 98
17:18:48.952 [nacos-grpc-client-executor-9080] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 101
17:18:48.970 [nacos-grpc-client-executor-9080] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 101
17:19:18.037 [nacos-grpc-client-executor-9091] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 105
17:19:18.053 [nacos-grpc-client-executor-9091] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 105
18:58:49.315 [nacos-grpc-client-executor-10864] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 110
18:58:49.346 [nacos-grpc-client-executor-10864] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 110
18:59:33.422 [nacos-grpc-client-executor-10878] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Receive server push request, request = NotifySubscriberRequest, requestId = 115
18:59:33.440 [nacos-grpc-client-executor-10878] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9a7dda09-39a5-4d72-b2e4-8e502998a74d] Ack server push request, request = NotifySubscriberRequest, requestId = 115
22:20:00.803 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
22:20:00.807 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
22:20:01.135 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
22:20:01.135 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@2d65964[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
22:20:01.135 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1755047686088_127.0.0.1_4251
22:20:01.135 [nacos-grpc-client-executor-14406] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1755047686088_127.0.0.1_4251]Ignore complete event,isRunning:false,isAbandon=false
22:20:01.145 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@6f494e32[Running, pool size = 9, active threads = 0, queued tasks = 0, completed tasks = 14407]
