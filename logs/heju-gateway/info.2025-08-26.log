09:44:42.183 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:44:43.595 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of e6ff0c84-59df-46d7-adef-ccaf68d4d9b9_config-0
09:44:43.762 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 91 ms to scan 1 urls, producing 3 keys and 6 values 
09:44:43.807 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 4 keys and 9 values 
09:44:43.823 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 0 ms to scan 1 urls, producing 3 keys and 10 values 
09:44:43.855 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 17 ms to scan 1 urls, producing 1 keys and 5 values 
09:44:43.871 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 16 ms to scan 1 urls, producing 1 keys and 7 values 
09:44:43.907 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 28 ms to scan 1 urls, producing 2 keys and 8 values 
09:44:43.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6ff0c84-59df-46d7-adef-ccaf68d4d9b9_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:44:43.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6ff0c84-59df-46d7-adef-ccaf68d4d9b9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000021a923b94f0
09:44:43.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6ff0c84-59df-46d7-adef-ccaf68d4d9b9_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000021a923b9710
09:44:43.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6ff0c84-59df-46d7-adef-ccaf68d4d9b9_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:44:43.914 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6ff0c84-59df-46d7-adef-ccaf68d4d9b9_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:44:43.925 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6ff0c84-59df-46d7-adef-ccaf68d4d9b9_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:44:46.051 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6ff0c84-59df-46d7-adef-ccaf68d4d9b9_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756172685666_127.0.0.1_2321
09:44:46.051 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6ff0c84-59df-46d7-adef-ccaf68d4d9b9_config-0] Notify connected event to listeners.
09:44:46.051 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6ff0c84-59df-46d7-adef-ccaf68d4d9b9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:44:46.055 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [e6ff0c84-59df-46d7-adef-ccaf68d4d9b9_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000021a924f0fb0
09:44:46.406 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:44:53.463 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:44:55.870 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:44:56.887 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 9cf2acf3-9595-46f7-8afa-bab7da8ba161_config-0
09:44:56.887 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cf2acf3-9595-46f7-8afa-bab7da8ba161_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:44:56.894 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cf2acf3-9595-46f7-8afa-bab7da8ba161_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000021a923b94f0
09:44:56.894 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cf2acf3-9595-46f7-8afa-bab7da8ba161_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000021a923b9710
09:44:56.894 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cf2acf3-9595-46f7-8afa-bab7da8ba161_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:44:56.895 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cf2acf3-9595-46f7-8afa-bab7da8ba161_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:44:56.895 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cf2acf3-9595-46f7-8afa-bab7da8ba161_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:44:57.031 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cf2acf3-9595-46f7-8afa-bab7da8ba161_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756172696910_127.0.0.1_2335
09:44:57.031 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cf2acf3-9595-46f7-8afa-bab7da8ba161_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:44:57.031 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cf2acf3-9595-46f7-8afa-bab7da8ba161_config-0] Notify connected event to listeners.
09:44:57.031 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [9cf2acf3-9595-46f7-8afa-bab7da8ba161_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000021a924f0fb0
09:44:57.215 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4acd8d2d-d264-4ff4-b307-332f24aa9060
09:44:57.223 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4acd8d2d-d264-4ff4-b307-332f24aa9060] RpcClient init label, labels = {module=naming, source=sdk}
09:44:57.225 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4acd8d2d-d264-4ff4-b307-332f24aa9060] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:44:57.225 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4acd8d2d-d264-4ff4-b307-332f24aa9060] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:44:57.225 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4acd8d2d-d264-4ff4-b307-332f24aa9060] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:44:57.225 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4acd8d2d-d264-4ff4-b307-332f24aa9060] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:44:57.375 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4acd8d2d-d264-4ff4-b307-332f24aa9060] Success to connect to server [localhost:8848] on start up, connectionId = 1756172697242_127.0.0.1_2336
09:44:57.376 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4acd8d2d-d264-4ff4-b307-332f24aa9060] Notify connected event to listeners.
09:44:57.376 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4acd8d2d-d264-4ff4-b307-332f24aa9060] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:44:57.376 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4acd8d2d-d264-4ff4-b307-332f24aa9060] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000021a924f0fb0
09:44:57.939 [nacos-grpc-client-executor-14] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4acd8d2d-d264-4ff4-b307-332f24aa9060] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:44:57.939 [nacos-grpc-client-executor-14] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4acd8d2d-d264-4ff4-b307-332f24aa9060] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:44:58.122 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4acd8d2d-d264-4ff4-b307-332f24aa9060] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:44:58.122 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4acd8d2d-d264-4ff4-b307-332f24aa9060] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:44:58.222 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.45:8081 register finished
09:44:58.287 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 17.361 seconds (JVM running for 31.764)
09:44:58.303 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:44:58.303 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:44:58.303 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:44:58.661 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4acd8d2d-d264-4ff4-b307-332f24aa9060] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:44:58.661 [nacos-grpc-client-executor-31] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4acd8d2d-d264-4ff4-b307-332f24aa9060] Ack server push request, request = NotifySubscriberRequest, requestId = 4
09:44:58.773 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4acd8d2d-d264-4ff4-b307-332f24aa9060] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:44:58.773 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4acd8d2d-d264-4ff4-b307-332f24aa9060] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:45:28.257 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4acd8d2d-d264-4ff4-b307-332f24aa9060] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:45:28.258 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4acd8d2d-d264-4ff4-b307-332f24aa9060] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:45:28.264 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4acd8d2d-d264-4ff4-b307-332f24aa9060] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:45:28.264 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4acd8d2d-d264-4ff4-b307-332f24aa9060] Ack server push request, request = NotifySubscriberRequest, requestId = 10
16:34:09.980 [nacos-grpc-client-executor-8021] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4acd8d2d-d264-4ff4-b307-332f24aa9060] Receive server push request, request = NotifySubscriberRequest, requestId = 15
16:34:10.000 [nacos-grpc-client-executor-8021] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4acd8d2d-d264-4ff4-b307-332f24aa9060] Ack server push request, request = NotifySubscriberRequest, requestId = 15
16:34:49.005 [nacos-grpc-client-executor-8033] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4acd8d2d-d264-4ff4-b307-332f24aa9060] Receive server push request, request = NotifySubscriberRequest, requestId = 18
16:34:49.005 [nacos-grpc-client-executor-8033] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4acd8d2d-d264-4ff4-b307-332f24aa9060] Ack server push request, request = NotifySubscriberRequest, requestId = 18
20:31:06.294 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
20:31:06.294 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
20:31:06.642 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
20:31:06.642 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@36ad2cb8[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
20:31:06.642 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756172697242_127.0.0.1_2336
20:31:06.644 [nacos-grpc-client-executor-12463] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756172697242_127.0.0.1_2336]Ignore complete event,isRunning:false,isAbandon=false
20:31:06.652 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@4287b026[Running, pool size = 8, active threads = 0, queued tasks = 0, completed tasks = 12464]
