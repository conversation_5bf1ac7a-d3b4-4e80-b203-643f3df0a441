09:18:59.307 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:19:00.049 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4ddeaf34-e032-4b82-8082-1aed8ec36bbf_config-0
09:19:00.135 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 38 ms to scan 1 urls, producing 3 keys and 6 values 
09:19:00.176 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 4 keys and 9 values 
09:19:00.186 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 3 keys and 10 values 
09:19:00.196 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 5 values 
09:19:00.202 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 1 keys and 7 values 
09:19:00.217 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
09:19:00.217 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ddeaf34-e032-4b82-8082-1aed8ec36bbf_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:19:00.222 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ddeaf34-e032-4b82-8082-1aed8ec36bbf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001d59c3b5248
09:19:00.222 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ddeaf34-e032-4b82-8082-1aed8ec36bbf_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001d59c3b5468
09:19:00.222 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ddeaf34-e032-4b82-8082-1aed8ec36bbf_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:19:00.222 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ddeaf34-e032-4b82-8082-1aed8ec36bbf_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:19:00.240 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ddeaf34-e032-4b82-8082-1aed8ec36bbf_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:01.517 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ddeaf34-e032-4b82-8082-1aed8ec36bbf_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754615941269_127.0.0.1_12131
09:19:01.517 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ddeaf34-e032-4b82-8082-1aed8ec36bbf_config-0] Notify connected event to listeners.
09:19:01.517 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ddeaf34-e032-4b82-8082-1aed8ec36bbf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:19:01.517 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4ddeaf34-e032-4b82-8082-1aed8ec36bbf_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001d59c4ecf98
09:19:01.671 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:19:06.662 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:19:07.808 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:19:08.372 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of f656dedf-0b9a-4566-9508-d3198143a98e_config-0
09:19:08.373 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f656dedf-0b9a-4566-9508-d3198143a98e_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:19:08.373 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f656dedf-0b9a-4566-9508-d3198143a98e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001d59c3b5248
09:19:08.374 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f656dedf-0b9a-4566-9508-d3198143a98e_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001d59c3b5468
09:19:08.374 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f656dedf-0b9a-4566-9508-d3198143a98e_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:19:08.374 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f656dedf-0b9a-4566-9508-d3198143a98e_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:19:08.375 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f656dedf-0b9a-4566-9508-d3198143a98e_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:08.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f656dedf-0b9a-4566-9508-d3198143a98e_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754615948386_127.0.0.1_12153
09:19:08.502 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f656dedf-0b9a-4566-9508-d3198143a98e_config-0] Notify connected event to listeners.
09:19:08.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f656dedf-0b9a-4566-9508-d3198143a98e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:19:08.502 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [f656dedf-0b9a-4566-9508-d3198143a98e_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001d59c4ecf98
09:19:08.628 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 2889ae59-a4fa-4efa-83ff-2bf0a3cbc915
09:19:08.628 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] RpcClient init label, labels = {module=naming, source=sdk}
09:19:08.631 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:19:08.631 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:19:08.631 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:19:08.632 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:19:08.763 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Success to connect to server [localhost:8848] on start up, connectionId = 1754615948642_127.0.0.1_12154
09:19:08.763 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Notify connected event to listeners.
09:19:08.764 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:19:08.764 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001d59c4ecf98
09:19:09.310 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ************:8081 register finished
09:19:09.356 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 10.706 seconds (JVM running for 13.632)
09:19:09.364 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:19:09.365 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:19:09.366 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:19:09.536 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:19:09.537 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:19:09.844 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:19:09.845 [nacos-grpc-client-executor-22] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:19:39.450 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:19:39.454 [nacos-grpc-client-executor-40] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:19:39.463 [nacos-grpc-client-executor-41] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:19:39.463 [nacos-grpc-client-executor-41] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:19:39.569 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:19:39.573 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Ack server push request, request = NotifySubscriberRequest, requestId = 9
09:19:39.580 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Receive server push request, request = NotifySubscriberRequest, requestId = 10
09:19:39.580 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Ack server push request, request = NotifySubscriberRequest, requestId = 10
15:00:18.204 [nacos-grpc-client-executor-6814] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Receive server push request, request = NotifySubscriberRequest, requestId = 16
15:00:18.225 [nacos-grpc-client-executor-6814] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Ack server push request, request = NotifySubscriberRequest, requestId = 16
15:01:01.633 [nacos-grpc-client-executor-6829] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Receive server push request, request = NotifySubscriberRequest, requestId = 20
15:01:01.642 [nacos-grpc-client-executor-6829] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Ack server push request, request = NotifySubscriberRequest, requestId = 20
15:18:37.276 [nacos-grpc-client-executor-7163] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Receive server push request, request = NotifySubscriberRequest, requestId = 25
15:18:37.287 [nacos-grpc-client-executor-7163] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Ack server push request, request = NotifySubscriberRequest, requestId = 25
15:19:14.545 [nacos-grpc-client-executor-7175] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Receive server push request, request = NotifySubscriberRequest, requestId = 29
15:19:14.561 [nacos-grpc-client-executor-7175] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Ack server push request, request = NotifySubscriberRequest, requestId = 29
17:09:24.022 [nacos-grpc-client-executor-9304] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Receive server push request, request = NotifySubscriberRequest, requestId = 34
17:09:24.044 [nacos-grpc-client-executor-9304] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Ack server push request, request = NotifySubscriberRequest, requestId = 34
17:09:52.140 [nacos-grpc-client-executor-9314] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Receive server push request, request = NotifySubscriberRequest, requestId = 38
17:09:52.156 [nacos-grpc-client-executor-9314] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [2889ae59-a4fa-4efa-83ff-2bf0a3cbc915] Ack server push request, request = NotifySubscriberRequest, requestId = 38
18:08:24.137 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:08:24.147 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:08:24.469 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:08:24.470 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@3e2d8bb6[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:08:24.470 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754615948642_127.0.0.1_12154
18:08:24.473 [nacos-grpc-client-executor-10426] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754615948642_127.0.0.1_12154]Ignore complete event,isRunning:false,isAbandon=false
18:08:24.478 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@d11aa87[Running, pool size = 8, active threads = 0, queued tasks = 0, completed tasks = 10427]
18:09:05.037 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
18:09:05.757 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 81b628d6-c89a-46c6-9a09-31590d28f4f0_config-0
18:09:05.877 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 65 ms to scan 1 urls, producing 3 keys and 6 values 
18:09:05.920 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 16 ms to scan 1 urls, producing 4 keys and 9 values 
18:09:05.939 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 19 ms to scan 1 urls, producing 3 keys and 10 values 
18:09:05.954 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 1 keys and 5 values 
18:09:05.962 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 6 ms to scan 1 urls, producing 1 keys and 7 values 
18:09:05.988 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 15 ms to scan 1 urls, producing 2 keys and 8 values 
18:09:05.994 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81b628d6-c89a-46c6-9a09-31590d28f4f0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:09:05.994 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81b628d6-c89a-46c6-9a09-31590d28f4f0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x00000240813b9ed0
18:09:05.997 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81b628d6-c89a-46c6-9a09-31590d28f4f0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x00000240813ba0f0
18:09:05.997 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81b628d6-c89a-46c6-9a09-31590d28f4f0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:09:05.997 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81b628d6-c89a-46c6-9a09-31590d28f4f0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:09:06.015 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81b628d6-c89a-46c6-9a09-31590d28f4f0_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:09:08.516 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81b628d6-c89a-46c6-9a09-31590d28f4f0_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754647748068_127.0.0.1_6724
18:09:08.519 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81b628d6-c89a-46c6-9a09-31590d28f4f0_config-0] Notify connected event to listeners.
18:09:08.520 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81b628d6-c89a-46c6-9a09-31590d28f4f0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:09:08.521 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [81b628d6-c89a-46c6-9a09-31590d28f4f0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000240814f20a0
18:09:08.899 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
18:09:20.564 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
18:09:24.091 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
18:09:26.097 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of feff7677-8b9c-471c-a04e-2f8b0609e39d_config-0
18:09:26.097 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [feff7677-8b9c-471c-a04e-2f8b0609e39d_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
18:09:26.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [feff7677-8b9c-471c-a04e-2f8b0609e39d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x00000240813b9ed0
18:09:26.099 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [feff7677-8b9c-471c-a04e-2f8b0609e39d_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x00000240813ba0f0
18:09:26.102 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [feff7677-8b9c-471c-a04e-2f8b0609e39d_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
18:09:26.104 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [feff7677-8b9c-471c-a04e-2f8b0609e39d_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
18:09:26.105 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [feff7677-8b9c-471c-a04e-2f8b0609e39d_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:09:26.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [feff7677-8b9c-471c-a04e-2f8b0609e39d_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754647766151_127.0.0.1_6846
18:09:26.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [feff7677-8b9c-471c-a04e-2f8b0609e39d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:09:26.329 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [feff7677-8b9c-471c-a04e-2f8b0609e39d_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000240814f20a0
18:09:26.329 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [feff7677-8b9c-471c-a04e-2f8b0609e39d_config-0] Notify connected event to listeners.
18:09:26.967 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of a4eb936f-a98e-4784-896b-518f022fdd15
18:09:26.970 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] RpcClient init label, labels = {module=naming, source=sdk}
18:09:26.976 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
18:09:26.978 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
18:09:26.980 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
18:09:26.982 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
18:09:27.140 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Success to connect to server [localhost:8848] on start up, connectionId = 1754647767003_127.0.0.1_6850
18:09:27.142 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Notify connected event to listeners.
18:09:27.142 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
18:09:27.142 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x00000240814f20a0
18:09:28.176 [nacos-grpc-client-executor-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Receive server push request, request = NotifySubscriberRequest, requestId = 43
18:09:28.177 [nacos-grpc-client-executor-4] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Ack server push request, request = NotifySubscriberRequest, requestId = 43
18:09:28.922 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Receive server push request, request = NotifySubscriberRequest, requestId = 44
18:09:28.922 [nacos-grpc-client-executor-18] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Ack server push request, request = NotifySubscriberRequest, requestId = 44
18:09:28.947 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Receive server push request, request = NotifySubscriberRequest, requestId = 46
18:09:28.948 [nacos-grpc-client-executor-19] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Ack server push request, request = NotifySubscriberRequest, requestId = 46
18:09:28.964 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Receive server push request, request = NotifySubscriberRequest, requestId = 47
18:09:28.964 [nacos-grpc-client-executor-20] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Ack server push request, request = NotifySubscriberRequest, requestId = 47
18:09:28.988 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Receive server push request, request = NotifySubscriberRequest, requestId = 45
18:09:28.992 [nacos-grpc-client-executor-21] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Ack server push request, request = NotifySubscriberRequest, requestId = 45
18:09:29.424 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ************:8081 register finished
18:09:29.570 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 25.165 seconds (JVM running for 27.631)
18:09:29.594 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
18:09:29.598 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
18:09:29.603 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
18:09:29.999 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Receive server push request, request = NotifySubscriberRequest, requestId = 48
18:09:30.082 [nacos-grpc-client-executor-32] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Ack server push request, request = NotifySubscriberRequest, requestId = 48
18:09:33.003 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Receive server push request, request = NotifySubscriberRequest, requestId = 49
18:09:33.028 [nacos-grpc-client-executor-38] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Ack server push request, request = NotifySubscriberRequest, requestId = 49
18:09:34.625 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Receive server push request, request = NotifySubscriberRequest, requestId = 51
18:09:34.641 [nacos-grpc-client-executor-39] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Ack server push request, request = NotifySubscriberRequest, requestId = 51
18:09:40.335 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Receive server push request, request = NotifySubscriberRequest, requestId = 53
18:09:40.356 [nacos-grpc-client-executor-42] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Ack server push request, request = NotifySubscriberRequest, requestId = 53
18:09:42.079 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Receive server push request, request = NotifySubscriberRequest, requestId = 55
18:09:42.098 [nacos-grpc-client-executor-43] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [a4eb936f-a98e-4784-896b-518f022fdd15] Ack server push request, request = NotifySubscriberRequest, requestId = 55
18:15:36.730 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
18:15:36.732 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
18:15:37.054 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
18:15:37.054 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@797008c6[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
18:15:37.054 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754647767003_127.0.0.1_6850
18:15:37.055 [nacos-grpc-client-executor-154] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1754647767003_127.0.0.1_6850]Ignore complete event,isRunning:false,isAbandon=false
18:15:37.057 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@3751e57[Running, pool size = 10, active threads = 0, queued tasks = 0, completed tasks = 155]
