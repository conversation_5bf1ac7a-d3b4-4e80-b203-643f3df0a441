09:32:59.314 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:33:00.172 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 6e4a9346-8c19-4165-a4a5-94ac31418515_config-0
09:33:00.281 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 51 ms to scan 1 urls, producing 3 keys and 6 values 
09:33:00.328 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 19 ms to scan 1 urls, producing 4 keys and 9 values 
09:33:00.342 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 3 keys and 10 values 
09:33:00.358 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 14 ms to scan 1 urls, producing 1 keys and 5 values 
09:33:00.372 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 1 keys and 7 values 
09:33:00.386 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 14 ms to scan 1 urls, producing 2 keys and 8 values 
09:33:00.391 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e4a9346-8c19-4165-a4a5-94ac31418515_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:33:00.391 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e4a9346-8c19-4165-a4a5-94ac31418515_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000028f013b8638
09:33:00.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e4a9346-8c19-4165-a4a5-94ac31418515_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000028f013b8858
09:33:00.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e4a9346-8c19-4165-a4a5-94ac31418515_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:33:00.392 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e4a9346-8c19-4165-a4a5-94ac31418515_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:33:00.411 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e4a9346-8c19-4165-a4a5-94ac31418515_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:33:01.950 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e4a9346-8c19-4165-a4a5-94ac31418515_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754962381628_127.0.0.1_12685
09:33:01.951 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e4a9346-8c19-4165-a4a5-94ac31418515_config-0] Notify connected event to listeners.
09:33:01.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e4a9346-8c19-4165-a4a5-94ac31418515_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:33:01.952 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [6e4a9346-8c19-4165-a4a5-94ac31418515_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000028f014f0228
09:33:02.177 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:33:11.588 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:33:17.992 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:33:19.881 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 26f900d1-6af9-4349-a472-4c2d58ef83df_config-0
09:33:19.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26f900d1-6af9-4349-a472-4c2d58ef83df_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:33:19.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26f900d1-6af9-4349-a472-4c2d58ef83df_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x0000028f013b8638
09:33:19.883 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26f900d1-6af9-4349-a472-4c2d58ef83df_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x0000028f013b8858
09:33:19.885 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26f900d1-6af9-4349-a472-4c2d58ef83df_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:33:19.885 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26f900d1-6af9-4349-a472-4c2d58ef83df_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:33:19.887 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26f900d1-6af9-4349-a472-4c2d58ef83df_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:33:20.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26f900d1-6af9-4349-a472-4c2d58ef83df_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754962399929_127.0.0.1_12726
09:33:20.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26f900d1-6af9-4349-a472-4c2d58ef83df_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:33:20.481 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26f900d1-6af9-4349-a472-4c2d58ef83df_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000028f014f0228
09:33:20.483 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [26f900d1-6af9-4349-a472-4c2d58ef83df_config-0] Notify connected event to listeners.
09:33:20.898 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 00a7056f-660f-4955-9e52-fc026d0137c5
09:33:20.902 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] RpcClient init label, labels = {module=naming, source=sdk}
09:33:20.906 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:33:20.908 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:33:20.910 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:33:20.910 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:33:21.187 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Success to connect to server [localhost:8848] on start up, connectionId = 1754962400950_127.0.0.1_12729
09:33:21.187 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:33:21.187 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Notify connected event to listeners.
09:33:21.189 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x0000028f014f0228
09:33:21.797 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Receive server push request, request = NotifySubscriberRequest, requestId = 2
09:33:21.799 [nacos-grpc-client-executor-9] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Ack server push request, request = NotifySubscriberRequest, requestId = 2
09:33:22.090 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Receive server push request, request = NotifySubscriberRequest, requestId = 3
09:33:22.092 [nacos-grpc-client-executor-10] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Ack server push request, request = NotifySubscriberRequest, requestId = 3
09:33:22.644 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.2:8081 register finished
09:33:22.733 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 24.162 seconds (JVM running for 29.831)
09:33:22.749 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:33:22.751 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:33:22.753 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:33:23.169 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Receive server push request, request = NotifySubscriberRequest, requestId = 4
09:33:23.171 [nacos-grpc-client-executor-24] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [00a7056f-660f-4955-9e52-fc026d0137c5] Ack server push request, request = NotifySubscriberRequest, requestId = 4
