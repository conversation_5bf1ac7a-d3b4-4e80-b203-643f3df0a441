09:02:13.384 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
09:02:13.391 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
09:02:13.719 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
09:02:13.719 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@1a70d456[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
09:02:13.720 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1756257853047_127.0.0.1_14730
09:02:13.721 [nacos-grpc-client-executor-27326] INFO  c.a.n.c.r.c.g.GrpcClient - [printIfInfoEnabled,60] - [1756257853047_127.0.0.1_14730]Ignore complete event,isRunning:false,isAbandon=false
09:02:13.728 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@144337cb[Running, pool size = 5, active threads = 0, queued tasks = 0, completed tasks = 27327]
09:35:30.616 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
09:35:31.315 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 665cf391-f1d0-4140-9ff9-3e2c4a5ca214_config-0
09:35:31.413 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 46 ms to scan 1 urls, producing 3 keys and 6 values 
09:35:31.445 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 4 keys and 9 values 
09:35:31.459 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 10 ms to scan 1 urls, producing 3 keys and 10 values 
09:35:31.471 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 5 values 
09:35:31.482 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 8 ms to scan 1 urls, producing 1 keys and 7 values 
09:35:31.495 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 11 ms to scan 1 urls, producing 2 keys and 8 values 
09:35:31.498 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [665cf391-f1d0-4140-9ff9-3e2c4a5ca214_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:35:31.498 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [665cf391-f1d0-4140-9ff9-3e2c4a5ca214_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001c5663b94f0
09:35:31.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [665cf391-f1d0-4140-9ff9-3e2c4a5ca214_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001c5663b9710
09:35:31.499 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [665cf391-f1d0-4140-9ff9-3e2c4a5ca214_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:35:31.500 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [665cf391-f1d0-4140-9ff9-3e2c4a5ca214_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:35:31.511 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [665cf391-f1d0-4140-9ff9-3e2c4a5ca214_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:35:32.764 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [665cf391-f1d0-4140-9ff9-3e2c4a5ca214_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756344932532_127.0.0.1_9618
09:35:32.765 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [665cf391-f1d0-4140-9ff9-3e2c4a5ca214_config-0] Notify connected event to listeners.
09:35:32.765 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [665cf391-f1d0-4140-9ff9-3e2c4a5ca214_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:35:32.765 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [665cf391-f1d0-4140-9ff9-3e2c4a5ca214_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001c5664f1210
09:35:32.888 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
09:35:37.956 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
09:35:39.236 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
09:35:40.081 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 082fde68-7892-4b08-ab7c-6565094ddabb_config-0
09:35:40.081 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [082fde68-7892-4b08-ab7c-6565094ddabb_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
09:35:40.081 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [082fde68-7892-4b08-ab7c-6565094ddabb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x000001c5663b94f0
09:35:40.082 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [082fde68-7892-4b08-ab7c-6565094ddabb_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x000001c5663b9710
09:35:40.082 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [082fde68-7892-4b08-ab7c-6565094ddabb_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
09:35:40.082 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [082fde68-7892-4b08-ab7c-6565094ddabb_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
09:35:40.082 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [082fde68-7892-4b08-ab7c-6565094ddabb_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:35:40.205 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [082fde68-7892-4b08-ab7c-6565094ddabb_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1756344940092_127.0.0.1_9654
09:35:40.205 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [082fde68-7892-4b08-ab7c-6565094ddabb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:35:40.205 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [082fde68-7892-4b08-ab7c-6565094ddabb_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001c5664f1210
09:35:40.205 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [082fde68-7892-4b08-ab7c-6565094ddabb_config-0] Notify connected event to listeners.
09:35:40.323 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of d8df2dae-e098-41a2-9901-2d49d2a6104c
09:35:40.324 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8df2dae-e098-41a2-9901-2d49d2a6104c] RpcClient init label, labels = {module=naming, source=sdk}
09:35:40.326 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8df2dae-e098-41a2-9901-2d49d2a6104c] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
09:35:40.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8df2dae-e098-41a2-9901-2d49d2a6104c] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
09:35:40.327 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8df2dae-e098-41a2-9901-2d49d2a6104c] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
09:35:40.328 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8df2dae-e098-41a2-9901-2d49d2a6104c] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
09:35:40.460 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8df2dae-e098-41a2-9901-2d49d2a6104c] Success to connect to server [localhost:8848] on start up, connectionId = 1756344940339_127.0.0.1_9655
09:35:40.460 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8df2dae-e098-41a2-9901-2d49d2a6104c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
09:35:40.461 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8df2dae-e098-41a2-9901-2d49d2a6104c] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$466/0x000001c5664f1210
09:35:40.461 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8df2dae-e098-41a2-9901-2d49d2a6104c] Notify connected event to listeners.
09:35:40.963 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway ************:8081 register finished
09:35:40.999 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 11.031 seconds (JVM running for 13.671)
09:35:41.006 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
09:35:41.007 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
09:35:41.008 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
09:35:41.315 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8df2dae-e098-41a2-9901-2d49d2a6104c] Receive server push request, request = NotifySubscriberRequest, requestId = 1
09:35:41.315 [nacos-grpc-client-executor-23] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8df2dae-e098-41a2-9901-2d49d2a6104c] Ack server push request, request = NotifySubscriberRequest, requestId = 1
09:36:11.192 [nacos-grpc-client-executor-48] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8df2dae-e098-41a2-9901-2d49d2a6104c] Receive server push request, request = NotifySubscriberRequest, requestId = 7
09:36:11.193 [nacos-grpc-client-executor-48] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8df2dae-e098-41a2-9901-2d49d2a6104c] Ack server push request, request = NotifySubscriberRequest, requestId = 7
09:36:11.216 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8df2dae-e098-41a2-9901-2d49d2a6104c] Receive server push request, request = NotifySubscriberRequest, requestId = 6
09:36:11.224 [nacos-grpc-client-executor-49] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8df2dae-e098-41a2-9901-2d49d2a6104c] Ack server push request, request = NotifySubscriberRequest, requestId = 6
09:36:11.306 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8df2dae-e098-41a2-9901-2d49d2a6104c] Receive server push request, request = NotifySubscriberRequest, requestId = 8
09:36:11.308 [nacos-grpc-client-executor-50] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8df2dae-e098-41a2-9901-2d49d2a6104c] Ack server push request, request = NotifySubscriberRequest, requestId = 8
09:36:11.318 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8df2dae-e098-41a2-9901-2d49d2a6104c] Receive server push request, request = NotifySubscriberRequest, requestId = 9
09:36:11.319 [nacos-grpc-client-executor-51] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [d8df2dae-e098-41a2-9901-2d49d2a6104c] Ack server push request, request = NotifySubscriberRequest, requestId = 9
