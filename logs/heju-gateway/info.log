10:40:52.815 [background-preinit] INFO  o.h.v.i.util.Version - [<clinit>,21] - HV000001: Hibernate Validator 6.2.5.Final
10:40:53.435 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 41e851a9-b402-4125-a3bb-c94bf4dc43db_config-0
10:40:53.511 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 45 ms to scan 1 urls, producing 3 keys and 6 values 
10:40:53.551 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 12 ms to scan 1 urls, producing 4 keys and 9 values 
10:40:53.560 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 6 ms to scan 1 urls, producing 3 keys and 10 values 
10:40:53.576 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 10 ms to scan 1 urls, producing 1 keys and 5 values 
10:40:53.583 [main] INFO  o.r.Reflections - [scan,232] - <PERSON><PERSON> took 7 ms to scan 1 urls, producing 1 keys and 7 values 
10:40:53.600 [main] INFO  o.r.Reflections - [scan,232] - Reflections took 17 ms to scan 1 urls, producing 2 keys and 8 values 
10:40:53.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41e851a9-b402-4125-a3bb-c94bf4dc43db_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:40:53.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41e851a9-b402-4125-a3bb-c94bf4dc43db_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x00000136013b5248
10:40:53.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41e851a9-b402-4125-a3bb-c94bf4dc43db_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x00000136013b5468
10:40:53.606 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41e851a9-b402-4125-a3bb-c94bf4dc43db_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:40:53.607 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41e851a9-b402-4125-a3bb-c94bf4dc43db_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:40:53.616 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41e851a9-b402-4125-a3bb-c94bf4dc43db_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:40:59.132 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41e851a9-b402-4125-a3bb-c94bf4dc43db_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:41:00.336 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41e851a9-b402-4125-a3bb-c94bf4dc43db_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754793659683_127.0.0.1_4713
10:41:00.336 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41e851a9-b402-4125-a3bb-c94bf4dc43db_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:41:00.336 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41e851a9-b402-4125-a3bb-c94bf4dc43db_config-0] Notify connected event to listeners.
10:41:00.341 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41e851a9-b402-4125-a3bb-c94bf4dc43db_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$467/0x00000136014f16a0
10:41:00.768 [main] INFO  c.h.g.HeJuGatewayApplication - [logStartupProfileInfo,637] - The following 1 profile is active: "dev"
10:41:15.818 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayFilter,144] - [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
10:41:19.480 [main] INFO  c.a.c.s.g.s.SentinelSCGAutoConfiguration - [sentinelGatewayBlockExceptionHandler,134] - [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
10:41:20.921 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 4662d835-e45a-4d24-8504-c7049a6c61c0_config-0
10:41:20.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4662d835-e45a-4d24-8504-c7049a6c61c0_config-0] RpcClient init label, labels = {module=config, Vipserver-Tag=null, source=sdk, Amory-Tag=null, Location-Tag=null, taskId=0, AppName=unknown}
10:41:20.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4662d835-e45a-4d24-8504-c7049a6c61c0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$451/0x00000136013b5248
10:41:20.929 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4662d835-e45a-4d24-8504-c7049a6c61c0_config-0] Register server push request handler:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$$Lambda$452/0x00000136013b5468
10:41:20.931 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4662d835-e45a-4d24-8504-c7049a6c61c0_config-0] Registry connection listener to current client:com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$1
10:41:20.931 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4662d835-e45a-4d24-8504-c7049a6c61c0_config-0] RpcClient init, ServerListFactory = com.alibaba.nacos.client.config.impl.ClientWorker$ConfigRpcTransportClient$2
10:41:20.933 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4662d835-e45a-4d24-8504-c7049a6c61c0_config-0] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:41:21.079 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4662d835-e45a-4d24-8504-c7049a6c61c0_config-0] Success to connect to server [localhost:8848] on start up, connectionId = 1754793680951_127.0.0.1_4923
10:41:21.080 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4662d835-e45a-4d24-8504-c7049a6c61c0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:41:21.080 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4662d835-e45a-4d24-8504-c7049a6c61c0_config-0] Notify connected event to listeners.
10:41:21.081 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4662d835-e45a-4d24-8504-c7049a6c61c0_config-0] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$467/0x00000136014f16a0
10:41:21.359 [main] INFO  c.a.n.c.r.client - [lambda$createClient$0,80] - [RpcClientFactory] create a new rpc client of 13847038-cf2b-45a2-8b80-8d66dbeb9dab
10:41:21.360 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13847038-cf2b-45a2-8b80-8d66dbeb9dab] RpcClient init label, labels = {module=naming, source=sdk}
10:41:21.367 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13847038-cf2b-45a2-8b80-8d66dbeb9dab] RpcClient init, ServerListFactory = com.alibaba.nacos.client.naming.core.ServerListManager
10:41:21.367 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13847038-cf2b-45a2-8b80-8d66dbeb9dab] Registry connection listener to current client:com.alibaba.nacos.client.naming.remote.gprc.redo.NamingGrpcRedoService
10:41:21.368 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13847038-cf2b-45a2-8b80-8d66dbeb9dab] Register server push request handler:com.alibaba.nacos.client.naming.remote.gprc.NamingPushRequestHandler
10:41:21.369 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13847038-cf2b-45a2-8b80-8d66dbeb9dab] Try to connect to server on start up, server: {serverIp = 'localhost', server main port = 8848}
10:41:21.511 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13847038-cf2b-45a2-8b80-8d66dbeb9dab] Success to connect to server [localhost:8848] on start up, connectionId = 1754793681388_127.0.0.1_4924
10:41:21.511 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13847038-cf2b-45a2-8b80-8d66dbeb9dab] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$ConnectResetRequestHandler
10:41:21.511 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13847038-cf2b-45a2-8b80-8d66dbeb9dab] Notify connected event to listeners.
10:41:21.511 [main] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13847038-cf2b-45a2-8b80-8d66dbeb9dab] Register server push request handler:com.alibaba.nacos.common.remote.client.RpcClient$$Lambda$467/0x00000136014f16a0
10:41:22.104 [nacos-grpc-client-executor-14] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13847038-cf2b-45a2-8b80-8d66dbeb9dab] Receive server push request, request = NotifySubscriberRequest, requestId = 3
10:41:22.104 [nacos-grpc-client-executor-14] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13847038-cf2b-45a2-8b80-8d66dbeb9dab] Ack server push request, request = NotifySubscriberRequest, requestId = 3
10:41:22.295 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13847038-cf2b-45a2-8b80-8d66dbeb9dab] Receive server push request, request = NotifySubscriberRequest, requestId = 4
10:41:22.295 [nacos-grpc-client-executor-15] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13847038-cf2b-45a2-8b80-8d66dbeb9dab] Ack server push request, request = NotifySubscriberRequest, requestId = 4
10:41:22.311 [nacos-grpc-client-executor-16] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13847038-cf2b-45a2-8b80-8d66dbeb9dab] Receive server push request, request = NotifySubscriberRequest, requestId = 5
10:41:22.312 [nacos-grpc-client-executor-16] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13847038-cf2b-45a2-8b80-8d66dbeb9dab] Ack server push request, request = NotifySubscriberRequest, requestId = 5
10:41:22.827 [main] INFO  c.a.c.n.r.NacosServiceRegistry - [register,75] - nacos registry, DEFAULT_GROUP heju-gateway 192.168.1.83:8081 register finished
10:41:22.919 [main] INFO  c.h.g.HeJuGatewayApplication - [logStarted,61] - Started HeJuGatewayApplication in 30.798 seconds (JVM running for 36.706)
10:41:22.931 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway-dev.yml, group=DEFAULT_GROUP
10:41:22.934 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway, group=DEFAULT_GROUP
10:41:22.934 [main] INFO  c.a.c.n.r.NacosContextRefresher - [registerNacosListener,129] - [Nacos Config] Listening config: dataId=heju-gateway.yml, group=DEFAULT_GROUP
10:41:23.381 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13847038-cf2b-45a2-8b80-8d66dbeb9dab] Receive server push request, request = NotifySubscriberRequest, requestId = 6
10:41:23.383 [nacos-grpc-client-executor-30] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13847038-cf2b-45a2-8b80-8d66dbeb9dab] Ack server push request, request = NotifySubscriberRequest, requestId = 6
10:41:52.473 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13847038-cf2b-45a2-8b80-8d66dbeb9dab] Receive server push request, request = NotifySubscriberRequest, requestId = 10
10:41:52.474 [nacos-grpc-client-executor-46] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13847038-cf2b-45a2-8b80-8d66dbeb9dab] Ack server push request, request = NotifySubscriberRequest, requestId = 10
10:41:52.485 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13847038-cf2b-45a2-8b80-8d66dbeb9dab] Receive server push request, request = NotifySubscriberRequest, requestId = 9
10:41:52.485 [nacos-grpc-client-executor-47] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [13847038-cf2b-45a2-8b80-8d66dbeb9dab] Ack server push request, request = NotifySubscriberRequest, requestId = 9
14:08:20.009 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,94] - De-registering from Nacos Server now...
14:08:20.020 [SpringApplicationShutdownHook] INFO  c.a.c.n.r.NacosServiceRegistry - [deregister,114] - De-registration finished.
14:08:20.354 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,454] - Shutdown rpc client, set status to shutdown
14:08:20.355 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [shutdown,456] - Shutdown client event executor java.util.concurrent.ScheduledThreadPoolExecutor@5e40143[Running, pool size = 2, active threads = 2, queued tasks = 0, completed tasks = 0]
14:08:20.355 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.client - [closeConnection,591] - Close current connection 1754793681388_127.0.0.1_4924
14:08:20.355 [SpringApplicationShutdownHook] INFO  c.a.n.c.r.c.g.GrpcClient - [shutdown,85] - Shutdown grpc executor java.util.concurrent.ThreadPoolExecutor@7cb6054f[Running, pool size = 4, active threads = 0, queued tasks = 0, completed tasks = 4070]
14:08:22.594 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [4662d835-e45a-4d24-8504-c7049a6c61c0_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
14:08:22.594 [com.alibaba.nacos.client.remote.worker] INFO  c.a.n.c.r.client - [printIfInfoEnabled,60] - [41e851a9-b402-4125-a3bb-c94bf4dc43db_config-0] Try to reconnect to a new server, server is  not appointed, will choose a random server.
