09:28:39.037 [reactor-http-nio-2] ERROR c.h.g.f.<PERSON><PERSON><PERSON><PERSON> - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/enterprise/getInfo
09:28:39.824 [reactor-http-nio-6] ERROR c.h.g.f.AuthFilter - [unauthorizedResponse,100] - [鉴权异常处理]请求路径:/system/user/getInfo
10:19:45.732 [boundedElastic-81] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/fieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
10:19:45.981 [boundedElastic-81] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/fieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
16:07:11.263 [boundedElastic-395] ERROR c.h.g.h.<PERSON>ExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/searchFieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
16:07:11.279 [boundedElastic-395] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/list,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
16:44:11.981 [boundedElastic-429] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/fieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
16:44:12.553 [boundedElastic-429] ERROR c.h.g.h.GatewayExceptionHandler - [handle,43] - [网关异常处理]请求路径:/system/universal/fieldList,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for heju-system"
