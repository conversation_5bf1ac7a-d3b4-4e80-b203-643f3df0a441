package com.heju.common.core.constant.basic;

/**
 * 基类通用常量
 *
 * <AUTHOR>
 */
public class EntityConstants {

    /** 名称 */
    public static final String NAME = "name";

    /** 状态 */
    public static final String STATUS = "status";

    /** 显示顺序 */
    public static final String SORT = "sort";

    /** 备注 */
    public static final String REMARK = "remark";

    /** 创建者Id */
    public static final String CREATE_BY = "createBy";

    /** 创建时间 */
    public static final String CREATE_TIME = "createTime";

    /** 更新者Id */
    public static final String UPDATE_BY = "updateBy";

    /** 更新时间 */
    public static final String UPDATE_TIME = "updateTime";

    /** 删除标志 */
    public static final String DEL_FLAG = "delFlag";

    /** 公共数据（Y是 N否） */
    public static final String IS_COMMON = "isCommon";

    /** 父级Id */
    public static final String PARENT_ID = "parentId";

    /** 祖籍列表 */
    public static final String ANCESTORS = "ancestors";

    /** 层级 */
    public static final String LEVEL = "level";

}
