package com.heju.common.core.constant.system;

import lombok.AllArgsConstructor;
import lombok.Getter;

public class ReportConstants {

    /** 时间类型 */
    @Getter
    @AllArgsConstructor
    public enum TimeType {

        YEARS("2", "年度"),
        SEASON("1", "季度"),
        MONTH("0", "月度");

        private final String code;
        private final String info;

    }

    /** 报表种类 */
    @Getter
    @AllArgsConstructor
    public enum reportClass {

        TAX_REPORT("3", "税务申报信息表"),
        FINANCE_REPORT("2", "财税报表信息表"),
        BILL_REPORT("1", "发票管理详情表"),
        BANK_REPORT("0", "银行报表信息表");

        private final String code;
        private final String info;

    }

    /** 银行报表类型 */
    @Getter
    @AllArgsConstructor
    public enum BankType {

        NUMBER("3","银行报表类型数量"),
        WATER("2", "流水"),
        RECEIPT("1", "回单"),
        STATEMENTS("0", "对账单");

        private final String code;
        private final String info;

    }

    /** 发票类型 */
    @Getter
    @AllArgsConstructor
    public enum BillType {

        NUMBER("2","发票类型数量"),
        ORDINARY("1", "普票"),
        DEDICATED("0", "专票");

        private final String code;
        private final String info;

    }

    /** 财税报表类型 */
    @Getter
    @AllArgsConstructor
    public enum FinanceType {

        NUMBER("4","财税报表类型数量"),
        ACCOUNT_BALANCE_SHEET("3", "科目余额表"),
        CASH_FLOW_STATEMENT("2", "现金流量表"),
        INCOME_STATEMENT("1", "利润表"),
        BALANCE_SHEET("0", "资产负债表");

        private final String code;
        private final String info;

    }

    /** 税务申报类型 */
    @Getter
    @AllArgsConstructor
    public enum TaxType {

        NUMBER("7","税务申报类型数量"),
        PROPERTY_AND_LAND_USE_TAX("6", "房产及土地使用税申报表"),
        SOCIAL_INSURANCE_PREMIUMS("5", "社会保险费申报表"),
        FINANCIAL_STATEMENT("4", "财务报表申报表"),
        STAMP_DUTY("3", "印花税申报表"),
        INDIVIDUAL_INCOME_TAX("2", "个税申报表"),
        CORPORATE_INCOME_TAX("1", "企业所得税申报表"),
        VAT_AND_ADDITIONAL_TAX("0", "增值税及附加税申报表");

        private final String code;
        private final String info;

    }

}
