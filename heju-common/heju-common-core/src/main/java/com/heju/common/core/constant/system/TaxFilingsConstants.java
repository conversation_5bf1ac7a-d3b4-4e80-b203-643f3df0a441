package com.heju.common.core.constant.system;

import lombok.AllArgsConstructor;
import lombok.Getter;

public class TaxFilingsConstants {

    /** 时间类型 */
    @Getter
    @AllArgsConstructor
    public enum TimeType {

        SEASON_NUMBER("4", "季度数量"),
        MONTH_NUMBER("12", "月度数量");

        private final String code;
        private final String info;

    }

    /** 税务申报类型 */
    @Getter
    @AllArgsConstructor
    public enum TaxType {

        NUMBER("4","税务申报类型数量"),
        INCOME_TAX("3", "所得税"),
        TARIFF("2", "关税"),
        EXCISE_DUTY("1", "消费税"),
        GAINS_TAX("0", "增值税");

        private final String code;
        private final String info;

    }

    /** 税务申报状态 */
    @Getter
    @AllArgsConstructor
    public enum TaxStatus {

        NOT_DECLARED("3", "未申报"),
        REJECTED("2", "审批驳回"),
        PASSED("1", "已通过"),
        SUBMITTED("0", "已申报");

        private final String code;
        private final String info;

    }
}
