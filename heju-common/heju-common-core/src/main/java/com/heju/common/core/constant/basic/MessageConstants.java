package com.heju.common.core.constant.basic;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 消息通知常量
 */
public class MessageConstants {

    /** 消息类型 */
    @Getter
    @AllArgsConstructor
    public enum type {

        ENTITY_CHANGES("1", "实体变更");

        private final String code;
        private final String info;

    }

    /** 是否接收站内信 */
    @Getter
    @AllArgsConstructor
    public enum Reception {

        YES("1", "接收站内信"),
        NO("0","不接收站内信");

        private final String code;
        private final String info;

    }

    /** 信息状态 */
    @Getter
    @AllArgsConstructor
    public enum Status {

        READ("1", "已读"),
        UNREAD("0","未读");

        private final String code;
        private final String info;

    }
}
