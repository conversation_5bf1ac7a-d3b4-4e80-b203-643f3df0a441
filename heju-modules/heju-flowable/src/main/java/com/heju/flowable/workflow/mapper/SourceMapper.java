package com.heju.flowable.workflow.mapper;

import com.heju.common.datasource.annotation.Master;
import com.heju.tenant.api.source.domain.dto.TeSourceDto;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 流程分类Mapper接口
 *
 * <AUTHOR>
 * @date 2022-01-15
 */
@Master
public interface SourceMapper {

    @Select("select * from te_source where del_flag=0")
    List<TeSourceDto> selectSourceList();


}
