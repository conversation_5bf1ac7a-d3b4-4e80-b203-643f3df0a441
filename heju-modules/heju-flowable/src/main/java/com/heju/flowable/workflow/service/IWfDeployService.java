package com.heju.flowable.workflow.service;

import com.heju.common.flowable.core.domain.ProcessQuery;
import com.heju.flowable.core.domain.PageQuery;
import com.heju.flowable.page.TableDataInfo;
import com.heju.flowable.workflow.domain.vo.WfDeployVo;

import java.util.List;

/**
 * <AUTHOR>
 * @createTime 2022/6/30 9:03
 */
public interface IWfDeployService {

    TableDataInfo<WfDeployVo> queryPageList(ProcessQuery processQuery, PageQuery pageQuery);

    TableDataInfo<WfDeployVo> queryPublishList(String processKey, PageQuery pageQuery);

    void updateState(String definitionId, String stateCode);

    String queryBpmnXmlById(String definitionId);

    void deleteByIds(List<String> deployIds);
}
