create table hj_material
(
    id            bigint                                   not null comment '素材Id'
        primary key,
    folder_id     bigint       default 0                   not null comment '分类Id',
    nick          varchar(100)                             not null comment '素材昵称',
    name          varchar(100)                             not null comment '素材名称',
    original_name varchar(100)                             not null comment '原图名称',
    url           varchar(200)                             not null comment '素材地址',
    original_url  varchar(200)                             not null comment '原图地址',
    size          decimal(8, 4)                            not null comment '素材大小',
    type          char         default '0'                 not null comment '素材类型（0默认素材 1系统素材）',
    sort          int unsigned default 0                   not null comment '显示顺序',
    status        char         default '0'                 not null comment '状态（0正常 1停用）',
    create_by     bigint                                   null comment '创建者',
    create_time   datetime     default current_timestamp() null comment '创建时间',
    update_by     bigint                                   null comment '更新者',
    update_time   datetime                                 null on update current_timestamp() comment '更新时间',
    del_flag      tinyint      default 0                   not null comment '删除标志（0正常 1删除）',
    tenant_id     bigint                                   not null comment '租户Id'
)
    comment '素材信息表';

create table hj_material_folder
(
    id          bigint                                   not null comment '分类Id'
        primary key,
    parent_id   bigint       default 0                   not null comment '父类Id',
    name        varchar(100)                             not null comment '分类名称',
    level       int                                      not null comment '树层级',
    ancestors   varchar(500) default ''                  null comment '祖级列表',
    type        char         default '0'                 not null comment '分类类型（0默认文件夹 1系统文件夹）',
    sort        int unsigned default 0                   not null comment '显示顺序',
    status      char         default '0'                 not null comment '状态（0正常 1停用）',
    create_by   bigint                                   null comment '创建者',
    create_time datetime     default current_timestamp() null comment '创建时间',
    update_by   bigint                                   null comment '更新者',
    update_time datetime                                 null on update current_timestamp() comment '更新时间',
    del_flag    tinyint      default 0                   not null comment '删除标志（0正常 1删除）',
    tenant_id   bigint                                   not null comment '租户Id'
)
    comment '素材分类信息表';

create table sys_bank_report
(
    id              bigint auto_increment comment '报表id'
        primary key,
    entity_id       bigint                                   not null comment '实体id',
    entity_name     varchar(50)                              null comment '实体名称',
    code            varchar(12)                              null comment '报表编码',
    reporttype_type char                                     not null comment '银行报表类型(0对账单,1回单,2流水)',
    reporttime_type char                                     not null comment '报表时间类型(0月度,1季度,2年度)',
    year            year                                     null comment '年份',
    month           varchar(50)                              null comment '月份',
    season          char                                     null comment '季度(0春季,1夏季,2秋季,3冬季)',
    report_address  varchar(500)                             null comment '报表地址',
    remark          varchar(500)                             null comment '备注',
    sort            int unsigned default 0                   not null comment '显示顺序',
    status          char         default '0'                 not null comment '状态（0正常 1停用）',
    create_time     datetime     default current_timestamp() null comment '创建时间',
    update_time     datetime                                 null on update current_timestamp() comment '更新时间',
    create_by       bigint                                   null comment '创建人',
    update_by       bigint                                   null comment '修改人',
    del_flag        tinyint      default 0                   null comment '删除标志(0 正常,1 删除)',
    tenant_id       bigint                                   not null comment '租户Id'
)
    comment '银行报表信息表';

create table sys_bill_report
(
    id              bigint auto_increment comment '发票id'
        primary key,
    entity_id       bigint                                   not null comment '实体id',
    entity_name     varchar(50)                              null comment '实体名称',
    code            varchar(12)                              null comment '发票编码',
    bill_type       char                                     not null comment '发票类型(0 专票,1 普票)',
    reporttime_type char                                     not null comment '报表时间类型(0月度,1季度,2年度)',
    year            year                                     null comment '年份',
    month           varchar(50)                              null comment '月份',
    season          char                                     null comment '季度(0春季,1夏季,2秋季,3冬季)',
    report_address  varchar(500)                             null comment '报表地址',
    remark          varchar(500)                             null comment '备注',
    sort            int unsigned default 0                   not null comment '显示顺序',
    status          char         default '0'                 not null comment '状态（0正常 1停用）',
    create_time     datetime     default current_timestamp() null comment '创建时间',
    update_time     datetime                                 null on update current_timestamp() comment '更新时间',
    create_by       bigint                                   null comment '创建人',
    update_by       bigint                                   null comment '修改人',
    del_flag        tinyint      default 0                   null comment '删除标志(0 正常,1 删除)',
    tenant_id       bigint                                   not null comment '租户Id'
)
    comment '发票管理详情表';

create table sys_company
(
    id          bigint                                   not null comment '公司id'
        primary key,
    code        varchar(64)                              null comment '公司编码',
    name        varchar(64)                              null comment '公司名称',
    credit_no   varchar(64)                              null comment '统一信用代码',
    third_ids   varchar(255)                             null comment '同步三方系统id（以，隔开）',
    sort        int unsigned default 0                   not null comment '显示顺序',
    status      char         default '0'                 not null comment '状态（0正常 1停用）',
    remark      varchar(200)                             null comment '备注',
    create_by   bigint                                   null comment '创建者',
    create_time datetime     default current_timestamp() null comment '创建时间',
    update_by   bigint                                   null comment '更新者',
    update_time datetime                                 null on update current_timestamp() comment '更新时间',
    del_flag    tinyint      default 0                   not null comment '删除标志(0正常 1删除)',
    tenant_id   bigint                                   not null comment '租户Id',
    no2hr_id    bigint                                   null comment '2号人事id'
)
    comment '子公司表';

create table sys_company_third_auth_merge
(
    id            bigint auto_increment comment '主键id'
        primary key,
    company_id    bigint null comment '企业id',
    third_auth_id bigint null comment '第三方认证id',
    tenant_id     bigint null comment '租户id',
    third_id      bigint null comment '第三方id'
)
    comment '公司第三方认证关联表';

create table sys_company_third_merge
(
    id         bigint not null
        primary key,
    company_id bigint null,
    third_id   bigint null,
    tenant_id  bigint null
)
    comment '公司第三方关联表';

create table sys_dept
(
    id              bigint                                   not null comment '部门id'
        primary key,
    parent_id       bigint       default 0                   null comment '父部门id',
    company_id      bigint                                   null comment '所属公司id',
    company_name    varchar(255)                             null comment '所属公司名称',
    code            varchar(64)                              null comment '部门编码',
    name            varchar(30)  default ''                  null comment '部门名称',
    level           int                                      not null comment '树层级',
    ancestors       varchar(500) default ''                  null comment '祖级列表',
    leader          varchar(20)  default ''                  null comment '负责人',
    phone           varchar(11)  default ''                  null comment '联系电话',
    email           varchar(50)  default ''                  null comment '邮箱',
    sort            int unsigned default 0                   not null comment '显示顺序',
    status          char         default '0'                 not null comment '状态（0正常 1停用）',
    remark          varchar(200)                             null comment '备注',
    create_by       bigint                                   null comment '创建者',
    create_time     datetime     default current_timestamp() null comment '创建时间',
    update_by       bigint                                   null comment '更新者',
    update_time     datetime                                 null on update current_timestamp() comment '更新时间',
    del_flag        tinyint      default 0                   not null comment '删除标志(0正常 1删除)',
    tenant_id       bigint                                   not null comment '租户Id',
    no2hr_id        bigint                                   null comment '2号人事id',
    no2hr_parent_id bigint                                   null comment '2号人事id父节点'
)
    comment '部门信息表';


create table sys_entity
(
    id                        bigint                                   not null comment '公司id'
        primary key,
    code                      varchar(64)                              null comment '公司编码',
    name                      varchar(255)                             null,
    legal_person_name         varchar(255)                             null comment '法定代表人',
    credit_no                 varchar(255)                             null comment '统一社会信用代码',
    register_capital          varchar(255)                             null comment '注册资本',
    register_capital_currency varchar(255)                             null comment '注册资本币种',
    reg_type                  varchar(255)                             null comment '公司类型',
    business_scope            text         default ''                  null comment '经营范围',
    actual_capital            varchar(255)                             null comment '实缴注册资本',
    address                   varchar(255)                             null comment '注册地址',
    postal_code               varchar(255)                             null comment '邮编',
    contact_no                varchar(11)                              null comment '联系电话',
    business_status           varchar(255)                             null comment '经营状态',
    business_term             varchar(255)                             null comment '营业期限',
    belong_org                varchar(255)                             null comment '登记机关',
    register_no               varchar(255)                             null comment '工商注册号',
    bank_name                 varchar(255)                             null comment '开户行名称',
    bank_account              varchar(255)                             null comment '银行账号',
    start_date                date                                     null comment '成立日期',
    partner_total             int(10)                                  null comment '股东数量',
    branch_total              int(10)                                  null comment '分支机构数量',
    employee_total            int(10)                                  null comment '董事会成员数量',
    sort                      int unsigned default 0                   not null comment '显示顺序',
    status                    char         default '0'                 not null comment '状态（0正常 1停用）',
    remark                    varchar(200)                             null comment '备注',
    create_by                 bigint                                   null comment '创建者',
    create_time               datetime     default current_timestamp() null comment '创建时间',
    update_by                 bigint                                   null comment '更新者',
    update_time               datetime                                 null on update current_timestamp() comment '更新时间',
    del_flag                  tinyint      default 0                   not null comment '删除标志(0正常 1删除)',
    tenant_id                 bigint                                   null comment '租户Id'
)
    comment '实体基础表';

create table sys_entity_examine
(
    id             bigint                                   not null
        primary key,
    field_comment  varchar(255)                             null comment '字段中文名',
    field_name     varchar(255)                             null comment '字段编码',
    field_belong   varchar(255)                             null comment '字段所属模块',
    before_text    varchar(255)                             null comment '修改前内容',
    after_text     varchar(255)                             null comment '修改后内容',
    entity_id      bigint                                   null comment '实体id',
    entity_name    varchar(255)                             null comment '实体名称',
    list_id        bigint                                   null comment '所属列表id',
    sort           int unsigned default 0                   null comment '显示顺序',
    examine_status int(1)                                   null comment '审核状态(0-待审核 1-通过 2-驳回)',
    status         char         default '0'                 not null comment '状态（0正常 1停用）',
    remark         varchar(200)                             null comment '备注',
    create_by      bigint                                   null comment '创建者',
    create_time    datetime     default current_timestamp() null comment '创建时间',
    update_by      bigint                                   null comment '更新者',
    update_time    datetime                                 null on update current_timestamp() comment '更新时间',
    del_flag       tinyint      default 0                   not null comment '删除标志(0正常 1删除)'
)
    comment '实体信息审核';

create table sys_entity_field
(
    id            bigint                                   not null
        primary key,
    field_comment varchar(255)                             null comment '字段中文名',
    field_name    varchar(255)                             null comment '字段编码',
    field_type    varchar(11)                              null comment '字段类型',
    field_length  varchar(11)                              null comment '字段长度',
    field_belong  varchar(11)                              null comment '字段所属模块',
    is_change     int                                      null comment '是否可变',
    sort          int unsigned default 0                   not null comment '显示顺序',
    status        char         default '0'                 not null comment '状态（0正常 1停用）',
    remark        varchar(200)                             null comment '备注',
    create_by     bigint                                   null comment '创建者',
    create_time   datetime     default current_timestamp() null comment '创建时间',
    update_by     bigint                                   null comment '更新者',
    update_time   datetime                                 null on update current_timestamp() comment '更新时间',
    del_flag      tinyint      default 0                   not null comment '删除标志(0正常 1删除)',
    tenant_id     bigint                                   null comment '租户Id'
);

INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790616069357678594, '实体名称', 'name', '1', '2', '1', 0, 0, '0', null, 1, '2024-05-15 13:31:52', 1, '2024-05-22 12:39:31', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790617948280025089, '法定代表人', 'legal_person_name', '1', '1', '1', 0, 0, '0', null, 1, '2024-05-15 13:39:20', null, '2024-05-15 14:22:46', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790618048188346369, '统一社会信用代码', 'credit_no', '1', '2', '1', 0, 1, '0', null, 1, '2024-05-15 13:39:44', 1, '2024-05-17 16:56:57', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790618181672071169, '注册资本', 'register_capital', '1', '1', '1', 0, 2, '0', null, 1, '2024-05-15 13:40:16', null, '2024-05-15 14:38:21', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790618292468805633, '注册资本币种', 'register_capital_currency', '1', '1', '1', 0, 3, '0', null, 1, '2024-05-15 13:40:42', null, '2024-05-15 14:38:27', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790618386618347522, '公司类型', 'reg_type', '5', '1', '1', 0, 6, '0', null, 1, '2024-05-15 13:41:05', null, '2024-05-15 14:39:03', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790619445763993601, '经营范围', 'business_scope', '6', '3', '1', 0, 0, '0', null, 1, '2024-05-15 13:45:17', null, '2024-05-15 14:39:29', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790619727977738241, '实缴注册资本', 'actual_capital', '1', '1', '1', 1, 5, '0', null, 1, '2024-05-15 13:46:24', null, '2024-05-22 14:49:20', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790619854276620290, '注册地址', 'address', '1', '3', '1', 0, 0, '0', null, 1, '2024-05-15 13:46:54', 1, '2024-05-15 14:40:30', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790620035042734082, '注册地址邮编', 'postal_code', '1', '1', '1', 1, 0, '0', null, 1, '2024-05-15 13:47:38', 1, '2024-05-22 17:20:18', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790620202810699777, '注册地址联系电话', 'contact_no', '1', '1', '1', 1, 0, '0', null, 1, '2024-05-15 13:48:18', null, null, 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790620473871790082, '经营状态', 'business_status', '5', '1', '1', 0, 1, '0', null, 1, '2024-05-15 13:49:22', null, '2024-05-15 14:38:14', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790620601361854466, '营业期限', 'business_term', '1', '2', '1', 0, 3, '0', null, 1, '2024-05-15 13:49:53', null, '2024-05-15 14:37:36', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790620745381670914, '登记机关', 'belong_org', '1', '2', '1', 0, 2, '0', null, 1, '2024-05-15 13:50:27', null, '2024-05-15 14:37:28', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790620854660067329, '工商注册号', 'register_no', '1', '2', '1', 0, 4, '0', null, 1, '2024-05-15 13:50:53', null, '2024-05-15 14:38:36', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790620949816242177, '开户行名称', 'bank_name', '1', '2', '1', 1, 5, '0', null, 1, '2024-05-15 13:51:16', null, '2024-05-22 14:48:49', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790621033404526594, '银行账号', 'bank_account', '1', '2', '1', 1, 4, '0', null, 1, '2024-05-15 13:51:36', null, '2024-05-22 14:48:52', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790621370572042241, '成立日期', 'start_date', '4', '1', '1', 0, 7, '0', null, 1, '2024-05-15 13:52:56', null, '2024-05-15 14:39:23', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790621992318889986, '纳税人姓名', 'name', '1', '2', '2', 0, 0, '0', null, 1, '2024-05-15 13:55:24', null, '2024-05-15 14:25:34', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790622142588219394, '纳税人识别号', 'credit_no', '1', '2', '2', 0, 1, '0', null, 1, '2024-05-15 13:56:00', null, '2024-05-22 16:47:36', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790622295428657154, '登记注册类型', 'registration_type', '5', '2', '2', 1, 2, '0', null, 1, '2024-05-15 13:56:36', null, '2024-05-15 14:41:22', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790622381487386625, '批准设立机关', 'establish_agency', '1', '2', '2', 1, 3, '0', null, 1, '2024-05-15 13:56:57', null, '2024-05-15 14:41:24', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790622536919904258, '组织机构代码', 'organization_code', '1', '2', '2', 1, 4, '0', null, 1, '2024-05-15 13:57:34', null, '2024-05-15 14:41:26', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790622597930250241, '批准设立证明或文件号', 'license_number', '1', '2', '2', 1, 5, '0', null, 1, '2024-05-15 13:57:49', null, '2024-05-15 14:41:33', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790622828952514562, '生产经营地址', 'business_address', '1', '3', '2', 1, 1, '0', null, 1, '2024-05-15 13:58:44', null, '2024-05-15 14:41:56', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790622937081671682, '邮编', 'business_postal_code', '1', '1', '2', 1, 1, '0', null, 1, '2024-05-15 13:59:09', 1, '2024-05-15 14:41:57', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790623008061878274, '联系电话', 'business_contact_no', '1', '1', '2', 1, 1, '0', null, 1, '2024-05-15 13:59:26', null, '2024-05-15 14:41:59', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790623169278341122, '核算方式', 'accounting_method', '1', '1', '2', 1, 0, '0', null, 1, '2024-05-15 14:00:05', 1, '2024-05-21 17:54:04', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790623245421735938, '从业人数', 'employment_people_no', '2', '1', '2', 1, 1, '0', null, 1, '2024-05-15 14:00:23', null, '2024-05-15 14:42:07', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790623312220221442, '其中外籍人数', 'foreign_people_no', '2', '1', '2', 1, 2, '0', null, 1, '2024-05-15 14:00:39', null, '2024-05-15 14:42:08', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790623394730569729, '单位性质', 'unit_nature', '5', '1', '2', 1, 3, '0', null, 1, '2024-05-15 14:00:59', null, '2024-05-15 14:42:13', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790623470165127170, '网站网址', 'website_url', '1', '2', '2', 1, 6, '0', null, 1, '2024-05-15 14:01:17', null, '2024-05-15 14:42:38', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790623545637433345, '国标行业', 'standard_industry', '1', '2', '2', 1, 7, '0', null, 1, '2024-05-15 14:01:35', null, '2024-05-15 14:42:40', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790623698901495810, '适用会计制度', 'accounting_system', '5', '2', '2', 1, 8, '0', null, 1, '2024-05-15 14:02:11', null, '2024-05-15 14:42:44', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790623812357419009, '税务代理人名称', 'tax_agent_name', '1', '1', '2', 1, 9, '0', null, 1, '2024-05-15 14:02:38', null, '2024-05-15 14:44:40', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790623902354599938, '税务代理人联系电话', 'tax_agent_contact_no', '1', '1', '2', 1, 10, '0', null, 1, '2024-05-15 14:03:00', null, '2024-05-15 14:44:43', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790623974853144578, '税务代理人电子邮箱', 'tax_agent_e_mail', '1', '1', '2', 1, 11, '0', null, 1, '2024-05-15 14:03:17', null, '2024-05-15 14:44:48', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790624059192209410, '投资总额', 'investment_total', '1', '1', '2', 1, 4, '0', null, 1, '2024-05-15 14:03:37', null, '2024-05-15 14:45:08', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790624127689388034, '投资币种', 'investment_total_currency', '1', '1', '2', 1, 5, '0', null, 1, '2024-05-15 14:03:53', 1, '2024-05-15 14:45:10', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790624194039083010, '主管税务局', 'tax_bureau', '1', '1', '2', 1, 6, '0', null, 1, '2024-05-15 14:04:09', null, '2024-05-15 14:45:11', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790624270673211393, '税务主管科室', 'tax_department', '1', '1', '2', 1, 7, '0', null, 1, '2024-05-15 14:04:27', null, '2024-05-15 14:45:13', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790624357101039618, '税务主管人员', 'tax_officials', '1', '1', '2', 1, 8, '0', null, 1, '2024-05-15 14:04:48', null, '2024-05-15 14:45:15', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790624418715365378, '税务主管人员电话', 'tax_officials_phone_no', '1', '1', '2', 1, 9, '0', null, 1, '2024-05-15 14:05:03', null, '2024-05-15 14:45:17', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790624523833012225, '税务主管人员手机', 'tax_officials_telephone_no', '1', '1', '2', 1, 10, '0', null, 1, '2024-05-15 14:05:28', null, '2024-05-15 14:45:20', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790624619056295937, '税务主管科室科长', 'tax_department_chief_name', '1', '1', '2', 1, 11, '0', null, 1, '2024-05-15 14:05:50', null, '2024-05-15 14:45:21', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790624755689943041, '税务主管科室科长电话', 'tax_department_chief_phone_no', '1', '1', '2', 1, 12, '0', null, 1, '2024-05-15 14:06:23', null, '2024-05-15 14:45:23', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790624825692876802, '税务主管科室科长手机', 'tax_department_chief_telephone_no', '1', '1', '2', 1, 13, '0', null, 1, '2024-05-15 14:06:40', null, '2024-05-15 14:45:26', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790624892302618625, '主管税务局分管事项', 'tax_bureau_responsibilities', '1', '1', '2', 1, 14, '0', null, 1, '2024-05-15 14:06:56', null, '2024-05-15 14:45:28', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790624964327206913, '主管税务局分管局长', 'tax_bureau_responsibilities_director', '1', '1', '2', 1, 15, '0', null, 1, '2024-05-15 14:07:13', null, '2024-05-15 14:45:34', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790625030014201857, '分管局长电话', 'tax_bureau_responsibilities_director_phone', '1', '1', '2', 1, 16, '0', null, 1, '2024-05-15 14:07:28', null, '2024-05-15 14:45:36', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790625084481433601, '分管局长手机号码', 'tax_bureau_responsibilities_director_telephone', '1', '1', '2', 1, 17, '0', null, 1, '2024-05-15 14:07:41', null, '2024-05-15 14:45:38', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790625189221593089, '税控软件型号', 'tax_control_software_model', '1', '1', '3', 1, 0, '0', null, 1, '2024-05-15 14:08:06', null, null, 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790625254149419009, '税控软件编号', 'tax_control_software_number', '1', '1', '3', 1, 1, '0', null, 1, '2024-05-15 14:08:22', null, '2024-05-15 14:45:57', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790625332285108225, '发票勾选认证平台账号', 'invoice_software_account', '1', '1', '3', 1, 2, '0', null, 1, '2024-05-15 14:08:40', null, '2024-05-15 14:45:58', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790625391965859842, '发票勾选认证平台密码', 'invoice_software_password', '1', '1', '3', 1, 3, '0', null, 1, '2024-05-15 14:08:55', null, '2024-05-15 14:46:00', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790625448165339137, '税控软件账号', 'tax_control_software_account', '1', '1', '3', 1, 4, '0', null, 1, '2024-05-15 14:09:08', null, '2024-05-15 14:46:01', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790625510605942785, '税控软件登录密码', 'tax_control_software_login_password', '1', '1', '3', 1, 5, '0', null, 1, '2024-05-15 14:09:23', null, '2024-05-15 14:46:03', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790625604528992258, '税控软件用户密码', 'tax_control_software_user_password', '1', '1', '3', 1, 6, '0', null, 1, '2024-05-15 14:09:45', null, '2024-05-15 14:46:05', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790625715619328001, '税控软件用户名', 'tax_control_software_username', '1', '1', '3', 1, 7, '0', null, 1, '2024-05-15 14:10:12', null, '2024-05-15 14:46:06', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790625767158935553, '地方电子税务局账号', 'local_electronictax_bureau_account', '1', '1', '3', 1, 8, '0', null, 1, '2024-05-15 14:10:24', null, '2024-05-15 14:46:08', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790625841507168258, '地方电子税务局密码', 'local_electronictax_bureau_password', '1', '1', '3', 1, 9, '0', null, 1, '2024-05-15 14:10:42', null, '2024-05-15 14:46:09', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790626167698190337, '地方电子税务局登录人账号', 'local_electronictax_bureau_user_account', '1', '1', '3', 1, 10, '0', null, 1, '2024-05-15 14:12:00', null, '2024-05-15 14:46:12', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790626272933277697, '地方电子税务局登录人密码', 'local_electronictax_bureau_user_password', '1', '1', '3', 1, 11, '0', null, 1, '2024-05-15 14:12:25', null, '2024-05-15 14:46:13', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790626349361885185, '自然人电子税务局账号', 'natural_electronictax_bureau_account', '1', '1', '3', 1, 12, '0', null, 1, '2024-05-15 14:12:43', null, '2024-05-15 14:46:14', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790626627154833409, '自然人电子税务局密码', 'natural_electronictax_bureau_password', '1', '1', '3', 1, 13, '0', null, 1, '2024-05-15 14:13:49', null, '2024-05-15 14:46:16', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790626686219022338, '自然人电子税务局登录人账号', 'natural_electronictax_bureau_user_account', '1', '1', '3', 1, 14, '0', null, 1, '2024-05-15 14:14:03', null, '2024-05-15 14:46:18', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790626772042870785, '自然人电子税务局登录人密码', 'natural_electronictax_bureau_user_password', '1', '1', '3', 1, 15, '0', null, 1, '2024-05-15 14:14:24', null, '2024-05-15 14:46:20', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790626977836396546, '经营范围', 'business_scope', '6', '3', '2', 0, 0, '0', null, 1, '2024-05-15 14:15:13', null, '2024-05-15 14:46:30', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790627266031218689, '注册地址', 'address', '1', '3', '2', 0, 0, '0', null, 1, '2024-05-15 14:16:22', null, '2024-05-15 14:25:52', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790627443181842434, '邮编', 'postal_code', '1', '1', '2', 0, 0, '0', null, 1, '2024-05-15 14:17:04', null, '2024-05-22 17:20:26', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790628402792460290, '注册资本', 'register_capital', '1', '1', '2', 0, 0, '0', null, 1, '2024-05-15 14:20:53', null, '2024-05-15 14:25:54', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790628469670637569, '注册资本币种', 'register_capital_currency', '1', '1', '2', 0, 0, '0', null, 1, '2024-05-15 14:21:08', null, '2024-05-15 14:25:57', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id)VALUES (1790632094472040449, '实体编码', 'code', '1', '1', '1', 0, 0, '0', null, 1, '2024-05-15 14:35:33', null, '2024-05-15 14:35:49', 0,null);
INSERT INTO sys_entity_field (id, field_comment, field_name, field_type, field_length, field_belong, is_change, sort, status, remark, create_by, create_time, update_by, update_time, del_flag,tenant_id) VALUES (1790677026591080450, '测试字段', 'test_test_test', '1', '2', '1', 1, 0, '0', null, 1, '2024-05-15 17:34:05', 1, '2024-05-15 17:34:16', 1,null);

create table sys_entity_saic_branch
(
    id        bigint       not null comment '分支机构id'
        primary key,
    name      varchar(255) null comment '分支机构名称',
    entity_id bigint       null comment '公司id'
)
    comment '实体工商分支机构表';

create table sys_entity_saic_change_record
(
    id            bigint auto_increment
        primary key,
    change_date   date         null comment '变更日期',
    change_type   varchar(255) null comment '变更类型',
    change_before text         null comment '变更前',
    change_after  text         null comment '变更后',
    entity_id     bigint       null comment '实体id'
)
    comment '实体工商变更记录表';

create table sys_entity_saic_employee
(
    id        bigint       not null comment '董事会成员id'
        primary key,
    name      varchar(255) null comment '董事会成员姓名',
    title     varchar(255) null comment '董事会成员职位',
    entity_id bigint       null comment '公司id'
)
    comment '实体工商董事会成员表';


create table sys_entity_saic_partner
(
    id                   bigint auto_increment comment '股东id'
        primary key,
    total_real_capital   varchar(255) null comment '实缴出资额',
    partner_type         varchar(255) null comment '股东类型',
    total_should_capital varchar(255) null comment '认缴出资额',
    partner_name         varchar(255) null comment '股东名称',
    percent              varchar(255) null comment '占股比例',
    entity_id            bigint       null comment '公司id'
)
    comment '实体工商股东表';

create table sys_entity_taxation
(
    entity_id                                      bigint       not null comment '实体id'
        primary key,
    registration_type                              varchar(255) null comment '登记注册类型',
    establish_agency                               varchar(255) null comment '批准设立机关',
    organization_code                              varchar(255) null comment '组织机构代码',
    license_number                                 varchar(255) null comment '批准设立证明或文件号',
    business_address                               varchar(255) null comment '生产经营地址',
    business_postal_code                           varchar(255) null comment '邮编',
    business_contact_no                            varchar(11)  null comment '联系电话',
    accounting_method                              varchar(255) null,
    employment_people_no                           int          null comment '从业人数',
    foreign_people_no                              int          null comment '其中外籍人数',
    unit_nature                                    varchar(255) null comment '单位性质',
    website_url                                    varchar(255) null comment '网站网址',
    standard_industry                              varchar(255) null comment '国标行业',
    accounting_system                              varchar(255) null comment '适用会计制度',
    tax_agent_name                                 varchar(255) null comment '税务代理人名称',
    tax_agent_contact_no                           varchar(255) null comment '税务代理人联系电话',
    tax_agent_e_mail                               varchar(255) null comment '税务代理人电子邮箱',
    investment_total                               varchar(255) null comment '投资总额',
    investment_total_currency                      varchar(255) null comment '币种',
    tax_bureau                                     varchar(255) null comment '主管税务局',
    tax_department                                 varchar(255) null comment '税务主管科室',
    tax_officials                                  varchar(255) null comment '税务主管人员',
    tax_officials_phone_no                         varchar(255) null comment '税务主管人员电话',
    tax_officials_telephone_no                     varchar(255) null comment '税务主管人员手机',
    tax_department_chief_name                      varchar(255) null comment '税务主管科室科长',
    tax_department_chief_phone_no                  varchar(255) null comment '税务主管科室科长电话',
    tax_department_chief_telephone_no              varchar(255) null comment '税务主管科室科长手机',
    tax_bureau_responsibilities                    varchar(255) null comment '主管税务局分管事项',
    tax_bureau_responsibilities_director           varchar(255) null comment '主管税务局分管局长',
    tax_bureau_responsibilities_director_phone     varchar(255) null comment '分管局长电话',
    tax_bureau_responsibilities_director_telephone varchar(255) null comment '分管局长手机号码',
    tax_control_software_model                     varchar(255) null comment '税控软件型号',
    tax_control_software_number                    varchar(255) null comment '税控软件编号',
    invoice_software_account                       varchar(255) null comment '发票勾选认证平台账号',
    invoice_software_password                      varchar(255) null comment '发票勾选认证平台密码',
    tax_control_software_account                   varchar(255) null comment '税控软件账号',
    tax_control_software_login_password            varchar(255) null comment '税控软件登录密码',
    tax_control_software_user_password             varchar(255) null comment '税控软件用户密码',
    tax_control_software_username                  varchar(255) null comment '税控软件用户名',
    local_electronictax_bureau_account             varchar(255) null comment '地方电子税务局账号',
    local_electronictax_bureau_password            varchar(255) null comment '地方电子税务局密码',
    local_electronictax_bureau_user_account        varchar(255) null comment '地方电子税务局登录人账号',
    local_electronictax_bureau_user_password       varchar(255) null comment '地方电子税务局登录人密码',
    natural_electronictax_bureau_account           varchar(255) null comment '自然人电子税务局账号',
    natural_electronictax_bureau_password          varchar(255) null comment '自然人电子税务局密码',
    natural_electronictax_bureau_user_account      varchar(255) null comment '自然人电子税务局登录人账号',
    natural_electronictax_bureau_user_password     varchar(255) null comment '自然人电子税务局登录人密码'
)
    comment '实体税务基础表';

create table sys_entity_taxation_invoice_type
(
    id                                bigint       not null
        primary key,
    invoice_type                      varchar(255) null comment '发票种类',
    invoice_limit                     varchar(255) null comment '单份发票最高开票限额',
    month_invoice_number              int          null comment '每月最高购票数量',
    everytime_invoice_number          int          null comment '每次最高购票数量',
    hold_invoice_number               int          null comment '持票最高数量',
    offline_invoice_time_limit        varchar(255) null comment '离线开票时限',
    offline_invoice_accumulated_limit varchar(255) null comment '离线开票累计限额',
    starttime                         date         null comment '有效期起',
    endtime                           date         null comment '有效期止',
    entity_id                         bigint       null comment '实体id'
)
    comment '实体税务票种认定';

create table sys_entity_taxation_type
(
    id                 bigint(11)   not null
        primary key,
    collection_project varchar(255) null comment '征收项目',
    collection_item    varchar(255) null comment '征收品目',
    tax_paymen_period  varchar(255) null comment '纳税期限',
    starttime          date         null comment '有限期起',
    endtime            date         null comment '有限期止',
    entity_id          bigint(11)   null comment '实体id'
)
    comment '实体税务税费种认定';

create table sys_entity_taxation_user
(
    id              bigint       not null
        primary key,
    user_name       varchar(255) null comment '姓名',
    user_type       varchar(255) null comment '人员类型',
    identity_type   varchar(255) null comment '身份证件种类',
    identity_number varchar(255) null comment '身份证件号码',
    phone           varchar(255) null comment '固定电话',
    telephone       varchar(255) null comment '移动电话',
    e_mail          varchar(255) null comment '电子邮箱',
    entity_id       bigint       null comment '实体id'
)
    comment '实体税务人员信息';

create table sys_file
(
    id          bigint                                   not null comment '文件Id'
        primary key,
    folder_id   bigint       default 0                   not null comment '分类Id',
    name        varchar(100)                             not null comment '文件名称',
    nick        varchar(100)                             null comment '文件别名',
    url         varchar(500)                             not null comment '文件地址',
    size        bigint       default 0                   not null comment '文件大小',
    type        char         default '0'                 not null comment '文件类型（0默认 1系统）',
    sort        int unsigned default 0                   not null comment '显示顺序',
    status      char         default '0'                 not null comment '状态（0正常 1停用）',
    create_by   bigint                                   null comment '创建者',
    create_time datetime     default current_timestamp() null comment '创建时间',
    update_by   bigint                                   null comment '更新者',
    update_time datetime                                 null on update current_timestamp() comment '更新时间',
    del_flag    tinyint      default 0                   not null comment '删除标志（0正常 1删除）',
    tenant_id   bigint                                   not null comment '租户Id'
)
    comment '文件信息表';

create table sys_file_folder
(
    id          bigint                                   not null comment '分类Id'
        primary key,
    parent_id   bigint       default 0                   not null comment '父分类Id',
    name        varchar(100)                             not null comment '分类名称',
    level       int                                      not null comment '树层级',
    ancestors   varchar(500) default ''                  null comment '祖级列表',
    type        char         default '0'                 not null comment '分类类型（0默认文件夹 1系统文件夹）',
    sort        int unsigned default 0                   not null comment '显示顺序',
    status      char         default '0'                 not null comment '状态（0正常 1停用）',
    create_by   bigint                                   null comment '创建者',
    create_time datetime     default current_timestamp() null comment '创建时间',
    update_by   bigint                                   null comment '更新者',
    update_time datetime                                 null on update current_timestamp() comment '更新时间',
    del_flag    tinyint      default 0                   not null comment '删除标志（0正常 1删除）',
    tenant_id   bigint                                   not null comment '租户Id'
)
    comment '文件分类信息表';

create table sys_finance_report
(
    id              bigint auto_increment comment '报表id'
        primary key,
    entity_id       bigint                                   not null comment '实体id',
    entity_name     varchar(50)                              null comment '实体名称',
    code            varchar(12)                              null comment '报表编码',
    finance_type    char                                     not null comment '财务报表类型(0资产负债表,1利润表,2现金流量表,3科目余额表)',
    reporttime_type char                                     not null comment '报表时间类型(0月度,1季度,2年度)',
    year            year                                     null comment '年份',
    month           varchar(50)                              null comment '月份',
    season          char                                     null comment '季度(0春季,1夏季,2秋季,3冬季)',
    report_address  varchar(500)                             null comment '报表地址',
    remark          varchar(500)                             null comment '备注',
    sort            int unsigned default 0                   not null comment '显示顺序',
    status          char         default '0'                 not null comment '状态（0正常 1停用）',
    create_time     datetime     default current_timestamp() null comment '创建时间',
    update_time     datetime                                 null on update current_timestamp() comment '更新时间',
    create_by       bigint                                   null comment '创建人',
    update_by       bigint                                   null comment '修改人',
    del_flag        tinyint      default 0                   null comment '删除标志(0 正常,1 删除)',
    tenant_id       bigint                                   not null comment '租户Id'
)
    comment '财税报表信息表';

create table sys_job_log
(
    id             bigint auto_increment comment '任务日志Id'
        primary key,
    job_id         bigint                                    not null comment '任务Id',
    name           varchar(64)                               not null comment '任务名称',
    job_group      varchar(64)                               not null comment '任务组名',
    invoke_target  varchar(500)                              not null comment '调用目标字符串',
    invoke_tenant  varchar(500)                              not null comment '调用租户字符串',
    job_message    varchar(500)                              null comment '日志信息',
    status         char          default '0'                 not null comment '执行状态（0正常 1失败）',
    exception_info varchar(2000) default ''                  null comment '异常信息',
    create_time    datetime      default current_timestamp() null comment '创建时间',
    del_time       datetime                                  null on update current_timestamp() comment '删除时间',
    del_flag       tinyint       default 0                   not null comment '删除标志(0正常 1删除)',
    tenant_id      bigint                                    not null comment '租户Id'
)
    comment '定时任务调度日志表';

create table sys_login_log
(
    id              bigint auto_increment comment '访问Id'
        primary key,
    enterprise_name varchar(50)  default ''                  null comment '企业账号',
    user_id         bigint       default -2                  not null comment '用户Id',
    user_name       varchar(50)  default ''                  null comment '用户账号',
    user_nick       varchar(50)  default ''                  null comment '用户名称',
    ipaddr          varchar(128) default ''                  null comment '登录IP地址',
    status          char         default '0'                 null comment '登录状态（0成功 1失败）',
    msg             varchar(255) default ''                  null comment '提示信息',
    access_time     datetime     default current_timestamp() null comment '访问时间',
    del_time        datetime                                 null on update current_timestamp() comment '删除时间',
    del_flag        tinyint      default 0                   not null comment '删除标志(0正常 1删除)',
    tenant_id       bigint                                   not null comment '租户Id'
)
    comment '系统访问记录';

create index idx_sys_login_log_lt
    on sys_login_log (access_time);

create index idx_sys_login_log_s
    on sys_login_log (status);
	
create table sys_notice
(
    id          bigint                               not null comment '公告Id'
        primary key,
    name        varchar(50)                          not null comment '公告标题',
    type        char     default '0'                 not null comment '公告类型（0通知 1公告）',
    content     longblob                             null comment '公告内容',
    status      char     default '0'                 null comment '公告状态（0待发送 1已发送 2已关闭 3发送失败 4发送异常）',
    remark      varchar(200)                         null comment '备注',
    create_by   bigint                               null comment '创建者',
    create_time datetime default current_timestamp() null comment '创建时间',
    update_by   bigint                               null comment '更新者',
    update_time datetime                             null on update current_timestamp() comment '更新时间',
    del_flag    tinyint  default 0                   not null comment '删除标志(0正常 1删除)',
    tenant_id   bigint                               not null comment '租户Id'
)
    comment '通知公告表';

create table sys_notice_log
(
    id             bigint                               not null comment 'id'
        primary key,
    notice_id      bigint                               not null comment '公告Id',
    user_id        bigint                               not null comment '用户Id',
    receive_status char                                 not null comment '发送状态（0成功 1失败）',
    status         char     default '0'                 null comment '阅读状态（0未读 1已读）',
    remark         varchar(200)                         null comment '备注',
    create_time    datetime default current_timestamp() null comment '创建时间',
    del_flag       tinyint  default 0                   not null comment '删除标志(0正常 1删除)',
    tenant_id      bigint                               not null comment '租户Id'
)
    comment '通知公告记录表';
	
create table sys_operate_log
(
    id             bigint auto_increment comment '日志主键'
        primary key,
    title          varchar(50)   default ''                  null comment '模块标题',
    business_type  char(2)       default '00'                null comment '业务类型（0其它 1新增 2修改 3删除）',
    method         varchar(100)  default ''                  null comment '方法名称',
    request_method varchar(10)   default ''                  null comment '请求方式',
    operate_type   char(2)       default '00'                null comment '操作类别（00其它 01后台 02手机端）',
    user_id        bigint        default -2                  not null comment '操作人员',
    user_name      varchar(50)                               null comment '操作人员账号',
    user_nick      varchar(50)                               null comment '操作人员名称',
    url            varchar(255)  default ''                  null comment '请求URL',
    ip             varchar(128)  default ''                  null comment '主机地址',
    param          varchar(2000) default ''                  null comment '请求参数',
    location       varchar(255)  default ''                  null comment '操作地点',
    json_result    varchar(2000) default ''                  null comment '返回参数',
    status         char          default '0'                 null comment '操作状态（0正常 1异常）',
    error_msg      varchar(2000) default ''                  null comment '错误消息',
    cost_time      bigint        default 0                   null comment '消耗时间',
    operate_time   datetime      default current_timestamp() null comment '操作时间',
    del_time       datetime                                  null on update current_timestamp() comment '删除时间',
    del_flag       tinyint       default 0                   not null comment '删除标志(0正常 1删除)',
    tenant_id      bigint                                    not null comment '租户Id'
)
    comment '操作日志记录';

create index idx_sys_operate_log_bt
    on sys_operate_log (business_type);

create index idx_sys_operate_log_ot
    on sys_operate_log (operate_time);

create index idx_sys_operate_log_s
    on sys_operate_log (status);
	
create table sys_organize_role_merge
(
    id        bigint auto_increment comment 'id'
        primary key,
    dept_id   bigint null comment '部门id',
    post_id   bigint null comment '岗位id',
    user_id   bigint null comment '用户id',
    role_id   bigint not null comment '角色Id',
    tenant_id bigint not null comment '租户Id',
    constraint dept_id
        unique (dept_id, post_id, user_id, role_id)
)
    comment '组织和角色关联表';
	
create table sys_post
(
    id           bigint                                   not null comment '岗位Id'
        primary key,
    dept_id      bigint                                   not null comment '部门Id',
    dept_name    varchar(255)                             null comment '所属部门名称',
    company_id   bigint                                   null comment '所属公司id',
    company_name varchar(255)                             null comment '所属公司名称',
    code         varchar(64)                              null comment '岗位编码',
    name         varchar(50)                              not null comment '岗位名称',
    sort         int unsigned default 0                   not null comment '显示顺序',
    status       char         default '0'                 not null comment '状态（0正常 1停用）',
    remark       varchar(200)                             null comment '备注',
    create_by    bigint                                   null comment '创建者',
    create_time  datetime     default current_timestamp() null comment '创建时间',
    update_by    bigint                                   null comment '更新者',
    update_time  datetime                                 null on update current_timestamp() comment '更新时间',
    del_flag     tinyint      default 0                   not null comment '删除标志(0正常 1删除)',
    tenant_id    bigint                                   not null comment '租户Id',
    no2hr_id     bigint                                   null comment '2号人事id'
)
    comment '岗位信息表';
	
create table sys_role
(
    id          bigint                                   not null comment '角色Id'
        primary key,
    code        varchar(64)                              null comment '角色编码',
    name        varchar(30)                              not null comment '角色名称',
    role_key    varchar(100)                             null comment '角色权限字符串',
    data_scope  char         default '1'                 null comment '数据范围（1全部数据权限 2自定数据权限 3本部门数据权限 4本部门及以下数据权限 5本岗位数据权限  6仅本人数据权限）',
    sort        int unsigned default 0                   not null comment '显示顺序',
    status      char         default '0'                 not null comment '状态（0正常 1停用）',
    remark      varchar(200)                             null comment '备注',
    create_by   bigint                                   null comment '创建者',
    create_time datetime     default current_timestamp() null comment '创建时间',
    update_by   bigint                                   null comment '更新者',
    update_time datetime                                 null on update current_timestamp() comment '更新时间',
    del_flag    tinyint      default 0                   not null comment '删除标志（0正常 1删除）',
    tenant_id   bigint                                   not null comment '租户Id'
)
    comment '角色信息表';

create table sys_role_dept_merge
(
    id        bigint not null comment 'id'
        primary key,
    role_id   bigint not null comment '角色Id',
    dept_id   bigint not null comment '部门Id',
    tenant_id bigint not null comment '租户Id',
    constraint role_id
        unique (role_id, dept_id)
)
    comment '角色和部门-岗位关联表';
	
create table sys_role_menu_merge
(
    id        bigint not null comment 'id'
        primary key,
    role_id   bigint not null comment '角色Id',
    menu_id   bigint not null comment '菜单Id',
    tenant_id bigint not null comment '租户Id',
    constraint role_id
        unique (role_id, menu_id)
)
    comment '角色和菜单关联表';

create table sys_role_module_merge
(
    id        bigint not null comment 'id'
        primary key,
    role_id   bigint not null comment '角色Id',
    module_id bigint not null comment '模块Id',
    tenant_id bigint not null comment '租户Id',
    constraint role_id
        unique (role_id, module_id)
)
    comment '角色和模块关联表';
	
create table sys_role_post_merge
(
    id        bigint not null comment 'id'
        primary key,
    role_id   bigint not null comment '角色Id',
    post_id   bigint not null comment '岗位Id',
    tenant_id bigint not null comment '租户Id',
    constraint role_id
        unique (role_id, post_id)
)
    comment '角色和部门-岗位关联表';

create table sys_tax_report
(
    id              bigint auto_increment comment '报表id'
        primary key,
    entity_id       bigint                                   not null comment '实体id',
    entity_name     varchar(50)                              null comment '实体名称',
    code            varchar(12)                              null comment '报表编码',
    tax_type        char                                     not null comment '税务申报报表类型(0 增值税及附加税申报表,1 企业所得税申报表,2 个税申报表,3 印花税申报表,4 财务报表申报表,5 社会保险费申报表,6 房产及土地使用税申报表)',
    reporttime_type char                                     not null comment '报表时间类型(0月度,1季度,2年度)',
    year            year                                     null comment '年份',
    month           varchar(50)                              null comment '月份',
    season          char                                     null comment '季度(0春季,1夏季,2秋季,3冬季)',
    report_address  varchar(500)                             null comment '报表地址',
    remark          varchar(500)                             null comment '备注',
    sort            int unsigned default 0                   not null comment '显示顺序',
    status          char         default '0'                 not null comment '状态（0正常 1停用）',
    create_time     datetime     default current_timestamp() null comment '创建时间',
    update_time     datetime                                 null on update current_timestamp() comment '更新时间',
    create_by       bigint                                   null comment '创建人',
    update_by       bigint                                   null comment '修改人',
    del_flag        tinyint      default 0                   null comment '删除标志(0 正常,1 删除)',
    tenant_id       bigint                                   not null comment '租户Id'
)
    comment '税务申报信息表';
	
create table sys_tenant_menu_merge
(
    id        bigint not null comment 'id'
        primary key,
    menu_id   bigint not null comment '菜单Id',
    tenant_id bigint null comment '租户Id',
    constraint menu_id
        unique (menu_id, tenant_id)
)
    comment '租户和菜单关联表';
	
create table sys_tenant_module_merge
(
    id        bigint not null comment 'id'
        primary key,
    module_id bigint not null comment '模块Id',
    tenant_id bigint null comment '租户Id',
    constraint module_id
        unique (module_id, tenant_id)
)
    comment '租户和模块关联表';
	
create table sys_third_auth
(
    id          bigint                                   not null comment 'id'
        primary key,
    code        varchar(64)                              null comment '三方认证信息编码',
    name        varchar(64)                              null comment '三方认证信息名称',
    auth_json   varchar(255)                             null comment '三方认证信息内容',
    is_admin    char                                     null comment '是否超管（0是 1否）',
    third_id    bigint                                   null comment '第三方模块id',
    sort        int unsigned default 0                   not null comment '显示顺序',
    status      char         default '0'                 not null comment '状态（0正常 1停用）',
    remark      varchar(200)                             null comment '备注',
    create_by   bigint                                   null comment '创建者',
    create_time datetime     default current_timestamp() null comment '创建时间',
    update_by   bigint                                   null comment '更新者',
    update_time datetime                                 null on update current_timestamp() comment '更新时间',
    del_flag    tinyint      default 0                   not null comment '删除标志(0正常 1删除)',
    tenant_id   bigint                                   not null comment '租户Id'
)
    comment '第三方认证信息表';

create table sys_user
(
    id          bigint                                         not null comment '用户Id'
        primary key,
    code        varchar(64)                                    null comment '用户编码',
    user_name   varchar(30)                                    not null comment '用户账号',
    nick_name   varchar(30)                                    not null comment '用户昵称',
    user_type   varchar(2)   default '01'                      null comment '用户类型（00超管用户 01普通用户）',
    phone       varchar(11)  default ''                        null comment '手机号码',
    email       varchar(50)  default ''                        null comment '用户邮箱',
    sex         char         default '2'                       null comment '用户性别（0男 1女 2保密）',
    avatar      varchar(100) default ''                        null comment '头像地址',
    profile     varchar(100) default '这个人很懒，暂未留下什么' null comment '个人简介',
    password    varchar(100) default ''                        null comment '密码',
    login_ip    varchar(128) default ''                        null comment '最后登录IP',
    login_date  datetime                                       null comment '最后登录时间',
    sort        int unsigned default 0                         not null comment '显示顺序',
    status      char         default '0'                       not null comment '状态（0正常 1停用）',
    reception   char         default '0'                       not null comment '是否接收站内信（0不接收，1接收）',
    remark      varchar(200)                                   null comment '备注',
    create_by   bigint                                         null comment '创建者',
    create_time datetime     default current_timestamp()       null comment '创建时间',
    update_by   bigint                                         null comment '更新者',
    update_time datetime                                       null on update current_timestamp() comment '更新时间',
    del_flag    tinyint      default 0                         not null comment '删除标志(0正常 1删除)',
    tenant_id   bigint                                         not null comment '租户Id',
    no2hr_id    bigint                                         null comment '2号人事id',
    open_id     varchar(255)                                   null comment 'openId'
)
    comment '用户信息表';
	
create table sys_user_post_merge
(
    id        bigint not null comment 'id'
        primary key,
    user_id   bigint not null comment '用户Id',
    post_id   bigint not null comment '职位Id',
    tenant_id bigint not null comment '租户Id',
    constraint user_id
        unique (user_id, post_id)
)
    comment '用户-岗位关联表';

create table sys_service_management
(
    id            bigint auto_increment comment '服务id'
        primary key,
    name          varchar(15)                              not null comment '服务项名称',
    parent_id     bigint                                   null comment '上级服务',
    code          varchar(10)                              null comment '服务项编码',
    status        char                                     not null comment '状态(0开启,1关闭)',
    is_recommend  char                                     not null comment '是否推荐(0是,1否)',
    services_icon varchar(255)                             null comment '服务项图标',
    sort          int                                      null comment '显示顺序',
    create_time   datetime     default current_timestamp() null comment '创建时间',
    update_time   datetime                                 null comment '修改时间',
    create_by     varchar(20)                              null comment '创建人',
    update_by     varchar(20)                              null comment '修改人',
    del_flag      tinyint      default 0                   null comment '删除标志(0 正常,1 删除)',
    level         int                                      not null comment '树层级',
    ancestors     varchar(500) default ''                  null comment '祖级列表',
    remark        varchar(500)                             null comment '备注'
)
    comment '服务项目信息表';

create table sys_business_annual_report
(
    id              bigint auto_increment comment '报表id'
        primary key,
    entity_id       bigint                                   not null comment '实体id',
    code            varchar(12)                              null comment '报表编码',
    reporttype_type char                                     not null comment '工商年报类型(0工商年报)',
    year            year                                     null comment '年份',
    report_address  varchar(500)                             null comment '报表地址',
    remark          varchar(500)                             null comment '备注',
    sort            int unsigned default 0                   not null comment '显示顺序',
    status          char         default '0'                 not null comment '状态（0正常 1停用）',
    declare_status  char         default '0'                 not null comment '0已申报 1申报失败 2审批通过 3审批驳回',
    declare_remark  varchar(500)                             null comment '无法申报原因',
    reason          varchar(500)                             null comment '驳回原因',
    create_time     datetime     default current_timestamp() null comment '创建时间',
    update_time     datetime                                 null on update current_timestamp() comment '更新时间',
    create_by       bigint                                   null comment '创建人',
    update_by       bigint                                   null comment '修改人',
    del_flag        tinyint      default 0                   null comment '删除标志(0 正常,1 删除)',
    tenant_id       bigint                                   not null comment '租户Id'
)
    comment '工商年报信息表';

create table sys_tax_filings
(
    id              bigint auto_increment comment '申报id'
        primary key,
    entity_id       bigint                                   not null comment '实体id',
    code            varchar(12)                              null comment '申报编码',
    tax_type        char                                     not null comment '税务申报类型(0 增值税,1 消费税,2 关税,3 所得税)',
    reporttime_type char                                     not null comment '报表时间类型(0月度,1季度,2年度)',
    year            year                                     null comment '年份',
    month           varchar(50)                              null comment '月份',
    season          char                                     null comment '季度(0春季,1夏季,2秋季,3冬季)',
    report_address  varchar(500)                             null comment '报表地址',
    remark          varchar(500)                             null comment '备注',
    sort            int unsigned default 0                   not null comment '显示顺序',
    status          char         default '0'                 not null comment '状态（0正常 1停用）',
    declare_status  char         default '0'                 not null comment '0已申报 1审批通过 2审批驳回',
    declare_remark  varchar(500)                             null comment '无法申报原因',
    reason          varchar(500)                             null comment '驳回原因',
    create_time     datetime     default current_timestamp() null comment '创建时间',
    update_time     datetime                                 null on update current_timestamp() comment '更新时间',
    create_by       bigint                                   null comment '创建人',
    update_by       bigint                                   null comment '修改人',
    del_flag        tinyint      default 0                   null comment '删除标志(0 正常,1 删除)',
    tenant_id       bigint                                   not null comment '租户Id'
)
    comment '税务申报表';

create table sys_message
(
    id                bigint auto_increment comment '消息Id'
        primary key,
    title             varchar(255)                             null comment '通知标题',
    content           varchar(255)                             null comment '通知内容',
    type              int                                      null comment '通知类型（1-实体变更通知）',
    status            char         default '0'                 null comment '状态（0-未读，1-已读）',
    sort              int unsigned default 0                   null comment '显示顺序',
    send_user_id      bigint                                   null comment '发送人',
    receive_user_id   bigint                                   null comment '接收人',
    change_message_id bigint                                   not null comment '实体更变消息详情id',
    create_time       datetime     default current_timestamp() null comment '申请时间',
    tenant_id         bigint                                   null comment '租户id'
)
    comment '消息通知表';

create table sys_message_entity_change
(
    id            bigint auto_increment comment '消息实体变更通知Id'
        primary key,
    entity_id     bigint       null comment '实体id',
    change_field  varchar(255) null comment '变更字段',
    change_before text         null comment '变更前',
    change_after  text         null comment '变更后'
)
    comment '消息实体变更通知表';


create table sys_role_entity_field_merge
(
    id              bigint auto_increment
        primary key,
    role_id         bigint not null comment '角色id',
    entity_field_id bigint not null comment '实体信息字段id'
);