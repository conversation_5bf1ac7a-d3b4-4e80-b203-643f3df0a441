package com.heju.tenant.tenant.service;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.web.entity.service.IBaseService;
import com.heju.system.api.authority.domain.dto.SysTenantMenuDto;
import com.heju.tenant.api.tenant.domain.dto.TeTenantDto;
import com.heju.tenant.api.tenant.domain.query.TeTenantQuery;
import com.heju.tenant.tenant.domain.model.TeTenantRegister;

import java.util.List;

/**
 * 租户管理 服务层
 *
 * <AUTHOR>
 */
public interface ITeTenantService extends IBaseService<TeTenantQuery, TeTenantDto> {

    /**
     * 获取租户权限
     *
     * @param id id
     * @return 权限Ids
     */
    Long[] selectAuth(Long id);

    /**
     * 修改租户权限
     *
     * @param id      id
     * @param authIds 权限Ids
     */
    void updateAuth(Long id, Long[] authIds);

    /**
     * 新增租户 | 包含数据初始化
     *
     * @param tenantRegister 租户初始化对象
     * @return 结果
     */
    int insert(TeTenantRegister tenantRegister);

    /**
     * 校验数据源策略是否被使用
     *
     * @param strategyId 数据源策略id
     * @return 结果 | true/false 存在/不存在
     */
    boolean checkStrategyExist(Long strategyId);

    /**
     * 校验租户是否为默认租户
     *
     * @param id 租户id
     * @return 结果 | true/false 是/不是
     */
    boolean checkIsDefault(Long id);

    /**
     * 租户组织数据初始化
     *
     * @param tenantRegister 租户初始化对象
     */
    void organizeInit(TeTenantRegister tenantRegister);

    /**
     * 租户权限数据初始化
     *
     * @param tenantRegister 租户初始化对象
     */
    void authorityInit(TeTenantRegister tenantRegister);

    AjaxResult listByIds(String telephone);

    Long tenantAndUserInit(TeTenantRegister tenantRegister);

    AjaxResult listByUnionId(String unionId);

    TeTenantDto selectByName(String sourceName);

    TeTenantDto selectByNick(String sourceName);

    Long insertTenantAndUser(TeTenantRegister tenantRegister);

    void updateTheDatabase(String sql);

    /**
     * 查询租户已有菜单
     * @return
     */
    List<Long> getTenantHasMenu(Long tenantId);

    /**
     * 修改租户菜单
     * @param sysTenantMenuDto
     */
    void updateTenantMenu(SysTenantMenuDto sysTenantMenuDto);
}