package com.heju.tenant.tenant.controller;

import com.heju.common.core.constant.basic.SecurityConstants;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.result.R;
import com.heju.common.security.annotation.InnerAuth;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.api.organize.feign.RemoteUserService;
import com.heju.tenant.api.tenant.domain.dto.BaseUserDto;
import com.heju.tenant.tenant.service.ITeInviteRegisterService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Objects;


/**
 * 二维码邀请 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/inviteRegister")
public class SysInviteRegister {

    @Resource
    private RemoteUserService remoteUserService;

    @Resource
    private ITeInviteRegisterService inviteRegisterService;

    /**
     * 获取岗位树
     * @param sourceName 数据源
     * @param openId openId
     * @return 查询到的岗位树
     */
    @GetMapping("/option")
    public AjaxResult option(String sourceName ,String openId) {
        return remoteUserService.remoteOption(sourceName, SecurityConstants.INNER);
    }

    /**
     * 添加用户
     * @param sourceName 数据源
     * @param user 用户信息
     * @return 成功/失败
     */
    @PostMapping("/addUser")
    public AjaxResult addUser(String sourceName , @RequestBody SysUserDto user){
//        BaseUserDto baseUserDto = new BaseUserDto();
//        baseUserDto.setOpenId(user.getOpenId());
//        inviteRegisterService.getBaseUserByCondition(baseUserDto);
        return inviteRegisterService.addUser(sourceName,user);
    }

    /**
     * 根据手机号/openId查询基础用户信息
     * @param baseUserDto BaseUserDto
     * @return 查询到的BaseUser信息
     */
    @InnerAuth
    @GetMapping("/getBaseUser")
    public AjaxResult selectBaseUser(@RequestParam(value = "baseUserDto") BaseUserDto baseUserDto){
        BaseUserDto baseUserByCondition = inviteRegisterService.getBaseUserByCondition(baseUserDto);
        if (Objects.isNull(baseUserByCondition)){
            AjaxResult.error("查询基础用户信息失败");
        }
        return AjaxResult.success(baseUserByCondition);
    }

    /**
     * 更改用户微信绑定
     * @param dto BaseUserDto
     * @return 成功/失败
     */
    @InnerAuth
    @PutMapping("/editWechat")
    public AjaxResult editWechatBind(@RequestBody BaseUserDto dto){
        return inviteRegisterService.updateBaseUser(dto);
    }
}
