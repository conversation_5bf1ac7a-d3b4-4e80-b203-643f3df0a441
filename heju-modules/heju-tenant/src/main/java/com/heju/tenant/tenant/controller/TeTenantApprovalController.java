package com.heju.tenant.tenant.controller;
import com.heju.common.cache.constant.CacheConstants;
import com.heju.common.core.constant.basic.TenantConstants;
import com.heju.common.core.utils.core.NumberUtil;
import com.heju.common.core.utils.core.ObjectUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.log.annotation.Log;
import com.heju.common.log.enums.BusinessType;
import com.heju.common.redis.service.RedisService;
import com.heju.common.security.annotation.Logical;
import com.heju.common.security.annotation.RequiresPermissions;
import com.heju.common.security.auth.Auth;
import com.heju.common.web.entity.controller.BaseController;
import com.heju.tenant.api.tenant.domain.dto.TeTenantApprovalDto;
import com.heju.tenant.api.tenant.domain.query.TeTenantApprovalQuery;
import com.heju.tenant.tenant.service.ITeTenantApprovalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.UUID;

/**
 * 租户审批管理 业务处理
 *
 * <AUTHOR>
 */
@RestController
@RequestMapping("/approval")
public class TeTenantApprovalController extends BaseController<TeTenantApprovalQuery, TeTenantApprovalDto, ITeTenantApprovalService> {



    /** 定义节点名称 */
    @Override
    protected String getNodeName() {
        return "租户审批" ;
    }

    @Resource
    private ITeTenantApprovalService tenantApprovalService;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Resource
    private RedisService redisService;




    /**
     * 查询租户审批列表
     */
    @Override
    @GetMapping("/list")
    @RequiresPermissions(Auth.TE_TENANT_APPROVAL_LIST)
    public AjaxResult list(TeTenantApprovalQuery tenantApproval) {
        return super.list(tenantApproval);
    }

    /**
     * 查询租户审批详细
     */
    @Override
    @GetMapping(value = "/{id}")
    @RequiresPermissions(Auth.TE_TENANT_APPROVAL_SINGLE)
    public AjaxResult getInfo(@PathVariable Serializable id) {
        return super.getInfo(id);
    }


    /**
     * 租户审批修改状态
     */
    @Override
    @PutMapping("/status")
    @RequiresPermissions(value = {Auth.TE_TENANT_APPROVAL_ES}, logical = Logical.OR)
    @Log(title = "租户审批管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult editStatus(@RequestBody TeTenantApprovalDto tenantApproval) {
        return super.editStatus(tenantApproval);
    }

    /**
     * 获取租户审批选择框列表
     */
    @Override
    @GetMapping("/option")
    public AjaxResult option() {
        return super.option();
    }

    /**
     * 租户申请
     * @param dto 申请信息
     * @return 成功/失败
     */
    @PostMapping("/register")
    public AjaxResult register(@RequestBody TeTenantApprovalDto dto){
        return tenantApprovalService.registerJudge(dto);
    }

    /**
     * 驳回租户申请
     * @param dto 驳回原因
     * @return 成功/失败
     */
    @PutMapping("/overrule")
    @RequiresPermissions(value = {Auth.TE_TENANT_APPROVAL_ES}, logical = Logical.OR)
    public AjaxResult overrule(@RequestBody TeTenantApprovalDto dto){
        return AjaxResult.success(tenantApprovalService.overrule(dto));
    }


    /**
     * 审批通过
     * @param tenantApproval 审批dto
     * @return 审批账户uuid
     */
    @PutMapping("/pass")
    @RequiresPermissions(value = {Auth.TE_TENANT_APPROVAL_ES}, logical = Logical.OR)
    @Log(title = "租户审批管理", businessType = BusinessType.UPDATE_STATUS)
    public AjaxResult Approved(@RequestBody TeTenantApprovalDto tenantApproval) {
        UUID random = UUID.randomUUID();
        String uuid =random.toString().replaceAll("-", "");

        //uuid存到redis
        HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
        hashOperations.put(CacheConstants.CacheType.SYS_APPROVAL_TENANT_KEY.getCode(),uuid,TenantConstants.ApprovalType.ING.getCode());

        tenantApprovalService.approved(tenantApproval,uuid);
        return AjaxResult.success(uuid);
    }

    /**
     * 获取审批通过状态
     * @param uuid 审批账户uuid
     * @return 审批状态(0 正在审批,1 审批成功,2 审批失败)
     */
    @GetMapping("/poll/{uuid}")
    public String  pollRedisHash(@PathVariable String uuid) {
        return tenantApprovalService.pollRedisHash(uuid);
    }

}
