package com.heju.tenant.tenant.service.impl;

import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.heju.common.core.constant.basic.SecurityConstants;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.core.web.result.R;
import com.heju.system.api.authority.feign.RemoteRoleService;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.api.organize.feign.RemoteUserService;
import com.heju.tenant.api.tenant.domain.dto.BaseUserDto;
import com.heju.tenant.api.tenant.domain.dto.TeTenantDto;
import com.heju.tenant.api.tenant.domain.dto.UserTenantMergeDto;
import com.heju.tenant.tenant.manager.ITeInviteRegisterManager;
import com.heju.tenant.tenant.mapper.TeInviteRegisterMapper;
import com.heju.tenant.tenant.service.ITeInviteRegisterService;
import com.heju.tenant.tenant.service.ITeTenantService;
import com.heju.tenant.tenant.utils.DefaultMenu;
import lombok.Getter;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

@Service
public class TeInviteRegisterServiceImpl implements ITeInviteRegisterService {

    @Resource
    private ITeInviteRegisterManager iTeInviteRegisterManager;

    @Resource
    private TeInviteRegisterMapper inviteRegisterMapper;

    @Getter
    @Resource
    private RemoteUserService remoteUserService;

    @Resource
    private RemoteRoleService remoteRoleService;

    @Resource
    private ITeTenantService tenantService;

    @Resource
    private DefaultMenu defaultMenu;


    @Override
    @DSTransactional
    public AjaxResult addUser(String sourceName, SysUserDto user) {
        //根据sourceName获取租户信息
        TeTenantDto tenantDto = tenantService.selectByNick(sourceName);
        //根据openId查询baseUser
        BaseUserDto baseUserByOpenId = getBaseUserByOpenId(user.getOpenId());
        BaseUserDto baseUserByPhone = getBaseUserByPhone(user.getPhone());
        SysUserDto userDto = new SysUserDto();
        try {
            //如果openId能查到 手机号查不到
            if (!Objects.isNull(baseUserByOpenId) && Objects.isNull(baseUserByPhone)) {
                if (baseUserByOpenId.getTelephone() != null && !baseUserByOpenId.getTelephone().equals(user.getPhone())) {
                    return AjaxResult.error("该微信号已绑定过其他手机号");
                } else {
                    String baseUserId = iTeInviteRegisterManager.baseUserBinding(baseUserByOpenId.getId(), user.getOpenId(), user.getPhone());
                    userDto = remoteUserService.addInnerUser(user, tenantDto.getId(), sourceName, SecurityConstants.INNER).getData();
                    UserTenantMergeDto userTenantMergeDto = iTeInviteRegisterManager.userTenantMergeBinding(baseUserId, tenantDto.getId(), userDto.getId());
                    addUserTenantMerge(userTenantMergeDto);
                }
            }
            //如果手机号能查到 openId查不到
            if (Objects.isNull(baseUserByOpenId) && !Objects.isNull(baseUserByPhone)) {
                if (baseUserByPhone.getOpenId() != null && !baseUserByPhone.getOpenId().equals(user.getOpenId())) {
                    return AjaxResult.error("该手机号已绑定过其他微信号");
                } else {
                    String baseUserId = iTeInviteRegisterManager.baseUserBinding(baseUserByPhone.getId(), user.getOpenId(), user.getPhone());
                    userDto = remoteUserService.addInnerUser(user, tenantDto.getId(), sourceName, SecurityConstants.INNER).getData();
                    UserTenantMergeDto userTenantMergeDto = iTeInviteRegisterManager.userTenantMergeBinding(baseUserId, tenantDto.getId(), userDto.getId());
                    addUserTenantMerge(userTenantMergeDto);
                }
            }
            //都查不到
            if (Objects.isNull(baseUserByOpenId) && Objects.isNull(baseUserByPhone)) {
                String baseUserId = iTeInviteRegisterManager.baseUserBinding(null, user.getOpenId(), user.getPhone());
                userDto = remoteUserService.addInnerUser(user, tenantDto.getId(), sourceName, SecurityConstants.INNER).getData();
                UserTenantMergeDto userTenantMergeDto = iTeInviteRegisterManager.userTenantMergeBinding(baseUserId, tenantDto.getId(), userDto.getId());
                addUserTenantMerge(userTenantMergeDto);
            }
            //都能查到
            if (!Objects.isNull(baseUserByOpenId) && !Objects.isNull(baseUserByPhone)) {
                userDto = remoteUserService.addInnerUser(user, tenantDto.getId(), sourceName, SecurityConstants.INNER).getData();
                UserTenantMergeDto userTenantMergeDto = iTeInviteRegisterManager.userTenantMergeBinding(baseUserByOpenId.getId(), tenantDto.getId(), userDto.getId());
                addUserTenantMerge(userTenantMergeDto);
            }
            //查询角色id
            R<SysUserDto> roleIdsByRoleKey = remoteRoleService.getRoleIdsByRoleKey(defaultMenu.getRoleKey(), SecurityConstants.INNER, sourceName, tenantDto.getId());
            SysUserDto data = roleIdsByRoleKey.getData();
            userDto.setRoleIds(data.getRoleIds());
            remoteUserService.innerEditRoleAuth(userDto, SecurityConstants.INNER, tenantDto.getId(), sourceName);
        } catch (Exception e) {
            return AjaxResult.error("添加用户失败");
        }
        return AjaxResult.success("添加用户成功！");
    }

    /**
     * 根据手机号查询BaseUser
     */
    @Override
    public BaseUserDto getBaseUserByPhone(String phone) {
        return inviteRegisterMapper.getBaseUserByPhone(phone);
    }

    /**
     * 根据openId查询baseUser
     */
    @Override
    public BaseUserDto getBaseUserByOpenId(String openId) {
        return inviteRegisterMapper.getBaseUserByOpenId(openId);
    }

    /**
     * 添加用户与租户绑定数据
     */
    @Override
    public int addUserTenantMerge(UserTenantMergeDto dto) {
        return inviteRegisterMapper.setUserTenantMerge(dto);
    }

    @Override
    public AjaxResult updateBaseUser(BaseUserDto dto) {
        BaseUserDto baseUserByOpenId = getBaseUserByOpenId(dto.getOpenId());
        if (!Objects.isNull(baseUserByOpenId) && !Objects.equals(baseUserByOpenId.getTelephone(), dto.getTelephone())) {
            return AjaxResult.error("该微信已绑定其他账号");
        }
        BaseUserDto temp = new BaseUserDto();
        temp.setTelephone(dto.getTelephone());
        BaseUserDto baseUserByCondition = getBaseUserByCondition(temp);
        if (dto.getOpenId() == null) {
            int end = inviteRegisterMapper.wechatUnbinding(dto.getId());
            return end > 0 ? AjaxResult.success("绑定微信成功") : AjaxResult.error("绑定失败");
        }else {
            dto.setId(baseUserByCondition.getId());
            int end = inviteRegisterMapper.updateBaseUser(dto);
            return end > 0 ? AjaxResult.success("绑定微信成功") : AjaxResult.error("绑定失败");
        }
    }

    /**
     * 根据手机号/openId查询BaseUser
     *
     * @param dto BaseUserDto
     * @return 查询到的BaseUser
     */
    @Override
    public BaseUserDto getBaseUserByCondition(BaseUserDto dto) {
        if (dto.getOpenId() != null) {
            return inviteRegisterMapper.getBaseUserByOpenId(dto.getOpenId());
        }
        if (dto.getTelephone() != null) {
            return inviteRegisterMapper.getBaseUserByPhone(dto.getTelephone());
        }
        return null;
    }

}
