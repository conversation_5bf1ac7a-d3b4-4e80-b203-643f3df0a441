package com.heju.tenant.tenant.service.impl;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.heju.common.cache.constant.CacheConstants;
import com.heju.common.core.constant.basic.OperateConstants;
import com.heju.common.core.constant.basic.SecurityConstants;
import com.heju.common.core.constant.basic.TenantConstants;
import com.heju.common.core.utils.core.NumberUtil;
import com.heju.common.core.utils.core.ObjectUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.api.authority.domain.dto.SysRoleDto;
import com.heju.system.api.authority.domain.dto.SysRoleGroupDto;
import com.heju.system.api.authority.feign.RemoteRoleService;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.tenant.api.source.domain.dto.TeSourceDto;
import com.heju.tenant.api.tenant.domain.dto.TeStrategyDto;
import com.heju.tenant.api.tenant.domain.dto.TeTenantApprovalDto;
import com.heju.tenant.api.tenant.domain.dto.TeTenantDto;
import com.heju.tenant.api.tenant.domain.query.TeTenantApprovalQuery;
import com.heju.tenant.source.service.impl.TeSourceServiceImpl;
import com.heju.tenant.tenant.domain.model.TeTenantRegister;
import com.heju.tenant.tenant.manager.ITeTenantApprovalManager;
import com.heju.tenant.tenant.mapper.TeTenantApprovalMapper;
import com.heju.tenant.tenant.service.ITeTenantApprovalService;
import com.heju.tenant.tenant.utils.MySQLDatabaseUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * 租户审批管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class TeTenantApprovalServiceImpl extends BaseServiceImpl<TeTenantApprovalQuery, TeTenantApprovalDto, ITeTenantApprovalManager> implements ITeTenantApprovalService {

    @Autowired
    private TeStrategyServiceImpl strategyService;

    @Autowired
    private TeSourceServiceImpl sourceService;

    @Autowired
    private TeTenantServiceImpl tenantService;

    @Autowired
    private TeTenantApprovalMapper approvalMapper;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private ITeTenantApprovalManager approvalManager;

    @Autowired
    private MySQLDatabaseUtil mySQLDatabaseUtil;

    @Autowired
    private RemoteRoleService remoteRoleService;


    /**
     * 查询租户审批对象列表 | 数据权限
     *
     * @param tenantApproval 租户审批对象
     * @return 租户审批对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"TeTenantApprovalMapper"})
    public List<TeTenantApprovalDto> selectListScope(TeTenantApprovalQuery tenantApproval) {
        return baseManager.selectList(tenantApproval);
    }

    /**
     * 添加租户审核信息
     *
     * @param dto 数据对象
     * @return 结果
     */
    @Override
    @DSTransactional
    public int insert(TeTenantApprovalDto dto) {
        startHandle(OperateConstants.ServiceType.ADD, null, dto);
        int row = baseManager.insert(dto);
        endHandle(OperateConstants.ServiceType.ADD, row, null, dto);
        return row;
    }

    /**
     * 审批通过
     * @param dto 数据对象
     */
    @DSTransactional
    @Override
    @Async
    public void approved(TeTenantApprovalDto dto,String uuid) {
        TeTenantApprovalDto originDto = selectById(dto.getId());
        startHandle(OperateConstants.ServiceType.EDIT, originDto, dto);
        //创建数据库/生成表
        String databaseName = mySQLDatabaseUtil.createDatabase(uuid);
        try {
            //添加数据源
            TeSourceDto teSourceDto = approvalManager.wrapSource(dto,uuid);
            sourceService.insert(teSourceDto); //添加数据源
            sourceService.refreshCache(); //刷新
            //添加策略源
            TeSourceDto tempSource = sourceService.selectBySlave(teSourceDto.getSlave());
            TeStrategyDto strategyDto = approvalManager.wrapStrategy(tempSource,dto);
            strategyService.insert(strategyDto); //添加策略源
            strategyService.refreshCache(); //刷新
            //获取策略源对象
            TeStrategyDto tempStrategy = strategyService.selectBySourceSlave(strategyDto.getSourceSlave());
            //封装租户数据
            TeTenantDto teTenantDto = approvalManager.wrapTenant(tempStrategy,teSourceDto,dto);
            //封装管理员账户数据
            SysUserDto user = approvalManager.wrapUser(dto);
            //添加租户与管理员账户数据与权限范围
            TeTenantRegister tenantRegister = new TeTenantRegister();
            tenantRegister.setTenant(teTenantDto);
            tenantRegister.setUser(user);
            tenantRegister.setAuthIds(dto.getAuthIds());
            //添加获取返回租户中的用户id
            Long userId = tenantService.insertTenantAndUser(tenantRegister);
            //获取租户id
            TeTenantDto tempTenant = tenantService.selectByCondition(teTenantDto);
            Long tenantId = tempTenant.getId();
            //创建基础角色组
            SysRoleGroupDto roleGroupDto = approvalManager.wrapBaseRoleGroup(userId);
            remoteRoleService.addInner(roleGroupDto,SecurityConstants.INNER,uuid);
            //添加默认普通员工角色
            SysRoleDto defaultRole = approvalManager.wrapDefaultEmployee(tenantId,roleGroupDto.getId());
            remoteRoleService.addInner(defaultRole, SecurityConstants.INNER,uuid,tenantId);
            String baseUserId;
            if (dto.getPhone() == null){
                baseUserId = approvalMapper.selectBaseUserByOpenId(dto.getOpenid());
            }else {
                baseUserId = approvalMapper.selectBaseUserByPhone(dto.getPhone());
            }
            if (baseUserId == null){
                UUID randomUUID = UUID.randomUUID();
                baseUserId=randomUUID.toString().replaceAll("-", "");
                //将用户信息存入用户表中 base_user
                approvalMapper.addBaseUser(baseUserId,dto.getPhone(),dto.getOpenid());
            }
            //存入用户租户关系信息
            approvalMapper.addUserTenantMerge(baseUserId,tenantId,userId);
            int row = baseManager.update(dto);
            endHandle(OperateConstants.ServiceType.EDIT, row, originDto, dto);
        } catch (Exception e) {
            //修改redis里面的状态为失败
            HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
            hashOperations.put(CacheConstants.CacheType.SYS_APPROVAL_TENANT_KEY.getCode(),uuid, TenantConstants.ApprovalType.FAIL.getCode());
            //删除数据库
            mySQLDatabaseUtil.deleteDateBase(databaseName);
            throw new RuntimeException(e);
        }

        //修改redis里面的状态为完成
        HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
        hashOperations.put(CacheConstants.CacheType.SYS_APPROVAL_TENANT_KEY.getCode(),uuid, TenantConstants.ApprovalType.OK.getCode());
    }

    /**
     * 驳回
     * @param dto 状态(已驳回) 驳回原因
     * @return 成功/失败
     */
    @Override
    public int overrule(TeTenantApprovalDto dto) {
        TeTenantApprovalDto originDto = selectById(dto.getId());
        startHandle(OperateConstants.ServiceType.EDIT, originDto, dto);
        originDto.setStatus(dto.getStatus());
        originDto.setReason(dto.getReason());
        int row = baseManager.update(originDto);
        endHandle(OperateConstants.ServiceType.EDIT, row, originDto, dto);
        return row;
    }


    /**
     * 获取审批通过状态
     * @param uuid 审批账户uuid
     * @return 审批状态(0 正在审批,1 审批成功,2 审批失败)
     */
    public String pollRedisHash(String uuid) {
        HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
        String end =  hashOperations.get(CacheConstants.CacheType.SYS_APPROVAL_TENANT_KEY.getCode(),uuid);
        if (end.equals(TenantConstants.ApprovalType.OK.getCode()) | end.equals(TenantConstants.ApprovalType.FAIL.getCode())){
            hashOperations.delete(CacheConstants.CacheType.SYS_APPROVAL_TENANT_KEY.getCode(),uuid);
        }
        return end;
    }

    @Override
    public AjaxResult registerJudge(TeTenantApprovalDto dto) {
        //判断用户是微信注册还是手机号注册
        if (Objects.isNull(dto.getOpenid())){
            Object code =  redisService.getCacheMapValue(CacheConstants.CacheType.SYS_TELEPHONE_CODE_KEY.getCode(),dto.getPhone());
            if (ObjectUtil.isNull(code)) {
                return AjaxResult.warn("验证码已过期,请重新发送!");
            }
            StringBuilder cacheCode = new StringBuilder(code.toString());
            while (cacheCode.length()< NumberUtil.Six){
                cacheCode.insert(NumberUtil.Zero, "0");
            }

            if (!cacheCode.toString().equals(dto.getCode())) {
                return AjaxResult.warn("验证码错误!");
            }
        }
        int row = baseManager.insert(dto);
        endHandle(OperateConstants.ServiceType.ADD, row, null, dto);
        return AjaxResult.success(row);
    }
}
