package com.heju.tenant;

import com.heju.common.security.annotation.EnableCustomConfig;
import com.heju.common.security.annotation.EnableRyFeignClients;
import com.heju.common.swagger.annotation.EnableCustomSwagger;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

/**
 * 租户模块
 *
 * <AUTHOR>
 */
@EnableCustomConfig
@EnableCustomSwagger
@EnableRyFeignClients
@SpringBootApplication
@EnableAsync
public class HeJuTenantApplication {
    public static void main(String[] args) {
        SpringApplication.run(HeJuTenantApplication.class, args);
        System.out.println("(♥◠‿◠)ﾉﾞ  租户管理模块启动成功   ლ(´ڡ`ლ)ﾞ  \n" +
                " ('-. .-.          \n" +
                "( OO )  /          \n" +
                ",--. ,--.     ,--. \n" +
                "|  | |  | .-')| ,| \n" +
                "|   .|  |( OO |(_| \n" +
                "|       || `-'|  | \n" +
                "|  .-.  |,--. |  | \n" +
                "|  | |  ||  '-'  / \n" +
                "`--' `--' `-----'  \n");
    }
}