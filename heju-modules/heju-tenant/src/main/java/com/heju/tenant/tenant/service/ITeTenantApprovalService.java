package com.heju.tenant.tenant.service;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.web.entity.service.IBaseService;
import com.heju.tenant.api.tenant.domain.dto.TeTenantApprovalDto;
import com.heju.tenant.api.tenant.domain.query.TeTenantApprovalQuery;

/**
 * 租户审批管理 服务层
 *
 * <AUTHOR>
 */
public interface ITeTenantApprovalService extends IBaseService<TeTenantApprovalQuery, TeTenantApprovalDto> {

    /**
     * 审批通过
     * @param tenantApproval 状态(已审批)
     */
    void approved(TeTenantApprovalDto tenantApproval,String slave);

    /**
     * 驳回审批
     * @param dto 状态(已驳回) 驳回原因
     * @return 成功/失败
     */

    int overrule(TeTenantApprovalDto dto);

    /**
     * 获取审批状态
     * @param uuid 审批账户uuid
     * @return 审批状态(0 正在审批,1 审批成功,2 审批失败)
     */

    String pollRedisHash(String uuid);

    AjaxResult registerJudge(TeTenantApprovalDto dto);
}