package com.heju.system.file.domain.po;

import com.baomidou.mybatisplus.annotation.TableName;
import com.heju.common.core.annotation.Excel;
import com.heju.common.core.web.tenant.base.TTreeEntity;
import com.heju.system.file.domain.dto.SysFilePositionDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;


/**
 * 文件存储位置 持久化对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("sys_file_position")
public class SysFilePositionPo extends TTreeEntity<SysFilePositionDto> {

    @Serial
    private static final long serialVersionUID = 1L;

    /** 存储位置编码 */
    @Excel(name = "存储位置编码")
    protected String code;

    /** 备注 */
    @Excel(name = "备注")
    protected String remark;

}
