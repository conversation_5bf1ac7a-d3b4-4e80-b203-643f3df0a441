package com.heju.system.approval.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import com.heju.system.approval.domain.dto.SysApprovalCustomerinfoDto;
import com.heju.system.approval.domain.query.SysApprovalCustomerinfoQuery;
import com.heju.system.approval.manager.ISysApprovalCustomerinfoManager;
import com.heju.system.approval.service.ISysApprovalCustomerinfoService;
import com.heju.system.forms.field.domain.dto.SysFieldDto;
import com.heju.system.forms.field.manager.ISysFieldManager;
import com.heju.system.forms.optionValue.domain.po.SysOptionValuePo;
import com.heju.system.forms.optionValue.mapper.SysOptionValueMapper;
import com.heju.system.forms.sheet.domain.dto.SysSheetDto;
import com.heju.system.forms.sheet.manager.ISysSheetManager;
import com.heju.system.organize.manager.ISysUserManager;
import com.heju.system.utils.FieldTypeConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 客户信息审核管理 服务层处理
 *
 * <AUTHOR>
 */
@Slf4j
@Service
public class SysApprovalCustomerinfoServiceImpl extends BaseServiceImpl<SysApprovalCustomerinfoQuery, SysApprovalCustomerinfoDto, ISysApprovalCustomerinfoManager> implements ISysApprovalCustomerinfoService {

    @Resource
    private ISysSheetManager sheetManager;

    @Resource
    private ISysFieldManager fieldManager;

    @Resource
    private ISysUserManager userManager;

    @Resource
    private SysOptionValueMapper optionValueMapper;

    /**
     * 查询客户信息审核对象列表 | 数据权限
     *
     * @return 客户信息审核对象集合
     */

    @Override
    public List<SysApprovalCustomerinfoDto> selectListScope(SysApprovalCustomerinfoQuery approvalCustomerinfo) {
        // （1）sql 查
        //  return sysApprovalCustomerinfoManager.selectByQuery(approvalCustomerinfo);

        // （2）业务查
        // 1. 取出 sys_approval_Customerinfo 表所有数据
        List<SysApprovalCustomerinfoDto> list = baseManager.selectByQuery(approvalCustomerinfo);

        // 2. 取出所有 fieldId, sheetId, createBy, 并去重
        Set<Long> fieldIds = new HashSet<>();
        Set<Long> sheetIds = new HashSet<>();
        Set<Long> createByIds = new HashSet<>();
        list.forEach(dto -> {
            if (dto.getFieldId() != null) { fieldIds.add(dto.getFieldId()); }
            if (dto.getSheetId() != null) { sheetIds.add(dto.getSheetId()); }
            if (dto.getCreateBy() != null) { createByIds.add(dto.getCreateBy()); }
        });

        // 避免空集合查询，提高效率
        List<SysFieldDto> sysFieldDtos = fieldIds.isEmpty() ? Collections.emptyList() : fieldManager.selectListByIds(fieldIds);
        List<SysSheetDto> sysSheetDtos = sheetIds.isEmpty() ? Collections.emptyList() : sheetManager.selectListByIds(sheetIds);
        List<SysUserDto> sysUserDtos = createByIds.isEmpty() ? Collections.emptyList() : userManager.selectListByIds(createByIds);

        Map<Long, String> fieldMap = sysFieldDtos.stream().
                collect(Collectors.toMap(SysFieldDto::getId, SysFieldDto::getName, (v1, v2) -> v1));
        Map<Long, String> sheetMap = sysSheetDtos.stream()
                .collect(Collectors.toMap(SysSheetDto::getId, SysSheetDto::getName, (v1, v2) -> v1));
        Map<Long, String> sheetApiMap = sysSheetDtos.stream()
                .collect(Collectors.toMap(SysSheetDto::getId, SysSheetDto::getApiName, (v1, v2) -> v1));
        Map<Long, String> userMap = sysUserDtos.stream()
                .collect(Collectors.toMap(SysUserDto::getId, SysUserDto::getNickName, (v1, v2) -> v1));

        // 1. 收集所有需要查询的业务ID，并按表名进行分组
        Map<String, Set<Long>> businessIdsByApiName = new HashMap<>();
        for (SysApprovalCustomerinfoDto dto : list) {
            if (dto.getAssBusinessId() != null && dto.getAssSheetName() != null) {
                businessIdsByApiName.computeIfAbsent(dto.getAssSheetName(), k -> new HashSet<>()).add(dto.getAssBusinessId());
            }
        }

        // 2. 批量查询所有 businessName，并存入一个大 Map 中
        Map<Long, String> allBusinessNameMap = new HashMap<>();
        businessIdsByApiName.forEach((apiName, businessIds) -> {
            List<SysApprovalCustomerinfoDto> businessNameList = baseManager.selectBusinessName(apiName, businessIds);
            businessNameList.forEach(businessNameDto -> {
                    allBusinessNameMap.put(businessNameDto.getAssBusinessId(), businessNameDto.getBusinessName());
                }
            );
        });

        list.forEach(sysApprovalCustomerinfoDto -> {
            sysApprovalCustomerinfoDto.setSheetName(sheetMap.get(sysApprovalCustomerinfoDto.getSheetId()));
            sysApprovalCustomerinfoDto.setSheetApiName(sheetApiMap.get(sysApprovalCustomerinfoDto.getSheetId()));
            sysApprovalCustomerinfoDto.setFieldName(fieldMap.get(sysApprovalCustomerinfoDto.getFieldId()));
            sysApprovalCustomerinfoDto.setCreateName(userMap.get(sysApprovalCustomerinfoDto.getCreateBy()));
            sysApprovalCustomerinfoDto.setBusinessName(allBusinessNameMap.get(sysApprovalCustomerinfoDto.getAssBusinessId()));
        });
        return list;
    }


    /**
     * 查询客户信息审核详细
     *
     * @param id Id 客户信息审核id
     * @return 客户信息审核对象
     */
    @Override
    public SysApprovalCustomerinfoDto selectById(Serializable id) {
        // 业务查
        // 1. 取出 id下 的sys_approval_Customerinfo 表记录
        SysApprovalCustomerinfoDto dto = baseManager.selectById(id);
        if (dto == null) {
            return null;
        }
        // 2. 查出 fieldName, sheetName, dynamicTableName, createName 并赋值给 dto
        dto.setFieldName(fieldManager.selectById(dto.getFieldId()).getName());
        dto.setSheetName(sheetManager.selectById(dto.getSheetId()).getName());
        dto.setSheetApiName(sheetManager.selectById(dto.getSheetId()).getApiName());
        dto.setCreateName(userManager.selectById(dto.getCreateBy()).getNickName());
        SysApprovalCustomerinfoDto approvalCustomerinfoDto = baseManager.selectBusinessNameById(dto.getAssSheetName(), dto.getAssBusinessId());
        if (approvalCustomerinfoDto != null) {
            dto.setBusinessName(approvalCustomerinfoDto.getBusinessName());
        }
        return dto;
    }

    /**
     * 通过客户信息审核
     * @param approvalCustomerinfo 客户信息审核对象
     * @return int 影响行数
     */
    @Override
    public int pass(SysApprovalCustomerinfoDto approvalCustomerinfo) {
        return baseManager.updatePass(approvalCustomerinfo);
    }

    /**
     * 驳回客户信息审核
     * @param approvalCustomerinfo 客户信息审核对象
     * @return SysApprovalCustomerinfoDto
     */
    @Override
    public int reject(SysApprovalCustomerinfoDto approvalCustomerinfo) {
        return baseManager.updateReject(approvalCustomerinfo);
    }

    @Override
    public int insert(SysApprovalCustomerinfoDto approvalCustomerinfo) {
        approvalCustomerinfo.setSheetId(sheetManager.selectOne(approvalCustomerinfo.getSheetApiName()).getId());
        SysFieldDto fieldDto = fieldManager.selectById(approvalCustomerinfo.getFieldId());
        // 引用类型 中的 单选 多选 级联
        if (Objects.equals(fieldDto.getFieldType(), FieldTypeConstants.QUOTE)) {
            // 查看原始字段信息
            SysFieldDto originalField = fieldManager.selectById(fieldDto.getQuoteSheetFieldId());
            if (originalField.getFieldType().equals(FieldTypeConstants.SELECT_SINGLE) ||
                    originalField.getFieldType().equals(FieldTypeConstants.SELECT_MULTI) ||
                    originalField.getFieldType().equals(FieldTypeConstants.CASCADE)) {
                // 使用通用的 convertAndSetNames 方法来处理被引用字段的数据
                convertAndSetNames(approvalCustomerinfo, originalField);
            }
        } else if (fieldDto.getFieldType().equals(FieldTypeConstants.SELECT_SINGLE) ||
                fieldDto.getFieldType().equals(FieldTypeConstants.SELECT_MULTI) ||
                fieldDto.getFieldType().equals(FieldTypeConstants.CASCADE)){
            convertAndSetNames(approvalCustomerinfo, fieldDto);
        }
        return baseManager.insertApproval(approvalCustomerinfo);
    }

    /**
     * 通用方法，用于根据ID列表、数据源和分隔符来转换并拼接名称
     */
    private void convertAndSetNames(SysApprovalCustomerinfoDto approvalCustomerinfo, SysFieldDto fieldDto) {
        String beforeIds = approvalCustomerinfo.getBeforeUpdateId();
        String afterIds = approvalCustomerinfo.getAfterUpdateId();
        if ((beforeIds == null || beforeIds.isEmpty()) && (afterIds == null || afterIds.isEmpty())) return; // 都空，返回
        Set<Long> idSet = new HashSet<>();
        String separator;
        // 根据字段类型确定分隔符和ID集合
        if (Objects.equals(fieldDto.getFieldType(), FieldTypeConstants.CASCADE)) {
            separator = "-";
            collectIds(beforeIds, separator, idSet);
            collectIds(afterIds, separator, idSet);
        } else {
            separator = ",";
        }
        collectIds(beforeIds, separator, idSet);
        collectIds(afterIds, separator, idSet);
        if (idSet.isEmpty()) return; // 判空
        // 根据选项类型获取数据源（Map）
        Map<Long, String> nameMap = new HashMap<>();;
        if (fieldDto.getOptionType() != null && fieldDto.getOptionType().equals(3)) {
            // 业务
            List<SysSheetDto> sheetDtoList = sheetManager.selectListByIds(idSet);
            for (SysSheetDto sheetDto : sheetDtoList) {
                // list不会传入 optionFieldList，使用默认选择字段：id 和 name
                String name = sheetDto.getApiName().equals("sys_user") ? "`nick_name`" : "`name`";
                List<Map<String, Object>> option = baseManager.businessOption(sheetDto.getApiName(), name);
                // 将 option 转 map, id -> name
                Map<Long, String> currentMap = option.stream().filter(m -> m.containsKey("id"))
                        .collect(Collectors.toMap(
                                m -> ((Number) m.get("id")).longValue(),
                                m -> m.get("name").toString()));
                nameMap.putAll(currentMap);
            }
        } else {
            // 非业务
            LambdaQueryWrapper<SysOptionValuePo> wrapper = new LambdaQueryWrapper<>();
            wrapper.in(SysOptionValuePo::getId, idSet);
            List<SysOptionValuePo> optionValueList = optionValueMapper.selectList(wrapper);
            nameMap = optionValueList.stream().collect(Collectors.toMap(SysOptionValuePo::getId, SysOptionValuePo::getName, (v1, v2) -> v1));
        }
        String[] beforeIdArray = beforeIds == null || beforeIds.isEmpty() ? new String[0] : beforeIds.split(separator);
        String[] afterIdArray = afterIds == null || afterIds.isEmpty() ? new String[0] : afterIds.split(separator);
        approvalCustomerinfo.setBeforeUpdate(joinNames(beforeIdArray, nameMap, separator));
        approvalCustomerinfo.setAfterUpdate(joinNames(afterIdArray, nameMap, separator));
    }

    /**
     * 将 ids 拆分并添加到 targetSet 中
     */
    private void collectIds(String ids, String delimiter, Set<Long> targetSet) {
        if (ids == null || ids.isEmpty()) return;
        Arrays.stream(ids.split(delimiter))
                .filter(id -> !id.isEmpty())
                .map(Long::valueOf)
                .forEach(targetSet::add);
    }

    /**
     * 将 id 数组中的值替换为对应的 name
     */
    private String joinNames(String[] ids, Map<Long, String> valueMap, String delimiter) {
        return Arrays.stream(ids)
                .filter(id -> id != null && !id.isEmpty())
                .map(Long::valueOf)
                .map(id -> valueMap.getOrDefault(id, ""))
                .collect(Collectors.joining(delimiter));
    }
}