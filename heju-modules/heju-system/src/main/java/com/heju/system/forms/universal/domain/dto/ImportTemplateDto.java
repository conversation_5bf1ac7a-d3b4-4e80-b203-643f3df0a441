package com.heju.system.forms.universal.domain.dto;

import com.heju.system.forms.universal.domain.po.ImportFieldPo;
import com.heju.system.forms.universal.domain.po.ImportTemplatePo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class ImportTemplateDto extends ImportTemplatePo {
    @Serial
    private static final long serialVersionUID = 1L;

    private ImportTemplatePo importTemplatePo;

    private List<ImportFieldPo> fields;

}
