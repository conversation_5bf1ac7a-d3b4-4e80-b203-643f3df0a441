package com.heju.system.forms.field.domain.model;

import com.heju.common.core.web.entity.model.BaseConverter;
import com.heju.system.forms.field.domain.dto.SysFieldDto;
import com.heju.system.forms.field.domain.po.SysFieldPo;
import com.heju.system.forms.field.domain.query.SysFieldQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 字段管理 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysFieldConverter extends BaseConverter<SysFieldQuery, SysFieldDto, SysFieldPo> {
}
