package com.heju.system.file.manager.impl;

import com.heju.common.web.entity.manager.impl.TreeManagerImpl;
import com.heju.system.file.domain.dto.SysFileClassifyDto;
import com.heju.system.file.domain.model.SysFileClassifyConverter;
import com.heju.system.file.domain.po.SysFileClassifyPo;
import com.heju.system.file.domain.query.SysFileClassifyQuery;
import com.heju.system.file.manager.ISysFileClassifyManager;
import com.heju.system.file.mapper.SysFileClassifyMapper;
import org.springframework.stereotype.Component;

/**
 * 文件分类管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysFileClassifyManager extends TreeManagerImpl<SysFileClassifyQuery, SysFileClassifyDto, SysFileClassifyPo, SysFileClassifyMapper, SysFileClassifyConverter> implements ISysFileClassifyManager {




    /*@Override
    public List<SysAuthTree> selectRoleGroupAuthScope(Long roleGroupId) {

        List<SysMenuDto> menus = menuManager.selectRoleList(roleGroupId);
        if (menus.isEmpty()){
            return new ArrayList<>();
        }
        return menus.stream().map(SysAuthTree::new).collect(Collectors.toList());
    }*/

}
