package com.heju.system.approval.mapper;

import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.approval.domain.dto.SysApprovalCustomerinfoDto;
import com.heju.system.approval.domain.po.SysApprovalCustomerinfoPo;
import com.heju.system.approval.domain.query.SysApprovalCustomerinfoQuery;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 客户信息审核管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysApprovalCustomerinfoMapper extends BaseMapper<SysApprovalCustomerinfoQuery, SysApprovalCustomerinfoDto, SysApprovalCustomerinfoPo> {

    // 客户信息审核列表 查找 业务表的 businessName
    @Select("<script>" +
            " select `name` AS businessName, `id` AS assBusinessId from ${apiName} where id in " +
            "<foreach collection='businessIds' item='item' index='index' open='(' separator=',' close=')'>" +
            "#{item}" +
            "</foreach> " +
            " </script>")
    List<SysApprovalCustomerinfoDto> selectBusinessName(@Param("apiName") String apiName, @Param("businessIds") Set<Long> businessIds);

    // 客户信息审核详细
    @Select("<script>" +
            "select `name` AS businessName, `id` AS assBusinessId from ${apiName} where id = #{businessId}" +
            "</script>")
    SysApprovalCustomerinfoDto selectBusinessNameById(@Param("apiName") String apiName, @Param("businessId") Long businessId);

    // 业务表 option
    @Select("select `id`, ${name} as name from ${sheetApiName} where del_flag=0")
    List<Map<String,Object>> businessOption(@Param("sheetApiName") String sheetApiName, @Param("name") String name);

    // 条件查询
//    List<SysApprovalCustomerinfoDto> selectByQuery(@Param("query") SysApprovalCustomerinfoQuery query);
}