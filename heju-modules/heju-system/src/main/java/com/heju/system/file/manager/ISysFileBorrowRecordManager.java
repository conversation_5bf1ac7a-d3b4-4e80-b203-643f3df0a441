package com.heju.system.file.manager;

import com.heju.common.web.entity.manager.IBaseManager;
import com.heju.system.file.domain.dto.SysFileBorrowRecordDto;
import com.heju.system.file.domain.query.SysFileBorrowRecordQuery;

import java.util.List;

/**
 * 文件借阅记录管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysFileBorrowRecordManager extends IBaseManager<SysFileBorrowRecordQuery, SysFileBorrowRecordDto> {

    /**
     * 查列表
     * @param query 数据查询对象
     * @return
     */
    List<SysFileBorrowRecordDto> selectList(SysFileBorrowRecordQuery query);

    /**
     * 查借阅记录列表
     * @param query 数据查询对象
     * @return
     */
    List<SysFileBorrowRecordDto> selectOverTimeList(SysFileBorrowRecordQuery query);
}
