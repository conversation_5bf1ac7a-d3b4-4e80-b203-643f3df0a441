package com.heju.system.file.mapper;

import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.datasource.annotation.Master;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.file.domain.dto.SysFileRoleMergeDto;
import com.heju.system.file.domain.po.SysFileRoleMergePo;
import com.heju.system.file.domain.query.SysFileRoleMergeQuery;

/**
 * 文件角色权限关联管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysFileRoleMergeMapper extends BaseMapper<SysFileRoleMergeQuery, SysFileRoleMergeDto, SysFileRoleMergePo> {
}
