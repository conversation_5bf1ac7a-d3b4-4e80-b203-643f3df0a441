package com.heju.system.utils;


import java.util.*;
import java.util.stream.Collectors;

public class PageAndSortUtil {

    /**
     * 对嵌套结构进行排序分页，返回分页对象
     *
     * @param data       原始数据（List<Map<String, Map<String, Object>>>）
     * @param sortField  排序字段，例如："sys_company.name"
     * @param ascending  是否升序（true 为升序，false 为降序）
     * @param pageNum    当前页码，从 1 开始
     * @param pageSize   每页条数
     * @return PageResult 包含分页数据和总条数
     */
    public static PageUniversalResult<Map<String, Map<String, Object>>> paginateAndSortWithPageInfo(
            List<Map<String, Map<String, Object>>> data,
            String sortField,
            boolean ascending,
            int pageNum,
            int pageSize
    ) {
        // 计算总条数
        long total = data.size();

        // 构造排序器
        Comparator<Map<String, Map<String, Object>>> comparator = Comparator.comparing(
                item -> {
                    Object value = getFieldValue(item, sortField);
                    return value instanceof Comparable ? (Comparable) value : null;
                },
                Comparator.nullsLast(Comparator.naturalOrder())
        );
        if (!ascending) {
            comparator = comparator.reversed();
        }

        // 排序 + 分页
        List<Map<String, Map<String, Object>>> pagedList = data.stream()
                .sorted(comparator)
                .skip((long) (pageNum - 1) * pageSize)
                .limit(pageSize)
                .collect(Collectors.toList());

        return new PageUniversalResult<>(total, pageNum, pageSize, pagedList);
    }

    /**
     * 从嵌套结构中获取字段值，例如 "sys_company.name"
     */
    private static Object getFieldValue(Map<String, Map<String, Object>> record, String sortField) {
        String[] parts = sortField.split("\\.");
        if (parts.length != 2) return null;
        String tableName = parts[0];
        String fieldName = parts[1];
        Map<String, Object> tableData = record.get(tableName);
        if (tableData == null) return null;
        return tableData.get(fieldName);
    }
}
