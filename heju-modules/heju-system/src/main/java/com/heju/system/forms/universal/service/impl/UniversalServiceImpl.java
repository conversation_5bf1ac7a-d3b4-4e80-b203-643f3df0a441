package com.heju.system.forms.universal.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.heju.common.core.constant.basic.SqlConstants;
import com.heju.common.core.utils.core.NumberUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.security.utils.SecurityUtils;
import com.heju.system.entity.domain.dto.*;
import com.heju.system.entity.domain.po.SysEntity;
import com.heju.system.entity.domain.po.SysEntityPo;
import com.heju.system.entity.mapper.SysEntityMapper;
import com.heju.system.forms.cascade.manager.ISysCascadeManager;
import com.heju.system.forms.field.domain.dto.SysFieldDto;
import com.heju.system.forms.field.domain.merge.SysFieldRoleMerge;
import com.heju.system.forms.field.domain.query.SysFieldQuery;
import com.heju.system.forms.field.mapper.merge.SysFieldRoleMergeMapper;
import com.heju.system.forms.field.service.ISysFieldService;
import com.heju.system.forms.option.domain.po.SysOptionPo;
import com.heju.system.forms.option.mapper.SysOptionMapper;
import com.heju.system.forms.optionValue.domain.po.SysOptionValuePo;
import com.heju.system.forms.optionValue.mapper.SysOptionValueMapper;
import com.heju.system.forms.sheet.domain.dto.SysSheetDto;
import com.heju.system.forms.sheet.service.ISysSheetService;
import com.heju.system.forms.universal.domain.query.UniversalQuery;
import com.heju.system.forms.universal.mapper.UniversalMapper;
import com.heju.system.forms.universal.service.UniversalService;
import com.heju.system.utils.CompanyBaseInfoUtil;
import com.heju.system.utils.FieldTypeConstants;
import com.heju.system.utils.RelationTypeConstants;
import com.heju.common.redis.service.RedisService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 级联管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class UniversalServiceImpl  implements UniversalService {

    @Resource
    private ISysFieldService fieldService;

    @Resource
    private ISysSheetService sheetService;

    @Resource
    private UniversalMapper universalMapper;

    @Resource
    private SysOptionValueMapper optionValueMapper;

    @Resource
    private SysFieldRoleMergeMapper fieldRoleMergeMapper;

    @Resource
    private SysOptionMapper optionMapper;

    @Resource
    private SysEntityMapper sysEntityMapper;

    @Resource
    private ISysCascadeManager sysCascadeManager;

    @Resource
    private RedisService redisService;
    @Override
    public AjaxResult list(UniversalQuery query) {
        String sheetApiName = query.getSheetApiName();
        //查询表单id
        SysSheetDto sysSheetDto = sheetService.selectOneByApiName(sheetApiName);
        if(sysSheetDto==null){
            return AjaxResult.error("错误的业务表单");
        }
        Long sheetId=sysSheetDto.getId();
        List<Long> sheetIds=new ArrayList<>();
        sheetIds.add(sheetId);
        List<SysFieldDto> sysFieldDtos = getFieldByRoleIds(sheetIds);
        String searchField=String.join(",",sysFieldDtos.stream().map(SysFieldDto::getApiName).
                filter(Objects::nonNull).map(name -> sheetApiName+"." + name).toList()) ;
        String selectSql="select "+searchField;
        String fromSql=" from "+sheetApiName;
        SysFieldQuery fieldQuery=new SysFieldQuery();
        fieldQuery.setRelationSheetId(sheetId);
        fieldQuery.setRelationType(NumberUtil.Zero);
        List<SysFieldDto> relationFieldList = fieldService.selectList(fieldQuery);
        List<Map<String, Object>> universalList = new ArrayList<>();
        if (relationFieldList != null && !relationFieldList.isEmpty()) {
            Map<SysSheetDto, List<SysFieldDto>> listFieldByQueryGroupBySheet = getListFieldByQueryGroupBySheet(relationFieldList);
            for (SysFieldDto sysFieldDto : relationFieldList) {
                for (SysSheetDto sheetDto : listFieldByQueryGroupBySheet.keySet()) {
                    List<SysFieldDto> fieldRelationList = listFieldByQueryGroupBySheet.get(sheetDto);
                    if(Objects.equals(sheetDto.getId(), sysFieldDto.getSheetId())){
                        String relationApiName = sheetDto.getApiName();
                        String searchRelationField=String.join(",",fieldRelationList.stream().map(SysFieldDto::getApiName).
                                filter(Objects::nonNull).map(name -> relationApiName+"." + name).toList()) ;
                        fromSql=fromSql+" left join "+relationApiName +" on "+sheetApiName+".`id`="+relationApiName+"."+sysFieldDto.getApiName();
                        selectSql=selectSql+","+searchRelationField;
                    }
                }
            }
            List<SysFieldDto> mergedList = listFieldByQueryGroupBySheet.values().stream().flatMap(List::stream).toList();
            sysFieldDtos.addAll(mergedList);
        }
        query.setPage((query.getPage() - 1) * query.getPageSize());
        query.setOrderSort(StrUtil.toUnderlineCase(query.getOrderSort()));
        universalList = universalMapper.selectList(selectSql+fromSql, query);

        //根据字段类型分组
        Map<String, List<SysFieldDto>> groupFieldType = sysFieldDtos.stream()
                .collect(Collectors.groupingBy(SysFieldDto::getFieldType));
        //级联
//        List<SysFieldDto> cascadeList = new ArrayList<>(groupFieldType.get(FieldTypeConstants.CASCADE));
        List<SysFieldDto> cascadeList = new ArrayList<>(
                Optional.ofNullable(groupFieldType.get(FieldTypeConstants.CASCADE))
                        .orElse(Collections.emptyList())
        );
        List<SysFieldDto> selectSingleList = new ArrayList<>();
        List<SysFieldDto> selectMultiList = new ArrayList<>();
        List<SysFieldDto> selectSingleSheetList = new ArrayList<>();
        List<SysFieldDto> selectMultiSheetList = new ArrayList<>();
        //循环单选，区分业务选项和其他类型选项
//        for (SysFieldDto sysFieldDto : groupFieldType.get(FieldTypeConstants.SELECT_SINGLE)) {
//            if(Objects.equals(sysFieldDto.getOptionType(), 3)){
//                selectSingleSheetList.add(sysFieldDto);
//            }else{
//                selectSingleList.add(sysFieldDto);
//            }
//        }
        Optional.ofNullable(groupFieldType.get(FieldTypeConstants.SELECT_SINGLE))
                .orElse(Collections.emptyList())
                .forEach(sysFieldDto -> {
                    if (Objects.equals(sysFieldDto.getOptionType(), 3)) {
                        selectSingleSheetList.add(sysFieldDto);
                    } else {
                        selectSingleList.add(sysFieldDto);
                    }
                });
        //循环多，区分业务选项和其他类型选项
//        for (SysFieldDto sysFieldDto : groupFieldType.get(FieldTypeConstants.SELECT_MULTI)) {
//            if(Objects.equals(sysFieldDto.getOptionType(),3)){
//                selectMultiSheetList.add(sysFieldDto);
//            }else{
//                selectMultiList.add(sysFieldDto);
//            }
//        }
        List<SysFieldDto> multiFIelds = groupFieldType.get(FieldTypeConstants.SELECT_MULTI);
        if (multiFIelds != null) {
            for (SysFieldDto sysFieldDto : multiFIelds) {
                if(Objects.equals(sysFieldDto.getOptionType(),3)){
                    selectMultiSheetList.add(sysFieldDto);
                }else{
                    selectMultiList.add(sysFieldDto);
                }
            }
        }
        Set<Long> optionIdList=new HashSet<>();
        if(selectSingleList.size()>0){
            optionIdList.addAll(selectSingleList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        if(selectMultiList.size()>0){
            optionIdList.addAll(selectMultiList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }

        Set<Long> sheetIdList=new HashSet<>();
        if(selectSingleSheetList.size()>0){
            sheetIdList.addAll(selectSingleSheetList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        if(selectMultiSheetList.size()>0){
            sheetIdList.addAll(selectMultiSheetList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        Map<Long, String> optionValueMap=new HashMap<>();
        Map<Long,Map<Long, String>> sheetValueMap=new HashMap<>();
        if(optionIdList.size()>0){
            LambdaQueryWrapper<SysOptionValuePo> lqw = Wrappers.lambdaQuery();
            lqw.in(SysOptionValuePo::getOptionId,optionIdList);
            List<SysOptionValuePo> sysOptionValuePoList = optionValueMapper.selectList(lqw);
            optionValueMap = sysOptionValuePoList.stream().collect(Collectors.toMap(SysOptionValuePo::getId, SysOptionValuePo::getName, (v1, v2) -> v1));
        }
        if(sheetIdList.size()>0){
            List<SysSheetDto> sheetDtoList = sheetService.selectListByIds(sheetIdList);
            for (SysSheetDto sheetDto : sheetDtoList) {
                // list不会传入 optionFieldList，使用默认选择字段：id 和 name
                String optionSelectSql = "";
                if (sheetDto.getApiName().equals("sys_user")) {
                    optionSelectSql = "`id`,`nick_name`";
                } else {
                    optionSelectSql = "`id`,`name`";
                }
                List<Map<String, Object>> option = universalMapper.option(sheetDto.getApiName(), optionSelectSql);
                Map<Long, String> optionMap = option.stream()
                        .filter(m -> m.containsKey("id"))
                        .collect(Collectors.toMap(
                                m -> ((Number) m.get("id")).longValue(),
                                m -> {
                                    // 提取除"id"外的第一个字段作为值
                                    return m.entrySet().stream()
                                            .filter(e -> !"id".equals(e.getKey()))
                                            .findFirst()
                                            .map(e -> String.valueOf(e.getValue()))
                                            .orElse("");
                                }
                        ));
                sheetValueMap.put(sheetDto.getId(),optionMap);
            }
        }
        if (universalList != null && !universalList.isEmpty())
            for (Map<String, Object> stringObjectMap : universalList) {
                for (SysFieldDto fieldDto : selectSingleList) {
                    Long fieldValue = Long.parseLong(stringObjectMap.get(fieldDto.getApiName()).toString());
                    stringObjectMap.put(fieldDto.getApiName(),optionValueMap.get(fieldValue));
                }
                for (SysFieldDto fieldDto : selectMultiList) {
                    String fieldValue = stringObjectMap.get(fieldDto.getApiName()).toString();
                    List<String> split = Arrays.stream(fieldValue.split(",")).toList();
                    List<String> multiValue=new ArrayList<>();
                    for (String s : split) {
                        Long a=Long.parseLong(s);
                        multiValue.add(optionValueMap.get(a));
                    }
                    stringObjectMap.put(fieldDto.getApiName(),String.join(",",multiValue));
                }
                for (SysFieldDto fieldDto : selectSingleSheetList) {
                    Long fieldValue = Long.parseLong(stringObjectMap.get(fieldDto.getApiName()).toString());
                    stringObjectMap.put(fieldDto.getApiName(),sheetValueMap.get(fieldDto.getOptionId()).get(fieldValue));
                }
                for (SysFieldDto fieldDto : selectMultiSheetList) {
                    String fieldValue = stringObjectMap.get(fieldDto.getApiName()).toString();
                    List<String> split = Arrays.stream(fieldValue.split(",")).toList();
                    List<String> multiValue=new ArrayList<>();
                    for (String s : split) {
                        Long a=Long.parseLong(s);
                        multiValue.add(sheetValueMap.get(fieldDto.getOptionId()).get(a));
                    }
                    stringObjectMap.put(fieldDto.getApiName(),String.join(",",multiValue));
                }
                for (SysFieldDto fieldDto : cascadeList) {
                    String fieldValue = stringObjectMap.get(fieldDto.getApiName()).toString();
                    List<String> fieldValueList = Arrays.stream(fieldValue.split("-")).toList();
                    LambdaQueryWrapper<SysOptionValuePo> lqw = Wrappers.lambdaQuery();
                    lqw = Wrappers.lambdaQuery();
                    lqw.in(SysOptionValuePo::getId,fieldValueList);
                    List<SysOptionValuePo> sysOptionValuePos = optionValueMapper.selectList(lqw);
//                    Map<String, String> stringMap = sysOptionValuePos.stream().collect(Collectors.toMap(SysOptionValuePo::getApiName, SysOptionValuePo::getName, (v1, v2) -> v1));
                    Map<Long, String> stringMap = sysOptionValuePos.stream().collect(Collectors.toMap(SysOptionValuePo::getId, SysOptionValuePo::getName, (v1, v2) -> v1));
                    List<String> optionValueList=new ArrayList<>();
                    for (String s : fieldValueList) {
                        Long id = Long.valueOf(s);
//                        optionValueList.add(stringMap.get(s));
                        optionValueList.add(stringMap.get(id));
                    }
                    stringObjectMap.put(fieldDto.getApiName(),String.join("-",optionValueList));
                }
            }
        Map<String, Object> resultMap = new HashMap<>();
        if (universalList != null && !universalList.isEmpty()){
            resultMap.put("universalList", universalList);
        }
        resultMap.put("total", universalMapper.countList(query.getSheetApiName()));
        resultMap.put("pageSize", query.getPageSize());
        return AjaxResult.success(resultMap);
    }

    @Override
    public AjaxResult addFieldList(UniversalQuery query) {
        //查询表单id
        SysSheetDto sysSheetDto = sheetService.selectOneByApiName(query.getSheetApiName());
        if(sysSheetDto==null){
            return AjaxResult.error("错误的业务表单");
        }
        List<SysSheetDto> sheetList=new ArrayList<>();
        sheetList.add(sysSheetDto);
        Map<String, Map<String, Object>> stringMapMap = buildSheetFieldTree(sheetList);
        return AjaxResult.success(stringMapMap);
    }

    /**
     * 递归查出关联的所有表
     * @param sheetList
     * @return
     */
    public Map<String,Map<String,Object>> buildSheetFieldTree(List<SysSheetDto> sheetList){
        Map<String, Map<String, Object>> resultMap = new LinkedHashMap<>();
        for (SysSheetDto sysSheetDto : sheetList) {
            Map<String, Object> currentNode = new LinkedHashMap<>();
            List<SysFieldDto> currentFields = switchFieldType(sysSheetDto.getId());
            currentNode.put("fields", currentFields);
            SysFieldQuery fieldQuery=new SysFieldQuery();
            fieldQuery.setRelationSheetId(sysSheetDto.getId());
            List<SysFieldDto> relationFieldList = fieldService.selectList(fieldQuery);
            if (relationFieldList != null && !relationFieldList.isEmpty()) {
                Map<Integer, List<SysFieldDto>> groupRelationType = relationFieldList.stream()
                        .collect(Collectors.groupingBy(SysFieldDto::getRelationType));
                // 一对一 判空
                Set<Long> oneCollect = new HashSet<>();
                if (groupRelationType.get(RelationTypeConstants.ONE) != null) {
                    oneCollect = groupRelationType.get(RelationTypeConstants.ONE).stream().map(SysFieldDto::getSheetId).filter(Objects::nonNull).collect(Collectors.toSet());
                }
                // 一对多 判空
                Set<Long> manyCollect = new HashSet<>();
                if (groupRelationType.get(RelationTypeConstants.MANY) != null) {
                    manyCollect = groupRelationType.get(RelationTypeConstants.MANY).stream().map(SysFieldDto::getSheetId).filter(Objects::nonNull).collect(Collectors.toSet());
                }
                Set<Long> totalCollect=new HashSet<>(oneCollect);
                totalCollect.addAll(manyCollect);
                List<SysSheetDto> sheetDtoList = sheetService.selectListByIds(totalCollect);
                Map<Long, SysSheetDto> sheetDtoMap = sheetDtoList.stream()
                        .collect(Collectors.toMap(SysSheetDto::getId, Function.identity()));
                // 递归子表结构
                Map<String, Map<String, Object>> childMap = buildSheetFieldTree(sheetDtoList);
                // 构造子表节点映射
                Map<String, Map<String, Object>> subMap = new LinkedHashMap<>();
                for (Long sheetId : oneCollect) {
                    SysSheetDto child = sheetDtoMap.get(sheetId);
                    String key = child.getApiName() + "|" + child.getName();
                    if (childMap.containsKey(key)) {
                        subMap.put(key, childMap.get(key));
                    }
                }
                for (Long sheetId : manyCollect) {
                    SysSheetDto child = sheetDtoMap.get(sheetId);
                    String key = child.getApiName() + "|" + child.getName() + "|List";
                    if (childMap.containsKey(child.getApiName() + "|" + child.getName())) {
                        subMap.put(key, childMap.get(child.getApiName() + "|" + child.getName()));
                    }
                }
                if (!subMap.isEmpty()) {
                    currentNode.put("children", subMap);
                }
            }
            // 当前节点的 key
            String nodeKey = sysSheetDto.getApiName() + "|" + sysSheetDto.getName();
            resultMap.put(nodeKey, currentNode);
        }
        return resultMap;
    }


    @Override
    public AjaxResult searchFieldList(UniversalQuery query) {
        List<SysFieldDto> listFieldByQuery = getListFieldByQuery(query);
        if(listFieldByQuery==null){
            return AjaxResult.error("错误的业务表单");
        }
        return AjaxResult.success(listFieldByQuery);
    }


    public List<SysFieldDto> getListFieldByQuery(UniversalQuery query){
        //查询表单id
        SysSheetDto sysSheetDto = sheetService.selectOneByApiName(query.getSheetApiName());
        if(sysSheetDto==null){
            return null;
        }
        List<Long> sheetIds=new ArrayList<>();
        sheetIds.add(sysSheetDto.getId());
        List<SysFieldDto> sysFieldDtos = getFieldByRoleIds(sheetIds);
        SysFieldQuery fieldQuery=new SysFieldQuery();
        fieldQuery.setRelationSheetId(sysSheetDto.getId());
        fieldQuery.setRelationType(NumberUtil.Zero);
        List<SysFieldDto> relationFieldList = fieldService.selectList(fieldQuery);
        if (relationFieldList != null && !relationFieldList.isEmpty()) {
            Set<Long> collect = relationFieldList.stream().map(SysFieldDto::getSheetId).filter(Objects::nonNull).collect(Collectors.toSet());
            List<SysFieldDto> fieldByRoleIds = getFieldByRoleIds(collect.stream().toList());
            sysFieldDtos.addAll(fieldByRoleIds);
        }
        return sysFieldDtos;
    }

    public Map<SysSheetDto,List<SysFieldDto>> getListFieldByQueryGroupBySheet(List<SysFieldDto> relationFieldList){
        Set<Long> collect = relationFieldList.stream().map(SysFieldDto::getSheetId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<SysFieldDto> fieldByRoleIds = getFieldByRoleIds(collect.stream().toList());
        Map<Long, List<SysFieldDto>> fieldMap = fieldByRoleIds.stream()
                .collect(Collectors.groupingBy(SysFieldDto::getSheetId));
        Map<SysSheetDto,List<SysFieldDto>> sysSheetDtoListMap=new HashMap<>();
        List<SysSheetDto> sheetDtoList = sheetService.selectListByIds(collect);
        Map<Long, SysSheetDto> sheetMap = sheetDtoList.stream().collect(Collectors.toMap(SysSheetDto::getId, Function.identity()));
        for (Long sheetMapId : sheetMap.keySet()) {
            sysSheetDtoListMap.put(sheetMap.get(sheetMapId),fieldMap.get(sheetMapId));
        }
        return sysSheetDtoListMap;
    }

    /**
     * 根据表单id获取可见字段列表
     * @param sheetIds 表单id集合
     * @return 字段列表
     */
    public List<SysFieldDto> getFieldByRoleIds(List<Long> sheetIds){
        //查询角色可见字段
        List<SysFieldDto> sysFieldPoList;
        if(SecurityUtils.getUser().isAdmin()){
            sysFieldPoList=fieldService.selectBySheetIds(sheetIds);
        }else {
            Long[] roleIds = SecurityUtils.getUser().getRoleIds();
            LambdaQueryWrapper<SysFieldRoleMerge> lqw = Wrappers.lambdaQuery();
            lqw.in(SysFieldRoleMerge::getSheetId, sheetIds);
            lqw.in(SysFieldRoleMerge::getRoleId, (Object[]) roleIds);
            List<SysFieldRoleMerge> fieldRoleMerges = fieldRoleMergeMapper.selectList(lqw);
            Set<Long> fieldIds = fieldRoleMerges.stream().map(SysFieldRoleMerge::getFieldId).collect(Collectors.toSet());
            sysFieldPoList= fieldService.selectListByIds(fieldIds);
        }
        return sysFieldPoList;
    }

    public List<SysFieldDto> switchFieldType(Long sheetId){
        List<Long> sheetIds=new ArrayList<>();
        sheetIds.add(sheetId);
        List<SysFieldDto> sysFieldDtos = getFieldByRoleIds(sheetIds);
        Map<String, List<SysFieldDto>> groupFieldType = sysFieldDtos.stream()
                .collect(Collectors.groupingBy(SysFieldDto::getFieldType));
        for (String key : groupFieldType.keySet()) {
            List<SysFieldDto> sysFieldList = groupFieldType.get(key);
            switch (key) {
                case FieldTypeConstants.SELECT_SINGLE , FieldTypeConstants.SELECT_MULTI-> {
//                    List<SysFieldDto> optionList = sysFieldList.stream().filter(sysFieldDto ->
//                            sysFieldDto.getOptionType() != NumberUtil.Three).toList();
                    List<SysFieldDto> optionList = Optional.ofNullable(sysFieldList)
                            .orElse(Collections.emptyList()).stream()
                            .filter(sysFieldDto -> !Objects.equals(sysFieldDto.getOptionType(), 3))
                            .toList();
                    if(optionList.size()>0){
                        Set<Long> optionIdList = optionList.stream().map(SysFieldDto::getOptionId).filter(Objects::nonNull).collect(Collectors.toSet());
                        LambdaQueryWrapper<SysOptionValuePo> lqw = Wrappers.lambdaQuery();
                        lqw.in(SysOptionValuePo::getOptionId, optionIdList);
                        List<SysOptionValuePo> sysOptionValuePoList = optionValueMapper.selectList(lqw);
                        Map<Long, List<SysOptionValuePo>> collect = sysOptionValuePoList.stream()
                                .collect(Collectors.groupingBy(SysOptionValuePo::getOptionId));
                        for (SysFieldDto fieldDto : sysFieldList) {
                            fieldDto.setOptionValuePoList(collect.get(fieldDto.getOptionId()));
                        }
                    }
                }
                case FieldTypeConstants.CASCADE -> {
                    List<Long> casecadeIdList = sysFieldList.stream().map(SysFieldDto::getCascadeId).filter(Objects::nonNull).toList();
                    Map<Long, List<Tree<String>>> casecadeTreeMap=new HashMap<>();
                    for (Long casecadeId : casecadeIdList) {
                        List<Tree<String>> trees = sysCascadeManager.selectCascadeRelation(casecadeId);
                        casecadeTreeMap.put(casecadeId,trees);
                    }
                    for (SysFieldDto fieldDto : sysFieldList) {
                        fieldDto.setCasecadeTreeList(casecadeTreeMap.get(fieldDto.getCascadeId()));
                    }
                }
            }
        }
        return groupFieldType.values().stream().flatMap(List::stream).toList();
    }

    @Override
    public AjaxResult getInfo(UniversalQuery query) {
        String sheetApiName = query.getSheetApiName();
        //查询表单id
        SysSheetDto sysSheetDto = sheetService.selectOneByApiName(sheetApiName);
        if(sysSheetDto==null){
            return AjaxResult.error("错误的业务表单");
        }
        Long sheetId=sysSheetDto.getId();
        List<Long> sheetIds=new ArrayList<>();
        sheetIds.add(sheetId);
        List<SysFieldDto> sysFieldDtos = getFieldByRoleIds(sheetIds);
        String searchField=String.join(",",sysFieldDtos.stream().map(SysFieldDto::getApiName).
                filter(Objects::nonNull).map(name -> sheetApiName+"." + name).toList()) ;
        String selectSql="select "+searchField;
        String fromSql=" from "+sheetApiName;
        SysFieldQuery fieldQuery=new SysFieldQuery();
        fieldQuery.setRelationSheetId(sheetId);
        List<SysFieldDto> relationFieldList = fieldService.selectList(fieldQuery);
        List<SysFieldDto> oneRelationFieldList=new ArrayList<>();
        List<SysFieldDto> manyRelationFieldList=new ArrayList<>();
        Map<SysSheetDto, List<SysFieldDto>> listFieldByQueryGroupBySheet=new HashMap<>();
        if (relationFieldList != null && !relationFieldList.isEmpty()) {
            Map<Integer, List<SysFieldDto>> groupRelationType = relationFieldList.stream()
                    .collect(Collectors.groupingBy(SysFieldDto::getRelationType));
            listFieldByQueryGroupBySheet = getListFieldByQueryGroupBySheet(relationFieldList);
            // 在获取列表时进行 null 检查
            oneRelationFieldList = Optional.ofNullable(groupRelationType.get(RelationTypeConstants.ONE))
                    .orElse(Collections.emptyList());
            manyRelationFieldList = Optional.ofNullable(groupRelationType.get(RelationTypeConstants.MANY))
                    .orElse(Collections.emptyList());
        }
        if(!oneRelationFieldList.isEmpty()) {
            for (SysFieldDto sysFieldDto : oneRelationFieldList) {
                for (SysSheetDto sheetDto : listFieldByQueryGroupBySheet.keySet()) {
                    if (Objects.equals(sheetDto.getId(), sysFieldDto.getSheetId())) {
                        List<SysFieldDto> fieldRelationList = listFieldByQueryGroupBySheet.get(sheetDto);
                        String relationApiName = sheetDto.getApiName();
                        String searchRelationField = String.join(",", fieldRelationList.stream().map(SysFieldDto::getApiName).
                                filter(Objects::nonNull).map(name -> relationApiName + "." + name).toList());
                        fromSql = fromSql + " left join " + relationApiName + " on " + relationApiName + "." + sysFieldDto.getApiName() + "=" + sheetApiName + "." + "`id`";
                        selectSql = selectSql + "," + searchRelationField;
                    }
                }
            }
        }
        String sql=selectSql+fromSql+" where "+sheetApiName+".`id`="+query.getId() +" and "+sheetApiName+".del_flag=0 ";
        Map<String, Object> universalInfo  = universalMapper.selectInfo(sql);
        if(!manyRelationFieldList.isEmpty()) {
            for (SysFieldDto sysFieldDto : manyRelationFieldList) {
                for (SysSheetDto sheetDto : listFieldByQueryGroupBySheet.keySet()) {
                    if(Objects.equals(sheetDto.getId(), sysFieldDto.getSheetId())){
                        List<SysFieldDto> fieldRelationList = listFieldByQueryGroupBySheet.get(sheetDto);
                        String relationApiName = sheetDto.getApiName();
                        String searchRelationField=String.join(",",fieldRelationList.stream().map(SysFieldDto::getApiName).
                                filter(Objects::nonNull).map(name -> relationApiName+"." + name).toList()) ;
                        String relationSql="select "+searchRelationField+" from "+relationApiName+" where "+sysFieldDto.getApiName()+"="+query.getId();
                        List<Map<String, Object>> relationList = universalMapper.selectRelationList(relationSql);
                        universalInfo.put(relationApiName+"|List",relationList);
                    }
                }
            }
        }
        return AjaxResult.success(universalInfo);
    }

    @Override
    public AjaxResult add(UniversalQuery query) {
        JSONObject jsonObject = JSON.parseObject(query.getUniversalJson());
        if(query.getAddType()==1){
            return customerAdd(jsonObject);
        }else if(query.getAddType()==2){
            return normalAdd(jsonObject,query.getSheetApiName());
        }else if(query.getAddType()==3){
            return onlyAdd(jsonObject, query.getSheetApiName() ,query.getForeignKey());
        }
        return AjaxResult.error();
    }

    @Override
    public AjaxResult edit(UniversalQuery query) {
        JSONObject jsonObject = JSON.parseObject(query.getUniversalJson());
        Long id = query.getId();
        String sheetApiName = query.getSheetApiName();
        Long sheetId = sheetService.selectOneByApiName(sheetApiName).getId();
        Map<String, Object> beforeObjectMap = universalMapper.selectById(id, sheetApiName);
        if (universalMapper.update(jsonObject, id, sheetApiName) > 0) {
            Map<String, Object> afterObjectMap = universalMapper.selectById(id, sheetApiName);
            Set<String> fieldApiNameSet = jsonObject.keySet();
            // fieldList 解析 universalJson 后， 拿到要更新的字段
            List<SysFieldDto> fieldList = fieldService.selectByApiNames(fieldApiNameSet.stream().toList(), sheetId);
            List<Long> ids = fieldList.stream().map(SysFieldDto::getId).toList();
            List<SysFieldDto> quoteFieldList = fieldService.selectQuoteByIds(ids);
            // 引用规则
            List<SysFieldDto> refercencingFieldList = fieldService.selectReferencingByIds(ids);
            List<SysFieldDto> referencedFieldList = fieldService.selectReferencedByIds(ids);
            for (SysFieldDto sysFieldDto : fieldList) {
                //修改的字段里有引用规则当前表字段的
                for (SysFieldDto fieldDto : refercencingFieldList) {
                    if (Objects.equals(sysFieldDto.getId(), fieldDto.getReferencingFieldId())) {
                        String searchFieldApiName = fieldService.selectById(fieldDto.getQuoteSheetFieldId()).getApiName();
                        String searchSheetApiName = sheetService.selectById(fieldDto.getQuoteSheetId()).getApiName();
                        Object searchValue = afterObjectMap.get(sysFieldDto.getApiName());
                        String quoteRuleFieldApiName = fieldService.selectById(fieldDto.getReferencedFieldId()).getApiName();
//                        String quoteReferencingFieldValue = fieldService.selectById(sysFieldDto.getReferencingFieldId()).getApiName();
                        String quoteReferencingFieldValue = fieldService.selectById(fieldDto.getReferencingFieldId()).getApiName();
                        Object o = universalMapper.selectQuoteFieldVale(searchSheetApiName, searchFieldApiName, quoteRuleFieldApiName, searchValue);
                        universalMapper.updateQuoteField(fieldDto.getApiName(), sheetApiName, o, quoteReferencingFieldValue, searchValue);
                    }
                }
                //修改的字段里有引用规则引用表字段的
                for (SysFieldDto fieldDto : referencedFieldList) {
                    if (Objects.equals(sysFieldDto.getId(), fieldDto.getReferencedFieldId())) {
                        String searchFieldApiName = fieldService.selectById(fieldDto.getReferencingFieldId()).getApiName();
                        String searchSheetApiName = sheetService.selectById(fieldDto.getSheetId()).getApiName();
                        Object updateValue = afterObjectMap.get(sysFieldDto.getApiName());
                        Object updateBeforeValue = beforeObjectMap.get(sysFieldDto.getApiName());
                        universalMapper.updateQuoteField(searchFieldApiName, searchSheetApiName, updateValue, searchFieldApiName, updateBeforeValue);
                    }
                }
                //修改的字段里被引用 a.name(修改) <- b.name
                for (SysFieldDto fieldDto : quoteFieldList) {
                    if (Objects.equals(sysFieldDto.getId(), fieldDto.getQuoteSheetFieldId())) {
                        String updateFieldApiName = fieldDto.getApiName();
                        String updateSheetApiName = sheetService.selectById(fieldDto.getSheetId()).getApiName();
                        Object updateValue = afterObjectMap.get(sysFieldDto.getApiName());
//                        String quoteRuleFieldApiName = fieldService.selectById(fieldDto.getReferencingFieldId()).getApiName();
                        String quoteRuleFieldApiName = fieldService.selectById(fieldDto.getReferencedFieldId()).getApiName();
//                        Object quoteRuleFieldValue = afterObjectMap.get(fieldService.selectById(fieldDto.getReferencedFieldId()).getApiName());
                        Object quoteRuleFieldValue = afterObjectMap.get(fieldService.selectById(fieldDto.getReferencingFieldId()).getApiName());
                        universalMapper.updateQuoteField(updateFieldApiName, updateSheetApiName, updateValue, quoteRuleFieldApiName, quoteRuleFieldValue);
                    }
                }
                //修改的字段里有引用字段  a.name <- b.name(修改) <- c.name
                if (Objects.equals(sysFieldDto.getFieldType(), FieldTypeConstants.QUOTE)) {
                    String updateFieldApiName = fieldService.selectById(sysFieldDto.getQuoteSheetFieldId()).getApiName();
                    String updateSheetApiName = sheetService.selectById(sysFieldDto.getQuoteSheetId()).getApiName();
                    Object updateValue = afterObjectMap.get(sysFieldDto.getApiName());
                    String quoteRuleFieldApiName = fieldService.selectById(sysFieldDto.getReferencedFieldId()).getApiName();
                    Object quoteRuleFieldValue = afterObjectMap.get(fieldService.selectById(sysFieldDto.getReferencingFieldId()).getApiName());
                    universalMapper.updateQuoteField(updateFieldApiName, updateSheetApiName, updateValue, quoteRuleFieldApiName, quoteRuleFieldValue);
                    //修改所有引用该字段的数据
                    List<Long> quoteSheetFiledIds = new ArrayList<>();
                    quoteSheetFiledIds.add(sysFieldDto.getQuoteSheetFieldId());
                    List<SysFieldDto> quoteSheetFieldList = fieldService.selectQuoteByIds(quoteSheetFiledIds);
                    String selectSheetApiName = sheetService.selectById(sysFieldDto.getQuoteSheetId()).getApiName();
//                    Map<String, Object> quoteObjectMap =universalMapper.selectByQuote(selectSheetApiName,updateFieldApiName,quoteRuleFieldValue);
                    Map<String, Object> quoteObjectMap = universalMapper.selectByQuote(selectSheetApiName, updateFieldApiName, updateValue);
                    for (SysFieldDto fieldDto : quoteSheetFieldList) {
                        String updateApiName = fieldDto.getApiName();
                        String sheetUpdateApiName = sheetService.selectById(fieldDto.getSheetId()).getApiName();
                        String quoteFieldApiName = fieldService.selectById(fieldDto.getReferencingFieldId()).getApiName();
                        Object quoteFieldValue = quoteObjectMap.get(fieldService.selectById(sysFieldDto.getReferencedFieldId()).getApiName());
                        universalMapper.updateQuoteField(updateApiName, sheetUpdateApiName, updateValue, quoteFieldApiName, quoteFieldValue);
                    }
                }
            }
            return AjaxResult.success();
        } else {
            return AjaxResult.error();
        }
    }

    @Override
    public AjaxResult batchRemove(UniversalQuery query) {
        if(universalMapper.delete(query.getSheetApiName(),query.getIdList())>0){
            return AjaxResult.success();
        }else{
            return AjaxResult.error();
        }
    }

    @Override
    public AjaxResult option(UniversalQuery query) {
        // 默认选择字段：id 和 name
        String selectSql = "`id`,`name`";
        List<String> optionFieldList = query.getOptionFieldList();
        // 如果 optionFieldList 不为空，使用传入字段替代默认
        if (optionFieldList != null && !optionFieldList.isEmpty()) {
            selectSql = String.join(",", optionFieldList);
        }
        SysSheetDto sysSheetDto = sheetService.selectById(query.getSheetId());
        return AjaxResult.success(universalMapper.option(sysSheetDto.getApiName(), selectSql));
    }

    @Override
    public AjaxResult check(UniversalQuery query) {
        // 检查 credit_no 是否存在于 实体表
        List<SysEntityPo> sysEntityPos = sysEntityMapper.select();
        List<String> creditNoList = sysEntityPos.stream().map(SysEntityPo::getCreditNo).toList();
        if (creditNoList.contains(query.getCreditNo())) {
            return AjaxResult.success(creditNoList.indexOf(query.getCreditNo()));
        }
        return AjaxResult.success("");
    }

    @Override
    public AjaxResult getEntityInfo(UniversalQuery query) {
        // 前端传入 credit_no 和 sheetApiName
        // 第一种 查数据库， 将查到的数据 拼接在主表后面 结构是 List<Map<String, Object>>
        List<String> creditNoList = sysEntityMapper.select().stream().map(SysEntityPo::getCreditNo).toList();
        if (creditNoList.contains(query.getCreditNo())) {
            String sheetApiName = query.getSheetApiName();
            SysSheetDto sysSheetDto = sheetService.selectOneByApiName(sheetApiName);
            if (sysSheetDto == null) {
                return AjaxResult.error("错误的业务表单");
            }
            List<SysFieldDto> fieldByRoleIds = getFieldByRoleIds(Collections.singletonList(sysSheetDto.getId()));
            StringBuilder selectSql = new StringBuilder("select " + String.join(",", fieldByRoleIds.stream().map(SysFieldDto::getApiName)
                    .filter(Objects::nonNull).map(name -> query.getSheetApiName() + "." + name).toList()));
            StringBuilder formSql = new StringBuilder(" from " + query.getSheetApiName());

            SysFieldQuery fieldQuery = new SysFieldQuery();
            fieldQuery.setSheetId(sysSheetDto.getId());
            fieldQuery.setRelationType(NumberUtil.Zero);
            List<SysFieldDto> realtionFieldList = fieldService.selectList(fieldQuery);
            List<Map<String, Object>> universal = new ArrayList<>();
            if (realtionFieldList != null && !realtionFieldList.isEmpty()) {
                Map<SysSheetDto, List<SysFieldDto>> listFieldByQueryGroupBySheet = getListFieldByQueryGroupBySheet(realtionFieldList);
                for (SysFieldDto sysFieldDto : realtionFieldList) {
                    for (SysSheetDto sheetDto : listFieldByQueryGroupBySheet.keySet()) {
                        List<SysFieldDto> fieldRelationList = listFieldByQueryGroupBySheet.get(sheetDto);
                        if (Objects.equals(sheetDto.getId(), sysFieldDto.getSheetId())) {
                            String relationApiName = sheetDto.getApiName();
                            String searchRelationField = String.join(",", fieldRelationList.stream().map(SysFieldDto::getApiName)
                                    .filter(Objects::nonNull).map(name -> relationApiName + "." + name).toList());
                            selectSql.append(",").append(searchRelationField);
                            formSql.append(" left join ").append(relationApiName).append(" on ")
                                    .append(sheetApiName).append(".`id`=").append(relationApiName).append(".").append(sysFieldDto.getApiName());
                        }
                    }
                }
                listFieldByQueryGroupBySheet.values().stream().flatMap(List::stream).forEach(fieldByRoleIds::add);
            }
            query.setPage((query.getPage() - 1) * query.getPageSize());
            universal = universalMapper.selectList(String.valueOf(selectSql.append(formSql)), query);

            // 处理字段类型转换（与list方法类似的逻辑）
            universal = processFieldTypes(universal, fieldByRoleIds);

            // 将查询结果缓存到Redis，缓存30分钟
            cacheEntityInfo(query.getCreditNo(), query.getSheetApiName(), universal, 30);

            return AjaxResult.success(universal);
        }

        // 第二步：查 redis 根据 credit_no 和 sheetApiName
        List<Map<String, Object>> redisData = getEntityInfoFromCache(query.getCreditNo(), query.getSheetApiName());

        if (redisData != null && !redisData.isEmpty()) {
            return AjaxResult.success(redisData);
        }

        return AjaxResult.error("未找到相关数据");
    }

    /**
     * 处理字段类型转换，将ID转换为对应的显示值
     * @param dataList 原始数据列表
     * @param fieldList 字段列表
     * @return 处理后的数据列表
     */
    private List<Map<String, Object>> processFieldTypes(List<Map<String, Object>> dataList, List<SysFieldDto> fieldList) {
        if (dataList == null || dataList.isEmpty() || fieldList == null || fieldList.isEmpty()) {
            return dataList;
        }

        // 根据字段类型分组
        Map<String, List<SysFieldDto>> groupFieldType = fieldList.stream()
                .collect(Collectors.groupingBy(SysFieldDto::getFieldType));

        // 级联字段
        List<SysFieldDto> cascadeList = Optional.ofNullable(groupFieldType.get(FieldTypeConstants.CASCADE))
                .orElse(Collections.emptyList());

        // 单选和多选字段分类
        List<SysFieldDto> selectSingleList = new ArrayList<>();
        List<SysFieldDto> selectMultiList = new ArrayList<>();
        List<SysFieldDto> selectSingleSheetList = new ArrayList<>();
        List<SysFieldDto> selectMultiSheetList = new ArrayList<>();

        // 处理单选字段
        Optional.ofNullable(groupFieldType.get(FieldTypeConstants.SELECT_SINGLE))
                .orElse(Collections.emptyList())
                .forEach(sysFieldDto -> {
                    if (Objects.equals(sysFieldDto.getOptionType(), 3)) {
                        selectSingleSheetList.add(sysFieldDto);
                    } else {
                        selectSingleList.add(sysFieldDto);
                    }
                });

        // 处理多选字段
        Optional.ofNullable(groupFieldType.get(FieldTypeConstants.SELECT_MULTI))
                .orElse(Collections.emptyList())
                .forEach(sysFieldDto -> {
                    if (Objects.equals(sysFieldDto.getOptionType(), 3)) {
                        selectMultiSheetList.add(sysFieldDto);
                    } else {
                        selectMultiList.add(sysFieldDto);
                    }
                });

        // 获取选项值映射
        Map<Long, String> optionValueMap = getOptionValueMap(selectSingleList, selectMultiList);
        Map<Long, Map<Long, String>> sheetValueMap = getSheetValueMap(selectSingleSheetList, selectMultiSheetList);

        // 处理数据转换
        return processDataConversion(dataList, selectSingleList, selectMultiList,
                selectSingleSheetList, selectMultiSheetList, cascadeList,
                optionValueMap, sheetValueMap);
    }

    /**
     * 获取选项值映射
     */
    private Map<Long, String> getOptionValueMap(List<SysFieldDto> selectSingleList, List<SysFieldDto> selectMultiList) {
        Set<Long> optionIdList = new HashSet<>();
        if (!selectSingleList.isEmpty()) {
            optionIdList.addAll(selectSingleList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        if (!selectMultiList.isEmpty()) {
            optionIdList.addAll(selectMultiList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }

        Map<Long, String> optionValueMap = new HashMap<>();
        if (!optionIdList.isEmpty()) {
            LambdaQueryWrapper<SysOptionValuePo> lqw = Wrappers.lambdaQuery();
            lqw.in(SysOptionValuePo::getOptionId, optionIdList);
            List<SysOptionValuePo> sysOptionValuePoList = optionValueMapper.selectList(lqw);
            optionValueMap = sysOptionValuePoList.stream()
                    .collect(Collectors.toMap(SysOptionValuePo::getId, SysOptionValuePo::getName, (v1, v2) -> v1));
        }
        return optionValueMap;
    }

    /**
     * 获取业务表选项值映射
     */
    private Map<Long, Map<Long, String>> getSheetValueMap(List<SysFieldDto> selectSingleSheetList, List<SysFieldDto> selectMultiSheetList) {
        Set<Long> sheetIdList = new HashSet<>();
        if (!selectSingleSheetList.isEmpty()) {
            sheetIdList.addAll(selectSingleSheetList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }
        if (!selectMultiSheetList.isEmpty()) {
            sheetIdList.addAll(selectMultiSheetList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
        }

        Map<Long, Map<Long, String>> sheetValueMap = new HashMap<>();
        if (!sheetIdList.isEmpty()) {
            List<SysSheetDto> sheetDtoList = sheetService.selectListByIds(sheetIdList);
            for (SysSheetDto sheetDto : sheetDtoList) {
                String optionSelectSql = sheetDto.getApiName().equals("sys_user") ? "`id`,`nick_name`" : "`id`,`name`";
                List<Map<String, Object>> option = universalMapper.option(sheetDto.getApiName(), optionSelectSql);
                Map<Long, String> optionMap = option.stream()
                        .filter(m -> m.containsKey("id"))
                        .collect(Collectors.toMap(
                                m -> ((Number) m.get("id")).longValue(),
                                m -> m.entrySet().stream()
                                        .filter(e -> !"id".equals(e.getKey()))
                                        .findFirst()
                                        .map(e -> String.valueOf(e.getValue()))
                                        .orElse("")
                        ));
                sheetValueMap.put(sheetDto.getId(), optionMap);
            }
        }
        return sheetValueMap;
    }

    /**
     * 执行数据转换
     */
    private List<Map<String, Object>> processDataConversion(
            List<Map<String, Object>> dataList,
            List<SysFieldDto> selectSingleList,
            List<SysFieldDto> selectMultiList,
            List<SysFieldDto> selectSingleSheetList,
            List<SysFieldDto> selectMultiSheetList,
            List<SysFieldDto> cascadeList,
            Map<Long, String> optionValueMap,
            Map<Long, Map<Long, String>> sheetValueMap) {

        for (Map<String, Object> dataMap : dataList) {
            // 处理单选选项字段
            for (SysFieldDto fieldDto : selectSingleList) {
                Object fieldValue = dataMap.get(fieldDto.getApiName());
                if (fieldValue != null) {
                    try {
                        Long valueId = Long.parseLong(fieldValue.toString());
                        dataMap.put(fieldDto.getApiName(), optionValueMap.get(valueId));
                    } catch (NumberFormatException e) {
                        // 如果转换失败，保持原值
                    }
                }
            }

            // 处理多选选项字段
            for (SysFieldDto fieldDto : selectMultiList) {
                Object fieldValue = dataMap.get(fieldDto.getApiName());
                if (fieldValue != null) {
                    try {
                        String[] valueIds = fieldValue.toString().split(",");
                        List<String> multiValue = Arrays.stream(valueIds)
                                .map(id -> {
                                    try {
                                        return optionValueMap.get(Long.parseLong(id.trim()));
                                    } catch (NumberFormatException e) {
                                        return id.trim();
                                    }
                                })
                                .filter(Objects::nonNull)
                                .toList();
                        dataMap.put(fieldDto.getApiName(), String.join(",", multiValue));
                    } catch (Exception e) {
                        // 如果处理失败，保持原值
                    }
                }
            }

            // 处理单选业务表字段
            for (SysFieldDto fieldDto : selectSingleSheetList) {
                Object fieldValue = dataMap.get(fieldDto.getApiName());
                if (fieldValue != null && sheetValueMap.containsKey(fieldDto.getOptionId())) {
                    try {
                        Long valueId = Long.parseLong(fieldValue.toString());
                        String displayValue = sheetValueMap.get(fieldDto.getOptionId()).get(valueId);
                        if (displayValue != null) {
                            dataMap.put(fieldDto.getApiName(), displayValue);
                        }
                    } catch (NumberFormatException e) {
                        // 如果转换失败，保持原值
                    }
                }
            }

            // 处理多选业务表字段
            for (SysFieldDto fieldDto : selectMultiSheetList) {
                Object fieldValue = dataMap.get(fieldDto.getApiName());
                if (fieldValue != null && sheetValueMap.containsKey(fieldDto.getOptionId())) {
                    try {
                        String[] valueIds = fieldValue.toString().split(",");
                        Map<Long, String> optionMap = sheetValueMap.get(fieldDto.getOptionId());
                        List<String> multiValue = Arrays.stream(valueIds)
                                .map(id -> {
                                    try {
                                        return optionMap.get(Long.parseLong(id.trim()));
                                    } catch (NumberFormatException e) {
                                        return id.trim();
                                    }
                                })
                                .filter(Objects::nonNull)
                                .toList();
                        dataMap.put(fieldDto.getApiName(), String.join(",", multiValue));
                    } catch (Exception e) {
                        // 如果处理失败，保持原值
                    }
                }
            }

            // 处理级联字段
            for (SysFieldDto fieldDto : cascadeList) {
                Object fieldValue = dataMap.get(fieldDto.getApiName());
                if (fieldValue != null) {
                    try {
                        String[] fieldValueList = fieldValue.toString().split("-");
                        List<Long> idList = Arrays.stream(fieldValueList)
                                .map(id -> {
                                    try {
                                        return Long.parseLong(id.trim());
                                    } catch (NumberFormatException e) {
                                        return null;
                                    }
                                })
                                .filter(Objects::nonNull)
                                .toList();

                        if (!idList.isEmpty()) {
                            LambdaQueryWrapper<SysOptionValuePo> lqw = Wrappers.lambdaQuery();
                            lqw.in(SysOptionValuePo::getId, idList);
                            List<SysOptionValuePo> sysOptionValuePos = optionValueMapper.selectList(lqw);
                            Map<Long, String> nameMap = sysOptionValuePos.stream()
                                    .collect(Collectors.toMap(SysOptionValuePo::getId, SysOptionValuePo::getName, (v1, v2) -> v1));

                            List<String> optionValueList = idList.stream()
                                    .map(nameMap::get)
                                    .filter(Objects::nonNull)
                                    .toList();
                            dataMap.put(fieldDto.getApiName(), String.join("-", optionValueList));
                        }
                    } catch (Exception e) {
                        // 如果处理失败，保持原值
                    }
                }
            }
        }

        return dataList;
    }

    /**
     * 将实体信息缓存到Redis
     * @param creditNo 统一社会信用代码
     * @param sheetApiName 表单API名称
     * @param data 要缓存的数据
     * @param expireMinutes 过期时间（分钟）
     */
    public void cacheEntityInfo(String creditNo, String sheetApiName, List<Map<String, Object>> data, long expireMinutes) {
        String redisKey = "entity_info:" + creditNo + ":" + sheetApiName;
        redisService.setCacheObject(redisKey, data, expireMinutes, TimeUnit.MINUTES);
    }

    /**
     * 从Redis获取实体信息
     * @param creditNo 统一社会信用代码
     * @param sheetApiName 表单API名称
     * @return 缓存的数据
     */
    public List<Map<String, Object>> getEntityInfoFromCache(String creditNo, String sheetApiName) {
        String redisKey = "entity_info:" + creditNo + ":" + sheetApiName;
        return redisService.getCacheObject(redisKey);
    }

    /**
     * 清除实体信息缓存
     * @param creditNo 统一社会信用代码
     * @param sheetApiName 表单API名称
     */
    public void clearEntityInfoCache(String creditNo, String sheetApiName) {
        String redisKey = "entity_info:" + creditNo + ":" + sheetApiName;
        redisService.deleteObject(redisKey);
    }

    public String buildInsertSql(String tableName, JSONObject sheetJson, Long id) {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ").append(tableName).append(" (");

        List<String> fields = new ArrayList<>();
        List<String> values = new ArrayList<>();

        fields.add("id");
        values.add("'" + id + "'");

        // 遍历 JSON 字段
        for (Map.Entry<String, Object> entry : sheetJson.entrySet()) {
            String field = entry.getKey();
            Object value = entry.getValue();

            if (value == null) continue;

            fields.add("`" + field + "`");

            // 判断是否是字符串类型
            if (value instanceof String || value instanceof Date) {
                values.add("'" + value.toString().replace("'", "''") + "'");
            } else {
                values.add(value.toString());
            }
        }

        sql.append(String.join(", ", fields));
        sql.append(") VALUES (");
        sql.append(String.join(", ", values));
        sql.append(");");

        return sql.toString();
    }

    public String buildBatchInsertSql(String tableName, JSONArray sheetArray) {
        if (sheetArray == null || sheetArray.isEmpty()) {
            return null;
        }
        // 第一个对象用于确定字段顺序
        JSONObject firstObj = sheetArray.getJSONObject(0);
        List<String> fields = new ArrayList<>();
        fields.add("id"); // 手动添加主键 ID

        for (String key : firstObj.keySet()) {
            fields.add("`" + key + "`");
        }

        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ").append(tableName)
                .append(" (").append(String.join(", ", fields)).append(") VALUES ");

        List<String> rowValues = new ArrayList<>();

        for (int i = 0; i < sheetArray.size(); i++) {
            JSONObject obj = sheetArray.getJSONObject(i);
            List<String> values = new ArrayList<>();
            // 生成 UUID 主键
            String uuid = UUID.randomUUID().toString();
            values.add("'" + uuid + "'");

            for (String key : firstObj.keySet()) {
                Object val = obj.get(key);
                if (val == null) {
                    values.add("NULL");
                } else if (val instanceof String || val instanceof Date) {
                    values.add("'" + val.toString().replace("'", "''") + "'");
                } else {
                    values.add(val.toString());
                }
            }
            rowValues.add("(" + String.join(", ", values) + ")");
        }

        sql.append(String.join(", ", rowValues)).append(";");
        return sql.toString();
    }

    public Integer  insertSql(Object object,String sheetApiName,Long id,Long foreignKey){
        Snowflake snowflake = IdUtil.getSnowflake(1, 0);
        JSONObject sheetJson=new JSONObject();
        JSONArray jsonArray=new JSONArray();
        if(sheetApiName.contains("List")){
            jsonArray = JSONArray.parseArray(object.toString());
            sheetApiName=sheetApiName.replace("List","");
        }else{
            sheetJson = JSONObject.parseObject(object.toString());
        }
        //查询表单id
        SysSheetDto sysSheetDto = sheetService.selectOneByApiName(sheetApiName);
        if(sysSheetDto==null){
            return null;
        }
        SysFieldQuery query=new SysFieldQuery();
        query.setSheetId(sysSheetDto.getId());
        List<SysFieldDto> sysFieldDtos = fieldService.selectList(query);
        JSONArray jsonArrayResult=new JSONArray();
        for (SysFieldDto sysFieldDto : sysFieldDtos) {
            if(Objects.equals(sysFieldDto.getFieldType(), FieldTypeConstants.QUOTE)){
                SysFieldDto quoteFieldDto = fieldService.selectById(sysFieldDto.getQuoteSheetFieldId());
                String quoteSheetApiName = sheetService.selectById(quoteFieldDto.getSheetId()).getApiName();
                String quoteFieldApiName = quoteFieldDto.getApiName();
                String quoteRuleFieldApiName = fieldService.selectById(sysFieldDto.getReferencedFieldId()).getApiName();
                String ruleFieldApiName = fieldService.selectById(sysFieldDto.getReferencingFieldId()).getApiName();
                if(jsonArray.size()>0){
                    for (Object o : jsonArray) {
//                        JSONObject jsonObject=JSONObject.parseObject(JSONObject.toJSONString(o));
                        JSONObject jsonObject=JSONObject.parseObject(o.toString());
                        Object fieldValue = jsonObject.get(ruleFieldApiName);
                        jsonObject.put(sysFieldDto.getApiName(),universalMapper.
                                selectQuoteFieldVale(quoteSheetApiName,quoteFieldApiName,quoteRuleFieldApiName,fieldValue));
                        jsonArrayResult.add(jsonObject);
                    }
                }else{
                    Object fieldValue = sheetJson.get(ruleFieldApiName);
                    sheetJson.put(sysFieldDto.getApiName(),universalMapper.
                            selectQuoteFieldVale(quoteSheetApiName,quoteFieldApiName,quoteRuleFieldApiName,fieldValue));
                }
                if(Objects.equals(sysFieldDto.getFieldType(), FieldTypeConstants.RELATION)){
                    if(jsonArray.size()>0){
                        for (Object o : jsonArray) {
//                            JSONObject jsonObject=JSONObject.parseObject(JSONObject.toJSONString(o));
                            JSONObject jsonObject=JSONObject.parseObject(o.toString());
                            jsonObject.put(sysFieldDto.getApiName(),foreignKey);
                            jsonArrayResult.add(jsonObject);
                        }
                    }else{
                        sheetJson.put(sysFieldDto.getApiName(),foreignKey);
                    }
                }
            }
        }
        String sql="";
        if(jsonArrayResult.size()>0){
            sql= buildBatchInsertSql(sheetApiName, jsonArrayResult);
        }else{
            if(id==null){
//                id=UUID.randomUUID().toString();
                id = snowflake.nextId();
            }
            sql= buildInsertSql(sheetApiName, sheetJson, id);
        }
        return universalMapper.insert(sql);
    }

    public AjaxResult onlyAdd(JSONObject jsonObject,String sheetApiName,Long foreignKey){
//        String id=UUID.randomUUID().toString();
        Snowflake snowflake = IdUtil.getSnowflake(1, 0);
        Long id = snowflake.nextId();
        Integer flag=insertSql(jsonObject.get(sheetApiName), sheetApiName,id,foreignKey);
        if(flag>0) {
            return AjaxResult.success();
        }else{
            return AjaxResult.error();
        }
    }

    public AjaxResult normalAdd(JSONObject jsonObject,String sheetApiName){
//        String id=UUID.randomUUID().toString();
        Snowflake snowflake = IdUtil.getSnowflake(1, 0);
        Long id = snowflake.nextId();
        //id 新增数据id    null 外键
        Integer flag=insertSql(jsonObject.get(sheetApiName), sheetApiName,id,null);
        if(flag==null){
            return AjaxResult.error("新增失败，错误的业务表单");
        }else if(flag>0) {
            for (String apiName : jsonObject.keySet()) {
                if (!apiName.equals(sheetApiName)) {
                    insertSql(jsonObject.get(apiName), apiName, null, id);
                }
            }
            return AjaxResult.success();
        }
        return AjaxResult.error();
    }

    public AjaxResult customerAdd(JSONObject jsonObject){
        //查询阿里api获取工商信息
        CompanyBaseInfoApiDto companyBaseInfoApiDto = CompanyBaseInfoUtil.getCompanyBaseInfo(jsonObject.get("credit_no").toString());

        if (companyBaseInfoApiDto.getName() == null) {
            return AjaxResult.error("统一社会信用代码错误，未找到该企业");
        }
        RegisterDataApiDto registerData = companyBaseInfoApiDto.getRegisterData();
        String legalPersonName=companyBaseInfoApiDto.getLegalPersonName();
        String name=companyBaseInfoApiDto.getName();
        LocalDate startDate ;
        try {
            startDate = LocalDate.parse(companyBaseInfoApiDto.getStartDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }catch (Exception e){
            startDate=null;
        }
        String code=jsonObject.get("code").toString();
        Long createBy=SecurityUtils.getUserId();
        SysOptionPo regType = optionMapper.selectOne(
                Wrappers.<SysOptionPo>query().lambda()
                        .eq(SysOptionPo::getApiName, "reg_type")
                        .last(SqlConstants.LIMIT_ONE));
        LambdaQueryWrapper<SysOptionValuePo> lqw = Wrappers.lambdaQuery();
        lqw.eq(SysOptionValuePo::getOptionId,regType.getId());
        List<SysOptionValuePo> regTypeList = optionValueMapper.selectList(lqw);
        SysOptionPo businessStatus = optionMapper.selectOne(
                Wrappers.<SysOptionPo>query().lambda()
                        .eq(SysOptionPo::getApiName, "business_status")
                        .last(SqlConstants.LIMIT_ONE));
        lqw=Wrappers.lambdaQuery();
        lqw.eq(SysOptionValuePo::getOptionId,businessStatus.getId());
        List<SysOptionValuePo> businessStatusList = optionValueMapper.selectList(lqw);
        for (SysOptionValuePo sysOptionValuePo : regTypeList) {
            if (sysOptionValuePo.getName().equals(registerData.getRegType())) {
                registerData.setRegType(sysOptionValuePo.getApiName());
                break;
            }
        }
        for (SysOptionValuePo sysOptionValuePo : businessStatusList) {
            if (sysOptionValuePo.getName().equals(registerData.getStatus())) {
                registerData.setStatus(sysOptionValuePo.getApiName());
                break;
            }
        }
        CustomerDto customerDto=new CustomerDto();
        BeanUtils.copyProperties(registerData,customerDto);
        customerDto.setLegalPersonName(legalPersonName);
        customerDto.setName(name);
        customerDto.setStartDate(startDate);
        customerDto.setCode(code);
        customerDto.setCreateBy(createBy);
        if (universalMapper.insertCustomer(customerDto) > 0) {
            Long id = customerDto.getId();
            universalMapper.insertCustomerTaxation(id);
            BranchDataApiDto branchDataApiDto = companyBaseInfoApiDto.getBranchDataApiDto();
            EmployeeDataApiDto employeeData = companyBaseInfoApiDto.getEmployeeData();
            ChangeRecordDataApiDto changeRecordData = companyBaseInfoApiDto.getChangeRecordData();
            PartnerDataApiDto partnerData = companyBaseInfoApiDto.getPartnerData();
            if (branchDataApiDto.getList().size() > 0) {
                List<Map<String,Object>> branchList=new ArrayList<>();
                for (BranchDataListDto branchDataListDto : branchDataApiDto.getList()) {
                    Map<String,Object> branchMap=new HashMap<>();
                    branchMap.put("customer_id",id);
                    branchMap.put("name",branchDataListDto.getName());
                    branchList.add(branchMap);
                }

                universalMapper.insertBatch(branchList,"bus_customer_branch");
            }
            if (employeeData.getList().size() > 0) {
                List<Map<String,Object>> employeeList=new ArrayList<>();
                for (EmployeeDataListDto employeeDataListDto : employeeData.getList()) {
                    Map<String,Object> employeeMap=new HashMap<>();
                    employeeMap.put("customer_id",id);
                    employeeMap.put("name",employeeDataListDto.getName());
                    employeeMap.put("title",employeeDataListDto.getTitle());
                    employeeList.add(employeeMap);
                }
                universalMapper.insertBatch(employeeList,"bus_customer_employee");
            }
            if (changeRecordData.getList().size() > 0) {
                List<Map<String,Object>> changeList=new ArrayList<>();
                for (ChangeRecordDataListDto changeRecordDataListDto : changeRecordData.getList()) {
                    Map<String,Object> changeMap=new HashMap<>();
                    changeMap.put("customer_id",id);
                    changeMap.put("change_type",changeRecordDataListDto.getItem());
                    changeMap.put("change_before",changeRecordDataListDto.getBefore());
                    changeMap.put("change_after",changeRecordDataListDto.getAfter());
                    try {
                        changeMap.put("change_date",LocalDate.parse(changeRecordDataListDto.getDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    } catch (Exception e) {
                        changeMap.put("change_date",null);
                    }
                    changeList.add(changeMap);
                }
                universalMapper.insertBatch(changeList,"bus_customer_change_record");
            }
            if (partnerData.getList().size() > 0) {
                List<Map<String,Object>> partnerList=new ArrayList<>();
                for (PartnerDataListDto partnerDataListDto : partnerData.getList()) {
                    Map<String, Object> partnerMap = new HashMap<>();
                    partnerMap.put("customer_id", id);
                    partnerMap.put("partner_name", partnerDataListDto.getPartnerName());
                    partnerMap.put("partner_type", partnerDataListDto.getPartnerType());
                    partnerMap.put("total_should_capital", partnerDataListDto.getTotalShouldCapital());
                    partnerMap.put("total_real_capital", partnerDataListDto.getTotalRealCapital());
                    partnerMap.put("percent", partnerDataListDto.getPercent());
                    partnerList.add(partnerMap);
                }
                universalMapper.insertBatch(partnerList,"bus_customer_partner");
            }
            return AjaxResult.success();
        }
        return AjaxResult.error();
    }
}
