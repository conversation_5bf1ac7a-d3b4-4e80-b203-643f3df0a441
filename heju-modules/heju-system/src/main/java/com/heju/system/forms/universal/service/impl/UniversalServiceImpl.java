package com.heju.system.forms.universal.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.heju.common.core.constant.basic.SqlConstants;
import com.heju.common.core.utils.core.NumberUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.redis.service.RedisService;
import com.heju.common.security.utils.SecurityUtils;
import com.heju.system.entity.domain.dto.*;
import com.heju.system.entity.domain.model.CompanyBaseInfoApiConverter;
import com.heju.system.entity.domain.po.SysEntityPo;
import com.heju.system.entity.mapper.SysEntityMapper;
import com.heju.system.forms.cascade.manager.ISysCascadeManager;
import com.heju.system.forms.field.domain.dto.SysFieldDto;
import com.heju.system.forms.field.domain.merge.SysFieldRoleMerge;
import com.heju.system.forms.field.domain.query.SysFieldQuery;
import com.heju.system.forms.field.mapper.merge.SysFieldRoleMergeMapper;
import com.heju.system.forms.field.service.ISysFieldService;
import com.heju.system.forms.option.domain.po.SysOptionPo;
import com.heju.system.forms.option.mapper.SysOptionMapper;
import com.heju.system.forms.optionValue.domain.po.SysOptionValuePo;
import com.heju.system.forms.optionValue.mapper.SysOptionValueMapper;
import com.heju.system.forms.sheet.domain.dto.SysSheetDto;
import com.heju.system.forms.sheet.service.ISysSheetService;
import com.heju.system.forms.universal.domain.query.UniversalQuery;
import com.heju.system.forms.universal.mapper.UniversalMapper;
import com.heju.system.forms.universal.service.UniversalService;
import com.heju.system.utils.*;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 级联管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class UniversalServiceImpl  implements UniversalService {

    @Resource
    private ISysFieldService fieldService;

    @Resource
    private ISysSheetService sheetService;

    @Resource
    private UniversalMapper universalMapper;

    @Resource
    private SysOptionValueMapper optionValueMapper;

    @Resource
    private SysFieldRoleMergeMapper fieldRoleMergeMapper;

    @Resource
    private SysOptionMapper optionMapper;

    @Resource
    private SysEntityMapper sysEntityMapper;

    @Resource
    private ISysCascadeManager sysCascadeManager;

    @Resource
    private RedisService redisService;

    @Resource
    private RedisTemplate redisTemplate;

    /**
     * 根据表单id获取可见字段列表
     * @param sheetId 表单id
     * @return 字段列表
     */
    public List<SysFieldDto> getFieldBySheetId(Long sheetId){
        //查询角色可见字段
        List<SysFieldDto> sysFieldPoList;
        if(SecurityUtils.getUser().isAdmin()){
            sysFieldPoList=fieldService.selectBySheetId(sheetId);
        }else {
            Long[] roleIds = SecurityUtils.getUser().getRoleIds();
            LambdaQueryWrapper<SysFieldRoleMerge> lqw = Wrappers.lambdaQuery();
            lqw.eq(SysFieldRoleMerge::getSheetId, sheetId);
            lqw.in(SysFieldRoleMerge::getRoleId, (Object[]) roleIds);
            List<SysFieldRoleMerge> fieldRoleMerges = fieldRoleMergeMapper.selectList(lqw);
            Set<Long> fieldIds = fieldRoleMerges.stream().map(SysFieldRoleMerge::getFieldId).collect(Collectors.toSet());
            sysFieldPoList= fieldService.selectListByIds(fieldIds);
            List<Long> sheetIds=new ArrayList<>();
            sheetIds.add(sheetId);
            List<SysFieldDto> sysFieldDtos = fieldService.selectPrimaryBySheetIds(sheetIds);
            sysFieldPoList.addAll(sysFieldDtos);
        }
        return sysFieldPoList;
    }

    /**
     * 处理单选、多选、级联、引用类型字段
     * @param sheetFieldMap 源字段
     * @param caseCadeFieldMap 级联字段
     * @param singleFieldMap 单选非业务选项字段
     * @param singleSheetFieldMap 单选业务选项字段
     * @param multiFieldMap 多选非业务选项字段
     * @param multiSheetFieldMap 多选业务选项字段
     * @param optionIdList 选项id
     * @param sheetIdList 业务选项对应表单id
     */
    public void handleByFieldType(Map<String, List<SysFieldDto>> sheetFieldMap,Map<String, List<SysFieldDto>> caseCadeFieldMap,
                                  Map<String, List<SysFieldDto>> singleFieldMap, Map<String, List<SysFieldDto>> singleSheetFieldMap ,
                                  Map<String, List<SysFieldDto>> multiFieldMap , Map<String, List<SysFieldDto>> multiSheetFieldMap,
                                  Set<Long> optionIdList, Set<Long> sheetIdList){
        //处理单选、多选、级联
        for (Map.Entry<String, List<SysFieldDto>> entry : sheetFieldMap.entrySet()) {
            String tableName = entry.getKey();
            List<SysFieldDto> fieldList = entry.getValue();

            // 筛选条件一：级联
            List<SysFieldDto> caseCadeFieldList = fieldList.stream()
                    .filter(f -> FieldTypeConstants.CASCADE.equals(f.getFieldType()))
                    .toList();

            // 筛选条件一：引用
            List<SysFieldDto> quoteFieldList = fieldList.stream()
                    .filter(f -> FieldTypeConstants.QUOTE.equals(f.getFieldType()))
                    .toList();

            // 筛选条件二：单选且业务选项
            List<SysFieldDto> singleSheetFieldList = fieldList.stream()
                    .filter(f -> FieldTypeConstants.SELECT_SINGLE.equals(f.getFieldType()) && Integer.valueOf(3).equals(f.getOptionType()))
                    .toList();

            // 筛选条件三：单选且非业务选项
            List<SysFieldDto> singleFieldList = fieldList.stream()
                    .filter(f -> FieldTypeConstants.SELECT_SINGLE.equals(f.getFieldType()) && !Integer.valueOf(3).equals(f.getOptionType()))
                    .toList();

            // 筛选条件四：多选且业务选项
            List<SysFieldDto> multiSheetFieldList = fieldList.stream()
                    .filter(f -> FieldTypeConstants.SELECT_MULTI.equals(f.getFieldType()) && Integer.valueOf(3).equals(f.getOptionType()))
                    .toList();

            // 筛选条件五：多选且非业务选项
            List<SysFieldDto> multiFieldList = fieldList.stream()
                    .filter(f -> FieldTypeConstants.SELECT_MULTI.equals(f.getFieldType()) && !Integer.valueOf(3).equals(f.getOptionType()))
                    .toList();

            if (!caseCadeFieldList.isEmpty()) {
                caseCadeFieldMap.computeIfAbsent(tableName, k -> new ArrayList<>()).addAll(caseCadeFieldList);
            }

            if (!singleFieldList.isEmpty()) {
                optionIdList.addAll(singleFieldList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
                singleFieldMap.computeIfAbsent(tableName, k -> new ArrayList<>()).addAll(singleFieldList);
            }

            if (!singleSheetFieldList.isEmpty()) {
                sheetIdList.addAll(singleSheetFieldList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
                singleSheetFieldMap.computeIfAbsent(tableName, k -> new ArrayList<>()).addAll(singleSheetFieldList);
            }

            if (!multiFieldList.isEmpty()) {
                optionIdList.addAll(multiFieldList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
                multiFieldMap.computeIfAbsent(tableName, k -> new ArrayList<>()).addAll(multiFieldList);
            }

            if (!multiSheetFieldList.isEmpty()) {
                sheetIdList.addAll(multiSheetFieldList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
                multiSheetFieldMap.computeIfAbsent(tableName, k -> new ArrayList<>()).addAll(multiSheetFieldList);
            }
            if (!quoteFieldList.isEmpty()) {
                Set<Long> fieldIdSet = quoteFieldList.stream().map(SysFieldDto::getQuoteSheetFieldId).collect(Collectors.toSet());
                List<SysFieldDto> fieldQuoteList = fieldService.selectListByIds(fieldIdSet);
                List<SysFieldDto> newFieldList=new ArrayList<>();
                Map<Long, List<SysFieldDto>> quoteFieldListMap = quoteFieldList.stream()
                        .collect(Collectors.groupingBy(SysFieldDto::getQuoteSheetFieldId));
                for (SysFieldDto sysFieldDto : fieldQuoteList) {
                    List<SysFieldDto> quoteFields = quoteFieldListMap.get(sysFieldDto.getId());
                    if (quoteFields != null) {
                        for (SysFieldDto quoteField : quoteFields) {
                            SysFieldDto newField = new SysFieldDto();
                            BeanUtils.copyProperties(sysFieldDto, newField);
                            newField.setApiName(quoteField.getApiName());
                            newFieldList.add(newField);
                        }
                    }
                }
                Map<String, List<SysFieldDto>> newSheetFieldMap=new HashMap<>();
                newSheetFieldMap.put(tableName, newFieldList);
                handleByFieldType(newSheetFieldMap,caseCadeFieldMap,singleFieldMap,singleSheetFieldMap,multiFieldMap,multiSheetFieldMap,optionIdList,sheetIdList);
            }
        }
    }

    @Override
    public AjaxResult list(UniversalQuery query) {
        String sheetApiName = query.getSheetApiName();
        //查询表单id
        SysSheetDto sysSheetDto = sheetService.selectOneByApiName(sheetApiName);
        if(sysSheetDto==null){
            return AjaxResult.error("错误的业务表单");
        }
        Long sheetId=sysSheetDto.getId();
        List<SysFieldDto> sysFieldDtos = getFieldBySheetId(sheetId);
        Map<String, List<SysFieldDto>> sheetFieldMap = new HashMap<>();
        sheetFieldMap.put(sheetApiName,sysFieldDtos);
        String searchField=String.join(",",sysFieldDtos.stream().map(SysFieldDto::getApiName).
                filter(Objects::nonNull).toList()) ;
        String sql="select "+searchField+" from "+sheetApiName;
        //主表数据
        List<Map<String, Object>> sheetApiNameValueList = universalMapper.selectAllList(sql);
        //获取1对1
        SysFieldQuery fieldQuery=new SysFieldQuery();
        fieldQuery.setRelationSheetId(sheetId);
        fieldQuery.setRelationType(NumberUtil.Zero);
        List<SysFieldDto> relationFieldList = fieldService.selectList(fieldQuery);
        Set<Long> collect = relationFieldList.stream().map(SysFieldDto::getSheetId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<SysSheetDto> sheetList = collect.isEmpty() ? new ArrayList<>() : sheetService.selectListByIds(collect);
//        List<SysSheetDto> sheetList = sheetService.selectListByIds(collect);
        Map<Long, String> sheetIdApiNameMap = sheetList.stream().collect(Collectors.toMap(SysSheetDto::getId, SysSheetDto::getApiName, (v1, v2) -> v1));
        Map<String, Map<Object, Map<String, Object>>> subTableIndexMap = new HashMap<>();
        for (SysFieldDto sysFieldDto : relationFieldList) {
            List<SysFieldDto> fieldList = getFieldBySheetId(sysFieldDto.getSheetId());
            String relationField=String.join(",",fieldList.stream().map(SysFieldDto::getApiName).
                    filter(Objects::nonNull).toList()) ;
            String relationSql="select "+relationField+" from "+sheetIdApiNameMap.get(sysFieldDto.getSheetId());
            List<Map<String, Object>> relationSheetApiNameValueList = universalMapper.selectAllList(relationSql);
            Map<Object, Map<String, Object>> indexMap = relationSheetApiNameValueList.stream()
                    .collect(Collectors.toMap(row -> row.get(sysFieldDto.getApiName()), row -> row));
            subTableIndexMap.put(sheetIdApiNameMap.get(sysFieldDto.getSheetId()), indexMap);
            sheetFieldMap.put(sheetIdApiNameMap.get(sysFieldDto.getSheetId()),fieldList);

        }
        List<Map<String, Map<String, Object>>> resultList = sheetApiNameValueList.stream()
                .map(company -> {
                    Map<String, Map<String, Object>> merged = new HashMap<>();
                    Object companyId = company.get("id");
                    // 添加主表数据
                    merged.put(sheetApiName, company);
                    // 添加子表数据
                    for (String tableName : sheetIdApiNameMap.values()) {
                        Map<Object, Map<String, Object>> indexMap = subTableIndexMap.get(tableName);
                        if (indexMap != null && indexMap.containsKey(companyId)) {
                            merged.put(tableName, indexMap.get(companyId));
                        }
                    }
                    return merged;
                }).toList();
        PageUniversalResult<Map<String, Map<String, Object>>> mapPageUniversalResult = PageAndSortUtil.paginateAndSortWithPageInfo(resultList, query.getOrderSort(), query.getAscending(), query.getPage(), query.getPageSize());
        List<Map<String, Map<String, Object>>> records = mapPageUniversalResult.getRecords();

        Map<String, List<SysFieldDto>> caseCadeFieldMap = new HashMap<>();
        Map<String, List<SysFieldDto>> singleFieldMap = new HashMap<>();
        Map<String, List<SysFieldDto>> singleSheetFieldMap = new HashMap<>();
        Map<String, List<SysFieldDto>> multiFieldMap = new HashMap<>();
        Map<String, List<SysFieldDto>> multiSheetFieldMap = new HashMap<>();
        Set<Long> optionIdList=new HashSet<>();
        Set<Long> sheetIdList=new HashSet<>();
        handleByFieldType(sheetFieldMap,caseCadeFieldMap,singleFieldMap,singleSheetFieldMap,multiFieldMap,
                multiSheetFieldMap,optionIdList,sheetIdList);
        Map<Long, String> optionValueMap=new HashMap<>();
        Map<Long,Map<Long, String>> sheetValueMap=new HashMap<>();
        if(optionIdList.size()>0){
            LambdaQueryWrapper<SysOptionValuePo> lqw = Wrappers.lambdaQuery();
            lqw.in(SysOptionValuePo::getOptionId,optionIdList);
            List<SysOptionValuePo> sysOptionValuePoList = optionValueMapper.selectList(lqw);
            optionValueMap = sysOptionValuePoList.stream().collect(Collectors.toMap(SysOptionValuePo::getId, SysOptionValuePo::getName, (v1, v2) -> v1));
        }
        if(sheetIdList.size()>0){
            List<SysSheetDto> sheetDtoList = sheetService.selectListByIds(sheetIdList);
            for (SysSheetDto sheetDto : sheetDtoList) {
                // list不会传入 optionFieldList，使用默认选择字段：id 和 name
                String optionSelectSql = "";
                if (sheetDto.getApiName().equals("sys_user")) {
                    optionSelectSql = "`id`,`nick_name`";
                } else {
                    optionSelectSql = "`id`,`name`";
                }
                List<Map<String, Object>> option = universalMapper.option(sheetDto.getApiName(), optionSelectSql);
                Map<Long, String> optionMap = option.stream()
                        .filter(m -> m.containsKey("id"))
                        .collect(Collectors.toMap(
                                m -> ((Number) m.get("id")).longValue(),
                                m -> {
                                    // 提取除"id"外的第一个字段作为值
                                    return m.entrySet().stream()
                                            .filter(e -> !"id".equals(e.getKey()))
                                            .findFirst()
                                            .map(e -> String.valueOf(e.getValue()))
                                            .orElse("");
                                }
                        ));
                sheetValueMap.put(sheetDto.getId(),optionMap);
            }
        }
        List<Map<String, Map<String, Object>>> newRecords=new ArrayList<>();
        if (records != null && !records.isEmpty()){
            for (Map<String, Map<String, Object>> record : records) {
                Map<String, Map<String, Object>> newRecord=new HashMap<>();
                for (String sheetApiNameString : record.keySet()) {
                    Map<String, Object> stringObjectMap = record.get(sheetApiNameString);
                    if (singleFieldMap.get(sheetApiNameString) != null) {
                        for (SysFieldDto sysFieldDto : singleFieldMap.get(sheetApiNameString)) {
                            Long fieldValue = Long.parseLong(stringObjectMap.get(sysFieldDto.getApiName()).toString());
                            stringObjectMap.put(sysFieldDto.getApiName(),optionValueMap.get(fieldValue));
                        }
                    }
                    if (multiFieldMap.get(sheetApiNameString) != null){
                        for (SysFieldDto sysFieldDto : multiFieldMap.get(sheetApiNameString)) {
                            String fieldValue = stringObjectMap.get(sysFieldDto.getApiName()).toString();
                            List<String> split = Arrays.stream(fieldValue.split(",")).toList();
                            List<String> multiValue=new ArrayList<>();
                            for (String s : split) {
                                Long a=Long.parseLong(s);
                                multiValue.add(optionValueMap.get(a));
                            }
                            stringObjectMap.put(sysFieldDto.getApiName(),multiValue);
                        }
                    }
                    if (singleSheetFieldMap.get(sheetApiNameString) != null){
                        for (SysFieldDto sysFieldDto : singleSheetFieldMap.get(sheetApiNameString)) {
                            Long fieldValue = Long.parseLong(stringObjectMap.get(sysFieldDto.getApiName()).toString());
                            stringObjectMap.put(sysFieldDto.getApiName(),sheetValueMap.get(fieldValue));
                        }
                    }
                    if (multiSheetFieldMap.get(sheetApiNameString) != null){
                        for (SysFieldDto sysFieldDto : multiSheetFieldMap.get(sheetApiNameString)) {
                            String fieldValue = stringObjectMap.get(sysFieldDto.getApiName()).toString();
                            List<String> split = Arrays.stream(fieldValue.split(",")).toList();
                            List<String> multiValue=new ArrayList<>();
                            for (String s : split) {
                                Long a=Long.parseLong(s);
                                multiValue.add(sheetValueMap.get(sysFieldDto.getOptionId()).get(a));
                            }
                            stringObjectMap.put(sysFieldDto.getApiName(),String.join(",",multiValue));
                        }
                    }
                    if (caseCadeFieldMap.get(sheetApiNameString) != null){
                        for (SysFieldDto fieldDto : caseCadeFieldMap.get(sheetApiNameString)) {
                            String fieldValue = stringObjectMap.get(fieldDto.getApiName()).toString();
                            List<String> fieldValueList = Arrays.stream(fieldValue.split("-")).toList();
                            LambdaQueryWrapper<SysOptionValuePo> lqw = Wrappers.lambdaQuery();
                            lqw = Wrappers.lambdaQuery();
                            lqw.in(SysOptionValuePo::getId,fieldValueList);
                            List<SysOptionValuePo> sysOptionValuePos = optionValueMapper.selectList(lqw);
//                    Map<String, String> stringMap = sysOptionValuePos.stream().collect(Collectors.toMap(SysOptionValuePo::getApiName, SysOptionValuePo::getName, (v1, v2) -> v1));
                            Map<Long, String> stringMap = sysOptionValuePos.stream().collect(Collectors.toMap(SysOptionValuePo::getId, SysOptionValuePo::getName, (v1, v2) -> v1));
                            List<String> optionValueList=new ArrayList<>();
                            for (String s : fieldValueList) {
                                Long id = Long.valueOf(s);
                                optionValueList.add(stringMap.get(id));
                            }
                            stringObjectMap.put(fieldDto.getApiName(),String.join("-",optionValueList));
                        }
                    }
                    newRecord.put(sheetApiNameString,stringObjectMap);
                }
                newRecords.add(newRecord);
            }
        }
        mapPageUniversalResult.setRecords(newRecords);
        return AjaxResult.success(mapPageUniversalResult);
    }

    @Override
    public AjaxResult addFieldList(UniversalQuery query) {
        //查询表单id
        SysSheetDto sysSheetDto = sheetService.selectOneByApiName(query.getSheetApiName());
        if(sysSheetDto==null){
            return AjaxResult.error("错误的业务表单");
        }
        List<SysSheetDto> sheetList=new ArrayList<>();
        sheetList.add(sysSheetDto);
        Map<String, Map<String, Object>> stringMapMap = buildSheetFieldTree(sheetList);
        return AjaxResult.success(stringMapMap);
    }

    /**
     * 递归查出关联的所有表
     * @param sheetList
     * @return
     */
    public Map<String,Map<String,Object>> buildSheetFieldTree(List<SysSheetDto> sheetList){
        Map<String, Map<String, Object>> resultMap = new LinkedHashMap<>();
        for (SysSheetDto sysSheetDto : sheetList) {
            Map<String, Object> currentNode = new LinkedHashMap<>();
            List<SysFieldDto> currentFields = switchFieldType(sysSheetDto.getId());
            currentNode.put("fields", currentFields);
            SysFieldQuery fieldQuery=new SysFieldQuery();
            fieldQuery.setRelationSheetId(sysSheetDto.getId());
            List<SysFieldDto> relationFieldList = fieldService.selectList(fieldQuery);
            if (relationFieldList != null && !relationFieldList.isEmpty()) {
                Map<Integer, List<SysFieldDto>> groupRelationType = relationFieldList.stream()
                        .collect(Collectors.groupingBy(SysFieldDto::getRelationType));
                // 一对一 判空
                Set<Long> oneCollect = new HashSet<>();
                if (groupRelationType.get(RelationTypeConstants.ONE) != null) {
                    oneCollect = groupRelationType.get(RelationTypeConstants.ONE).stream().map(SysFieldDto::getSheetId).filter(Objects::nonNull).collect(Collectors.toSet());
                }
                // 一对多 判空
                Set<Long> manyCollect = new HashSet<>();
                if (groupRelationType.get(RelationTypeConstants.MANY) != null) {
                    manyCollect = groupRelationType.get(RelationTypeConstants.MANY).stream().map(SysFieldDto::getSheetId).filter(Objects::nonNull).collect(Collectors.toSet());
                }
                Set<Long> totalCollect=new HashSet<>(oneCollect);
                totalCollect.addAll(manyCollect);
                List<SysSheetDto> sheetDtoList = sheetService.selectListByIds(totalCollect);
                Map<Long, SysSheetDto> sheetDtoMap = sheetDtoList.stream()
                        .collect(Collectors.toMap(SysSheetDto::getId, Function.identity()));
                // 递归子表结构
                Map<String, Map<String, Object>> childMap = buildSheetFieldTree(sheetDtoList);
                // 构造子表节点映射
                Map<String, Map<String, Object>> subMap = new LinkedHashMap<>();
                for (Long sheetId : oneCollect) {
                    SysSheetDto child = sheetDtoMap.get(sheetId);
                    String key = child.getApiName() + "|" + child.getName();
                    if (childMap.containsKey(key)) {
                        subMap.put(key, childMap.get(key));
                    }
                }
                for (Long sheetId : manyCollect) {
                    SysSheetDto child = sheetDtoMap.get(sheetId);
                    String key = child.getApiName() + "|" + child.getName() + "|List";
                    if (childMap.containsKey(child.getApiName() + "|" + child.getName())) {
                        subMap.put(key, childMap.get(child.getApiName() + "|" + child.getName()));
                    }
                }
                if (!subMap.isEmpty()) {
                    currentNode.put("children", subMap);
                }
            }
            // 当前节点的 key
            String nodeKey = sysSheetDto.getApiName() + "|" + sysSheetDto.getName();
            resultMap.put(nodeKey, currentNode);
        }
        return resultMap;
    }


    @Override
    public AjaxResult searchFieldList(UniversalQuery query) {
        SysSheetDto sysSheetDto = sheetService.selectOneByApiName(query.getSheetApiName());
        if(sysSheetDto==null){
            return AjaxResult.error("错误的业务表单");
        }
        List<SysFieldDto> fieldBySheetId = getFieldBySheetId(sysSheetDto.getId());
        Map<String,List<SysFieldDto>> result=new HashMap<>();
        result.put(query.getSheetApiName(),fieldBySheetId);
        //获取1对1
        SysFieldQuery fieldQuery=new SysFieldQuery();
        fieldQuery.setRelationSheetId(sysSheetDto.getId());
        fieldQuery.setRelationType(NumberUtil.Zero);
        List<SysFieldDto> relationFieldList = fieldService.selectList(fieldQuery);
        Set<Long> collect = relationFieldList.stream().map(SysFieldDto::getSheetId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<SysSheetDto> sheetList = sheetService.selectListByIds(collect);
        Map<Long, String> sheetIdApiNameMap = sheetList.stream().collect(Collectors.toMap(SysSheetDto::getId, SysSheetDto::getApiName, (v1, v2) -> v1));
        List<SysFieldDto> sysFieldDtoList = getFieldByRoleIds(collect.stream().toList());
        Map<Long, List<SysFieldDto>> sheetFieldMap = sysFieldDtoList.stream().collect(Collectors.groupingBy(SysFieldDto::getSheetId));
        for (Long sheetId : sheetIdApiNameMap.keySet()) {
            result.put(sheetIdApiNameMap.get(sheetId),sheetFieldMap.get(sheetId));
        }
        return AjaxResult.success(result);
    }

    public Map<SysSheetDto,List<SysFieldDto>> getListFieldByQueryGroupBySheet(List<SysFieldDto> relationFieldList){
        Set<Long> collect = relationFieldList.stream().map(SysFieldDto::getSheetId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<SysFieldDto> fieldByRoleIds = getFieldByRoleIds(collect.stream().toList());
        Map<Long, List<SysFieldDto>> fieldMap = fieldByRoleIds.stream()
                .collect(Collectors.groupingBy(SysFieldDto::getSheetId));
        Map<SysSheetDto,List<SysFieldDto>> sysSheetDtoListMap=new HashMap<>();
        List<SysSheetDto> sheetDtoList = sheetService.selectListByIds(collect);
        Map<Long, SysSheetDto> sheetMap = sheetDtoList.stream().collect(Collectors.toMap(SysSheetDto::getId, Function.identity()));
        for (Long sheetMapId : sheetMap.keySet()) {
            sysSheetDtoListMap.put(sheetMap.get(sheetMapId),fieldMap.get(sheetMapId));
        }
        return sysSheetDtoListMap;
    }

    /**
     * 根据表单id获取可见字段列表
     * @param sheetIds 表单id集合
     * @return 字段列表
     */
    public List<SysFieldDto> getFieldByRoleIds(List<Long> sheetIds){
        //查询角色可见字段
        List<SysFieldDto> sysFieldPoList;
        if(SecurityUtils.getUser().isAdmin()){
            sysFieldPoList=fieldService.selectBySheetIds(sheetIds);
        }else {
            Long[] roleIds = SecurityUtils.getUser().getRoleIds();
            LambdaQueryWrapper<SysFieldRoleMerge> lqw = Wrappers.lambdaQuery();
            lqw.in(SysFieldRoleMerge::getSheetId, sheetIds);
            lqw.in(SysFieldRoleMerge::getRoleId, (Object[]) roleIds);
            List<SysFieldRoleMerge> fieldRoleMerges = fieldRoleMergeMapper.selectList(lqw);
            Set<Long> fieldIds = fieldRoleMerges.stream().map(SysFieldRoleMerge::getFieldId).collect(Collectors.toSet());
            sysFieldPoList= fieldService.selectListByIds(fieldIds);
            List<SysFieldDto> sysFieldDtos = fieldService.selectPrimaryBySheetIds(sheetIds);
            sysFieldPoList.addAll(sysFieldDtos);
        }
        return sysFieldPoList;
    }

    public List<SysFieldDto> switchFieldType(Long sheetId){
        List<Long> sheetIds=new ArrayList<>();
        sheetIds.add(sheetId);
        List<SysFieldDto> sysFieldDtos = getFieldByRoleIds(sheetIds);
        Map<String, List<SysFieldDto>> groupFieldType = sysFieldDtos.stream()
                .collect(Collectors.groupingBy(SysFieldDto::getFieldType));
        setGroupFieldType(groupFieldType);
        return groupFieldType.values().stream().flatMap(List::stream).toList();
    }

    public void setGroupFieldType(Map<String, List<SysFieldDto>> groupFieldType){
        for (String key : groupFieldType.keySet()) {
            List<SysFieldDto> sysFieldList = groupFieldType.get(key);
            switch (key) {
                case FieldTypeConstants.SELECT_SINGLE , FieldTypeConstants.SELECT_MULTI-> {
//                    List<SysFieldDto> optionList = sysFieldList.stream().filter(sysFieldDto ->
//                            sysFieldDto.getOptionType() != NumberUtil.Three).toList();
                    List<SysFieldDto> optionList = Optional.ofNullable(sysFieldList)
                            .orElse(Collections.emptyList()).stream()
                            .filter(sysFieldDto -> !Objects.equals(sysFieldDto.getOptionType(), 3))
                            .toList();
                    if(optionList.size()>0){
                        Set<Long> optionIdList = optionList.stream().map(SysFieldDto::getOptionId).filter(Objects::nonNull).collect(Collectors.toSet());
                        LambdaQueryWrapper<SysOptionValuePo> lqw = Wrappers.lambdaQuery();
                        lqw.in(SysOptionValuePo::getOptionId, optionIdList);
                        List<SysOptionValuePo> sysOptionValuePoList = optionValueMapper.selectList(lqw);
                        Map<Long, List<SysOptionValuePo>> collect = sysOptionValuePoList.stream()
                                .collect(Collectors.groupingBy(SysOptionValuePo::getOptionId));
                        for (SysFieldDto fieldDto : sysFieldList) {
                            fieldDto.setOptionValuePoList(collect.get(fieldDto.getOptionId()));
                        }
                    }
                }
                case FieldTypeConstants.CASCADE -> {
                    List<Long> casecadeIdList = sysFieldList.stream().map(SysFieldDto::getCascadeId).filter(Objects::nonNull).toList();
                    Map<Long, List<Tree<String>>> casecadeTreeMap=new HashMap<>();
                    for (Long casecadeId : casecadeIdList) {
                        List<Tree<String>> trees = sysCascadeManager.selectCascadeRelation(casecadeId);
                        casecadeTreeMap.put(casecadeId,trees);
                    }
                    for (SysFieldDto fieldDto : sysFieldList) {
                        fieldDto.setCasecadeTreeList(casecadeTreeMap.get(fieldDto.getCascadeId()));
                    }
                }
            }
        }
    }
    @Override
    public AjaxResult getInfo(UniversalQuery query) {
        String sheetApiName = query.getSheetApiName();
        //查询表单id
        SysSheetDto sysSheetDto = sheetService.selectOneByApiName(sheetApiName);
        if(sysSheetDto==null){
            return AjaxResult.error("错误的业务表单");
        }
        Long sheetId=sysSheetDto.getId();
        List<SysFieldDto> sysFieldDtos = getFieldBySheetId(sheetId);
        Map<String, List<SysFieldDto>> sheetFieldMap = new HashMap<>();
        sheetFieldMap.put(sheetApiName,sysFieldDtos);
        String searchField=String.join(",",sysFieldDtos.stream().map(SysFieldDto::getApiName).
                filter(Objects::nonNull).toList()) ;
        String sql="select "+searchField+" from "+sheetApiName;
        //主表数据
        Map<String, Object> sheetApiNameValueMap = universalMapper.selectMapById(sql,query.getId());
        Map<String,Object> result=new HashMap<>();
        result.put(sheetApiName,sheetApiNameValueMap);
        //获取1对1
        SysFieldQuery fieldQuery=new SysFieldQuery();
        fieldQuery.setRelationSheetId(sheetId);
        List<SysFieldDto> relationFieldList = fieldService.selectList(fieldQuery);
        Map<Integer, List<SysFieldDto>> relationGroup = relationFieldList.stream()
                .collect(Collectors.groupingBy(SysFieldDto::getRelationType));
        Set<Long> collect = relationFieldList.stream().map(SysFieldDto::getSheetId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<SysSheetDto> sheetList = sheetService.selectListByIds(collect);
        List<SysFieldDto> fieldByRoleIds = getFieldByRoleIds(collect.stream().toList());
        Map<Long, List<SysFieldDto>> fieldGroup = fieldByRoleIds.stream()
                .collect(Collectors.groupingBy(SysFieldDto::getSheetId));
        Map<Long, String> sheetIdApiNameMap = sheetList.stream().collect(Collectors.toMap(SysSheetDto::getId, SysSheetDto::getApiName, (v1, v2) -> v1));
        for (SysFieldDto sysFieldDto : relationGroup.getOrDefault(NumberUtil.Zero, Collections.emptyList())) {
            List<SysFieldDto> fieldList = fieldGroup.get(sysFieldDto.getSheetId());
            String relationField=String.join(",",fieldList.stream().map(SysFieldDto::getApiName).
                    filter(Objects::nonNull).toList()) ;
            String relationSql="select "+relationField+" from "+sheetIdApiNameMap.get(sysFieldDto.getSheetId());
            Map<String, Object> relationSheetApiNameValueMap = universalMapper.selectByForeignKey(relationSql,query.getId(),sysFieldDto.getApiName());
            result.put(sheetIdApiNameMap.get(sysFieldDto.getSheetId()),relationSheetApiNameValueMap);
        }
        for (SysFieldDto sysFieldDto : relationGroup.getOrDefault(NumberUtil.One, Collections.emptyList())) {
            List<SysFieldDto> fieldList =  fieldGroup.get(sysFieldDto.getSheetId());
            String relationField=String.join(",",fieldList.stream().map(SysFieldDto::getApiName).
                    filter(Objects::nonNull).toList()) ;
            String relationSql="select "+relationField+" from "+sheetIdApiNameMap.get(sysFieldDto.getSheetId());
            List<Map<String, Object>> relationSheetApiNameValueMap = universalMapper.selectListByForeignKey(relationSql,query.getId(),sysFieldDto.getApiName());
            result.put(sheetIdApiNameMap.get(sysFieldDto.getSheetId())+"|List",relationSheetApiNameValueMap);
        }
        return AjaxResult.success(result);
    }

    @Override
    public AjaxResult add(UniversalQuery query) {
        JSONObject jsonObject = JSON.parseObject(query.getUniversalJson());
        if(query.getAddType()==1){
            return customerAdd(jsonObject);
        }else if(query.getAddType()==2){
            return normalAdd(jsonObject,query.getSheetApiName());
        }else if(query.getAddType()==3){
            return onlyAdd(jsonObject, query.getSheetApiName() ,query.getForeignKey());
        }
        return AjaxResult.error();
    }

    @Override
    public AjaxResult getInfoByCreditNo(UniversalQuery query) {
        String creditNo = query.getCreditNo();
        String sheetApiName = query.getSheetApiName();
        // 查询 传入的 credit_no 是否存在于 sys_base_entity
        Map<String, Object> entityMap = universalMapper.select(creditNo);
        // redis 查 credit_no
        @SuppressWarnings("unchecked")
        Map<String, Object> redisData = (Map<String, Object>) redisTemplate.opsForHash().entries(creditNo);
        if (entityMap != null && !entityMap.isEmpty()) {
            // 实体存在于数据库
            SysSheetDto sysSheetDto = sheetService.selectOneByApiName(sheetApiName);
            List<SysFieldDto> fieldByRoleIds = getFieldByRoleIds(Collections.singletonList(sysSheetDto.getId()));
            String searchField = String.join(",", fieldByRoleIds.stream().map(SysFieldDto::getApiName).filter(Objects::nonNull).toList());
            String mainSelectSql = "select " + searchField + " from " + sheetApiName;
            String whereField = "credit_no";
            if (sheetApiName.equals("bus_taxation_info")) whereField = "taxpayer_no";
            // 主表
            Map<String, Object> mainTableMap = universalMapper.selectByCreditNo(mainSelectSql, whereField, creditNo);
            Map<String, Object> result = new HashMap<>();
            result.put(sheetApiName, mainTableMap);
            // 处理子表
            SysFieldQuery fieldQuery = new SysFieldQuery();
            // 用主表id查关联表
            fieldQuery.setRelationSheetId(sysSheetDto.getId());
            List<SysFieldDto> relationFieldList = fieldService.selectList(fieldQuery);
            // relationType 字段用于标识关联类型 apiName 是子表中的外键名
            Map<Integer, List<SysFieldDto>> relationTypeGroup = relationFieldList.stream().collect(Collectors.groupingBy(SysFieldDto::getRelationType));
            // 关联表单集合
            Set<Long> collect = relationFieldList.stream().map(SysFieldDto::getSheetId).filter(Objects::nonNull).collect(Collectors.toSet());
            List<SysSheetDto> relationSheetList = sheetService.selectListByIds(collect);
            // 映射 sheetId -> sheetApiName
            Map<Long, String> sheetIdApiNameMap = relationSheetList.stream().collect(Collectors.toMap(SysSheetDto::getId, SysSheetDto::getApiName, (v1, v2) -> v1));
            // 关联表单字段集合（角色可见）
            List<SysFieldDto> relationSheetFieldByRoleIds = getFieldByRoleIds(collect.stream().toList());
            // 角色可见字段按表单分组
            Map<Long, List<SysFieldDto>> relationFieldGroup = relationSheetFieldByRoleIds.stream().collect(Collectors.groupingBy(SysFieldDto::getSheetId));
            for (SysFieldDto sysFieldDto : relationTypeGroup.getOrDefault(NumberUtil.Zero, Collections.emptyList())) {
                List<SysFieldDto> fieldDtoList = relationFieldGroup.get(sysFieldDto.getSheetId());
                String relationTableField = String.join(",", fieldDtoList.stream().map(SysFieldDto::getApiName).filter(Objects::nonNull).toList());
                String relationSql = "select " + relationTableField + " from " + sheetIdApiNameMap.get(sysFieldDto.getSheetId());
                Map<String, Object> relationSheetOneMap = universalMapper.selectByForeignKey(relationSql, (Long) mainTableMap.get("id"), sysFieldDto.getApiName());
                result.put(sheetIdApiNameMap.get(sysFieldDto.getSheetId()), relationSheetOneMap);
            }
            for (SysFieldDto sysFieldDto : relationTypeGroup.getOrDefault(NumberUtil.One, Collections.emptyList())) {
                List<SysFieldDto> fieldDtoList = relationFieldGroup.get(sysFieldDto.getSheetId());
                String relationTableField = String.join(",", fieldDtoList.stream().map(SysFieldDto::getApiName).filter(Objects::nonNull).toList());
                String relationSql = "select " + relationTableField + " from " + sheetIdApiNameMap.get(sysFieldDto.getSheetId());
                List<Map<String, Object>> relationSheetListMap = universalMapper.selectListByForeignKey(relationSql, (Long) mainTableMap.get("id"), sysFieldDto.getApiName());
                result.put(sheetIdApiNameMap.get(sysFieldDto.getSheetId()) + "|List", relationSheetListMap);
            }
            return AjaxResult.success(result);
        } else if (!redisData.isEmpty()) {
            addFromRedis(creditNo, sheetApiName);
            return AjaxResult.success(redisData);
        } else {
            // 1. 调用第三方 API 获取原始数据
            CompanyBaseInfoApiDto companyBaseInfoApiDto = CompanyBaseInfoUtil.getCompanyBaseInfo(creditNo);
            if (companyBaseInfoApiDto == null || companyBaseInfoApiDto.getName() == null) {
                return AjaxResult.error("统一社会信用代码错误，未找到该企业");
            }
            SysEntityPo sysEntityPo = CompanyBaseInfoApiConverter.companyBaseInfo2Entity(companyBaseInfoApiDto);
            Map<String, Object> stringObjectMap = convertEntityToRedisMap(sysEntityPo, sheetApiName);
            redisService.setCacheMap(creditNo, stringObjectMap);
//            addFromRedis(creditNo, sheetApiName);
            return AjaxResult.success(stringObjectMap);
        }
    }

    /**
     * 将 SysEntityPo 实体对象转换为 Redis Hash 所需的 Map 结构。
     *
     * @param sysEntityPo 完整的实体对象，包含主表和子表数据
     * @param mainTableName 主表名
     * @return 包含所有表数据的 Map
     */
    private Map<String, Object> convertEntityToRedisMap(SysEntityPo sysEntityPo, String mainTableName) {
        Map<String, Object> redisMap = new HashMap<>();
        // 转换主表数据
        // 手动转换将 SysEntityPo 转换为 Map
        Map<String, Object> mainDataMap = new HashMap<>();
        mainDataMap.put("credit_no", sysEntityPo.getCreditNo());
        mainDataMap.put("name", sysEntityPo.getName());
        mainDataMap.put("legal_person_name", sysEntityPo.getLegalPersonName());
        mainDataMap.put("register_capital", sysEntityPo.getRegisterCapital());
        mainDataMap.put("register_capital_currency", sysEntityPo.getRegisterCapitalCurrency());
//        mainDataMap.put("reg_type", sysEntityPo.getRegType());
        // 处理 regType 存 optionId
        SysOptionPo regType = optionMapper.selectOne(
                Wrappers.<SysOptionPo>query().lambda()
                        .eq(SysOptionPo::getApiName, "reg_type")
                        .last(SqlConstants.LIMIT_ONE));
        LambdaQueryWrapper<SysOptionValuePo> lqw = Wrappers.lambdaQuery();
        lqw.eq(SysOptionValuePo::getOptionId,regType.getId());
        List<SysOptionValuePo> regTypeList = optionValueMapper.selectList(lqw);
        for (SysOptionValuePo sysOptionValuePo : regTypeList) {
            if (sysOptionValuePo.getName().equals(sysEntityPo.getRegType())) {
                mainDataMap.put("reg_type", sysOptionValuePo.getId());
                break;
            }
        }
        mainDataMap.put("business_scope", sysEntityPo.getBusinessScope());
        mainDataMap.put("address", sysEntityPo.getAddress());
//        mainDataMap.put("business_status", sysEntityPo.getBusinessStatus());
        // 处理 businessStatus 存 optionId
        SysOptionPo businessStatus = optionMapper.selectOne(
                Wrappers.<SysOptionPo>query().lambda()
                        .eq(SysOptionPo::getApiName, "business_status")
                        .last(SqlConstants.LIMIT_ONE));
        lqw=Wrappers.lambdaQuery();
        lqw.eq(SysOptionValuePo::getOptionId,businessStatus.getId());
        List<SysOptionValuePo> businessStatusList = optionValueMapper.selectList(lqw);
        for (SysOptionValuePo sysOptionValuePo : businessStatusList) {
            if (sysOptionValuePo.getName().equals(sysEntityPo.getBusinessStatus())) {
                mainDataMap.put("business_status", sysOptionValuePo.getId());
                break;
            }
        }
        mainDataMap.put("business_term", sysEntityPo.getBusinessTerm());
        mainDataMap.put("belong_org", sysEntityPo.getBelongOrg());
        mainDataMap.put("register_no", sysEntityPo.getRegisterNo());
        mainDataMap.put("start_date", sysEntityPo.getStartDate());
        redisMap.put(mainTableName, mainDataMap);

        // 转换子表数据 一对多关系，将List直接放入
        if (sysEntityPo.getBranchPoList() != null && !sysEntityPo.getBranchPoList().isEmpty()) {
            redisMap.put("bus_entity_branch|List", sysEntityPo.getBranchPoList());
        }
        if (sysEntityPo.getEmployeePoList() != null && !sysEntityPo.getEmployeePoList().isEmpty()) {
            redisMap.put("bus_entity_employee|List", sysEntityPo.getEmployeePoList());
        }
        if (sysEntityPo.getChangeRecordPoList() != null && !sysEntityPo.getChangeRecordPoList().isEmpty()) {
            redisMap.put("bus_entity_change_record|List", sysEntityPo.getChangeRecordPoList());
        }
        if (sysEntityPo.getPartnerPoList() != null && !sysEntityPo.getPartnerPoList().isEmpty()) {
            redisMap.put("bus_entity_partner|List", sysEntityPo.getPartnerPoList());
        }
        return redisMap;
    }

    /**
     * 将 Redis 数据同步到数据库中
     *
     * @param creditNo     统一社会信用代码
     * @param sheetApiName 主表的 API 名称
     */
    @DSTransactional
    public void addFromRedis(String creditNo, String sheetApiName) {
        // 使用雪花算法生成主表的 ID
        Snowflake snowflake = IdUtil.getSnowflake(1, 0);
        Long mainId = snowflake.nextId();
        Map<String, Object> mainTableData = redisService.getCacheMapValue(creditNo, sheetApiName);
        String mainTableDataStr = JSONObject.toJSONString(mainTableData);
        Integer mainFlag = insertSql(mainTableDataStr, sheetApiName, mainId, null);
        if (mainFlag == null || mainFlag <= 0) {
            AjaxResult.error("新增主表记录失败：" + sheetApiName);
            return;
        }
        // 2. 获取所有关联表信息，并逐个从 Redis 中取出数据并插入
        // 在循环中，直接根据子表名 subTableName 获取数据
        SysSheetDto sysSheetDto = sheetService.selectOneByApiName(sheetApiName);
        SysFieldQuery sysFieldQuery = new SysFieldQuery();
        sysFieldQuery.setRelationSheetId(sysSheetDto.getId());
        List<SysFieldDto> relationFieldList = fieldService.selectList(sysFieldQuery);
        // 获取所有关联表的 API 名称
        Set<Long> collect = relationFieldList.stream().map(SysFieldDto::getSheetId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<SysSheetDto> relationSheetList = sheetService.selectListByIds(collect);
        Map<Long, String> sheetIdApiNameMap = relationSheetList.stream().collect(Collectors.toMap(SysSheetDto::getId, SysSheetDto::getApiName, (v1, v2) -> v1));
        for (SysFieldDto sysFieldDto : relationFieldList) {
            String subTableName = sheetIdApiNameMap.get(sysFieldDto.getSheetId());
            // 获取关联表数据
            Object subValue = redisService.getCacheMapValue(creditNo, subTableName);
            if (subValue == null) continue;
            String subValueStr = JSONObject.toJSONString(subValue);
            // 关联表一对一插入
            if (subValue instanceof Map) {
                Map<String, Object> subMap = (Map<String, Object>) subValue;
                if (!subMap.isEmpty()) {
                    Long subId = snowflake.nextId();
                    Integer subFlag = insertSql(subValueStr, subTableName, subId, mainId);
                    if (subFlag == null || subFlag <= 0) {
                        AjaxResult.error("新增一对一子表记录失败：" + subTableName);
                        return;
                    }
                }
            }
            // 关联表一对多插入
            else if (subValue instanceof List) {
                List<Map<String, Object>> subList = (List<Map<String, Object>>) subValue;
                if (!subList.isEmpty()) {
                    String subTableNameWithList = subTableName + "|List";
                    Long subId = snowflake.nextId();
                    Integer subFlag = insertSql(subValueStr, subTableNameWithList, subId, mainId);
                    if (subFlag == null || subFlag <= 0) {
                        AjaxResult.error("新增一对多子表记录失败：" + subTableName);
                        return;
                    }
                }
            }
        }
        AjaxResult.success("数据从Redis成功同步到数据库");
    }

    @Override
    public AjaxResult edit(UniversalQuery query) {
        JSONObject jsonObject = JSON.parseObject(query.getUniversalJson());
        Long id = query.getId();
        String sheetApiName = query.getSheetApiName();
        Long sheetId = sheetService.selectOneByApiName(sheetApiName).getId();
        Map<String, Object> beforeObjectMap = universalMapper.selectById(id, sheetApiName);
        if (universalMapper.update(jsonObject, id, sheetApiName) > 0) {
            Map<String, Object> afterObjectMap = universalMapper.selectById(id, sheetApiName);
            Set<String> fieldApiNameSet = jsonObject.keySet();
            // fieldList 解析 universalJson 后， 拿到要更新的字段
            List<SysFieldDto> fieldList = fieldService.selectByApiNames(fieldApiNameSet.stream().toList(), sheetId);
            List<Long> ids = fieldList.stream().map(SysFieldDto::getId).toList();
            List<SysFieldDto> quoteFieldList = fieldService.selectQuoteByIds(ids);
            // 引用规则
            List<SysFieldDto> refercencingFieldList = fieldService.selectReferencingByIds(ids);
            List<SysFieldDto> referencedFieldList = fieldService.selectReferencedByIds(ids);
            for (SysFieldDto sysFieldDto : fieldList) {
                //修改的字段里有引用规则当前表字段的
                for (SysFieldDto fieldDto : refercencingFieldList) {
                    if (Objects.equals(sysFieldDto.getId(), fieldDto.getReferencingFieldId())) {
                        String searchFieldApiName = fieldService.selectById(fieldDto.getQuoteSheetFieldId()).getApiName();
                        String searchSheetApiName = sheetService.selectById(fieldDto.getQuoteSheetId()).getApiName();
                        Object searchValue = afterObjectMap.get(sysFieldDto.getApiName());
                        String quoteRuleFieldApiName = fieldService.selectById(fieldDto.getReferencedFieldId()).getApiName();
                        String quoteReferencingFieldValue = fieldService.selectById(sysFieldDto.getReferencingFieldId()).getApiName();
                        Object o = universalMapper.selectQuoteFieldVale(searchSheetApiName, searchFieldApiName, quoteRuleFieldApiName, searchValue);
                        universalMapper.updateQuoteField(fieldDto.getApiName(), sheetApiName, o, quoteReferencingFieldValue, searchValue);
                    }
                }
                //修改的字段里有引用规则引用表字段的
                for (SysFieldDto fieldDto : referencedFieldList) {
                    if (Objects.equals(sysFieldDto.getId(), fieldDto.getReferencedFieldId())) {
                        String searchFieldApiName = fieldService.selectById(fieldDto.getReferencingFieldId()).getApiName();
                        String searchSheetApiName = sheetService.selectById(fieldDto.getSheetId()).getApiName();
                        Object updateValue = afterObjectMap.get(sysFieldDto.getApiName());
                        Object updateBeforeValue = beforeObjectMap.get(sysFieldDto.getApiName());
                        universalMapper.updateQuoteField(searchFieldApiName, searchSheetApiName, updateValue, searchFieldApiName, updateBeforeValue);
                    }
                }
                //修改的字段里被引用
                for (SysFieldDto fieldDto : quoteFieldList) {
                    if (Objects.equals(sysFieldDto.getId(), fieldDto.getQuoteSheetFieldId())) {
                        String updateFieldApiName = fieldDto.getApiName();
                        String updateSheetApiName = sheetService.selectById(fieldDto.getSheetId()).getApiName();
                        Object updateValue = afterObjectMap.get(sysFieldDto.getApiName());
                        String quoteRuleFieldApiName = fieldService.selectById(fieldDto.getReferencingFieldId()).getApiName();
                        Object quoteRuleFieldValue = afterObjectMap.get(fieldService.selectById(fieldDto.getReferencedFieldId()).getApiName());
                        universalMapper.updateQuoteField(updateFieldApiName, updateSheetApiName, updateValue, quoteRuleFieldApiName, quoteRuleFieldValue);
                    }
                }
                //修改的字段里有引用字段
                if (Objects.equals(sysFieldDto.getFieldType(), FieldTypeConstants.QUOTE)) {
                    String updateFieldApiName = fieldService.selectById(sysFieldDto.getQuoteSheetFieldId()).getApiName();
                    String updateSheetApiName = sheetService.selectById(sysFieldDto.getQuoteSheetId()).getApiName();
                    Object updateValue = afterObjectMap.get(sysFieldDto.getApiName());
                    String quoteRuleFieldApiName = fieldService.selectById(sysFieldDto.getReferencedFieldId()).getApiName();
                    Object quoteRuleFieldValue = afterObjectMap.get(fieldService.selectById(sysFieldDto.getReferencingFieldId()).getApiName());
                    universalMapper.updateQuoteField(updateFieldApiName, updateSheetApiName, updateValue, quoteRuleFieldApiName, quoteRuleFieldValue);
                    //修改所有引用该字段的数据
                    List<Long> quoteSheetFiledIds = new ArrayList<>();
                    quoteSheetFiledIds.add(sysFieldDto.getQuoteSheetFieldId());
                    List<SysFieldDto> quoteSheetFieldList = fieldService.selectQuoteByIds(quoteSheetFiledIds);
                    String selectSheetApiName = sheetService.selectById(sysFieldDto.getQuoteSheetId()).getApiName();
//                    Map<String, Object> quoteObjectMap =universalMapper.selectByQuote(selectSheetApiName,updateFieldApiName,quoteRuleFieldValue);
                    Map<String, Object> quoteObjectMap = universalMapper.selectByQuote(selectSheetApiName, updateFieldApiName, updateValue);
                    for (SysFieldDto fieldDto : quoteSheetFieldList) {
                        String updateApiName = fieldDto.getApiName();
                        String sheetUpdateApiName = sheetService.selectById(fieldDto.getSheetId()).getApiName();
                        String quoteFieldApiName = fieldService.selectById(fieldDto.getReferencingFieldId()).getApiName();
                        Object quoteFieldValue = quoteObjectMap.get(fieldService.selectById(sysFieldDto.getReferencedFieldId()).getApiName());
                        universalMapper.updateQuoteField(updateApiName, sheetUpdateApiName, updateValue, quoteFieldApiName, quoteFieldValue);
                    }
                }
            }
            return AjaxResult.success();
        } else {
            return AjaxResult.error();
        }
    }

    @Override
    public AjaxResult batchRemove(UniversalQuery query) {
        if(universalMapper.delete(query.getSheetApiName(),query.getIdList())>0){
            return AjaxResult.success();
        }else{
            return AjaxResult.error();
        }
    }

    @Override
    public AjaxResult option(UniversalQuery query) {
        // 默认选择字段：id 和 name
        String selectSql = "`id`,`name`";
        List<String> optionFieldList = query.getOptionFieldList();
        // 如果 optionFieldList 不为空，使用传入字段替代默认
        if (optionFieldList != null && !optionFieldList.isEmpty()) {
            selectSql = String.join(",", optionFieldList);
        }
        SysSheetDto sysSheetDto = sheetService.selectById(query.getSheetId());
        return AjaxResult.success(universalMapper.option(sysSheetDto.getApiName(), selectSql));
    }

    @Override
    public AjaxResult check(UniversalQuery query) {
        // 检查 credit_no 是否存在于 实体表
        List<SysEntityPo> sysEntityPos = sysEntityMapper.select();
        List<String> creditNoList = sysEntityPos.stream().map(SysEntityPo::getCreditNo).toList();
        if (creditNoList.contains(query.getCreditNo())) {
            return AjaxResult.success(creditNoList.indexOf(query.getCreditNo()));
        }
        return AjaxResult.success("");
    }

    public String buildInsertSql(String tableName, JSONObject sheetJson, Long id) {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ").append(tableName).append(" (");

        List<String> fields = new ArrayList<>();
        List<String> values = new ArrayList<>();

        fields.add("id");
//        values.add("'" + id + "'");
        values.add(String.valueOf(id));
        // 遍历 JSON 字段
        for (Map.Entry<String, Object> entry : sheetJson.entrySet()) {
            String field = entry.getKey();
            Object value = entry.getValue();

            if (value == null) continue;

            fields.add("`" + field + "`");

            // 判断是否是字符串类型
            if (value instanceof String || value instanceof Date) {
                values.add("'" + value.toString().replace("'", "''") + "'");
            } else {
                values.add(value.toString());
            }
        }

        sql.append(String.join(", ", fields));
        sql.append(") VALUES (");
        sql.append(String.join(", ", values));
        sql.append(");");

        return sql.toString();
    }

    public String buildBatchInsertSql(String tableName, JSONArray sheetArray) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 0);
        if (sheetArray == null || sheetArray.isEmpty()) {
            return null;
        }
        // 第一个对象用于确定字段顺序
        JSONObject firstObj = sheetArray.getJSONObject(0);
        List<String> fields = new ArrayList<>();
        fields.add("id"); // 手动添加主键 ID

        for (String key : firstObj.keySet()) {
            fields.add("`" + key + "`");
        }

        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ").append(tableName)
                .append(" (").append(String.join(", ", fields)).append(") VALUES ");

        List<String> rowValues = new ArrayList<>();

        for (int i = 0; i < sheetArray.size(); i++) {
            JSONObject obj = sheetArray.getJSONObject(i);
            List<String> values = new ArrayList<>();
            // 生成 UUID 主键
//            String uuid = UUID.randomUUID().toString();
//            values.add("'" + uuid + "'");
            Long id = snowflake.nextId();
            values.add(String.valueOf(id));

            for (String key : firstObj.keySet()) {
                Object val = obj.get(key);
                if (val == null) {
                    values.add("NULL");
                } else if (val instanceof String || val instanceof Date) {
                    values.add("'" + val.toString().replace("'", "''") + "'");
                } else {
                    values.add(val.toString());
                }
            }
            rowValues.add("(" + String.join(", ", values) + ")");
        }

        sql.append(String.join(", ", rowValues)).append(";");
        return sql.toString();
    }

    public Integer  insertSql(Object object,String sheetApiName,Long id,Long foreignKey){
        Snowflake snowflake = IdUtil.getSnowflake(1, 0);
        JSONObject sheetJson=new JSONObject();
        JSONArray jsonArray=new JSONArray();
        if(sheetApiName.contains("List")){
            jsonArray = JSONArray.parseArray(object.toString());
            sheetApiName=sheetApiName.replace("List","");
        }else{
            sheetJson = JSONObject.parseObject(object.toString());
        }
        //查询表单id
        SysSheetDto sysSheetDto = sheetService.selectOneByApiName(sheetApiName);
        if(sysSheetDto==null){
            return null;
        }
        SysFieldQuery query=new SysFieldQuery();
        query.setSheetId(sysSheetDto.getId());
        List<SysFieldDto> sysFieldDtos = fieldService.selectList(query);
        JSONArray jsonArrayResult=new JSONArray();
        for (SysFieldDto sysFieldDto : sysFieldDtos) {
            if(Objects.equals(sysFieldDto.getFieldType(), FieldTypeConstants.QUOTE)){
                SysFieldDto quoteFieldDto = fieldService.selectById(sysFieldDto.getQuoteSheetFieldId());
                String quoteSheetApiName = sheetService.selectById(quoteFieldDto.getSheetId()).getApiName();
                String quoteFieldApiName = quoteFieldDto.getApiName();
                String quoteRuleFieldApiName = fieldService.selectById(sysFieldDto.getReferencedFieldId()).getApiName();
                String ruleFieldApiName = fieldService.selectById(sysFieldDto.getReferencingFieldId()).getApiName();
                if(jsonArray.size()>0){
                    for (Object o : jsonArray) {
//                        JSONObject jsonObject=JSONObject.parseObject(JSONObject.toJSONString(o));
                        JSONObject jsonObject=JSONObject.parseObject(o.toString());
                        Object fieldValue = jsonObject.get(ruleFieldApiName);
                        jsonObject.put(sysFieldDto.getApiName(),universalMapper.
                                selectQuoteFieldVale(quoteSheetApiName,quoteFieldApiName,quoteRuleFieldApiName,fieldValue));
                        jsonArrayResult.add(jsonObject);
                    }
                }else{
                    Object fieldValue = sheetJson.get(ruleFieldApiName);
                    sheetJson.put(sysFieldDto.getApiName(),universalMapper.
                            selectQuoteFieldVale(quoteSheetApiName,quoteFieldApiName,quoteRuleFieldApiName,fieldValue));
                }
            }
            if(Objects.equals(sysFieldDto.getFieldType(), FieldTypeConstants.RELATION)){
                if(jsonArray.size()>0){
                    for (Object o : jsonArray) {
//                            JSONObject jsonObject=JSONObject.parseObject(JSONObject.toJSONString(o));
                        JSONObject jsonObject=JSONObject.parseObject(o.toString());
                        jsonObject.put(sysFieldDto.getApiName(),foreignKey);
                        jsonArrayResult.add(jsonObject);
                    }
                }else{
                    sheetJson.put(sysFieldDto.getApiName(),foreignKey);
                }
            }
        }
        String sql="";
        if(jsonArrayResult.size()>0){
            sql= buildBatchInsertSql(sheetApiName, jsonArrayResult);
        }else{
            if(id==null){
//                id=UUID.randomUUID().toString();
                id = snowflake.nextId();
            }
            sql= buildInsertSql(sheetApiName, sheetJson, id);
        }
        return universalMapper.insert(sql);
    }

    public AjaxResult onlyAdd(JSONObject jsonObject,String sheetApiName,Long foreignKey){
//        String id=UUID.randomUUID().toString();
        Snowflake snowflake = IdUtil.getSnowflake(1, 0);
        Long id = snowflake.nextId();
        Integer flag=insertSql(jsonObject.get(sheetApiName), sheetApiName,id,foreignKey);
        if(flag>0) {
            return AjaxResult.success();
        }else{
            return AjaxResult.error();
        }
    }

    public AjaxResult normalAdd(JSONObject jsonObject,String sheetApiName){
//        String id=UUID.randomUUID().toString();
        Snowflake snowflake = IdUtil.getSnowflake(1, 0);
        Long id = snowflake.nextId();
        //id 新增数据id    null 外键
        Integer flag=insertSql(jsonObject.get(sheetApiName), sheetApiName,id,null);
        if(flag==null){
            return AjaxResult.error("新增失败，错误的业务表单");
        }else if(flag>0) {
            for (String apiName : jsonObject.keySet()) {
                if (!apiName.equals(sheetApiName)) {
                    insertSql(jsonObject.get(apiName), apiName, null, id);
                }
            }
            return AjaxResult.success();
        }
        return AjaxResult.error();
    }

    public AjaxResult customerAdd(JSONObject jsonObject){
        //查询阿里api获取工商信息
        CompanyBaseInfoApiDto companyBaseInfoApiDto = CompanyBaseInfoUtil.getCompanyBaseInfo(jsonObject.get("credit_no").toString());

        if (companyBaseInfoApiDto.getName() == null) {
            return AjaxResult.error("统一社会信用代码错误，未找到该企业");
        }
        RegisterDataApiDto registerData = companyBaseInfoApiDto.getRegisterData();
        String legalPersonName=companyBaseInfoApiDto.getLegalPersonName();
        String name=companyBaseInfoApiDto.getName();
        LocalDate startDate ;
        try {
            startDate = LocalDate.parse(companyBaseInfoApiDto.getStartDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        }catch (Exception e){
            startDate=null;
        }
        String code=jsonObject.get("code").toString();
        Long createBy=SecurityUtils.getUserId();
        SysOptionPo regType = optionMapper.selectOne(
                Wrappers.<SysOptionPo>query().lambda()
                        .eq(SysOptionPo::getApiName, "reg_type")
                        .last(SqlConstants.LIMIT_ONE));
        LambdaQueryWrapper<SysOptionValuePo> lqw = Wrappers.lambdaQuery();
        lqw.eq(SysOptionValuePo::getOptionId,regType.getId());
        List<SysOptionValuePo> regTypeList = optionValueMapper.selectList(lqw);
        SysOptionPo businessStatus = optionMapper.selectOne(
                Wrappers.<SysOptionPo>query().lambda()
                        .eq(SysOptionPo::getApiName, "business_status")
                        .last(SqlConstants.LIMIT_ONE));
        lqw=Wrappers.lambdaQuery();
        lqw.eq(SysOptionValuePo::getOptionId,businessStatus.getId());
        List<SysOptionValuePo> businessStatusList = optionValueMapper.selectList(lqw);
        for (SysOptionValuePo sysOptionValuePo : regTypeList) {
            if (sysOptionValuePo.getName().equals(registerData.getRegType())) {
                registerData.setRegType(sysOptionValuePo.getApiName());
                break;
            }
        }
        for (SysOptionValuePo sysOptionValuePo : businessStatusList) {
            if (sysOptionValuePo.getName().equals(registerData.getStatus())) {
                registerData.setStatus(sysOptionValuePo.getApiName());
                break;
            }
        }
        CustomerDto customerDto=new CustomerDto();
        BeanUtils.copyProperties(registerData,customerDto);
        customerDto.setLegalPersonName(legalPersonName);
        customerDto.setName(name);
        customerDto.setStartDate(startDate);
        customerDto.setCode(code);
        customerDto.setCreateBy(createBy);
        if (universalMapper.insertCustomer(customerDto) > 0) {
            Long id = customerDto.getId();
            universalMapper.insertCustomerTaxation(id);
            BranchDataApiDto branchDataApiDto = companyBaseInfoApiDto.getBranchDataApiDto();
            EmployeeDataApiDto employeeData = companyBaseInfoApiDto.getEmployeeData();
            ChangeRecordDataApiDto changeRecordData = companyBaseInfoApiDto.getChangeRecordData();
            PartnerDataApiDto partnerData = companyBaseInfoApiDto.getPartnerData();
            if (branchDataApiDto.getList().size() > 0) {
                List<Map<String,Object>> branchList=new ArrayList<>();
                for (BranchDataListDto branchDataListDto : branchDataApiDto.getList()) {
                    Map<String,Object> branchMap=new HashMap<>();
                    branchMap.put("customer_id",id);
                    branchMap.put("name",branchDataListDto.getName());
                    branchList.add(branchMap);
                }

                universalMapper.insertBatch(branchList,"bus_customer_branch");
            }
            if (employeeData.getList().size() > 0) {
                List<Map<String,Object>> employeeList=new ArrayList<>();
                for (EmployeeDataListDto employeeDataListDto : employeeData.getList()) {
                    Map<String,Object> employeeMap=new HashMap<>();
                    employeeMap.put("customer_id",id);
                    employeeMap.put("name",employeeDataListDto.getName());
                    employeeMap.put("title",employeeDataListDto.getTitle());
                    employeeList.add(employeeMap);
                }
                universalMapper.insertBatch(employeeList,"bus_customer_employee");
            }
            if (changeRecordData.getList().size() > 0) {
                List<Map<String,Object>> changeList=new ArrayList<>();
                for (ChangeRecordDataListDto changeRecordDataListDto : changeRecordData.getList()) {
                    Map<String,Object> changeMap=new HashMap<>();
                    changeMap.put("customer_id",id);
                    changeMap.put("change_type",changeRecordDataListDto.getItem());
                    changeMap.put("change_before",changeRecordDataListDto.getBefore());
                    changeMap.put("change_after",changeRecordDataListDto.getAfter());
                    try {
                        changeMap.put("change_date",LocalDate.parse(changeRecordDataListDto.getDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd")));
                    } catch (Exception e) {
                        changeMap.put("change_date",null);
                    }
                    changeList.add(changeMap);
                }
                universalMapper.insertBatch(changeList,"bus_customer_change_record");
            }
            if (partnerData.getList().size() > 0) {
                List<Map<String,Object>> partnerList=new ArrayList<>();
                for (PartnerDataListDto partnerDataListDto : partnerData.getList()) {
                    Map<String, Object> partnerMap = new HashMap<>();
                    partnerMap.put("customer_id", id);
                    partnerMap.put("partner_name", partnerDataListDto.getPartnerName());
                    partnerMap.put("partner_type", partnerDataListDto.getPartnerType());
                    partnerMap.put("total_should_capital", partnerDataListDto.getTotalShouldCapital());
                    partnerMap.put("total_real_capital", partnerDataListDto.getTotalRealCapital());
                    partnerMap.put("percent", partnerDataListDto.getPercent());
                    partnerList.add(partnerMap);
                }
                universalMapper.insertBatch(partnerList,"bus_customer_partner");
            }
            return AjaxResult.success();
        }
        return AjaxResult.error();
    }


    /**
     * 根据引用ids查询被引用字段信息
     * @param query
     * @return
     */
    @Override
    public AjaxResult getQuoteInfo(UniversalQuery query) {
        //查当前引用字段信息
        List<SysFieldDto> sysFieldDtos = fieldService.selectListByIds(query.getIdList());
        //捞出所有的被引用字段id
        List<Long> quoteSheetFieldIds = sysFieldDtos.stream()
                .map(SysFieldDto::getQuoteSheetFieldId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        //查所有被引用字段信息
        List<SysFieldDto> quoteFieldDtos = fieldService.selectListByIds(quoteSheetFieldIds);
        //单选...处理
        Map<String, List<SysFieldDto>> groupFieldType = quoteFieldDtos.stream()
                .collect(Collectors.groupingBy(SysFieldDto::getFieldType));
        setGroupFieldType(groupFieldType);

        // 先建立 quoteFieldId -> SysFieldDto 的映射，便于快速查找
        Map<Long, SysFieldDto> quoteFieldMap = quoteFieldDtos.stream()
                .collect(Collectors.toMap(SysFieldDto::getId, dto -> dto));

        // 构建：原始字段 id -> 它所引用的字段（SysFieldDto）
        Map<Long, SysFieldDto> idToQuoteMap = sysFieldDtos.stream()
                .collect(Collectors.toMap(
                        SysFieldDto::getId,  // 原始字段 ID
                        field -> {
                            Long quoteId = field.getQuoteSheetFieldId();
                            if (quoteId != null && quoteFieldMap.containsKey(quoteId)) {
                                return quoteFieldMap.get(quoteId); // 返回单个引用字段的列表
                            }
                            return null; // 如果没有引用或未找到，返回空列表
                        },
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ));

        //捞出所有被引用字段的sheetId
        List<Long> quoteSheetIds = sysFieldDtos.stream()
                .map(SysFieldDto::getQuoteSheetId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<SysSheetDto> sysSheetDtos = new ArrayList<>();
        if (!quoteSheetIds.isEmpty()) {
            sysSheetDtos = sheetService.selectListByIds(quoteSheetIds);
        }
        // 构建 sheetId -> apiName 映射
        Map<Long, String> sheetIdToApiNameMap = sysSheetDtos.stream()
                .collect(Collectors.toMap(SysSheetDto::getId, SysSheetDto::getApiName));

        //  为每个 sysFieldDto 找到它的 quoteSheetApiName，并设置到它引用的 quoteFieldDto 上
        sysFieldDtos.forEach(sysField -> {
            Long currentSheetId = sysField.getQuoteSheetId(); // ← 当前字段所在的引用表 ID
            String apiName = sheetIdToApiNameMap.get(currentSheetId);
            if (apiName != null) {
                // 找到它引用的字段
                Long quoteFieldId = sysField.getQuoteSheetFieldId();
                if (quoteFieldId != null && quoteFieldMap.containsKey(quoteFieldId)) {
                    SysFieldDto quoteField = quoteFieldMap.get(quoteFieldId);
                    quoteField.setQuoteSheetApiName(apiName); //  塞入的是当前表的 apiName
                }
            }
        });

        Map<String, Object> result = new HashMap<>();
        result.put("idToQuoteMap",idToQuoteMap);
        return AjaxResult.success(result);
    }
}
