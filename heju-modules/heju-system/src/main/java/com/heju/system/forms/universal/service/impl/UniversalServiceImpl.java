package com.heju.system.forms.universal.service.impl;

import cn.hutool.core.lang.Snowflake;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.util.IdUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.heju.common.core.constant.basic.SqlConstants;
import com.heju.common.core.utils.core.NumberUtil;
import com.heju.common.core.utils.core.StrUtil;
import com.heju.common.core.web.entity.base.BasisEntity;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.redis.service.RedisService;
import com.heju.common.security.utils.SecurityUtils;
import com.heju.system.api.authority.domain.dto.SysRoleDto;
import com.heju.system.entity.domain.dto.*;
import com.heju.system.entity.domain.model.CompanyBaseInfoApiConverter;
import com.heju.system.entity.domain.po.SysEntityPo;
import com.heju.system.forms.cascade.manager.ISysCascadeManager;
import com.heju.system.forms.field.domain.dto.SysFieldDto;
import com.heju.system.forms.field.domain.merge.SysFieldRoleMerge;
import com.heju.system.forms.field.domain.query.SysFieldQuery;
import com.heju.system.forms.field.mapper.merge.SysFieldRoleMergeMapper;
import com.heju.system.forms.field.service.ISysFieldService;
import com.heju.system.forms.option.domain.po.SysOptionPo;
import com.heju.system.forms.option.mapper.SysOptionMapper;
import com.heju.system.forms.optionValue.domain.po.SysOptionValuePo;
import com.heju.system.forms.optionValue.mapper.SysOptionValueMapper;
import com.heju.system.forms.sheet.domain.dto.SysSheetDto;
import com.heju.system.forms.sheet.service.ISysSheetService;
import com.heju.system.forms.universal.domain.po.UniversalFilterPo;
import com.heju.system.forms.universal.domain.query.UniversalQuery;
import com.heju.system.forms.universal.mapper.UniversalMapper;
import com.heju.system.forms.universal.service.UniversalService;
import com.heju.system.utils.*;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 级联管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class UniversalServiceImpl  implements UniversalService {

    @Resource
    private ISysFieldService fieldService;

    @Resource
    private ISysSheetService sheetService;

    @Resource
    private UniversalMapper universalMapper;

    @Resource
    private SysOptionValueMapper optionValueMapper;

    @Resource
    private SysFieldRoleMergeMapper fieldRoleMergeMapper;

    @Resource
    private SysOptionMapper optionMapper;

    @Resource
    private ISysCascadeManager sysCascadeManager;

    @Resource
    private RedisService redisService;
    /**
     * 根据表单id获取可见字段列表
     * @param sheetId 表单id
     * @return 字段列表
     */
    public List<SysFieldDto> getFieldBySheetId(Long sheetId){
        //查询角色可见字段
        List<SysFieldDto> sysFieldPoList;
        if(SecurityUtils.getUser().isAdmin()){
            sysFieldPoList=fieldService.selectBySheetId(sheetId);
        }else {
//            Long[] roleIds = SecurityUtils.getUser().getRoleIds();
            List<SysRoleDto> roles = SecurityUtils.getUser().getRoles();
            List<Long> roleIds = roles.stream().map(SysRoleDto::getId).collect(Collectors.toList());
            LambdaQueryWrapper<SysFieldRoleMerge> lqw = Wrappers.lambdaQuery();
            lqw.eq(SysFieldRoleMerge::getSheetId, sheetId);
//            lqw.in(SysFieldRoleMerge::getRoleId, (Object[]) roleIds);
            lqw.in(SysFieldRoleMerge::getRoleId, roleIds);
            List<SysFieldRoleMerge> fieldRoleMerges = fieldRoleMergeMapper.selectList(lqw);
            Set<Long> fieldIds = fieldRoleMerges.stream().map(SysFieldRoleMerge::getFieldId).collect(Collectors.toSet());
//            sysFieldPoList= fieldService.selectListByIds(fieldIds);
            List<SysFieldDto> sysBusFieldDtos = fieldService.selectListByIds(fieldIds);
            sysFieldPoList = sysBusFieldDtos.stream().sorted(Comparator.comparing(SysFieldDto::getCreateTime).reversed()).collect(Collectors.toList());
            List<Long> sheetIds=new ArrayList<>();
            sheetIds.add(sheetId);
            List<SysFieldDto> sysFieldDtos = fieldService.selectPrimaryBySheetIds(sheetIds);
            sysFieldPoList.addAll(sysFieldDtos);
        }
        return sysFieldPoList;
    }

    /**
     * 处理单选、多选、级联、引用类型字段
     * @param sheetFieldMap 源字段
     * @param caseCadeFieldMap 级联字段
     * @param singleFieldMap 单选非业务选项字段
     * @param singleSheetFieldMap 单选业务选项字段
     * @param multiFieldMap 多选非业务选项字段
     * @param multiSheetFieldMap 多选业务选项字段
     * @param optionIdList 选项id
     * @param sheetIdList 业务选项对应表单id
     */
    public void handleByFieldType(Map<String, List<SysFieldDto>> sheetFieldMap,Map<String, List<SysFieldDto>> caseCadeFieldMap,
                                  Map<String, List<SysFieldDto>> singleFieldMap, Map<String, List<SysFieldDto>> singleSheetFieldMap ,
                                  Map<String, List<SysFieldDto>> multiFieldMap , Map<String, List<SysFieldDto>> multiSheetFieldMap,
                                  Set<Long> optionIdList, Set<Long> sheetIdList){
        //处理单选、多选、级联
        for (Map.Entry<String, List<SysFieldDto>> entry : sheetFieldMap.entrySet()) {
            String tableName = entry.getKey();
            List<SysFieldDto> fieldList = entry.getValue();

            // 筛选条件一：级联
            List<SysFieldDto> caseCadeFieldList = fieldList.stream()
                    .filter(f -> FieldTypeConstants.CASCADE.equals(f.getFieldType()))
                    .toList();

            // 筛选条件一：引用
            List<SysFieldDto> quoteFieldList = fieldList.stream()
                    .filter(f -> FieldTypeConstants.QUOTE.equals(f.getFieldType()))
                    .toList();

            // 筛选条件二：单选且业务选项
            List<SysFieldDto> singleSheetFieldList = fieldList.stream()
                    .filter(f -> FieldTypeConstants.SELECT_SINGLE.equals(f.getFieldType()) && Integer.valueOf(3).equals(f.getOptionType()))
                    .toList();

            // 筛选条件三：单选且非业务选项
            List<SysFieldDto> singleFieldList = fieldList.stream()
                    .filter(f -> FieldTypeConstants.SELECT_SINGLE.equals(f.getFieldType()) && !Integer.valueOf(3).equals(f.getOptionType()))
                    .toList();

            // 筛选条件四：多选且业务选项
            List<SysFieldDto> multiSheetFieldList = fieldList.stream()
                    .filter(f -> FieldTypeConstants.SELECT_MULTI.equals(f.getFieldType()) && Integer.valueOf(3).equals(f.getOptionType()))
                    .toList();

            // 筛选条件五：多选且非业务选项
            List<SysFieldDto> multiFieldList = fieldList.stream()
                    .filter(f -> FieldTypeConstants.SELECT_MULTI.equals(f.getFieldType()) && !Integer.valueOf(3).equals(f.getOptionType()))
                    .toList();

            if (!caseCadeFieldList.isEmpty()) {
                caseCadeFieldMap.computeIfAbsent(tableName, k -> new ArrayList<>()).addAll(caseCadeFieldList);
            }

            if (!singleFieldList.isEmpty()) {
                optionIdList.addAll(singleFieldList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
                singleFieldMap.computeIfAbsent(tableName, k -> new ArrayList<>()).addAll(singleFieldList);
            }

            if (!singleSheetFieldList.isEmpty()) {
                sheetIdList.addAll(singleSheetFieldList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
                singleSheetFieldMap.computeIfAbsent(tableName, k -> new ArrayList<>()).addAll(singleSheetFieldList);
            }

            if (!multiFieldList.isEmpty()) {
                optionIdList.addAll(multiFieldList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
                multiFieldMap.computeIfAbsent(tableName, k -> new ArrayList<>()).addAll(multiFieldList);
            }

            if (!multiSheetFieldList.isEmpty()) {
                sheetIdList.addAll(multiSheetFieldList.stream().map(SysFieldDto::getOptionId).collect(Collectors.toSet()));
                multiSheetFieldMap.computeIfAbsent(tableName, k -> new ArrayList<>()).addAll(multiSheetFieldList);
            }
            if (!quoteFieldList.isEmpty()) {
                Set<Long> fieldIdSet = quoteFieldList.stream().map(SysFieldDto::getQuoteSheetFieldId).collect(Collectors.toSet());
                List<SysFieldDto> fieldQuoteList = fieldService.selectListByIds(fieldIdSet);
                List<SysFieldDto> newFieldList=new ArrayList<>();
                Map<Long, List<SysFieldDto>> quoteFieldListMap = quoteFieldList.stream()
                        .collect(Collectors.groupingBy(SysFieldDto::getQuoteSheetFieldId));
                for (SysFieldDto sysFieldDto : fieldQuoteList) {
                    List<SysFieldDto> quoteFields = quoteFieldListMap.get(sysFieldDto.getId());
                    if (quoteFields != null) {
                        for (SysFieldDto quoteField : quoteFields) {
                            SysFieldDto newField = new SysFieldDto();
                            BeanUtils.copyProperties(sysFieldDto, newField);
                            newField.setApiName(quoteField.getApiName());
                            newFieldList.add(newField);
                        }
                    }
                }
                Map<String, List<SysFieldDto>> newSheetFieldMap=new HashMap<>();
                newSheetFieldMap.put(tableName, newFieldList);
                handleByFieldType(newSheetFieldMap,caseCadeFieldMap,singleFieldMap,singleSheetFieldMap,multiFieldMap,multiSheetFieldMap,optionIdList,sheetIdList);
            }
        }
    }

    @Override
    public AjaxResult list(UniversalQuery query) {
        String sheetApiName = query.getSheetApiName();
        //查询表单id
        SysSheetDto sysSheetDto = sheetService.selectOneByApiName(sheetApiName);
        if(sysSheetDto==null){
            return AjaxResult.error("错误的业务表单");
        }
        Long sheetId=sysSheetDto.getId();
        List<SysFieldDto> sysFieldDtos = getFieldBySheetId(sheetId);
        Map<String, List<SysFieldDto>> sheetFieldMap = new HashMap<>();
        sheetFieldMap.put(sheetApiName,sysFieldDtos);
        String searchField=String.join(",",sysFieldDtos.stream().map(SysFieldDto::getApiName).
                filter(Objects::nonNull).toList()) ;
        String sql="select "+searchField+" from "+sheetApiName;
        //主表数据
        List<Map<String, Object>> sheetApiNameValueList = universalMapper.selectAllList(sql);
        //获取1对1
        SysFieldQuery fieldQuery=new SysFieldQuery();
        fieldQuery.setRelationSheetId(sheetId);
        fieldQuery.setRelationType(NumberUtil.Zero);
        List<SysFieldDto> relationFieldList = fieldService.selectList(fieldQuery);
        Set<Long> collect = relationFieldList.stream().map(SysFieldDto::getSheetId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<SysSheetDto> sheetList = collect.isEmpty() ? new ArrayList<>() : sheetService.selectListByIds(collect);
        Map<Long, String> sheetIdApiNameMap = sheetList.stream().collect(Collectors.toMap(SysSheetDto::getId, SysSheetDto::getApiName, (v1, v2) -> v1));
        Map<String, Map<Object, Map<String, Object>>> subTableIndexMap = new HashMap<>();
        for (SysFieldDto sysFieldDto : relationFieldList) {
            List<SysFieldDto> fieldList = getFieldBySheetId(sysFieldDto.getSheetId());
            String relationField=String.join(",",fieldList.stream().map(SysFieldDto::getApiName).
                    filter(Objects::nonNull).toList()) ;
            String relationSql="select "+relationField+" from "+sheetIdApiNameMap.get(sysFieldDto.getSheetId());
            List<Map<String, Object>> relationSheetApiNameValueList = universalMapper.selectAllList(relationSql);
            Map<Object, Map<String, Object>> indexMap = relationSheetApiNameValueList.stream()
                    .collect(Collectors.toMap(row -> row.get(sysFieldDto.getApiName()), row -> row));
            subTableIndexMap.put(sheetIdApiNameMap.get(sysFieldDto.getSheetId()), indexMap);
            sheetFieldMap.put(sheetIdApiNameMap.get(sysFieldDto.getSheetId()),fieldList);
        }
        List<Map<String, Map<String, Object>>> resultList = sheetApiNameValueList.stream()
                .map(company -> {
                    Map<String, Map<String, Object>> merged = new HashMap<>();
                    Object companyId = company.get("id");
                    // 添加主表数据
                    merged.put(sheetApiName, company);
                    // 添加子表数据
                    for (String tableName : sheetIdApiNameMap.values()) {
                        Map<Object, Map<String, Object>> indexMap = subTableIndexMap.get(tableName);
                        if (indexMap != null && indexMap.containsKey(companyId)) {
                            merged.put(tableName, indexMap.get(companyId));
                        }
                    }
                    return merged;
                }).toList();

        // 构建(主表+子表字段)
        Map<Long, SysFieldDto> fieldMetaMap = new HashMap<>();
        for (SysFieldDto f : sysFieldDtos) {
            f.setSheetApiName(sheetApiName);
            fieldMetaMap.put(f.getId(), f);
        }
        for (Long sid : collect) {
            List<SysFieldDto> subFields = getFieldBySheetId(sid);
            String subApiName = sheetIdApiNameMap.get(sid);
            for (SysFieldDto f : subFields) {
                f.setSheetApiName(subApiName);
                fieldMetaMap.put(f.getId(), f);
            }
        }
        // 动态筛选器
        List<Map<String, Map<String, Object>>> filter = DynamicFilterUtil.filter(resultList, query.getFilters(), fieldMetaMap);
        // 分页 + 排序
        PageUniversalResult<Map<String, Map<String, Object>>> mapPageUniversalResult = PageAndSortUtil.paginateAndSortWithPageInfo(filter, query.getOrderSort(), query.getAscending(), query.getPage(), query.getPageSize());
        List<Map<String, Map<String, Object>>> records = mapPageUniversalResult.getRecords();

        Map<String, List<SysFieldDto>> caseCadeFieldMap = new HashMap<>();
        Map<String, List<SysFieldDto>> singleFieldMap = new HashMap<>();
        Map<String, List<SysFieldDto>> singleSheetFieldMap = new HashMap<>();
        Map<String, List<SysFieldDto>> multiFieldMap = new HashMap<>();
        Map<String, List<SysFieldDto>> multiSheetFieldMap = new HashMap<>();
        Set<Long> optionIdList=new HashSet<>();
        Set<Long> sheetIdList=new HashSet<>();
        handleByFieldType(sheetFieldMap,caseCadeFieldMap,singleFieldMap,singleSheetFieldMap,multiFieldMap,
                multiSheetFieldMap,optionIdList,sheetIdList);
        Map<Long, String> optionValueMap=new HashMap<>();
        Map<Long,Map<Long, String>> sheetValueMap=new HashMap<>();
        if(optionIdList.size()>0){
            LambdaQueryWrapper<SysOptionValuePo> lqw = Wrappers.lambdaQuery();
            lqw.in(SysOptionValuePo::getOptionId,optionIdList);
            List<SysOptionValuePo> sysOptionValuePoList = optionValueMapper.selectList(lqw);
            optionValueMap = sysOptionValuePoList.stream().collect(Collectors.toMap(SysOptionValuePo::getId, SysOptionValuePo::getName, (v1, v2) -> v1));
        }
        if(sheetIdList.size()>0){
            List<SysSheetDto> sheetDtoList = sheetService.selectListByIds(sheetIdList);
            for (SysSheetDto sheetDto : sheetDtoList) {
                // list不会传入 optionFieldList，使用默认选择字段：id 和 name
                String optionSelectSql = "";
                if (sheetDto.getApiName().equals("sys_user")) {
                    optionSelectSql = "`id`,`nick_name`";
                } else {
                    optionSelectSql = "`id`,`name`";
                }
                List<Map<String, Object>> option = universalMapper.option(sheetDto.getApiName(), optionSelectSql);
                Map<Long, String> optionMap = option.stream()
                        .filter(m -> m.containsKey("id"))
                        .collect(Collectors.toMap(
                                m -> ((Number) m.get("id")).longValue(),
                                m -> {
                                    // 提取除"id"外的第一个字段作为值
                                    return m.entrySet().stream()
                                            .filter(e -> !"id".equals(e.getKey()))
                                            .findFirst()
                                            .map(e -> String.valueOf(e.getValue()))
                                            .orElse("");
                                }
                        ));
                sheetValueMap.put(sheetDto.getId(),optionMap);
            }
        }
        List<Map<String, Map<String, Object>>> newRecords=new ArrayList<>();
        if (records != null && !records.isEmpty()){
            for (Map<String, Map<String, Object>> record : records) {
                Map<String, Map<String, Object>> newRecord=new HashMap<>();
                for (String sheetApiNameString : record.keySet()) {
                    Map<String, Object> stringObjectMap = record.get(sheetApiNameString);
                    if (singleFieldMap.get(sheetApiNameString) != null) {
                        for (SysFieldDto sysFieldDto : singleFieldMap.get(sheetApiNameString)) {
                            if (stringObjectMap.get(sysFieldDto.getApiName()) !=null) {
                                Long fieldValue = Long.parseLong(stringObjectMap.get(sysFieldDto.getApiName()).toString());
                                stringObjectMap.put(sysFieldDto.getApiName(),optionValueMap.get(fieldValue));
                            }
                        }
                    }
                    if (multiFieldMap.get(sheetApiNameString) != null){
                        for (SysFieldDto sysFieldDto : multiFieldMap.get(sheetApiNameString)) {
                            if (stringObjectMap.get(sysFieldDto.getApiName()) !=null) {
                                String fieldValue = stringObjectMap.get(sysFieldDto.getApiName()).toString();
                                List<String> split = Arrays.stream(fieldValue.split(",")).toList();
                                List<String> multiValue=new ArrayList<>();
                                for (String s : split) {
                                    if (s == null || s.isEmpty()) continue;
                                    Long a=Long.parseLong(s);
                                    if (optionValueMap.get(a) == null || optionValueMap.get(a).isEmpty()) continue;
                                    multiValue.add(optionValueMap.get(a));
                                }
                                stringObjectMap.put(sysFieldDto.getApiName(),String.join(",",multiValue));
                            }
                        }
                    }
                    if (singleSheetFieldMap.get(sheetApiNameString) != null){
                        for (SysFieldDto sysFieldDto : singleSheetFieldMap.get(sheetApiNameString)) {
                            if (stringObjectMap.get(sysFieldDto.getApiName()) != null) {
                                Long fieldValue = Long.parseLong(stringObjectMap.get(sysFieldDto.getApiName()).toString());
                                stringObjectMap.put(sysFieldDto.getApiName(),sheetValueMap.get(fieldValue));
                            }
                        }
                    }
                    if (multiSheetFieldMap.get(sheetApiNameString) != null){
                        for (SysFieldDto sysFieldDto : multiSheetFieldMap.get(sheetApiNameString)) {
                            if (stringObjectMap.get(sysFieldDto.getApiName()) != null) {
                                String fieldValue = stringObjectMap.get(sysFieldDto.getApiName()).toString();
                                List<String> split = Arrays.stream(fieldValue.split(",")).toList();
                                List<String> multiValue=new ArrayList<>();
                                for (String s : split) {
                                    if (s == null || s.isEmpty()) continue;
                                    Long a=Long.parseLong(s);
                                    if (sheetValueMap.get(sysFieldDto.getOptionId()) == null ||
                                            sheetValueMap.get(sysFieldDto.getOptionId()).get(a) == null) continue;
                                    multiValue.add(sheetValueMap.get(sysFieldDto.getOptionId()).get(a));
                                }
                                stringObjectMap.put(sysFieldDto.getApiName(),String.join(",",multiValue));
                            }
                        }
                    }
                    if (caseCadeFieldMap.get(sheetApiNameString) != null){
                        for (SysFieldDto fieldDto : caseCadeFieldMap.get(sheetApiNameString)) {
                            if (stringObjectMap.get(fieldDto.getApiName()) != null) {
                                String fieldValue = stringObjectMap.get(fieldDto.getApiName()).toString();
                                List<String> fieldValueList = Arrays.stream(fieldValue.split("-")).toList();
                                LambdaQueryWrapper<SysOptionValuePo> lqw;
                                lqw = Wrappers.lambdaQuery();
                                lqw.in(SysOptionValuePo::getId,fieldValueList);
                                List<SysOptionValuePo> sysOptionValuePos = optionValueMapper.selectList(lqw);
//                    Map<String, String> stringMap = sysOptionValuePos.stream().collect(Collectors.toMap(SysOptionValuePo::getApiName, SysOptionValuePo::getName, (v1, v2) -> v1));
                                Map<Long, String> stringMap = sysOptionValuePos.stream().collect(Collectors.toMap(SysOptionValuePo::getId, SysOptionValuePo::getName, (v1, v2) -> v1));
                                List<String> optionValueList=new ArrayList<>();
                                for (String s : fieldValueList) {
                                    if (s == null || s.isEmpty()) continue;
                                    Long id = Long.valueOf(s);
                                    if (stringMap.get(id) == null || stringMap.get(id).isEmpty()) continue;
                                    optionValueList.add(stringMap.get(id));
                                }
                                stringObjectMap.put(fieldDto.getApiName(),String.join("-",optionValueList));
                            }
                        }
                    }
                    newRecord.put(sheetApiNameString,stringObjectMap);
                }
                newRecords.add(newRecord);
            }
        }
        mapPageUniversalResult.setRecords(newRecords);
        return AjaxResult.success(mapPageUniversalResult);
    }

    @Override
    public AjaxResult addFieldList(UniversalQuery query) {
        //查询表单id
        SysSheetDto sysSheetDto = sheetService.selectOneByApiName(query.getSheetApiName());
        if(sysSheetDto==null){
            return AjaxResult.error("错误的业务表单");
        }
        List<SysSheetDto> sheetList=new ArrayList<>();
        sheetList.add(sysSheetDto);
        Map<String, Map<String, Object>> stringMapMap = buildSheetFieldTree(sheetList);
        return AjaxResult.success(stringMapMap);
    }

    /**
     * 递归查出关联的所有表
     * @param sheetList
     * @return
     */
    public Map<String,Map<String,Object>> buildSheetFieldTree(List<SysSheetDto> sheetList){
        Map<String, Map<String, Object>> resultMap = new LinkedHashMap<>();
        for (SysSheetDto sysSheetDto : sheetList) {
            Map<String, Object> currentNode = new LinkedHashMap<>();
            List<SysFieldDto> currentFields = switchFieldType(sysSheetDto.getId());
            currentNode.put("fields", currentFields);
            SysFieldQuery fieldQuery=new SysFieldQuery();
            fieldQuery.setRelationSheetId(sysSheetDto.getId());
            List<SysFieldDto> relationFieldList = fieldService.selectList(fieldQuery);
            if (relationFieldList != null && !relationFieldList.isEmpty()) {
                Map<Integer, List<SysFieldDto>> groupRelationType = relationFieldList.stream()
                        .collect(Collectors.groupingBy(SysFieldDto::getRelationType));
                // 一对一 判空
                Set<Long> oneCollect = new HashSet<>();
                if (groupRelationType.get(RelationTypeConstants.ONE) != null) {
                    oneCollect = groupRelationType.get(RelationTypeConstants.ONE).stream().map(SysFieldDto::getSheetId).filter(Objects::nonNull).collect(Collectors.toSet());
                }
                // 一对多 判空
                Set<Long> manyCollect = new HashSet<>();
                if (groupRelationType.get(RelationTypeConstants.MANY) != null) {
                    manyCollect = groupRelationType.get(RelationTypeConstants.MANY).stream().map(SysFieldDto::getSheetId).filter(Objects::nonNull).collect(Collectors.toSet());
                }
                Set<Long> totalCollect=new HashSet<>(oneCollect);
                totalCollect.addAll(manyCollect);
                List<SysSheetDto> sheetDtoList = sheetService.selectListByIds(totalCollect);
                Map<Long, SysSheetDto> sheetDtoMap = sheetDtoList.stream()
                        .collect(Collectors.toMap(SysSheetDto::getId, Function.identity()));
                // 递归子表结构
                Map<String, Map<String, Object>> childMap = buildSheetFieldTree(sheetDtoList);
                // 构造子表节点映射
                Map<String, Map<String, Object>> subMap = new LinkedHashMap<>();
                for (Long sheetId : oneCollect) {
                    SysSheetDto child = sheetDtoMap.get(sheetId);
                    String key = child.getApiName() + "|" + child.getName();
                    if (childMap.containsKey(key)) {
                        subMap.put(key, childMap.get(key));
                    }
                }
                for (Long sheetId : manyCollect) {
                    SysSheetDto child = sheetDtoMap.get(sheetId);
                    String key = child.getApiName() + "|" + child.getName() + "|List";
                    if (childMap.containsKey(child.getApiName() + "|" + child.getName())) {
                        subMap.put(key, childMap.get(child.getApiName() + "|" + child.getName()));
                    }
                }
                if (!subMap.isEmpty()) {
                    currentNode.put("children", subMap);
                }
            }
            // 当前节点的 key
            String nodeKey = sysSheetDto.getApiName() + "|" + sysSheetDto.getName();
            resultMap.put(nodeKey, currentNode);
        }
        return resultMap;
    }


    @Override
    public AjaxResult searchFieldList(UniversalQuery query) {
        SysSheetDto sysSheetDto = sheetService.selectOneByApiName(query.getSheetApiName());
        if(sysSheetDto==null){
            return AjaxResult.error("错误的业务表单");
        }
        List<SysFieldDto> fieldBySheetId = getFieldBySheetId(sysSheetDto.getId());
        Map<String,List<SysFieldDto>> result=new HashMap<>();
        result.put(query.getSheetApiName(),fieldBySheetId);
        //获取1对1
        SysFieldQuery fieldQuery=new SysFieldQuery();
        fieldQuery.setRelationSheetId(sysSheetDto.getId());
        fieldQuery.setRelationType(NumberUtil.Zero);
        List<SysFieldDto> relationFieldList = fieldService.selectList(fieldQuery);
        Set<Long> collect = relationFieldList.stream().map(SysFieldDto::getSheetId).filter(Objects::nonNull).collect(Collectors.toSet());
        if (!collect.isEmpty()){
            List<SysSheetDto> sheetList = sheetService.selectListByIds(collect);
            Map<Long, String> sheetIdApiNameMap = sheetList.stream().collect(Collectors.toMap(SysSheetDto::getId, SysSheetDto::getApiName, (v1, v2) -> v1));
            List<SysFieldDto> sysFieldDtoList = getFieldByRoleIds(collect.stream().toList());
            Map<Long, List<SysFieldDto>> sheetFieldMap = sysFieldDtoList.stream().collect(Collectors.groupingBy(SysFieldDto::getSheetId));
            for (Long sheetId : sheetIdApiNameMap.keySet()) {
                result.put(sheetIdApiNameMap.get(sheetId),sheetFieldMap.get(sheetId));
            }
        }
        return AjaxResult.success(result);
    }

    public Map<SysSheetDto,List<SysFieldDto>> getListFieldByQueryGroupBySheet(List<SysFieldDto> relationFieldList){
        Set<Long> collect = relationFieldList.stream().map(SysFieldDto::getSheetId).filter(Objects::nonNull).collect(Collectors.toSet());
        List<SysFieldDto> fieldByRoleIds = getFieldByRoleIds(collect.stream().toList());
        Map<Long, List<SysFieldDto>> fieldMap = fieldByRoleIds.stream()
                .collect(Collectors.groupingBy(SysFieldDto::getSheetId));
        Map<SysSheetDto,List<SysFieldDto>> sysSheetDtoListMap=new HashMap<>();
        List<SysSheetDto> sheetDtoList = sheetService.selectListByIds(collect);
        Map<Long, SysSheetDto> sheetMap = sheetDtoList.stream().collect(Collectors.toMap(SysSheetDto::getId, Function.identity()));
        for (Long sheetMapId : sheetMap.keySet()) {
            sysSheetDtoListMap.put(sheetMap.get(sheetMapId),fieldMap.get(sheetMapId));
        }
        return sysSheetDtoListMap;
    }

    /**
     * 根据表单id获取可见字段列表
     * @param sheetIds 表单id集合
     * @return 字段列表
     */
    public List<SysFieldDto> getFieldByRoleIds(List<Long> sheetIds){
        //查询角色可见字段
        List<SysFieldDto> sysFieldPoList;
        if(SecurityUtils.getUser().isAdmin()){
            sysFieldPoList=fieldService.selectBySheetIds(sheetIds);
        }else {
//            Long[] roleIds = SecurityUtils.getUser().getRoleIds();
            List<SysRoleDto> roles = SecurityUtils.getUser().getRoles();
            List<Long> roleIds = roles.stream().map(SysRoleDto::getId).collect(Collectors.toList());
            LambdaQueryWrapper<SysFieldRoleMerge> lqw = Wrappers.lambdaQuery();
            lqw.in(SysFieldRoleMerge::getSheetId, sheetIds);
//            lqw.in(SysFieldRoleMerge::getRoleId, (Object[]) roleIds);
            lqw.in(SysFieldRoleMerge::getRoleId, roleIds);
            List<SysFieldRoleMerge> fieldRoleMerges = fieldRoleMergeMapper.selectList(lqw);
            Set<Long> fieldIds = fieldRoleMerges.stream().map(SysFieldRoleMerge::getFieldId).collect(Collectors.toSet());
//            sysFieldPoList= fieldService.selectListByIds(fieldIds);
            List<SysFieldDto> sysBusFieldDtos = fieldService.selectListByIds(fieldIds);
            sysFieldPoList = sysBusFieldDtos.stream().sorted(Comparator.comparing(SysFieldDto::getCreateTime).reversed()).collect(Collectors.toList());
            List<SysFieldDto> sysFieldDtos = fieldService.selectPrimaryBySheetIds(sheetIds);
            sysFieldPoList.addAll(sysFieldDtos);
        }
        return sysFieldPoList;
    }

    public List<SysFieldDto> switchFieldType(Long sheetId){
        List<Long> sheetIds=new ArrayList<>();
        sheetIds.add(sheetId);
        List<SysFieldDto> sysFieldDtos = getFieldByRoleIds(sheetIds);
        Map<String, List<SysFieldDto>> groupFieldType = sysFieldDtos.stream()
                .collect(Collectors.groupingBy(
                        SysFieldDto::getFieldType,
                        LinkedHashMap::new,
                        Collectors.toList()));
        setGroupFieldType(groupFieldType);
        List<SysFieldDto> list = groupFieldType.values().stream().flatMap(List::stream).toList();
        List<SysFieldDto> sortedList = list.stream()
                .sorted(Comparator.comparing(SysFieldDto::getCreateTime,
                        Comparator.nullsFirst(Comparator.naturalOrder()))
                .reversed()).collect(Collectors.toList());
        return sortedList;
    }

    public void setGroupFieldType(Map<String, List<SysFieldDto>> groupFieldType){
        for (String key : groupFieldType.keySet()) {
            List<SysFieldDto> sysFieldList = groupFieldType.get(key);
            switch (key) {
                case FieldTypeConstants.SELECT_SINGLE , FieldTypeConstants.SELECT_MULTI-> {
//                    List<SysFieldDto> optionList = sysFieldList.stream().filter(sysFieldDto ->
//                            sysFieldDto.getOptionType() != NumberUtil.Three).toList();
                    List<SysFieldDto> optionList = Optional.ofNullable(sysFieldList)
                            .orElse(Collections.emptyList()).stream()
                            .filter(sysFieldDto -> !Objects.equals(sysFieldDto.getOptionType(), 3))
                            .toList();
                    if(optionList.size()>0){
                        Set<Long> optionIdList = optionList.stream().map(SysFieldDto::getOptionId).filter(Objects::nonNull).collect(Collectors.toSet());
                        LambdaQueryWrapper<SysOptionValuePo> lqw = Wrappers.lambdaQuery();
                        lqw.in(SysOptionValuePo::getOptionId, optionIdList);
                        List<SysOptionValuePo> sysOptionValuePoList = optionValueMapper.selectList(lqw);
                        Map<Long, List<SysOptionValuePo>> collect = sysOptionValuePoList.stream()
                                .collect(Collectors.groupingBy(SysOptionValuePo::getOptionId));
                        for (SysFieldDto fieldDto : sysFieldList) {
                            fieldDto.setOptionValuePoList(collect.get(fieldDto.getOptionId()));
                        }
                    }
                }
                case FieldTypeConstants.CASCADE -> {
                    List<Long> casecadeIdList = sysFieldList.stream().map(SysFieldDto::getCascadeId).filter(Objects::nonNull).toList();
                    Map<Long, List<Tree<String>>> casecadeTreeMap=new HashMap<>();
                    for (Long casecadeId : casecadeIdList) {
                        List<Tree<String>> trees = sysCascadeManager.selectCascadeRelation(casecadeId);
                        casecadeTreeMap.put(casecadeId,trees);
                    }
                    for (SysFieldDto fieldDto : sysFieldList) {
                        fieldDto.setCasecadeTreeList(casecadeTreeMap.get(fieldDto.getCascadeId()));
                    }
                }
            }
        }
    }

    @Override
    public AjaxResult getInfo(UniversalQuery query) {
        String sheetApiName = query.getSheetApiName();
        //查询表单id
        SysSheetDto sysSheetDto = sheetService.selectOneByApiName(sheetApiName);
        if(sysSheetDto==null){
            return AjaxResult.error("错误的业务表单");
        }
        Long sheetId=sysSheetDto.getId();
        List<SysFieldDto> sysFieldDtos = getFieldBySheetId(sheetId);
        Map<String, List<SysFieldDto>> sheetFieldMap = new HashMap<>();
        sheetFieldMap.put(sheetApiName,sysFieldDtos);
        String searchField=String.join(",",sysFieldDtos.stream().map(SysFieldDto::getApiName).
                filter(Objects::nonNull).toList()) ;
        String sql="select "+searchField+" from "+sheetApiName;
        //主表数据
        Map<String, Object> sheetApiNameValueMap = universalMapper.selectMapById(sql,query.getId());
        Map<String,Object> result=new HashMap<>();
        result.put(sheetApiName,sheetApiNameValueMap);
        //获取1对1
        SysFieldQuery fieldQuery=new SysFieldQuery();
        fieldQuery.setRelationSheetId(sheetId);
        List<SysFieldDto> relationFieldList = fieldService.selectList(fieldQuery);
        //判空处理
        if (relationFieldList == null || relationFieldList.isEmpty()) {
            return AjaxResult.success(result); // 没有关联数据，直接返回主表
        }
        Map<Integer, List<SysFieldDto>> relationGroup = relationFieldList.stream()
                .collect(Collectors.groupingBy(SysFieldDto::getRelationType));
//        Set<Long> collect = relationFieldList.stream().map(SysFieldDto::getSheetId).filter(Objects::nonNull).collect(Collectors.toSet());
        Set<Long> sheetIdSet = relationFieldList.stream()
                .map(SysFieldDto::getSheetId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        // 再次判空：防止 sheetIdSet 为空导致 IN () 错误
        List<SysSheetDto> sheetList = CollectionUtils.isEmpty(sheetIdSet)
                ? Collections.emptyList()
                : sheetService.selectListByIds(sheetIdSet);

        if (sheetList.isEmpty()) {
            return AjaxResult.success(result); // 没有关联表配置，直接返回
        }
//        List<SysSheetDto> sheetList = sheetService.selectListByIds(collect);
        List<SysFieldDto> fieldByRoleIds = getFieldByRoleIds(sheetIdSet.stream().toList());
        Map<Long, List<SysFieldDto>> fieldGroup = fieldByRoleIds.stream()
                .collect(Collectors.groupingBy(SysFieldDto::getSheetId));
        Map<Long, String> sheetIdApiNameMap = sheetList.stream().collect(Collectors.toMap(SysSheetDto::getId, SysSheetDto::getApiName, (v1, v2) -> v1));
        for (SysFieldDto sysFieldDto : relationGroup.getOrDefault(NumberUtil.Zero, Collections.emptyList())) {
            List<SysFieldDto> fieldList = fieldGroup.get(sysFieldDto.getSheetId());
            String relationField=String.join(",",fieldList.stream().map(SysFieldDto::getApiName).
                    filter(Objects::nonNull).toList()) ;
            String relationSql="select "+relationField+" from "+sheetIdApiNameMap.get(sysFieldDto.getSheetId());
            Map<String, Object> relationSheetApiNameValueMap = universalMapper.selectByForeignKey(relationSql,query.getId(),sysFieldDto.getApiName());
            result.put(sheetIdApiNameMap.get(sysFieldDto.getSheetId()),relationSheetApiNameValueMap);
        }
        for (SysFieldDto sysFieldDto : relationGroup.getOrDefault(NumberUtil.One, Collections.emptyList())) {
            List<SysFieldDto> fieldList =  fieldGroup.get(sysFieldDto.getSheetId());
            String relationField=String.join(",",fieldList.stream().map(SysFieldDto::getApiName).
                    filter(Objects::nonNull).toList()) ;
            String relationSql="select "+relationField+" from "+sheetIdApiNameMap.get(sysFieldDto.getSheetId());
            List<Map<String, Object>> relationSheetApiNameValueMap = universalMapper.selectListByForeignKey(relationSql,query.getId(),sysFieldDto.getApiName());
            result.put(sheetIdApiNameMap.get(sysFieldDto.getSheetId())+"|List",relationSheetApiNameValueMap);
        }
        return AjaxResult.success(result);
    }

    @Override
    public AjaxResult add(UniversalQuery query) {
        JSONObject jsonObject = JSON.parseObject(query.getUniversalJson());
        if(query.getAddType()==1){
            Snowflake snowflake = IdUtil.getSnowflake(1, 0);
            Long entityId = snowflake.nextId();
            Map<String, Object> redisData = redisService.getCacheMap(query.getCreditNo());
            String sheetApiName = query.getSheetApiName();
            if(redisData!=null && !redisData.isEmpty()){
                Map<String,Long> sheetApiNameMap=addFromRedis(query,entityId,snowflake,redisData);
                //处理前端传递数据
                Long id=sheetApiNameMap.get(sheetApiName);
                // 1.更新的表名 = redis中 表名 工商新增
                for (String sheetApi : jsonObject.keySet()) {
                    if(sheetApi.equals(sheetApiName)){
                        JSONObject json=JSONObject.parseObject(jsonObject.get(sheetApiName).toString());
                        editProcess(json,id,sheetApiName);
                    }else if(sheetApiNameMap.values().contains(sheetApi)){
                        Long reId=sheetApiNameMap.get(sheetApi);
                        JSONObject json=JSONObject.parseObject(jsonObject.get(sheetApiName).toString());
                        editProcess(json,reId,sheetApiName);
                    }else {
                        Object jsonValue = jsonObject.get(sheetApi);
                        if (jsonValue == null || (jsonValue instanceof JSONArray && ((JSONArray) jsonValue).isEmpty()) || ("[]".equals(jsonValue))) {
                            continue;
                        }
                        insertSql(jsonObject.get(sheetApi), sheetApi, null, id);
                    }
                }
                // 处理后将 redis 中的数据删除
                redisService.deleteObject(query.getCreditNo());
            } else {
                // 实体 id
                Long reId = universalMapper.selectByCreditNo(query.getCreditNo());
                Map<String, Object> stringObjectMap = universalMapper.selectByEntityId(reId, sheetApiName);
                // 注意: 这里获取的是符合 entity_id 的某个主表记录 id， 而非主表id字段的id
                Long id=Long.parseLong(stringObjectMap.get("id").toString());
                for (String sheetApi : jsonObject.keySet()) {
                    if(sheetApi.equals(sheetApiName)) {
                        JSONObject json = JSONObject.parseObject(jsonObject.get(sheetApiName).toString());
                        editProcess(json, id, sheetApiName);
                    }else if (!sheetApi.contains("List")) {
                        // 一对一
                        // 通过遍历当前主表1-1关系表 -查询关系表外键apiName 查询关系表里的主键id  没有就是新增，有就是修改
                        Long mainTableId = sheetService.selectOneByApiName(sheetApiName).getId();
                        SysFieldQuery fieldQuery = new SysFieldQuery();
                        fieldQuery.setRelationSheetId(mainTableId);
                        fieldQuery.setRelationType(NumberUtil.Zero);
                        List<String> foreignKeyName = fieldService.selectList(fieldQuery).stream().map(SysFieldDto::getApiName).filter(Objects::nonNull).toList();
                        for (String foreignKey : foreignKeyName) {
                            Long relationId = universalMapper.selectRelationId(foreignKey, id, sheetApi);
                            if (relationId != null) {
                                JSONObject json = JSONObject.parseObject(jsonObject.get(sheetApi).toString());
                                editProcess(json, relationId, sheetApi);
                            } else {
                                insertSql(jsonObject.get(sheetApi), sheetApi, null, id);
                            }
                        }
                    }else {
                        // 一对多新增
                        Object jsonValue = jsonObject.get(sheetApi);
                        if (jsonValue == null || (jsonValue instanceof JSONArray && ((JSONArray) jsonValue).isEmpty()) || ("[]".equals(jsonValue))) {
                            continue;
                        }
                        insertSql(jsonObject.get(sheetApi), sheetApi, null, id);
                    }
                }
            }
        }else if(query.getAddType()==2){
            return normalAdd(jsonObject,query.getSheetApiName());
        }else if(query.getAddType()==3){
            return onlyAdd(jsonObject, query.getSheetApiName() ,query.getForeignKey());
        }
        return AjaxResult.success();
    }

    @Override
    public AjaxResult edit(UniversalQuery query) {
        JSONObject jsonObject = JSON.parseObject(query.getUniversalJson());
        Long id = query.getId();
        String sheetApiName = query.getSheetApiName();
        if(editProcess(jsonObject,id,sheetApiName)){
            return AjaxResult.success();
        }else{
            return AjaxResult.error();
        }
    }

    public Boolean editProcess(JSONObject jsonObject,Long id, String sheetApiName){
        normalizeJsonObject(jsonObject);
        Long sheetId = sheetService.selectOneByApiName(sheetApiName).getId();
        Map<String, Object> beforeObjectMap = universalMapper.selectById(id, sheetApiName);
        if (universalMapper.update(jsonObject, id, sheetApiName) > 0) {
            Map<String, Object> afterObjectMap = universalMapper.selectById(id, sheetApiName);
            Set<String> fieldApiNameSet = jsonObject.keySet();
            // fieldList 解析 universalJson 后， 拿到要更新的字段
            List<SysFieldDto> fieldList = fieldService.selectByApiNames(fieldApiNameSet.stream().toList(), sheetId);
            //要更新字段id
            List<Long> ids = fieldList.stream().map(SysFieldDto::getId).toList();
            //查谁引用我 引用字段的信息
            List<SysFieldDto> quoteFieldList = fieldService.selectQuoteByIds(ids);
            // 引用规则
            List<SysFieldDto> refercencingFieldList = fieldService.selectReferencingByIds(ids);
            List<SysFieldDto> referencedFieldList = fieldService.selectReferencedByIds(ids);
            for (SysFieldDto sysFieldDto : fieldList) {
                //修改的字段里有引用规则当前表字段的
                for (SysFieldDto fieldDto : refercencingFieldList) {
                    if (Objects.equals(sysFieldDto.getId(), fieldDto.getReferencingFieldId())) {
                        String searchFieldApiName = fieldService.selectById(fieldDto.getQuoteSheetFieldId()).getApiName();
                        String searchSheetApiName = sheetService.selectById(fieldDto.getQuoteSheetId()).getApiName();
                        Object searchValue = afterObjectMap.get(sysFieldDto.getApiName());
                        String quoteRuleFieldApiName = fieldService.selectById(fieldDto.getReferencedFieldId()).getApiName();
//                        String quoteReferencingFieldValue = fieldService.selectById(sysFieldDto.getReferencingFieldId()).getApiName();
                        String quoteReferencingFieldValue = fieldService.selectById(fieldDto.getReferencingFieldId()).getApiName();
                        Object o = universalMapper.selectQuoteFieldVale(searchSheetApiName, searchFieldApiName, quoteRuleFieldApiName, searchValue);
                        universalMapper.updateQuoteField(fieldDto.getApiName(), sheetApiName, o, quoteReferencingFieldValue, searchValue);
                    }
                }
                //修改的字段里有引用规则引用表字段的
                for (SysFieldDto fieldDto : referencedFieldList) {
                    if (Objects.equals(sysFieldDto.getId(), fieldDto.getReferencedFieldId())) {
                        // 引用表 apiName sys_license_info
                        String searchSheetApiName = sheetService.selectById(fieldDto.getSheetId()).getApiName();
                        String searchFieldApiName = fieldService.selectById(fieldDto.getReferencingFieldId()).getApiName();
                        // 更新字段的值 (修改后)
                        Object updateValue = afterObjectMap.get(sysFieldDto.getApiName());
                        // 更新字段的值 (修改前)
                        Object updateBeforeValue = beforeObjectMap.get(sysFieldDto.getApiName());
                         universalMapper.updateQuoteField(searchFieldApiName, searchSheetApiName, updateValue, searchFieldApiName, updateBeforeValue);
//                        universalMapper.updateQuoteField(sysFieldDto.getApiName(), searchSheetApiName, updateValue, sysFieldDto.getApiName(), updateBeforeValue);
                    }
                }
                //修改字段里有被引用字段的
                for (SysFieldDto fieldDto : quoteFieldList) {
                    if (Objects.equals(sysFieldDto.getId(), fieldDto.getQuoteSheetFieldId())) {
                        //引用字段apiName
                        String updateFieldApiName = fieldDto.getApiName();
                        //引用表apiName
                        String updateSheetApiName = sheetService.selectById(fieldDto.getSheetId()).getApiName();
                        //被引用字段apiName
                        Object updateValue = afterObjectMap.get(sysFieldDto.getApiName());
                        String quoteRuleFieldApiName = fieldService.selectById(fieldDto.getReferencingFieldId()).getApiName();
                        Object quoteRuleFieldValue = afterObjectMap.get(fieldService.selectById(fieldDto.getReferencedFieldId()).getApiName());
                        universalMapper.updateQuoteField(updateFieldApiName, updateSheetApiName, updateValue, quoteRuleFieldApiName, quoteRuleFieldValue);
                    }
                }
                //修改的字段里有引用字段
                if (Objects.equals(sysFieldDto.getFieldType(), FieldTypeConstants.QUOTE)) {
                    String updateFieldApiName = fieldService.selectById(sysFieldDto.getQuoteSheetFieldId()).getApiName();
                    String updateSheetApiName = sheetService.selectById(sysFieldDto.getQuoteSheetId()).getApiName();
                    Object updateValue = afterObjectMap.get(sysFieldDto.getApiName());
                    String quoteRuleFieldApiName = fieldService.selectById(sysFieldDto.getReferencedFieldId()).getApiName();
                    Object quoteRuleFieldValue = afterObjectMap.get(fieldService.selectById(sysFieldDto.getReferencingFieldId()).getApiName());
                    universalMapper.updateQuoteField(updateFieldApiName, updateSheetApiName, updateValue, quoteRuleFieldApiName, quoteRuleFieldValue);
                    //修改所有引用该字段的数据
                    List<Long> quoteSheetFiledIds = new ArrayList<>();
                    quoteSheetFiledIds.add(sysFieldDto.getQuoteSheetFieldId());
                    List<SysFieldDto> quoteSheetFieldList = fieldService.selectQuoteByIds(quoteSheetFiledIds);
                    String selectSheetApiName = sheetService.selectById(sysFieldDto.getQuoteSheetId()).getApiName();
                    Map<String, Object> quoteObjectMap =universalMapper.selectByQuote(selectSheetApiName,quoteRuleFieldApiName,quoteRuleFieldValue);
//                    Map<String, Object> quoteObjectMap = universalMapper.selectByQuote(selectSheetApiName, updateFieldApiName, quoteRuleFieldValue);
                    for (SysFieldDto fieldDto : quoteSheetFieldList) {
                        String updateApiName = fieldDto.getApiName();
                        String sheetUpdateApiName = sheetService.selectById(fieldDto.getSheetId()).getApiName();
                        String quoteFieldApiName = fieldService.selectById(fieldDto.getReferencingFieldId()).getApiName();
//                        Object quoteFieldValue = quoteObjectMap.get(fieldService.selectById(sysFieldDto.getReferencedFieldId()).getApiName());
                        Object quoteFieldValue = quoteObjectMap.get(fieldService.selectById(fieldDto.getReferencedFieldId()).getApiName());
                        universalMapper.updateQuoteField(updateApiName, sheetUpdateApiName, updateValue, quoteFieldApiName, quoteFieldValue);
                    }
                }
            }
            return true;
        } else {
            return false;
        }
    }

    @Override
    @DSTransactional
    public AjaxResult batchRemove(UniversalQuery query) {
        // 校验
        if ((query.getIdList() == null || query.getIdList().isEmpty()) && (query.getCreditNoList() == null || query.getCreditNoList().isEmpty())) {
            return AjaxResult.error("删除条件不能为空");
        }
        // 主表
        int mainCount;
        if (query.getIdList() != null && !query.getIdList().isEmpty()) {
            mainCount = universalMapper.deleteByIds(query.getSheetApiName(), "`id`", query.getIdList());
        } else {
            mainCount = universalMapper.deleteByCreditNos(query.getSheetApiName(), query.getCreditNoList(), "credit_no");
        }
        // 关联表
        Long sheetId = sheetService.selectOneByApiName(query.getSheetApiName()).getId();
        SysFieldQuery fieldQuery = new SysFieldQuery();
        fieldQuery.setRelationType(NumberUtil.Zero);
        fieldQuery.setRelationSheetId(sheetId);
        List<SysFieldDto> fieldDtoList = fieldService.selectList(fieldQuery);
        for (SysFieldDto fieldDto : fieldDtoList) {
            String subSheetApiName = sheetService.selectById(fieldDto.getSheetId()).getApiName();
            String foreignKey = fieldDto.getApiName();
            if (query.getIdList() != null && !query.getIdList().isEmpty()) {
                universalMapper.deleteByIds(subSheetApiName, foreignKey, query.getIdList());
            } else {
                String field;
                if (subSheetApiName.equals("bus_taxation_info")) {
                    field = "taxpayer_no";
                } else {
                    field = "credit_no";
                }
                universalMapper.deleteByCreditNos(subSheetApiName, query.getCreditNoList(), field);
            }
        }
        return mainCount > 0 ? AjaxResult.success() : AjaxResult.error();
    }

    @Override
    public AjaxResult option(UniversalQuery query) {
        // 默认选择字段：id 和 name
        String selectSql = "`id`,`name`";
        List<String> optionFieldList = query.getOptionFieldList();
        // 如果 optionFieldList 不为空，使用传入字段替代默认
        if (optionFieldList != null && !optionFieldList.isEmpty()) {
            selectSql = String.join(",", optionFieldList);
        }
        SysSheetDto sysSheetDto = sheetService.selectById(query.getSheetId());
        return AjaxResult.success(universalMapper.option(sysSheetDto.getApiName(), selectSql));
    }

    public String buildInsertSql(String tableName, JSONObject sheetJson, Long id) {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ").append(tableName).append(" (");

        List<String> fields = new ArrayList<>();
        List<String> values = new ArrayList<>();

        fields.add("id");
//        values.add("'" + id + "'");
        values.add(String.valueOf(id));
        // 遍历 JSON 字段
        for (Map.Entry<String, Object> entry : sheetJson.entrySet()) {
            String field = entry.getKey();
            Object value = entry.getValue();

            if (value == null) continue;

            fields.add("`" + field + "`");

            // 判断是否是字符串类型
            if (value instanceof String || value instanceof Date) {
                values.add("'" + value.toString().replace("'", "''") + "'");
            } else {
                values.add(value.toString());
            }
        }

        sql.append(String.join(", ", fields));
        sql.append(") VALUES (");
        sql.append(String.join(", ", values));
        sql.append(");");

        return sql.toString();
    }

    public String buildBatchInsertSql(String tableName, JSONArray sheetArray) {
        Snowflake snowflake = IdUtil.getSnowflake(1, 0);
        if (sheetArray == null || sheetArray.isEmpty()) {
            return null;
        }
        // 第一个对象用于确定字段顺序
        JSONObject firstObj = sheetArray.getJSONObject(0);
        List<String> fields = new ArrayList<>();
        fields.add("id"); // 手动添加主键 ID

        for (String key : firstObj.keySet()) {
            fields.add("`" + key + "`");
        }

        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ").append(tableName)
                .append(" (").append(String.join(", ", fields)).append(") VALUES ");

        List<String> rowValues = new ArrayList<>();

        for (int i = 0; i < sheetArray.size(); i++) {
            JSONObject obj = sheetArray.getJSONObject(i);
            List<String> values = new ArrayList<>();
            // 生成 UUID 主键
//            String uuid = UUID.randomUUID().toString();
//            values.add("'" + uuid + "'");
            Long id = snowflake.nextId();
            values.add(String.valueOf(id));

            for (String key : firstObj.keySet()) {
                Object val = obj.get(key);
                if (val == null) {
                    values.add("NULL");
                } else if (val instanceof String || val instanceof Date) {
                    values.add("'" + val.toString().replace("'", "''") + "'");
                } else {
                    values.add(val.toString());
                }
            }
            rowValues.add("(" + String.join(", ", values) + ")");
        }

        sql.append(String.join(", ", rowValues)).append(";");
        return sql.toString();
    }

    public Integer  insertSql(Object object,String sheetApiName,Long id,Long foreignKey){
        Snowflake snowflake = IdUtil.getSnowflake(1, 0);
        JSONObject sheetJson=new JSONObject();
        JSONArray jsonArray=new JSONArray();
        if(sheetApiName.contains("List")){
            jsonArray = JSONArray.parseArray(object.toString());
            sheetApiName=sheetApiName.replace("List","");
        }else{
            sheetJson = JSONObject.parseObject(object.toString());
        }
        //查询表单id
        SysSheetDto sysSheetDto = sheetService.selectOneByApiName(sheetApiName);
        if(sysSheetDto==null){
            return null;
        }
        SysFieldQuery query=new SysFieldQuery();
        query.setSheetId(sysSheetDto.getId());
        List<SysFieldDto> sysFieldDtos = fieldService.selectList(query);
        JSONArray jsonArrayResult=new JSONArray();
        for (SysFieldDto sysFieldDto : sysFieldDtos) {
            if(Objects.equals(sysFieldDto.getFieldType(), FieldTypeConstants.QUOTE)){
                SysFieldDto quoteFieldDto = fieldService.selectById(sysFieldDto.getQuoteSheetFieldId());
                String quoteSheetApiName = sheetService.selectById(quoteFieldDto.getSheetId()).getApiName();
                String quoteFieldApiName = quoteFieldDto.getApiName();
                String quoteRuleFieldApiName = fieldService.selectById(sysFieldDto.getReferencedFieldId()).getApiName();
                String ruleFieldApiName = fieldService.selectById(sysFieldDto.getReferencingFieldId()).getApiName();
                if(jsonArray.size()>0){
                    for (Object o : jsonArray) {
                        JSONObject jsonObject=JSONObject.parseObject(o.toString());
                        Object fieldValue = jsonObject.get(ruleFieldApiName);
                        jsonObject.put(sysFieldDto.getApiName(),universalMapper.
                                selectQuoteFieldVale(quoteSheetApiName,quoteFieldApiName,quoteRuleFieldApiName,fieldValue));
                        jsonArrayResult.add(jsonObject);
                    }
                }else{
                    Object fieldValue = sheetJson.get(ruleFieldApiName);
                    sheetJson.put(sysFieldDto.getApiName(),universalMapper.
                            selectQuoteFieldVale(quoteSheetApiName,quoteFieldApiName,quoteRuleFieldApiName,fieldValue));
                }
            }
            if(Objects.equals(sysFieldDto.getFieldType(), FieldTypeConstants.RELATION)){
                if(jsonArray.size()>0){
                    for (Object o : jsonArray) {
                        JSONObject jsonObject=JSONObject.parseObject(o.toString());
                        jsonObject.put(sysFieldDto.getApiName(),foreignKey);
                        jsonArrayResult.add(jsonObject);
                    }
                }else{
                    sheetJson.put(sysFieldDto.getApiName(),foreignKey);
                }
            }
        }
        String sql="";
        if(jsonArrayResult.size()>0){
            sql= buildBatchInsertSql(sheetApiName, jsonArrayResult);
        }else{
            if(id==null){
                id = snowflake.nextId();
            }
            sql= buildInsertSql(sheetApiName, sheetJson, id);
        }
        return universalMapper.insert(sql);
    }

    public AjaxResult onlyAdd(JSONObject jsonObject,String sheetApiName,Long foreignKey){
        Snowflake snowflake = IdUtil.getSnowflake(1, 0);
        Long id = snowflake.nextId();
        Integer flag=insertSql(jsonObject.get(sheetApiName), sheetApiName,id,foreignKey);
        if(flag>0) {
            return AjaxResult.success();
        }else{
            return AjaxResult.error();
        }
    }

    public AjaxResult normalAdd(JSONObject jsonObject,String sheetApiName){
        Snowflake snowflake = IdUtil.getSnowflake(1, 0);
        Long id = snowflake.nextId();
        //id 新增数据id    null 外键
        Integer flag=insertSql(jsonObject.get(sheetApiName), sheetApiName,id,null);
        if(flag==null){
            return AjaxResult.error("新增失败，错误的业务表单");
        }else if(flag>0) {
            for (String apiName : jsonObject.keySet()) {
                if (!apiName.equals(sheetApiName)) {
                    insertSql(jsonObject.get(apiName), apiName, null, id);
                }
            }
            return AjaxResult.success();
        }
        return AjaxResult.error();
    }


    /**
     * 根据引用ids查询被引用字段信息
     * @param query
     * @return
     */
    @Override
    public AjaxResult getQuoteInfo(UniversalQuery query) {
        //查当前引用字段信息
        List<SysFieldDto> sysFieldDtos = fieldService.selectListByIds(query.getIdList());
        //捞出所有的被引用字段id
        List<Long> quoteSheetFieldIds = sysFieldDtos.stream()
                .map(SysFieldDto::getQuoteSheetFieldId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        //查所有被引用字段信息
        List<SysFieldDto> quoteFieldDtos = fieldService.selectListByIds(quoteSheetFieldIds);
        //单选...处理
        Map<String, List<SysFieldDto>> groupFieldType = quoteFieldDtos.stream()
                .collect(Collectors.groupingBy(SysFieldDto::getFieldType));
        setGroupFieldType(groupFieldType);

        // 先建立 quoteFieldId -> SysFieldDto 的映射，便于快速查找
        Map<Long, SysFieldDto> quoteFieldMap = quoteFieldDtos.stream()
                .collect(Collectors.toMap(SysFieldDto::getId, dto -> dto));

        // 构建：原始字段 id -> 它所引用的字段（SysFieldDto）
        Map<Long, SysFieldDto> idToQuoteMap = sysFieldDtos.stream()
                .collect(Collectors.toMap(
                        SysFieldDto::getId,  // 原始字段 ID
                        field -> {
                            Long quoteId = field.getQuoteSheetFieldId();
                            if (quoteId != null && quoteFieldMap.containsKey(quoteId)) {
                                return quoteFieldMap.get(quoteId); // 返回单个引用字段的列表
                            }
                            return null; // 如果没有引用或未找到，返回空列表
                        },
                        (existing, replacement) -> existing,
                        LinkedHashMap::new
                ));

        //捞出所有被引用字段的sheetId
        List<Long> quoteSheetIds = sysFieldDtos.stream()
                .map(SysFieldDto::getQuoteSheetId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        List<SysSheetDto> sysSheetDtos = new ArrayList<>();
        if (!quoteSheetIds.isEmpty()) {
            sysSheetDtos = sheetService.selectListByIds(quoteSheetIds);
        }
        // 构建 sheetId -> apiName 映射
        Map<Long, String> sheetIdToApiNameMap = sysSheetDtos.stream()
                .collect(Collectors.toMap(SysSheetDto::getId, SysSheetDto::getApiName));

        //  为每个 sysFieldDto 找到它的 quoteSheetApiName，并设置到它引用的 quoteFieldDto 上
        sysFieldDtos.forEach(sysField -> {
            Long currentSheetId = sysField.getQuoteSheetId(); // ← 当前字段所在的引用表 ID
            String apiName = sheetIdToApiNameMap.get(currentSheetId);
            if (apiName != null) {
                // 找到它引用的字段
                Long quoteFieldId = sysField.getQuoteSheetFieldId();
                if (quoteFieldId != null && quoteFieldMap.containsKey(quoteFieldId)) {
                    SysFieldDto quoteField = quoteFieldMap.get(quoteFieldId);
                    quoteField.setQuoteSheetApiName(apiName); //  塞入的是当前表的 apiName
                }
            }
        });

        Map<String, Object> result = new HashMap<>();
        result.put("idToQuoteMap",idToQuoteMap);
        return AjaxResult.success(result);
    }

    /**
     * 根据统一社会信用代码查询企业信息
     * @param query query
     * @return 企业信息
     */
    @Override
    public AjaxResult getInfoByCreditNo(UniversalQuery query) {
        String creditNo = query.getCreditNo();
        String sheetApiName = query.getSheetApiName();
        Map<String, Object> entityMap = universalMapper.select(creditNo);
        if (entityMap != null && !entityMap.isEmpty()) {
            return AjaxResult.error("该企业信息已存在，无法新增");
        } else if (redisService.hasKey(creditNo)) {
            Map<String, Object> resultDataMap = new HashMap<>();
            // 有1对1关联表就查关联表，没有就查当前表
            List<String> relationSheetNames = getRelationSheetNames(sheetApiName);
            for (String relationSheetName : relationSheetNames) {
                Map<String, Object> sheetData = redisService.getCacheMapValue(creditNo, relationSheetName);
                @SuppressWarnings("unchecked")
                Map<String, Object> mainData = (Map<String, Object>) sheetData.get(relationSheetName);
                String entityName;
                if (relationSheetName.equals("bus_taxation_info")) {
                    entityName = mainData.get("taxpayer_name").toString();
                } else {
                    entityName = mainData.get("name").toString();
                }
                if (!query.getName().equals(entityName)) {
                    return AjaxResult.error("无对应企业信息");
                }
                resultDataMap.put(relationSheetName, sheetData);
            }
            return AjaxResult.success(resultDataMap);
        } else {
            // 1. 调用第三方 API 获取原始数据
            CompanyBaseInfoApiDto companyBaseInfoApiDto = CompanyBaseInfoUtil.getCompanyBaseInfo(creditNo);
            if (companyBaseInfoApiDto == null || companyBaseInfoApiDto.getName() == null) {
                return AjaxResult.error("统一社会信用代码错误，未找到该企业");
            }
            SysEntityPo sysEntityPo = CompanyBaseInfoApiConverter.companyBaseInfo2Entity(companyBaseInfoApiDto);
            // 将原始数据转换为 Redis Hash 所需的 Map 结构
            Map<String, Object> businessMap = buildRedisDataMap(sysEntityPo);
            // 1. 工商
            redisService.setCacheMapValue(creditNo, "bus_entity_business", businessMap);
            // 2. 税务
            Map<String, Object> taxationMap = buildTaxationMap(sysEntityPo);
            redisService.setCacheMapValue(creditNo, "bus_taxation_info", taxationMap);
            Map<String, Object> resultDataMap = new HashMap<>();
            // 有1对1关联表就逐个取出封装返回，没有就取当前表数据返回
            List<String> relationSheetNames = getRelationSheetNames(sheetApiName);
            for (String relationSheetName : relationSheetNames) {
                resultDataMap.put(relationSheetName, redisService.getCacheMapValue(creditNo, relationSheetName));
            }
            return AjaxResult.success(resultDataMap);
        }
    }

    /**
     * 将 SysEntityPo 对象转换为 Map，键为下划线形式的数据库字段名
     */
    public Map<String, Object> buildRedisDataMap(SysEntityPo sysEntityPo) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> tempMap = new HashMap<>();
        if (sysEntityPo == null) return resultMap;
        Field[] fields = sysEntityPo.getClass().getDeclaredFields();
        for (Field field : fields) {
            try {
                field.setAccessible(true);
                String fieldName = field.getName();
                if (fieldName.equals("partnerTotal") || fieldName.equals("branchTotal") || fieldName.equals("employeeTotal")) continue;
                Object fieldValue = field.get(sysEntityPo);
                if (fieldValue == null || fieldValue instanceof List) continue;
                String columnName = StrUtil.toUnderlineCase(fieldName);
                tempMap.put(columnName, fieldValue);
            } catch (IllegalAccessException e) {
                throw new RuntimeException(e);
            }
        }
        // name 在 BaseEntity 取出来
        try {
            Class<?> superclass = sysEntityPo.getClass().getSuperclass();
            if (superclass != null) {
                Field nameField = superclass.getDeclaredField("name");
                nameField.setAccessible(true);
                Object nameValue = nameField.get(sysEntityPo);
                if (nameValue != null) {
                    String columnName = StrUtil.toUnderlineCase(nameField.getName());
                    tempMap.put(columnName, nameValue);
                }
            }
        } catch (IllegalAccessException | NoSuchFieldException e) {
            throw new RuntimeException(e);
        }
        // 存 redis， 特殊字段处理
        convertOptionType(tempMap);
        resultMap.put("bus_entity_business", tempMap);
        // 子表处理
        if (sysEntityPo.getBranchPoList() != null && !sysEntityPo.getBranchPoList().isEmpty()) {
            resultMap.put("bus_entity_branch|List", convertListToUnderlineMapList(sysEntityPo.getBranchPoList()));
        }
        if (sysEntityPo.getChangeRecordPoList() != null && !sysEntityPo.getChangeRecordPoList().isEmpty()) {
            resultMap.put("bus_entity_change_record|List", convertListToUnderlineMapList(sysEntityPo.getChangeRecordPoList()));
        }
        if (sysEntityPo.getEmployeePoList() != null && !sysEntityPo.getEmployeePoList().isEmpty()) {
            resultMap.put("bus_entity_employee|List", convertListToUnderlineMapList(sysEntityPo.getEmployeePoList()));
        }
        if (sysEntityPo.getPartnerPoList() != null && !sysEntityPo.getPartnerPoList().isEmpty()) {
            resultMap.put("bus_entity_partner|List", convertListToUnderlineMapList(sysEntityPo.getPartnerPoList()));
        }
        return resultMap;
    }

    /**
     * 将 SysEntityPo 对象中 List 转化为 map, 驼峰转换
     */
    public <T> List<Map<String, Object>> convertListToUnderlineMapList(List<T> dtoList) {
        if (dtoList == null || dtoList.isEmpty()) return Collections.emptyList();
        return dtoList.stream()
                .map(dto -> {
                    Map<String, Object> map = new HashMap<>();
                    List<Field> allFields = getAllFields(dto.getClass());
                    for (Field field : allFields) {
                        try {
                            field.setAccessible(true);
                            if ("serialVersionUID".equals(field.getName())) continue;
                            Object value = field.get(dto);
                            if (value == null) continue;
                            String dbFieldName = StrUtil.toUnderlineCase(field.getName());
                            map.put(dbFieldName, value);
                        } catch (IllegalAccessException e) {
                            throw new RuntimeException(e);
                        }
                    }
                    return map;
                })
                .collect(Collectors.toList());
    }

    /**
     * 递归获取一个类及其所有父类中声明的字段
     */
    private List<Field> getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        while (clazz != null && clazz != Object.class) {
            fields.addAll(Arrays.asList(clazz.getDeclaredFields()));
            clazz = clazz.getSuperclass();
        }
        return fields;
    }

    /**
     * 构造税务数据
     */
    public Map<String, Object> buildTaxationMap(SysEntityPo sysEntityPo) {
        Map<String, Object> resultMap = new HashMap<>();
        Map<String, Object> tempMap = new HashMap<>();
        tempMap.put("taxpayer_name", sysEntityPo.getName());
        tempMap.put("taxpayer_no", sysEntityPo.getCreditNo());
        tempMap.put("register_type", sysEntityPo.getRegType());
        tempMap.put("register_money", sysEntityPo.getRegisterCapital());
        tempMap.put("register_currency", sysEntityPo.getRegisterCapitalCurrency());
        tempMap.put("register_add", sysEntityPo.getAddress());
        tempMap.put("business_range", sysEntityPo.getBusinessScope());
        convertOptionType(tempMap);
        resultMap.put("bus_taxation_info", tempMap);
        return resultMap;
    }

    /**
     * 转换特殊选项类型字段，将值从名称改为ID
     */
    public void convertOptionType(Map<String, Object> map) {
        // 一个通用的方法来处理选项
        processOptionField(map, "reg_type", "reg_type");
        processOptionField(map, "business_status", "business_status");
        // 税务 登记注册类型 引用 工商 公司类型
        processOptionField(map, "register_type", "reg_type");
    }

    /**
     * 封装处理单个选项字段的逻辑
     */
    public void processOptionField(Map<String, Object> map, String fieldName, String assName) {
        // 从 Map 中获取字段值（名称）
        Object valueObj = map.get(fieldName);
        if (valueObj == null) return;
        String optionName = valueObj.toString();
        // 查选项 apiName 时用工商的字段名查
        SysOptionPo optionPo = optionMapper.selectOne(Wrappers.<SysOptionPo>query().lambda().eq(SysOptionPo::getApiName, assName).last(SqlConstants.LIMIT_ONE));
        if (optionPo == null) return;
        LambdaQueryWrapper<SysOptionValuePo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(SysOptionValuePo::getOptionId, optionPo.getId());
        List<SysOptionValuePo> optionValueList = optionValueMapper.selectList(wrapper);
        // 查找已存在的选项值
        SysOptionValuePo existingValue = optionValueList.stream()
                .filter(v -> v.getName().equals(optionName))
                .findFirst()
                .orElse(null);
        if (existingValue != null) {
            map.put(fieldName, String.valueOf(existingValue.getId()));
        } else {
            // 如果未找到，则创建并插入新选项值
            SysOptionValuePo newOptionValue = new SysOptionValuePo();
            newOptionValue.setName(optionName); // 值
            newOptionValue.setApiName(assName); // 字段名还是存工商字段名
            newOptionValue.setOptionId(optionPo.getId());
            optionValueMapper.insert(newOptionValue);
            // 插入后获取ID
            map.put(fieldName, String.valueOf(newOptionValue.getId()));
        }
    }

    /**
     * 获取关联表单名
     */
    public List<String> getRelationSheetNames(String sheetApiName) {
        Long id = sheetService.selectOneByApiName(sheetApiName).getId();
        SysFieldQuery sysFieldQuery = new SysFieldQuery();
        sysFieldQuery.setRelationSheetId(id);
        sysFieldQuery.setRelationType(NumberUtil.Zero);
        Set<Long> relationSheetId = fieldService.selectList(sysFieldQuery).stream().map(SysFieldDto::getSheetId).filter(Objects::nonNull).collect(Collectors.toSet());
        // 如果一对一没有关联表，就返回原表
        if (relationSheetId.isEmpty()) return Collections.singletonList(sheetApiName);
        // 如果有一对一关联表，就返回关联表
        return sheetService.selectListByIds(relationSheetId).stream().map(SysSheetDto::getApiName).filter(Objects::nonNull).toList();
    }

    /**
     * redis 数据 插入 数据库
     */
    public  Map<String,Long> addFromRedis(UniversalQuery query,Long entityId,Snowflake snowflake,Map<String, Object> redisData) {
        Map<String,Long> sheetApiNameMap=new HashMap<>();
        universalMapper.insertBaseEntity(entityId, query);
        sheetApiNameMap.put("sys_base_entity",entityId);
        for (String sheetName : redisData.keySet()) {
            @SuppressWarnings("unchecked")
            Map<String, Object> allDataForSheet = (Map<String, Object>) redisData.get(sheetName);
            if (allDataForSheet == null || allDataForSheet.isEmpty()) continue;
            @SuppressWarnings("unchecked")
            Map<String, Object> mainTableData = (Map<String, Object>) allDataForSheet.get(sheetName);
            if (mainTableData == null) continue;
            Long mainId = snowflake.nextId();
            mainTableData.put("id", mainId);
            mainTableData.put("entity_id", entityId);
            mainTableData.put("del_flag", NumberUtil.Zero);
            mainTableData.put("create_time", LocalDateTime.now());
            universalMapper.insertEntity(mainTableData, sheetName);
            sheetApiNameMap.put(sheetName,mainId);
            for (Map.Entry<String, Object> entry : allDataForSheet.entrySet()) {
                String key = entry.getKey();
                if (key.equals(sheetName)) continue;
                // 一对多
                if (key.endsWith("|List")) {
                    String subTableName = key.substring(0, key.length() - "|List".length());
                    @SuppressWarnings("unchecked")
                    List<Map<String, Object>> subTableList = (List<Map<String, Object>>) entry.getValue();
                    if (subTableList != null && !subTableList.isEmpty()) {
                        subTableList.forEach(item -> {
                            item.put("id", snowflake.nextId());
                            item.put("busi_id", mainId);
                            item.put("del_flag", NumberUtil.Zero);
                            item.put("create_time", LocalDateTime.now());
                        });
                        universalMapper.insertSubList(subTableList, subTableName);
                    }
                } else {
                    // 一对一 key为表名
                    @SuppressWarnings("unchecked")
                    Map<String, Object> subTableData = (Map<String, Object>) entry.getValue();
                    if (subTableData != null && !subTableData.isEmpty()) {
                        subTableData.put("id", snowflake.nextId());
                        subTableData.put("busi_id", mainId);
                        subTableData.put("del_flag", NumberUtil.Zero);
                        subTableData.put("create_time", LocalDateTime.now());
                        universalMapper.insertEntity(subTableData, key);
                    }
                }
            }
        }
        return sheetApiNameMap;
    }

    /**
     * 对包含数组的 JSON 字符串进行规范化处理 如：多选
     */
    public void normalizeJsonObject(JSONObject jsonObject) {
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            Object value = entry.getValue();
            if (value instanceof JSONArray) {
                jsonObject.put(entry.getKey(), String.join(",", ((JSONArray) value).toJavaList(String.class)));
            }
        }
    }



//    /**
//     * 根据 fieldId 查询并填充字段数据
//     * @param fieldId 字段ID
//     * @return 字段详情DTO
//     */
//    public SysFieldDto getFieldDetail(Long fieldId) {
//        SysFieldDto sysFieldDto = fieldService.selectById(fieldId);
//        if (sysFieldDto == null) return null;
//        SysFieldDto sourceField = sysFieldDto;
//        if (Objects.equals(sysFieldDto.getFieldType(), FieldTypeConstants.QUOTE)) {
//            sourceField = fieldService.selectById(sysFieldDto.getQuoteSheetFieldId());
//            if (sourceField == null) return sysFieldDto;
//        }
//        if (Objects.equals(sourceField.getFieldType(), FieldTypeConstants.SELECT_SINGLE) ||
//                Objects.equals(sourceField.getFieldType(), FieldTypeConstants.SELECT_MULTI)) {
//            LambdaQueryWrapper<SysOptionValuePo> wrapper = Wrappers.lambdaQuery();
//            wrapper.eq(SysOptionValuePo::getOptionId, sourceField.getOptionId());
//            List<SysOptionValuePo> sysOptionValuePos = optionValueMapper.selectList(wrapper);
//            sysFieldDto.setOptionValuePoList(sysOptionValuePos);
//        } else if (Objects.equals(sourceField.getFieldType(), FieldTypeConstants.CASCADE)) {
//            List<Tree<String>> trees = sysCascadeManager.selectCascadeRelation(sourceField.getCascadeId());
//            sysFieldDto.setCasecadeTreeList(trees);
//        }
//        return sysFieldDto;
//    }
}
