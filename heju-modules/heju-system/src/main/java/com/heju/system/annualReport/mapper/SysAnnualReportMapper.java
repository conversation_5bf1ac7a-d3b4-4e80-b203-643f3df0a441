package com.heju.system.annualReport.mapper;

import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.common.datasource.annotation.Isolate;
import com.heju.system.annualReport.domain.dto.SysAnnualReportDto;
import com.heju.system.annualReport.domain.po.SysAnnualReportPo;
import com.heju.system.annualReport.domain.query.SysAnnualReportQuery;
import org.apache.ibatis.annotations.*;

import java.util.List;
import java.util.Map;

/**
 * 工商年报管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysAnnualReportMapper extends BaseMapper<SysAnnualReportQuery, SysAnnualReportDto, SysAnnualReportPo> {
    @Select("<script>" +
            "SELECT " +
            "  sar.*, " +
            "  se.name AS entityName, " +
            "  su.nick_name AS createName " +
            "FROM sys_annual_report sar " +
            "LEFT JOIN sys_entity se ON sar.entity_id = se.id " +
            "LEFT JOIN sys_user su ON sar.create_by = su.id " +
            "WHERE sar.del_flag = 0 " +
            "<if test='query.entityId != null'> AND sar.entity_id = #{query.entityId} </if>" +
            "<if test='query.annualReportType != null'> AND annual_report_type = #{query.annualReportType} </if>" +
            "<if test='query.reportYear != null'> AND report_year = #{query.reportYear} </if>" +
            "ORDER BY sar.entity_id, sar.report_year ASC" +
            "</script>")
    List<Map<String, Object>> selectAllJoinedList(@Param("query") SysAnnualReportQuery query);


    @Select("<script>" +
            "select * from sys_annual_report where del_flag = '0'" +
            " and (case when #{entityId} is not null then entity_id = #{entityId} else 1=1 end)" +
            " and (case when #{reportYear} is not null then report_year = #{reportYear} else 1=1 end)" +
            "</script>")
    SysAnnualReportPo selectByEntityAndYear(SysAnnualReportDto annualReport);
}