package com.heju.system.dict.mapper;

import com.heju.common.datasource.annotation.Master;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.api.dict.domain.dto.SysDictTypeDto;
import com.heju.system.api.dict.domain.po.SysDictTypePo;
import com.heju.system.api.dict.domain.query.SysDictTypeQuery;

/**
 * 字典类型管理 数据层
 *
 * <AUTHOR>
 */
@Master
public interface SysDictTypeMapper extends BaseMapper<SysDictTypeQuery, SysDictTypeDto, SysDictTypePo> {
}
