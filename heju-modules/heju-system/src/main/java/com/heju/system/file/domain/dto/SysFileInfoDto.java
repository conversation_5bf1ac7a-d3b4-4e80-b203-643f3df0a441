package com.heju.system.file.domain.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.heju.system.file.domain.po.SysFileInfoPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;

import static com.heju.system.file.constant.TempStorageConstant.DEL_DAY;

/**
 * 文件信息 数据传输对象
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SysFileInfoDto extends SysFileInfoPo {

    @Serial
    private static final long serialVersionUID = 1L;

    private String  viewRoleIds;

    private String downloadRoleIds;

    //角色id列表
    private List<Long> roleViewIds;

    private List<Long> roleDownloadIds;

    private String classifyName;

    private String positionName;

    /**
     * 操作类型
     */
    private Integer operateType;

    private Integer isView;

    private Integer isDownload;

    private String operateTypes;

    private String isViews;

    private String isDownloads;

    private String fileSize;

    private Long delFlag;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime deleteTime;

    private Long recycleCountdown;

    public Long getRecycleCountdown() {
        LocalDateTime deleteTime = getDeleteTime();
        if (deleteTime == null) return null;
        long days = ChronoUnit.DAYS.between(deleteTime, LocalDateTime.now());
        long left = Integer.parseInt(DEL_DAY) - days;
        return left > 0 ? left : 0;
    }
}
