package com.heju.system.annualReport.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.annualReport.domain.dto.SysAnnualReportDto;
import com.heju.system.annualReport.domain.model.SysAnnualReportConverter;
import com.heju.system.annualReport.domain.po.SysAnnualReportPo;
import com.heju.system.annualReport.domain.query.SysAnnualReportQuery;
import com.heju.system.annualReport.manager.ISysAnnualReportManager;
import com.heju.system.annualReport.mapper.SysAnnualReportMapper;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 工商年报管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysAnnualReportManager extends BaseManagerImpl<SysAnnualReportQuery, SysAnnualReportDto, SysAnnualReportPo, SysAnnualReportMapper, SysAnnualReportConverter> implements ISysAnnualReportManager {

    /**
     * 全量查询所有年度报告，并按照公司ID和报告年份进行排序。
     * @param query 工商年报对象
     * @return 按照 entity_id 和 report_year 排序后的所有年度报告列表。
     */
    @Override
    public List<Map<String, Object>> selectByEntity(SysAnnualReportQuery query) {
        // 一对多查询
//        LambdaQueryWrapper<SysAnnualReportPo> wrapper = new LambdaQueryWrapper<>(annualReport);
//        wrapper.eq(SysAnnualReportPo::getDelFlag, 0L)
//                .orderByAsc(SysAnnualReportPo::getEntityId)
//                .orderByAsc(SysAnnualReportPo::getReportYear);
        return baseMapper.selectAllJoinedList(query);
    }

    /**
     * 全量查询所有年度报告 (不包含未申报)
     * @param annualReport 工商年报对象
     * @return 筛选后的所有年度报告列表
     */
    @Override
    public List<SysAnnualReportPo> selectPartList(SysAnnualReportQuery annualReport) {
        // 1.构造查询条件
        LambdaQueryWrapper<SysAnnualReportPo> wrapper = new LambdaQueryWrapper<>(annualReport);
        wrapper.eq(SysAnnualReportPo::getDelFlag, 0L)
                .ne(SysAnnualReportPo::getStatus, "0"); // 排除未申报
        return baseMapper.selectList(wrapper);
    }

    /**
     * 查询单个工商年报 通过entityId and reportYear
     * @param annualReport 工商年报对象
     * @return 工商年报对象
     */
    @Override
    public SysAnnualReportPo selectByEntityAndYear(SysAnnualReportDto annualReport) {
        return baseMapper.selectByEntityAndYear(annualReport);
    }
}

