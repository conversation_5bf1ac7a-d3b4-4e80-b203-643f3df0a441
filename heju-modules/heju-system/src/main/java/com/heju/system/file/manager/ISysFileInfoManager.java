package com.heju.system.file.manager;

import com.heju.common.web.entity.manager.IBaseManager;
import com.heju.system.file.domain.dto.SysFileInfoDto;
import com.heju.system.file.domain.query.SysFileInfoQuery;

import java.io.Serializable;
import java.util.List;

/**
 * 文件信息管理 数据封装层
 *
 * <AUTHOR>
 */
public interface ISysFileInfoManager extends IBaseManager<SysFileInfoQuery, SysFileInfoDto> {

    /**
     * 查列表
     * @param query
     * @return
     */
    List<SysFileInfoDto> selectByQuery(SysFileInfoQuery query);

    /**
     * 查admin列表
     * @param query
     * @return
     */
    List<SysFileInfoDto> selectAdminList(SysFileInfoQuery query);

    /**
     * 查详情
     * @param id
     * @return
     */
    SysFileInfoDto selectInfoById(Serializable id);

    void insertFileRole(String[] viewRole,String[] downloadRole,Long fileId);

    /**
     * 暂存管理查询
     * @param query
     * @return
     */
    List<SysFileInfoDto> tempStorageList(SysFileInfoQuery query);

    /**
     * 回收站列表查询
     * @param query
     * @return
     */
    List<SysFileInfoDto> selectRecycList(SysFileInfoQuery query);

    int updateByIds(List<Long> fileIds);
}
