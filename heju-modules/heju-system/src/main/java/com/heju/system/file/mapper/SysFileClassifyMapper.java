package com.heju.system.file.mapper;

import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.mapper.TreeMapper;
import com.heju.system.file.domain.dto.SysFileClassifyDto;
import com.heju.system.file.domain.po.SysFileClassifyPo;
import com.heju.system.file.domain.query.SysFileClassifyQuery;

/**
 * 文件分类管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysFileClassifyMapper extends TreeMapper<SysFileClassifyQuery, SysFileClassifyDto, SysFileClassifyPo> {

}
