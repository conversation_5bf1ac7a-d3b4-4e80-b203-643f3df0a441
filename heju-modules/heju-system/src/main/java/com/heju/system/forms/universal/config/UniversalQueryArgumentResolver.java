package com.heju.system.forms.universal.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.heju.system.forms.universal.domain.query.UniversalQuery;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.lang.reflect.Field;
import java.util.Map;

/**
 * UniversalQuery 自定义参数解析器
 * 用于处理动态字段，避免 InvalidPropertyException
 * 
 * <AUTHOR>
 */
@Component
public class UniversalQueryArgumentResolver implements HandlerMethodArgumentResolver {

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return UniversalQuery.class.isAssignableFrom(parameter.getParameterType());
    }

    @Override
    public Object resolveArgument(MethodParameter parameter, ModelAndViewContainer mavContainer,
                                  NativeWebRequest webRequest, WebDataBinderFactory binderFactory) throws Exception {
        
        HttpServletRequest request = webRequest.getNativeRequest(HttpServletRequest.class);
        UniversalQuery query = new UniversalQuery();
        
        // 判断是GET还是POST请求
        if ("GET".equalsIgnoreCase(request.getMethod())) {
            // GET请求：从查询参数中解析
            resolveFromQueryParams(request, query);
        } else if ("POST".equalsIgnoreCase(request.getMethod())) {
            // POST请求：从请求体中解析
            resolveFromRequestBody(request, query);
        }
        
        return query;
    }
    
    /**
     * 从查询参数中解析（GET请求）
     */
    private void resolveFromQueryParams(HttpServletRequest request, UniversalQuery query) throws Exception {
        Map<String, String[]> parameterMap = request.getParameterMap();
        
        for (Map.Entry<String, String[]> entry : parameterMap.entrySet()) {
            String fieldName = entry.getKey();
            String[] values = entry.getValue();
            String value = values.length > 0 ? values[0] : null;
            
            if (value == null || value.trim().isEmpty()) {
                continue;
            }
            
            // 尝试设置到UniversalQuery的已知字段
            if (setKnownField(query, fieldName, value)) {
                continue;
            }
            
            // 未知字段放入动态字段容器
            query.setDynamicField(fieldName, value);
        }
    }
    
    /**
     * 从请求体中解析（POST请求）
     */
    private void resolveFromRequestBody(HttpServletRequest request, UniversalQuery query) throws Exception {
        StringBuilder jsonBuilder = new StringBuilder();
        try (BufferedReader reader = request.getReader()) {
            String line;
            while ((line = reader.readLine()) != null) {
                jsonBuilder.append(line);
            }
        }
        
        String jsonString = jsonBuilder.toString();
        if (jsonString.trim().isEmpty()) {
            return;
        }
        
        // 解析JSON
        @SuppressWarnings("unchecked")
        Map<String, Object> jsonMap = objectMapper.readValue(jsonString, Map.class);
        
        for (Map.Entry<String, Object> entry : jsonMap.entrySet()) {
            String fieldName = entry.getKey();
            Object value = entry.getValue();
            
            // 尝试设置到UniversalQuery的已知字段
            if (setKnownField(query, fieldName, value)) {
                continue;
            }
            
            // 未知字段放入动态字段容器
            query.setDynamicField(fieldName, value);
        }
    }
    
    /**
     * 设置已知字段
     */
    private boolean setKnownField(UniversalQuery query, String fieldName, Object value) {
        try {
            Field field = UniversalQuery.class.getDeclaredField(fieldName);
            field.setAccessible(true);
            
            // 类型转换
            Object convertedValue = convertValue(value, field.getType());
            field.set(query, convertedValue);
            return true;
        } catch (NoSuchFieldException | IllegalAccessException e) {
            // 字段不存在或无法访问，返回false
            return false;
        }
    }
    
    /**
     * 简单的类型转换
     */
    private Object convertValue(Object value, Class<?> targetType) {
        if (value == null) {
            return null;
        }
        
        if (targetType.isAssignableFrom(value.getClass())) {
            return value;
        }
        
        String stringValue = value.toString();
        
        if (targetType == Integer.class || targetType == int.class) {
            return Integer.valueOf(stringValue);
        } else if (targetType == Long.class || targetType == long.class) {
            return Long.valueOf(stringValue);
        } else if (targetType == Boolean.class || targetType == boolean.class) {
            return Boolean.valueOf(stringValue);
        }
        
        return stringValue;
    }
}
