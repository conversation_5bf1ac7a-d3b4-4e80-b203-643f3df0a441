package com.heju.system.file.service.impl;

import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.system.file.domain.dto.SysFileRoleMergeDto;
import com.heju.system.file.domain.query.SysFileRoleMergeQuery;
import com.heju.system.file.manager.ISysFileRoleMergeManager;
import com.heju.system.file.service.ISysFileRoleMergeService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 文件角色权限关联管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysFileRoleMergeServiceImpl extends BaseServiceImpl<SysFileRoleMergeQuery, SysFileRoleMergeDto, ISysFileRoleMergeManager> implements ISysFileRoleMergeService {

    /**
     * 查询文件角色权限关联对象列表 | 数据权限
     *
     * @param fileRoleMerge 文件角色权限关联对象
     * @return 文件角色权限关联对象集合
     */
    @Override
    //@DataScope(userAlias = "createBy", mapperScope = {"SysFileRoleMergeMapper"})
    public List<SysFileRoleMergeDto> selectListScope(SysFileRoleMergeQuery fileRoleMerge) {
        return baseManager.selectList(fileRoleMerge);
    }

}
