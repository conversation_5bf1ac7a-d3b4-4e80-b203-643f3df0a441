package com.heju.system.utils;

public class FilterOperatorConstants {
    // 等于
    public static final String EQUAL = "1";
    // 不等于
    public static final String NOT_EQUAL = "2";
    // 包含
    public static final String LIKE = "3";
    // 为空
    public static final String IS_NULL = "4";
    // 非空
    public static final String IS_NOT_NULL = "5";
    // 属于 (IN)
    public static final String IN = "6";
    // 不属于 (NOT IN)
    public static final String NOT_IN = "7";
    // 大于
    public static final String GREATER_THAN = "8";
    // 大于等于
    public static final String GREATER_THAN_OR_EQUAL = "9";
    // 小于
    public static final String LESS_THAN = "10";
    // 小于等于
    public static final String LESS_THAN_OR_EQUAL = "11";
    // 介于 (BETWEEN)
    public static final String BETWEEN = "12";
}
