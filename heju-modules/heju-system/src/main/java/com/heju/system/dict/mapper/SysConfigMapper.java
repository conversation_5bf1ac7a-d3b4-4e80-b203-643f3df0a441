package com.heju.system.dict.mapper;

import com.heju.common.datasource.annotation.Master;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.system.api.dict.domain.dto.SysConfigDto;
import com.heju.system.api.dict.domain.po.SysConfigPo;
import com.heju.system.api.dict.domain.query.SysConfigQuery;

/**
 * 参数配置管理 数据层
 *
 * <AUTHOR>
 */
@Master
public interface SysConfigMapper extends BaseMapper<SysConfigQuery, SysConfigDto, SysConfigPo> {
}