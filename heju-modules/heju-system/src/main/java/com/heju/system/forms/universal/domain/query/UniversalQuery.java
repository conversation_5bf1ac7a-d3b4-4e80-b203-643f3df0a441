package com.heju.system.forms.universal.domain.query;

import lombok.Data;

import java.io.Serial;
import java.util.List;

@Data
public class UniversalQuery {

    @Serial
    private static final long serialVersionUID = 1L;

    private String sheetApiName;

    private Long sheetId;

    private Long id;

    //pageNum    当前页码，从 1 开始
    private Integer page;

    //pageSize   每页条数
    private Integer pageSize;

    //排序字段，例如："sys_company.name"
    private String orderSort;

    private List<String> sheetApiNameList;

    private Long fieldId;

    private List<Long> idList;

    private List<String> optionFieldList;

    private String universalJson;

    private Integer addType;

    private Long foreignKey;

    private String creditNo;

    //是否升序（true 为升序，false 为降序）
    private Boolean ascending;

}
