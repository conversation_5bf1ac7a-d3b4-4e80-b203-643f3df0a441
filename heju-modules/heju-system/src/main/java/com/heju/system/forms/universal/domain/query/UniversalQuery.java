package com.heju.system.forms.universal.domain.query;

import com.heju.system.forms.universal.domain.dto.UniversalFilterDto;
import lombok.Data;

import java.io.Serial;
import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
public class UniversalQuery implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    private String sheetApiName;

    private Long sheetId;

    private Long id;

    //pageNum    当前页码，从 1 开始
    private Integer page;

    //pageSize   每页条数
    private Integer pageSize;

    //排序字段，例如："sys_company.name"
    private String orderSort;

    private List<String> sheetApiNameList;

    private Long fieldId;

    // 批量删除(传入 id 情况)
    private List<Long> idList;

    private List<String> optionFieldList;

    private String universalJson;

    private Integer addType;

    private Long foreignKey;

    private String creditNo;

    // 批量删除(传入 credit_no 情况)
    private List<String> creditNoList;

    private String name;

    private String code;

    //是否升序（true 为升序，false 为降序）
    private Boolean ascending;

    // 筛选器条件
    private UniversalFilterDto filters;

    // 动态字段容器，用于接收前端传递的任意字段
    private Map<String, Object> dynamicFields = new HashMap<>();

    /**
     * 获取动态字段值
     * @param fieldName 字段名
     * @return 字段值
     */
    public Object getDynamicField(String fieldName) {
        return dynamicFields.get(fieldName);
    }

    /**
     * 设置动态字段值
     * @param fieldName 字段名
     * @param value 字段值
     */
    public void setDynamicField(String fieldName, Object value) {
        dynamicFields.put(fieldName, value);
    }
}
