package com.heju.system.forms.universal.controller;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.system.forms.universal.domain.query.UniversalQuery;
import com.heju.system.forms.universal.service.UniversalService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.ParseException;

@RestController
@RequestMapping("/universal")
public class UniversalController {

    @Resource
    private UniversalService universalService;


    /**
     * 通用查询业务表单列表
     * @param query 搜索条件json
     * @return 业务表单列表
     */
    @GetMapping("/list")
    public AjaxResult list(UniversalQuery query) {
        return universalService.list(query);
    }

    /**
     * 通用查询业务表单新增/修改时字段列表
     * @param query 查询条件
     * @return 业务表单列表
     */
    @GetMapping("/fieldList")
    public AjaxResult addFieldList(UniversalQuery query) {
        return universalService.addFieldList(query);
    }

    /**
     * 通用查询业务表单列表时字段列表
     * @param query 查询条件
     * @return 业务表单列表
     */
    @GetMapping("/searchFieldList")
    public AjaxResult searchFieldList(UniversalQuery query) {
        return universalService.searchFieldList(query);
    }

    /**
     * 通用查询业务详细
     */
    @GetMapping(value = "/getInfo")
    public AjaxResult getInfo(UniversalQuery query) {
        return universalService.getInfo(query);
    }

    /**
     * 通用业务新增
     */
    @PostMapping
    public AjaxResult add(@RequestBody UniversalQuery query) throws ParseException {
        return universalService.add(query);
    }

    /**
     * 通用业务修改
     */
    @PutMapping
    public AjaxResult edit(@RequestBody UniversalQuery query) {
        return universalService.edit(query);
    }

    /**
     * 通用业务批量删除
     */
    @DeleteMapping("/batch")
    public AjaxResult remove(UniversalQuery query) {
        return universalService.batchRemove(query);
    }

    /**
     * 获取通用业务选择框列表
     */
    @GetMapping("/option")
    public AjaxResult option(UniversalQuery query) {
        return universalService.option(query);
    }

    /**
     * 检查通用实体表信息
     */
    @GetMapping("/check")
    public AjaxResult check(UniversalQuery query) {
        return universalService.check(query);
    }

    /**
     * 根据引用字段id查询字段信息
     */
    @PostMapping("/getQuoteInfo")
    public AjaxResult getQuoteInfo(@RequestBody UniversalQuery query) {
        return universalService.getQuoteInfo(query);
    }

    /**
     * 根据统一社会信用代码查询企业信息
     */
    @GetMapping("/getInfoByCreditNo")
    public AjaxResult getInfoByCreditNo(UniversalQuery query) {
        return universalService.getInfoByCreditNo(query);
    }

//
//    /**
//     * 导入excel
//     *
//     * @param file 文件流
//     * @return 成功失败
//     */
//    @PostMapping("/importData")
//    @ResponseBody
//    @Log(title = "实体信息管理管理", businessType = BusinessType.IMPORT)
//    @RequiresPermissions(Auth.SYS_ENTITY_IMPORT)
//    public AjaxResult importData(MultipartFile file) throws Exception {
//        ExcelUtil<SysEntity> util = new ExcelUtil<>(SysEntity.class);
//        List<SysEntity> userList = util.importExcel(file.getInputStream());
//        String UUID = usingUUID();
//        redisService.setCacheMapValue(CacheConstants.CacheType.SYS_ENTITY_UUID_KEY.getCode(), UUID, NumberUtil.Zero);
//        entityService.importData(userList, UUID);
//        return AjaxResult.success(UUID);
//    }
//
//    /**
//     * @param redisUUID 导入excel 结果标识
//     * @return 成功/失败
//     */
//    @GetMapping("/getImportResult/{redisUUID}")
//    @ResponseBody
//    public AjaxResult getImportResult(@PathVariable String redisUUID) {
//        Object cacheMapValue = redisService.getCacheMapValue(CacheConstants.CacheType.SYS_ENTITY_UUID_KEY.getCode(), redisUUID);
//        if (!cacheMapValue.toString().equals("0")) {
//            redisService.deleteCacheMapValue(CacheConstants.CacheType.SYS_ENTITY_UUID_KEY.getCode(), redisUUID);
//        }
//        return AjaxResult.success(cacheMapValue.toString());
//    }
//
//    /**
//     * 下载模板
//     *
//     * @param response http请求response
//     */
//    @GetMapping("/importTemplate")
//    @ResponseBody
//    @Log(title = "实体信息管理管理", businessType = BusinessType.EXPORT)
//    @RequiresPermissions(Auth.SYS_ENTITY_TEMPLATE)
//    public void importTemplate(HttpServletResponse response) {
//        ExcelUtil<SysEntity> util = new ExcelUtil<>(SysEntity.class);
//        util.importTemplateExcel(response, "实体信息数据");
//    }
//
//    public static String usingUUID() {
//        UUID randomUUID = UUID.randomUUID();
//        return randomUUID.toString().replaceAll("-", "");
//    }
//
//    /**
//     * 获取实体下拉框列表
//     */
//    @GetMapping("/drop")
//    public SysEntityPo[] drop() {
//        return entityService.drop();
//    }
}
