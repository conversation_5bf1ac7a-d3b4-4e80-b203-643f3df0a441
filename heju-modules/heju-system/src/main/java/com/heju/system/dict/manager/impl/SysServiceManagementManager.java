package com.heju.system.dict.manager.impl;

import com.heju.common.core.utils.core.CollUtil;
import com.heju.system.dict.domain.po.SysServiceManagementPo;
import com.heju.system.dict.domain.dto.SysServiceManagementDto;
import com.heju.system.dict.domain.query.SysServiceManagementQuery;
import com.heju.system.dict.domain.model.SysServiceManagementConverter;
import com.heju.system.dict.domain.vo.SysServiceTree;
import com.heju.system.dict.mapper.SysServiceManagementMapper;
import com.heju.common.web.entity.manager.impl.TreeManagerImpl;
import com.heju.system.dict.manager.ISysServiceManagementManager;
import com.heju.system.organize.domain.vo.SysOrganizeTree;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 服务项目管理管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysServiceManagementManager extends TreeManagerImpl<SysServiceManagementQuery, SysServiceManagementDto, SysServiceManagementPo, SysServiceManagementMapper, SysServiceManagementConverter> implements ISysServiceManagementManager {

    @Override
    public List<SysServiceTree> selectSuperiorService() {
        SysServiceManagementQuery sysServiceManagementQuery = new SysServiceManagementQuery();
        List<SysServiceManagementDto> servicelist = this.selectList(sysServiceManagementQuery);
        return new ArrayList<>(servicelist.stream().map(SysServiceTree::new).collect(Collectors.toList()));
    }
}