package com.heju.system.file.manager.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.file.domain.dto.SysFileRecordDto;
import com.heju.system.file.domain.dto.SysFileRoleMergeDto;
import com.heju.system.file.domain.model.SysFileRoleMergeConverter;
import com.heju.system.file.domain.po.SysFileRoleMergePo;
import com.heju.system.file.domain.query.SysFileRoleMergeQuery;
import com.heju.system.file.manager.ISysFileRoleMergeManager;
import com.heju.system.file.mapper.SysFileRoleMergeMapper;
import org.springframework.stereotype.Component;

/**
 * 文件角色权限关联管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysFileRoleMergeManager extends BaseManagerImpl<SysFileRoleMergeQuery, SysFileRoleMergeDto, SysFileRoleMergePo, SysFileRoleMergeMapper, SysFileRoleMergeConverter> implements ISysFileRoleMergeManager {


    @Override
    public void deleteByFileId(Long fileId) {
        baseMapper.delete(new LambdaQueryWrapper<SysFileRoleMergePo>().eq(SysFileRoleMergePo::getFileId,fileId) );
    }
}
