package com.heju.system.dict.domain.model;

import com.heju.common.core.web.entity.model.TreeConverter;
import com.heju.system.dict.domain.dto.SysServiceManagementDto;
import com.heju.system.dict.domain.po.SysServiceManagementPo;
import com.heju.system.dict.domain.query.SysServiceManagementQuery;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;

/**
 * 服务项目管理 对象映射器
 *
 * <AUTHOR>
 */
@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface SysServiceManagementConverter extends TreeConverter<SysServiceManagementQuery, SysServiceManagementDto, SysServiceManagementPo> {
}
