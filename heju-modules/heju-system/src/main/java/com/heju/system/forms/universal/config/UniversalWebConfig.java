package com.heju.system.forms.universal.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import java.util.List;

/**
 * Universal模块Web配置
 * 
 * <AUTHOR>
 */
@Configuration
public class UniversalWebConfig implements WebMvcConfigurer {

    @Autowired
    private UniversalQueryArgumentResolver universalQueryArgumentResolver;

    @Override
    public void addArgumentResolvers(List<HandlerMethodArgumentResolver> resolvers) {
        resolvers.add(universalQueryArgumentResolver);
    }
}
