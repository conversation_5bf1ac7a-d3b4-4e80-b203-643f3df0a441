package com.heju.system.utils;

import com.heju.system.forms.field.domain.dto.SysFieldDto;
import com.heju.system.forms.universal.domain.dto.UniversalFilterDto;
import com.heju.system.forms.universal.domain.po.UniversalFilterPo;
import lombok.extern.slf4j.Slf4j;

import java.time.LocalDateTime;
import java.time.format.DateTimeParseException;
import java.util.*;

@Slf4j
public class DynamicFilterUtil {
    public static List<Map<String, Map<String, Object>>> filter(List<Map<String, Map<String, Object>>> data,
                                                                UniversalFilterDto filterDto,
                                                                Map<Long, SysFieldDto> fieldMetaMap) {
        if (filterDto == null || filterDto.getFilterPoList() == null || filterDto.getFilterPoList().isEmpty()) {
            return data;
        }
        Integer type = Optional.ofNullable(filterDto.getFilterType()).orElse(0);
        return data.stream()
                .filter(item -> matches(item, filterDto.getFilterPoList(), type, fieldMetaMap))
                .toList();
    }

    private static boolean matches(Map<String, Map<String, Object>> item,
                                   List<UniversalFilterPo> conditions,
                                   Integer type,
                                   Map<Long, SysFieldDto> fieldMetaMap) {
        return (type == 1)
                ? conditions.stream().anyMatch(cond -> matchOne(item, cond, fieldMetaMap)) // OR
                : conditions.stream().allMatch(cond -> matchOne(item, cond, fieldMetaMap)); // AND
    }

    private static boolean matchOne(Map<String, Map<String, Object>> item,
                                    UniversalFilterPo condition,
                                    Map<Long, SysFieldDto> fieldMetaMap) {
        SysFieldDto fieldMeta = fieldMetaMap.get(condition.getFieldId());
        if (fieldMeta == null) return false;
        String tableName = fieldMeta.getSheetApiName();
        String fieldName = fieldMeta.getApiName();
        Object fieldValue = getFieldValue(item, tableName, fieldName);
        List<Object> value = condition.getValue();

        if (condition.getOperator() == null) return false;
        return switch (condition.getOperator()) {
            case FilterOperatorConstants.EQUAL -> Objects.equals(fieldValue, value.get(0));
            case FilterOperatorConstants.NOT_EQUAL -> !Objects.equals(fieldValue, value.get(0));
            case FilterOperatorConstants.LIKE -> fieldValue != null && value.get(0) != null &&
                    fieldValue.toString().toLowerCase().contains(value.get(0).toString().toLowerCase());
            case FilterOperatorConstants.IS_NULL -> fieldValue == null;
            case FilterOperatorConstants.IS_NOT_NULL -> fieldValue != null;
            case FilterOperatorConstants.IN -> value.contains(fieldValue);
            case FilterOperatorConstants.NOT_IN -> !value.contains(fieldValue);
            case FilterOperatorConstants.GREATER_THAN -> compare(fieldValue, value.get(0)) > 0;
            case FilterOperatorConstants.GREATER_THAN_OR_EQUAL -> compare(fieldValue, value.get(0)) >= 0;
            case FilterOperatorConstants.LESS_THAN -> compare(fieldValue, value.get(0)) < 0;
            case FilterOperatorConstants.LESS_THAN_OR_EQUAL -> compare(fieldValue, value.get(0)) <= 0;
            case FilterOperatorConstants.BETWEEN ->{
                if (value == null || value.size() < 2) yield false;
                Object start = value.get(0);
                Object end = value.get(1);
                yield compare(fieldValue, start) >= 0 && compare(fieldValue, end) <= 0;
            }
            default -> { log.warn("Unsupported operator: {}", condition.getOperator()); yield false; }
        };
    }

    /**
     * 从 resultList 的结构中取值
     */
    private static Object getFieldValue(Map<String, Map<String, Object>> item, String tableName, String fieldName) {
        if (item == null) return null;
        Map<String, Object> tableData = item.get(tableName);
        if (tableData == null) return null;
        return tableData.get(fieldName);
    }

    /**
     * 比较两个对象的大小
     */
    @SuppressWarnings({"rawtypes", "unchecked"})
    private static int compare(Object o1, Object o2) {
        if (o1 == null || o2 == null) return -1;
        // 数字
        if (o1 instanceof Number && o2 instanceof Number) {
            return Double.compare(((Number) o1).doubleValue(), ((Number) o2).doubleValue());
        }
        // 处理java.util.Date / java.time.LocalDateTime 与  ISO 8601 格式的字符串
        if ((o1 instanceof Date || o1 instanceof LocalDateTime) && o2 instanceof String) {
            try {
                // 统一转换为LocalDateTime
                LocalDateTime ldt1;
                if (o1 instanceof Date) {
                    ldt1 = ((Date) o1).toInstant().atZone(java.time.ZoneId.systemDefault()).toLocalDateTime();
                } else {
                    ldt1 = (LocalDateTime) o1;
                }
                // 前端 iso 8901 字符串转化
                LocalDateTime ldt2 = LocalDateTime.parse((String) o2);
                return ldt1.compareTo(ldt2);
            } catch (DateTimeParseException ignored) {}
        }
        // 处理可比较对象，包括日期和字符串
        if (o1 instanceof Comparable && o2 instanceof Comparable) {
            // 确保类型相同，避免 ClassCastException
            if (o1.getClass().equals(o2.getClass())) {
                return ((Comparable) o1).compareTo(o2);
            }
        }
        return o1.toString().compareTo(o2.toString());
    }
}
