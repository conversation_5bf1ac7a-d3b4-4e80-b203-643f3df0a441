package com.heju.system.forms.universal.domain.dto;

import com.heju.system.forms.universal.domain.po.UniversalFilterPo;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serial;
import java.util.List;

@Data
@EqualsAndHashCode(callSuper = true)
public class UniversalFilterDto extends UniversalFilterPo {

    @Serial
    private static final long serialVersionUID = 1L;

    // 筛选 0-且 1-或
    private Integer filterType;

    // 筛选条件
    private List<UniversalFilterPo> filterPoList;
}
