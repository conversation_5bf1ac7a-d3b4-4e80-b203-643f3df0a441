package com.heju.system.utils;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.poi.ss.formula.functions.T;

import java.util.List;


@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageUniversalResult<T> {

    private long total;       // 总条数
    private int pageNum;      // 当前页码（从 1 开始）
    private int pageSize;     // 每页条数
    private List<T> records;  // 当前页数据
}
