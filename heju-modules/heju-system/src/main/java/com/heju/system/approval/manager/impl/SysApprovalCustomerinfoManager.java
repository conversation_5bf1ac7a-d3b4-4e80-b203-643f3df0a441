package com.heju.system.approval.manager.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DSTransactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.system.approval.domain.dto.SysApprovalCustomerinfoDto;
import com.heju.system.approval.domain.model.SysApprovalCustomerinfoConverter;
import com.heju.system.approval.domain.po.SysApprovalCustomerinfoPo;
import com.heju.system.approval.domain.query.SysApprovalCustomerinfoQuery;
import com.heju.system.approval.manager.ISysApprovalCustomerinfoManager;
import com.heju.system.approval.mapper.SysApprovalCustomerinfoMapper;
import com.heju.system.forms.field.manager.impl.SysFieldManager;
import com.heju.system.forms.sheet.manager.impl.SysSheetManager;
import com.heju.system.forms.universal.service.UniversalService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 客户信息审核管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysApprovalCustomerinfoManager extends BaseManagerImpl<SysApprovalCustomerinfoQuery, SysApprovalCustomerinfoDto, SysApprovalCustomerinfoPo, SysApprovalCustomerinfoMapper, SysApprovalCustomerinfoConverter> implements ISysApprovalCustomerinfoManager {

    @Autowired
    private SysFieldManager sysFieldManager;
    @Autowired
    private SysSheetManager sysSheetManager;
    @Resource
    private UniversalService universalService;
    
    public List<SysApprovalCustomerinfoDto> selectByQuery(SysApprovalCustomerinfoQuery query) {
        // 1. sql查
//        if (query.getFieldName() == null || query.getFieldName().isEmpty() && query.getSheetName() == null || query.getSheetName().isEmpty()) {
//            return new ArrayList<>();
//        }
//        return baseMapper.selectByQuery(query);

        // 2. 业务查
        LambdaQueryWrapper<SysApprovalCustomerinfoPo> queryWrapper = new LambdaQueryWrapper<>();
        String fieldName = query.getFieldName();
        String sheetName = query.getSheetName();
        int addType = query.getAddType();
        if (fieldName != null && !fieldName.isEmpty()) {
            // 通过 fieldName 查 sys_field 对应的 id 可能重复
            List<Long> fieldIds = sysFieldManager.selectIdsByFieldName(fieldName);
            if (fieldIds != null && !fieldIds.isEmpty()) {
                queryWrapper.in(SysApprovalCustomerinfoPo::getFieldId, fieldIds);
            }
        }
        if (sheetName != null && !sheetName.isEmpty()) {
            // 通过 sheetName 查 sys_sheet 对应的 id 可能重复
            List<Long> sheetIds = sysSheetManager.selectIdsBySheetName(sheetName); // sheetName 就是 apiName
            if (sheetIds != null && !sheetIds.isEmpty()) {
                queryWrapper.in(SysApprovalCustomerinfoPo::getSheetId, sheetIds);
            }
        }
        if (addType != 0) {
            queryWrapper.eq(SysApprovalCustomerinfoPo::getAddType, addType);
        }
        // 通过 fieldId 和 sheetId 查 sys_approval_customerinfo
        List<SysApprovalCustomerinfoPo> list = baseMapper.selectList(queryWrapper);
        return subMerge(mapperDto(list));
    }

    public List<SysApprovalCustomerinfoDto> selectBusinessName(String apiName, Set<Long> businessIds) {
        return baseMapper.selectBusinessName(apiName, businessIds);
    }

    public SysApprovalCustomerinfoDto selectBusinessNameById(String apiName, Long businessId) {
        return baseMapper.selectBusinessNameById(apiName, businessId);
    }

    @Override
    public int insertApproval(SysApprovalCustomerinfoDto approvalCustomerinfo) {
        return baseMapper.insert(approvalCustomerinfo);
    }

    @Override
    @DSTransactional
    public int updatePass(SysApprovalCustomerinfoDto approvalCustomerinfo) {
        UpdateWrapper<SysApprovalCustomerinfoPo> updateWrapper = new UpdateWrapper<>();
        if (approvalCustomerinfo.getId() != null) {
            updateWrapper.eq("id", approvalCustomerinfo.getId());
        }
        if (approvalCustomerinfo.getStatus() != null) {
            updateWrapper.set("status", approvalCustomerinfo.getStatus());
        }
        int count = baseMapper.update(null, updateWrapper);
        // 2.更新 sheetApiName 表的 businessId 下的 fieldId(转为 fieldApiName) 字段为 afterUpdate
        String fieldApiName = sysFieldManager.selectById(approvalCustomerinfo.getFieldId()).getApiName();
        JSONObject jsonObject = new JSONObject();
        // 单选 多选 级联 引用
        if (approvalCustomerinfo.getAfterUpdateId() != null) {
            jsonObject.put(fieldApiName, approvalCustomerinfo.getAfterUpdateId());
        } else {
            jsonObject.put(fieldApiName, approvalCustomerinfo.getAfterUpdate());
        }
        universalService.editProcess(jsonObject, approvalCustomerinfo.getBusinessId(), approvalCustomerinfo.getSheetApiName());
        return count;
    }

    @Override
    public int updateReject(SysApprovalCustomerinfoDto approvalCustomerinfo) {
        UpdateWrapper<SysApprovalCustomerinfoPo> updateWrapper = new UpdateWrapper<>();
        if (approvalCustomerinfo.getId() != null) {
            updateWrapper.eq("id", approvalCustomerinfo.getId());
        }
        if (approvalCustomerinfo.getStatus() != null) {
            updateWrapper.set("status", approvalCustomerinfo.getStatus());
        }
        if (approvalCustomerinfo.getRemark() != null) {
            updateWrapper.set("remark", approvalCustomerinfo.getRemark());
        }
        return baseMapper.update(null, updateWrapper);
    }

    @Override
    public List<Map<String, Object>> businessOption(String apiName, String name) {
        return baseMapper.businessOption(apiName, name);
    }
}