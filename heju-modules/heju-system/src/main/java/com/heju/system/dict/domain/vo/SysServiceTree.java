package com.heju.system.dict.domain.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.heju.common.core.constant.system.OrganizeConstants;
import com.heju.system.api.organize.domain.dto.SysDeptDto;
import com.heju.system.dict.domain.dto.SysServiceManagementDto;
import com.heju.system.dict.manager.ISysDictTypeManager;
import com.heju.system.organize.domain.vo.SysOrganizeTree;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
public class SysServiceTree {

    /** Id */
    private Long id;

    /** 父级Id（父Id为归属服务） */
    private Long parentId;

    /** 名称 */
    private String label;

    /** 状态 */
    private String status;


    @JsonInclude(JsonInclude.Include.NON_EMPTY)
    private List<SysServiceTree> children;

    /**
     * 服务转换
     */
    public SysServiceTree(SysServiceManagementDto serviceManagement) {
        this.id = serviceManagement.getId();
        this.parentId = serviceManagement.getParentId();
        this.label = serviceManagement.getName();
        this.status = serviceManagement.getStatus();
    }
}
