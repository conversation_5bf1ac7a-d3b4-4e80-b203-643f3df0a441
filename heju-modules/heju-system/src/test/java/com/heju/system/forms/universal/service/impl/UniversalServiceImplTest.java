package com.heju.system.forms.universal.service.impl;

import com.heju.common.core.web.result.AjaxResult;
import com.heju.system.forms.universal.domain.query.UniversalQuery;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;

/**
 * UniversalServiceImpl测试类
 * 
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
public class UniversalServiceImplTest {

    @Resource
    private UniversalServiceImpl universalService;

    @Test
    public void testGetEntityInfo() {
        // 创建测试查询对象
        UniversalQuery query = new UniversalQuery();
        query.setCreditNo("91110000000000000X"); // 示例统一社会信用代码
        query.setSheetApiName("test_sheet"); // 示例表单API名称
        query.setPage(1);
        query.setPageSize(10);

        // 执行查询
        AjaxResult result = universalService.getEntityInfo(query);

        // 验证结果
        System.out.println("查询结果: " + result);
        
        if (result.isSuccess()) {
            Object data = result.get("data");
            if (data instanceof List) {
                @SuppressWarnings("unchecked")
                List<Map<String, Object>> dataList = (List<Map<String, Object>>) data;
                System.out.println("查询到 " + dataList.size() + " 条记录");
                
                // 打印前几条记录的详细信息
                for (int i = 0; i < Math.min(3, dataList.size()); i++) {
                    System.out.println("记录 " + (i + 1) + ": " + dataList.get(i));
                }
            }
        }
    }

    @Test
    public void testCacheEntityInfo() {
        String creditNo = "91110000000000000X";
        String sheetApiName = "test_sheet";
        
        // 测试缓存功能
        List<Map<String, Object>> testData = List.of(
            Map.of("id", 1L, "name", "测试数据1"),
            Map.of("id", 2L, "name", "测试数据2")
        );
        
        // 缓存数据
        universalService.cacheEntityInfo(creditNo, sheetApiName, testData, 5);
        System.out.println("数据已缓存");
        
        // 从缓存获取数据
        List<Map<String, Object>> cachedData = universalService.getEntityInfoFromCache(creditNo, sheetApiName);
        System.out.println("从缓存获取的数据: " + cachedData);
        
        // 清除缓存
        universalService.clearEntityInfoCache(creditNo, sheetApiName);
        System.out.println("缓存已清除");
        
        // 再次尝试获取缓存数据
        List<Map<String, Object>> clearedData = universalService.getEntityInfoFromCache(creditNo, sheetApiName);
        System.out.println("清除后的缓存数据: " + clearedData);
    }
}
