package com.heju.system.authority.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.authority.domain.dto.SysDisplayInfoDto;
import com.heju.system.authority.domain.po.SysDisplayInfoPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-21T14:09:20+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysDisplayInfoConverterImpl implements SysDisplayInfoConverter {

    @Override
    public SysDisplayInfoDto mapperDto(SysDisplayInfoPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysDisplayInfoDto sysDisplayInfoDto = new SysDisplayInfoDto();

        sysDisplayInfoDto.setId( arg0.getId() );
        sysDisplayInfoDto.setSourceName( arg0.getSourceName() );
        sysDisplayInfoDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysDisplayInfoDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysDisplayInfoDto.setName( arg0.getName() );
        sysDisplayInfoDto.setStatus( arg0.getStatus() );
        sysDisplayInfoDto.setSort( arg0.getSort() );
        sysDisplayInfoDto.setRemark( arg0.getRemark() );
        sysDisplayInfoDto.setCreateBy( arg0.getCreateBy() );
        sysDisplayInfoDto.setCreateTime( arg0.getCreateTime() );
        sysDisplayInfoDto.setUpdateBy( arg0.getUpdateBy() );
        sysDisplayInfoDto.setUpdateTime( arg0.getUpdateTime() );
        sysDisplayInfoDto.setDelFlag( arg0.getDelFlag() );
        sysDisplayInfoDto.setCreateName( arg0.getCreateName() );
        sysDisplayInfoDto.setUpdateName( arg0.getUpdateName() );
        sysDisplayInfoDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysDisplayInfoDto.setDisplayInfo( arg0.getDisplayInfo() );
        sysDisplayInfoDto.setApiCode( arg0.getApiCode() );
        sysDisplayInfoDto.setUserId( arg0.getUserId() );

        return sysDisplayInfoDto;
    }

    @Override
    public List<SysDisplayInfoDto> mapperDto(Collection<SysDisplayInfoPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysDisplayInfoDto> list = new ArrayList<SysDisplayInfoDto>( arg0.size() );
        for ( SysDisplayInfoPo sysDisplayInfoPo : arg0 ) {
            list.add( mapperDto( sysDisplayInfoPo ) );
        }

        return list;
    }

    @Override
    public Page<SysDisplayInfoDto> mapperPageDto(Collection<SysDisplayInfoPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysDisplayInfoDto> page = new Page<SysDisplayInfoDto>();
        for ( SysDisplayInfoPo sysDisplayInfoPo : arg0 ) {
            page.add( mapperDto( sysDisplayInfoPo ) );
        }

        return page;
    }

    @Override
    public SysDisplayInfoPo mapperPo(SysDisplayInfoDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysDisplayInfoPo sysDisplayInfoPo = new SysDisplayInfoPo();

        sysDisplayInfoPo.setId( arg0.getId() );
        sysDisplayInfoPo.setSourceName( arg0.getSourceName() );
        sysDisplayInfoPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysDisplayInfoPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysDisplayInfoPo.setName( arg0.getName() );
        sysDisplayInfoPo.setStatus( arg0.getStatus() );
        sysDisplayInfoPo.setSort( arg0.getSort() );
        sysDisplayInfoPo.setRemark( arg0.getRemark() );
        sysDisplayInfoPo.setCreateBy( arg0.getCreateBy() );
        sysDisplayInfoPo.setCreateTime( arg0.getCreateTime() );
        sysDisplayInfoPo.setUpdateBy( arg0.getUpdateBy() );
        sysDisplayInfoPo.setUpdateTime( arg0.getUpdateTime() );
        sysDisplayInfoPo.setDelFlag( arg0.getDelFlag() );
        sysDisplayInfoPo.setCreateName( arg0.getCreateName() );
        sysDisplayInfoPo.setUpdateName( arg0.getUpdateName() );
        sysDisplayInfoPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysDisplayInfoPo.setDisplayInfo( arg0.getDisplayInfo() );
        sysDisplayInfoPo.setApiCode( arg0.getApiCode() );
        sysDisplayInfoPo.setUserId( arg0.getUserId() );

        return sysDisplayInfoPo;
    }

    @Override
    public List<SysDisplayInfoPo> mapperPo(Collection<SysDisplayInfoDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysDisplayInfoPo> list = new ArrayList<SysDisplayInfoPo>( arg0.size() );
        for ( SysDisplayInfoDto sysDisplayInfoDto : arg0 ) {
            list.add( mapperPo( sysDisplayInfoDto ) );
        }

        return list;
    }

    @Override
    public Page<SysDisplayInfoPo> mapperPagePo(Collection<SysDisplayInfoDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysDisplayInfoPo> page = new Page<SysDisplayInfoPo>();
        for ( SysDisplayInfoDto sysDisplayInfoDto : arg0 ) {
            page.add( mapperPo( sysDisplayInfoDto ) );
        }

        return page;
    }
}
