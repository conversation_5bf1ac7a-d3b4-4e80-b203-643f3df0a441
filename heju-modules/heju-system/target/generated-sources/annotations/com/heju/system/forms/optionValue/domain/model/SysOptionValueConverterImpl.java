package com.heju.system.forms.optionValue.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.forms.optionValue.domain.dto.SysOptionValueDto;
import com.heju.system.forms.optionValue.domain.po.SysOptionValuePo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-21T14:09:20+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysOptionValueConverterImpl implements SysOptionValueConverter {

    @Override
    public SysOptionValueDto mapperDto(SysOptionValuePo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysOptionValueDto sysOptionValueDto = new SysOptionValueDto();

        sysOptionValueDto.setId( arg0.getId() );
        sysOptionValueDto.setSourceName( arg0.getSourceName() );
        sysOptionValueDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysOptionValueDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysOptionValueDto.setName( arg0.getName() );
        sysOptionValueDto.setStatus( arg0.getStatus() );
        sysOptionValueDto.setSort( arg0.getSort() );
        sysOptionValueDto.setRemark( arg0.getRemark() );
        sysOptionValueDto.setCreateBy( arg0.getCreateBy() );
        sysOptionValueDto.setCreateTime( arg0.getCreateTime() );
        sysOptionValueDto.setUpdateBy( arg0.getUpdateBy() );
        sysOptionValueDto.setUpdateTime( arg0.getUpdateTime() );
        sysOptionValueDto.setDelFlag( arg0.getDelFlag() );
        sysOptionValueDto.setCreateName( arg0.getCreateName() );
        sysOptionValueDto.setUpdateName( arg0.getUpdateName() );
        sysOptionValueDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysOptionValueDto.setApiName( arg0.getApiName() );
        sysOptionValueDto.setOptionId( arg0.getOptionId() );

        return sysOptionValueDto;
    }

    @Override
    public List<SysOptionValueDto> mapperDto(Collection<SysOptionValuePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysOptionValueDto> list = new ArrayList<SysOptionValueDto>( arg0.size() );
        for ( SysOptionValuePo sysOptionValuePo : arg0 ) {
            list.add( mapperDto( sysOptionValuePo ) );
        }

        return list;
    }

    @Override
    public Page<SysOptionValueDto> mapperPageDto(Collection<SysOptionValuePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysOptionValueDto> page = new Page<SysOptionValueDto>();
        for ( SysOptionValuePo sysOptionValuePo : arg0 ) {
            page.add( mapperDto( sysOptionValuePo ) );
        }

        return page;
    }

    @Override
    public SysOptionValuePo mapperPo(SysOptionValueDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysOptionValuePo sysOptionValuePo = new SysOptionValuePo();

        sysOptionValuePo.setId( arg0.getId() );
        sysOptionValuePo.setSourceName( arg0.getSourceName() );
        sysOptionValuePo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysOptionValuePo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysOptionValuePo.setName( arg0.getName() );
        sysOptionValuePo.setStatus( arg0.getStatus() );
        sysOptionValuePo.setSort( arg0.getSort() );
        sysOptionValuePo.setRemark( arg0.getRemark() );
        sysOptionValuePo.setCreateBy( arg0.getCreateBy() );
        sysOptionValuePo.setCreateTime( arg0.getCreateTime() );
        sysOptionValuePo.setUpdateBy( arg0.getUpdateBy() );
        sysOptionValuePo.setUpdateTime( arg0.getUpdateTime() );
        sysOptionValuePo.setDelFlag( arg0.getDelFlag() );
        sysOptionValuePo.setCreateName( arg0.getCreateName() );
        sysOptionValuePo.setUpdateName( arg0.getUpdateName() );
        sysOptionValuePo.setEnterpriseId( arg0.getEnterpriseId() );
        sysOptionValuePo.setApiName( arg0.getApiName() );
        sysOptionValuePo.setOptionId( arg0.getOptionId() );

        return sysOptionValuePo;
    }

    @Override
    public List<SysOptionValuePo> mapperPo(Collection<SysOptionValueDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysOptionValuePo> list = new ArrayList<SysOptionValuePo>( arg0.size() );
        for ( SysOptionValueDto sysOptionValueDto : arg0 ) {
            list.add( mapperPo( sysOptionValueDto ) );
        }

        return list;
    }

    @Override
    public Page<SysOptionValuePo> mapperPagePo(Collection<SysOptionValueDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysOptionValuePo> page = new Page<SysOptionValuePo>();
        for ( SysOptionValueDto sysOptionValueDto : arg0 ) {
            page.add( mapperPo( sysOptionValueDto ) );
        }

        return page;
    }
}
