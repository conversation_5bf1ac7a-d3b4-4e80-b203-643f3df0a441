package com.heju.system.entity.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.entity.domain.dto.SysEntityFieldDto;
import com.heju.system.entity.domain.po.SysEntityFieldPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-21T14:09:20+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysEntityFieldConverterImpl implements SysEntityFieldConverter {

    @Override
    public SysEntityFieldDto mapperDto(SysEntityFieldPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysEntityFieldDto sysEntityFieldDto = new SysEntityFieldDto();

        sysEntityFieldDto.setId( arg0.getId() );
        sysEntityFieldDto.setSourceName( arg0.getSourceName() );
        sysEntityFieldDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysEntityFieldDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysEntityFieldDto.setName( arg0.getName() );
        sysEntityFieldDto.setStatus( arg0.getStatus() );
        sysEntityFieldDto.setSort( arg0.getSort() );
        sysEntityFieldDto.setCreateBy( arg0.getCreateBy() );
        sysEntityFieldDto.setCreateTime( arg0.getCreateTime() );
        sysEntityFieldDto.setUpdateBy( arg0.getUpdateBy() );
        sysEntityFieldDto.setUpdateTime( arg0.getUpdateTime() );
        sysEntityFieldDto.setDelFlag( arg0.getDelFlag() );
        sysEntityFieldDto.setCreateName( arg0.getCreateName() );
        sysEntityFieldDto.setUpdateName( arg0.getUpdateName() );
        sysEntityFieldDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysEntityFieldDto.setFieldComment( arg0.getFieldComment() );
        sysEntityFieldDto.setFieldName( arg0.getFieldName() );
        sysEntityFieldDto.setFieldType( arg0.getFieldType() );
        sysEntityFieldDto.setFieldLength( arg0.getFieldLength() );
        sysEntityFieldDto.setFieldBelong( arg0.getFieldBelong() );
        sysEntityFieldDto.setIsChange( arg0.getIsChange() );
        sysEntityFieldDto.setRemark( arg0.getRemark() );

        return sysEntityFieldDto;
    }

    @Override
    public List<SysEntityFieldDto> mapperDto(Collection<SysEntityFieldPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysEntityFieldDto> list = new ArrayList<SysEntityFieldDto>( arg0.size() );
        for ( SysEntityFieldPo sysEntityFieldPo : arg0 ) {
            list.add( mapperDto( sysEntityFieldPo ) );
        }

        return list;
    }

    @Override
    public Page<SysEntityFieldDto> mapperPageDto(Collection<SysEntityFieldPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysEntityFieldDto> page = new Page<SysEntityFieldDto>();
        for ( SysEntityFieldPo sysEntityFieldPo : arg0 ) {
            page.add( mapperDto( sysEntityFieldPo ) );
        }

        return page;
    }

    @Override
    public SysEntityFieldPo mapperPo(SysEntityFieldDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysEntityFieldPo sysEntityFieldPo = new SysEntityFieldPo();

        sysEntityFieldPo.setId( arg0.getId() );
        sysEntityFieldPo.setSourceName( arg0.getSourceName() );
        sysEntityFieldPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysEntityFieldPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysEntityFieldPo.setName( arg0.getName() );
        sysEntityFieldPo.setStatus( arg0.getStatus() );
        sysEntityFieldPo.setSort( arg0.getSort() );
        sysEntityFieldPo.setCreateBy( arg0.getCreateBy() );
        sysEntityFieldPo.setCreateTime( arg0.getCreateTime() );
        sysEntityFieldPo.setUpdateBy( arg0.getUpdateBy() );
        sysEntityFieldPo.setUpdateTime( arg0.getUpdateTime() );
        sysEntityFieldPo.setDelFlag( arg0.getDelFlag() );
        sysEntityFieldPo.setCreateName( arg0.getCreateName() );
        sysEntityFieldPo.setUpdateName( arg0.getUpdateName() );
        sysEntityFieldPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysEntityFieldPo.setFieldComment( arg0.getFieldComment() );
        sysEntityFieldPo.setFieldName( arg0.getFieldName() );
        sysEntityFieldPo.setFieldType( arg0.getFieldType() );
        sysEntityFieldPo.setFieldLength( arg0.getFieldLength() );
        sysEntityFieldPo.setFieldBelong( arg0.getFieldBelong() );
        sysEntityFieldPo.setIsChange( arg0.getIsChange() );
        sysEntityFieldPo.setRemark( arg0.getRemark() );

        return sysEntityFieldPo;
    }

    @Override
    public List<SysEntityFieldPo> mapperPo(Collection<SysEntityFieldDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysEntityFieldPo> list = new ArrayList<SysEntityFieldPo>( arg0.size() );
        for ( SysEntityFieldDto sysEntityFieldDto : arg0 ) {
            list.add( mapperPo( sysEntityFieldDto ) );
        }

        return list;
    }

    @Override
    public Page<SysEntityFieldPo> mapperPagePo(Collection<SysEntityFieldDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysEntityFieldPo> page = new Page<SysEntityFieldPo>();
        for ( SysEntityFieldDto sysEntityFieldDto : arg0 ) {
            page.add( mapperPo( sysEntityFieldDto ) );
        }

        return page;
    }
}
