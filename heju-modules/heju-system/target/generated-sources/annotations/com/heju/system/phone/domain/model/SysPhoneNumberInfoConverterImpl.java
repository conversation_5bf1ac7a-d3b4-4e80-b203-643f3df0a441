package com.heju.system.phone.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.phone.domain.dto.SysPhoneNumberInfoDto;
import com.heju.system.phone.domain.po.SysPhoneNumberInfoPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-21T14:09:20+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysPhoneNumberInfoConverterImpl implements SysPhoneNumberInfoConverter {

    @Override
    public SysPhoneNumberInfoDto mapperDto(SysPhoneNumberInfoPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysPhoneNumberInfoDto sysPhoneNumberInfoDto = new SysPhoneNumberInfoDto();

        sysPhoneNumberInfoDto.setId( arg0.getId() );
        sysPhoneNumberInfoDto.setSourceName( arg0.getSourceName() );
        sysPhoneNumberInfoDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysPhoneNumberInfoDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysPhoneNumberInfoDto.setName( arg0.getName() );
        sysPhoneNumberInfoDto.setStatus( arg0.getStatus() );
        sysPhoneNumberInfoDto.setSort( arg0.getSort() );
        sysPhoneNumberInfoDto.setRemark( arg0.getRemark() );
        sysPhoneNumberInfoDto.setCreateBy( arg0.getCreateBy() );
        sysPhoneNumberInfoDto.setCreateTime( arg0.getCreateTime() );
        sysPhoneNumberInfoDto.setUpdateBy( arg0.getUpdateBy() );
        sysPhoneNumberInfoDto.setUpdateTime( arg0.getUpdateTime() );
        sysPhoneNumberInfoDto.setDelFlag( arg0.getDelFlag() );
        sysPhoneNumberInfoDto.setCreateName( arg0.getCreateName() );
        sysPhoneNumberInfoDto.setUpdateName( arg0.getUpdateName() );
        sysPhoneNumberInfoDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysPhoneNumberInfoDto.setBrandName( arg0.getBrandName() );
        sysPhoneNumberInfoDto.setPhoneModel( arg0.getPhoneModel() );
        sysPhoneNumberInfoDto.setSimOne( arg0.getSimOne() );
        sysPhoneNumberInfoDto.setSimTwo( arg0.getSimTwo() );
        sysPhoneNumberInfoDto.setCustody( arg0.getCustody() );
        sysPhoneNumberInfoDto.setPosition( arg0.getPosition() );

        return sysPhoneNumberInfoDto;
    }

    @Override
    public List<SysPhoneNumberInfoDto> mapperDto(Collection<SysPhoneNumberInfoPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysPhoneNumberInfoDto> list = new ArrayList<SysPhoneNumberInfoDto>( arg0.size() );
        for ( SysPhoneNumberInfoPo sysPhoneNumberInfoPo : arg0 ) {
            list.add( mapperDto( sysPhoneNumberInfoPo ) );
        }

        return list;
    }

    @Override
    public Page<SysPhoneNumberInfoDto> mapperPageDto(Collection<SysPhoneNumberInfoPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysPhoneNumberInfoDto> page = new Page<SysPhoneNumberInfoDto>();
        for ( SysPhoneNumberInfoPo sysPhoneNumberInfoPo : arg0 ) {
            page.add( mapperDto( sysPhoneNumberInfoPo ) );
        }

        return page;
    }

    @Override
    public SysPhoneNumberInfoPo mapperPo(SysPhoneNumberInfoDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysPhoneNumberInfoPo sysPhoneNumberInfoPo = new SysPhoneNumberInfoPo();

        sysPhoneNumberInfoPo.setId( arg0.getId() );
        sysPhoneNumberInfoPo.setSourceName( arg0.getSourceName() );
        sysPhoneNumberInfoPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysPhoneNumberInfoPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysPhoneNumberInfoPo.setName( arg0.getName() );
        sysPhoneNumberInfoPo.setStatus( arg0.getStatus() );
        sysPhoneNumberInfoPo.setSort( arg0.getSort() );
        sysPhoneNumberInfoPo.setRemark( arg0.getRemark() );
        sysPhoneNumberInfoPo.setCreateBy( arg0.getCreateBy() );
        sysPhoneNumberInfoPo.setCreateTime( arg0.getCreateTime() );
        sysPhoneNumberInfoPo.setUpdateBy( arg0.getUpdateBy() );
        sysPhoneNumberInfoPo.setUpdateTime( arg0.getUpdateTime() );
        sysPhoneNumberInfoPo.setDelFlag( arg0.getDelFlag() );
        sysPhoneNumberInfoPo.setCreateName( arg0.getCreateName() );
        sysPhoneNumberInfoPo.setUpdateName( arg0.getUpdateName() );
        sysPhoneNumberInfoPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysPhoneNumberInfoPo.setBrandName( arg0.getBrandName() );
        sysPhoneNumberInfoPo.setPhoneModel( arg0.getPhoneModel() );
        sysPhoneNumberInfoPo.setSimOne( arg0.getSimOne() );
        sysPhoneNumberInfoPo.setSimTwo( arg0.getSimTwo() );
        sysPhoneNumberInfoPo.setCustody( arg0.getCustody() );
        sysPhoneNumberInfoPo.setPosition( arg0.getPosition() );

        return sysPhoneNumberInfoPo;
    }

    @Override
    public List<SysPhoneNumberInfoPo> mapperPo(Collection<SysPhoneNumberInfoDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysPhoneNumberInfoPo> list = new ArrayList<SysPhoneNumberInfoPo>( arg0.size() );
        for ( SysPhoneNumberInfoDto sysPhoneNumberInfoDto : arg0 ) {
            list.add( mapperPo( sysPhoneNumberInfoDto ) );
        }

        return list;
    }

    @Override
    public Page<SysPhoneNumberInfoPo> mapperPagePo(Collection<SysPhoneNumberInfoDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysPhoneNumberInfoPo> page = new Page<SysPhoneNumberInfoPo>();
        for ( SysPhoneNumberInfoDto sysPhoneNumberInfoDto : arg0 ) {
            page.add( mapperPo( sysPhoneNumberInfoDto ) );
        }

        return page;
    }
}
