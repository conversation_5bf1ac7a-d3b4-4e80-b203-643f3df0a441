package com.heju.system.entity.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.entity.domain.dto.SysEntityTaxationInvoiceTypeDto;
import com.heju.system.entity.domain.po.SysEntityTaxationInvoiceTypePo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-21T14:09:19+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysEntityTaxationInvoiceTypeConverterImpl implements SysEntityTaxationInvoiceTypeConverter {

    @Override
    public SysEntityTaxationInvoiceTypeDto mapperDto(SysEntityTaxationInvoiceTypePo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysEntityTaxationInvoiceTypeDto sysEntityTaxationInvoiceTypeDto = new SysEntityTaxationInvoiceTypeDto();

        sysEntityTaxationInvoiceTypeDto.setId( arg0.getId() );
        sysEntityTaxationInvoiceTypeDto.setSourceName( arg0.getSourceName() );
        sysEntityTaxationInvoiceTypeDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysEntityTaxationInvoiceTypeDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysEntityTaxationInvoiceTypeDto.setName( arg0.getName() );
        sysEntityTaxationInvoiceTypeDto.setStatus( arg0.getStatus() );
        sysEntityTaxationInvoiceTypeDto.setSort( arg0.getSort() );
        sysEntityTaxationInvoiceTypeDto.setRemark( arg0.getRemark() );
        sysEntityTaxationInvoiceTypeDto.setCreateBy( arg0.getCreateBy() );
        sysEntityTaxationInvoiceTypeDto.setCreateTime( arg0.getCreateTime() );
        sysEntityTaxationInvoiceTypeDto.setUpdateBy( arg0.getUpdateBy() );
        sysEntityTaxationInvoiceTypeDto.setUpdateTime( arg0.getUpdateTime() );
        sysEntityTaxationInvoiceTypeDto.setDelFlag( arg0.getDelFlag() );
        sysEntityTaxationInvoiceTypeDto.setCreateName( arg0.getCreateName() );
        sysEntityTaxationInvoiceTypeDto.setUpdateName( arg0.getUpdateName() );
        sysEntityTaxationInvoiceTypeDto.setInvoiceType( arg0.getInvoiceType() );
        sysEntityTaxationInvoiceTypeDto.setInvoiceLimit( arg0.getInvoiceLimit() );
        sysEntityTaxationInvoiceTypeDto.setMonthInvoiceNumber( arg0.getMonthInvoiceNumber() );
        sysEntityTaxationInvoiceTypeDto.setEverytimeInvoiceNumber( arg0.getEverytimeInvoiceNumber() );
        sysEntityTaxationInvoiceTypeDto.setHoldInvoiceNumber( arg0.getHoldInvoiceNumber() );
        sysEntityTaxationInvoiceTypeDto.setOfflineInvoiceTimeLimit( arg0.getOfflineInvoiceTimeLimit() );
        sysEntityTaxationInvoiceTypeDto.setOfflineInvoiceAccumulatedLimit( arg0.getOfflineInvoiceAccumulatedLimit() );
        sysEntityTaxationInvoiceTypeDto.setStarttime( arg0.getStarttime() );
        sysEntityTaxationInvoiceTypeDto.setEndtime( arg0.getEndtime() );
        sysEntityTaxationInvoiceTypeDto.setEntityId( arg0.getEntityId() );

        return sysEntityTaxationInvoiceTypeDto;
    }

    @Override
    public List<SysEntityTaxationInvoiceTypeDto> mapperDto(Collection<SysEntityTaxationInvoiceTypePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysEntityTaxationInvoiceTypeDto> list = new ArrayList<SysEntityTaxationInvoiceTypeDto>( arg0.size() );
        for ( SysEntityTaxationInvoiceTypePo sysEntityTaxationInvoiceTypePo : arg0 ) {
            list.add( mapperDto( sysEntityTaxationInvoiceTypePo ) );
        }

        return list;
    }

    @Override
    public Page<SysEntityTaxationInvoiceTypeDto> mapperPageDto(Collection<SysEntityTaxationInvoiceTypePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysEntityTaxationInvoiceTypeDto> page = new Page<SysEntityTaxationInvoiceTypeDto>();
        for ( SysEntityTaxationInvoiceTypePo sysEntityTaxationInvoiceTypePo : arg0 ) {
            page.add( mapperDto( sysEntityTaxationInvoiceTypePo ) );
        }

        return page;
    }

    @Override
    public SysEntityTaxationInvoiceTypePo mapperPo(SysEntityTaxationInvoiceTypeDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysEntityTaxationInvoiceTypePo sysEntityTaxationInvoiceTypePo = new SysEntityTaxationInvoiceTypePo();

        sysEntityTaxationInvoiceTypePo.setId( arg0.getId() );
        sysEntityTaxationInvoiceTypePo.setSourceName( arg0.getSourceName() );
        sysEntityTaxationInvoiceTypePo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysEntityTaxationInvoiceTypePo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysEntityTaxationInvoiceTypePo.setName( arg0.getName() );
        sysEntityTaxationInvoiceTypePo.setStatus( arg0.getStatus() );
        sysEntityTaxationInvoiceTypePo.setSort( arg0.getSort() );
        sysEntityTaxationInvoiceTypePo.setRemark( arg0.getRemark() );
        sysEntityTaxationInvoiceTypePo.setCreateBy( arg0.getCreateBy() );
        sysEntityTaxationInvoiceTypePo.setCreateTime( arg0.getCreateTime() );
        sysEntityTaxationInvoiceTypePo.setUpdateBy( arg0.getUpdateBy() );
        sysEntityTaxationInvoiceTypePo.setUpdateTime( arg0.getUpdateTime() );
        sysEntityTaxationInvoiceTypePo.setDelFlag( arg0.getDelFlag() );
        sysEntityTaxationInvoiceTypePo.setCreateName( arg0.getCreateName() );
        sysEntityTaxationInvoiceTypePo.setUpdateName( arg0.getUpdateName() );
        sysEntityTaxationInvoiceTypePo.setInvoiceType( arg0.getInvoiceType() );
        sysEntityTaxationInvoiceTypePo.setInvoiceLimit( arg0.getInvoiceLimit() );
        sysEntityTaxationInvoiceTypePo.setMonthInvoiceNumber( arg0.getMonthInvoiceNumber() );
        sysEntityTaxationInvoiceTypePo.setEverytimeInvoiceNumber( arg0.getEverytimeInvoiceNumber() );
        sysEntityTaxationInvoiceTypePo.setHoldInvoiceNumber( arg0.getHoldInvoiceNumber() );
        sysEntityTaxationInvoiceTypePo.setOfflineInvoiceTimeLimit( arg0.getOfflineInvoiceTimeLimit() );
        sysEntityTaxationInvoiceTypePo.setOfflineInvoiceAccumulatedLimit( arg0.getOfflineInvoiceAccumulatedLimit() );
        sysEntityTaxationInvoiceTypePo.setStarttime( arg0.getStarttime() );
        sysEntityTaxationInvoiceTypePo.setEndtime( arg0.getEndtime() );
        sysEntityTaxationInvoiceTypePo.setEntityId( arg0.getEntityId() );

        return sysEntityTaxationInvoiceTypePo;
    }

    @Override
    public List<SysEntityTaxationInvoiceTypePo> mapperPo(Collection<SysEntityTaxationInvoiceTypeDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysEntityTaxationInvoiceTypePo> list = new ArrayList<SysEntityTaxationInvoiceTypePo>( arg0.size() );
        for ( SysEntityTaxationInvoiceTypeDto sysEntityTaxationInvoiceTypeDto : arg0 ) {
            list.add( mapperPo( sysEntityTaxationInvoiceTypeDto ) );
        }

        return list;
    }

    @Override
    public Page<SysEntityTaxationInvoiceTypePo> mapperPagePo(Collection<SysEntityTaxationInvoiceTypeDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysEntityTaxationInvoiceTypePo> page = new Page<SysEntityTaxationInvoiceTypePo>();
        for ( SysEntityTaxationInvoiceTypeDto sysEntityTaxationInvoiceTypeDto : arg0 ) {
            page.add( mapperPo( sysEntityTaxationInvoiceTypeDto ) );
        }

        return page;
    }
}
