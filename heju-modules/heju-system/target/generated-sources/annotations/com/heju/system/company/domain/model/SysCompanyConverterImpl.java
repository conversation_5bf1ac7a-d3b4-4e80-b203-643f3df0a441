package com.heju.system.company.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.company.domain.dto.SysCompanyDto;
import com.heju.system.company.domain.po.SysCompanyPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-28T17:23:01+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysCompanyConverterImpl implements SysCompanyConverter {

    @Override
    public SysCompanyDto mapperDto(SysCompanyPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysCompanyDto sysCompanyDto = new SysCompanyDto();

        sysCompanyDto.setId( arg0.getId() );
        sysCompanyDto.setSourceName( arg0.getSourceName() );
        sysCompanyDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysCompanyDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysCompanyDto.setName( arg0.getName() );
        sysCompanyDto.setStatus( arg0.getStatus() );
        sysCompanyDto.setSort( arg0.getSort() );
        sysCompanyDto.setCreateBy( arg0.getCreateBy() );
        sysCompanyDto.setCreateTime( arg0.getCreateTime() );
        sysCompanyDto.setUpdateBy( arg0.getUpdateBy() );
        sysCompanyDto.setUpdateTime( arg0.getUpdateTime() );
        sysCompanyDto.setDelFlag( arg0.getDelFlag() );
        sysCompanyDto.setCreateName( arg0.getCreateName() );
        sysCompanyDto.setUpdateName( arg0.getUpdateName() );
        sysCompanyDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysCompanyDto.setCode( arg0.getCode() );
        sysCompanyDto.setCreditNo( arg0.getCreditNo() );
        sysCompanyDto.setRemark( arg0.getRemark() );

        return sysCompanyDto;
    }

    @Override
    public List<SysCompanyDto> mapperDto(Collection<SysCompanyPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysCompanyDto> list = new ArrayList<SysCompanyDto>( arg0.size() );
        for ( SysCompanyPo sysCompanyPo : arg0 ) {
            list.add( mapperDto( sysCompanyPo ) );
        }

        return list;
    }

    @Override
    public Page<SysCompanyDto> mapperPageDto(Collection<SysCompanyPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysCompanyDto> page = new Page<SysCompanyDto>();
        for ( SysCompanyPo sysCompanyPo : arg0 ) {
            page.add( mapperDto( sysCompanyPo ) );
        }

        return page;
    }

    @Override
    public SysCompanyPo mapperPo(SysCompanyDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysCompanyPo sysCompanyPo = new SysCompanyPo();

        sysCompanyPo.setId( arg0.getId() );
        sysCompanyPo.setSourceName( arg0.getSourceName() );
        sysCompanyPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysCompanyPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysCompanyPo.setName( arg0.getName() );
        sysCompanyPo.setStatus( arg0.getStatus() );
        sysCompanyPo.setSort( arg0.getSort() );
        sysCompanyPo.setCreateBy( arg0.getCreateBy() );
        sysCompanyPo.setCreateTime( arg0.getCreateTime() );
        sysCompanyPo.setUpdateBy( arg0.getUpdateBy() );
        sysCompanyPo.setUpdateTime( arg0.getUpdateTime() );
        sysCompanyPo.setDelFlag( arg0.getDelFlag() );
        sysCompanyPo.setCreateName( arg0.getCreateName() );
        sysCompanyPo.setUpdateName( arg0.getUpdateName() );
        sysCompanyPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysCompanyPo.setCode( arg0.getCode() );
        sysCompanyPo.setCreditNo( arg0.getCreditNo() );
        sysCompanyPo.setRemark( arg0.getRemark() );

        return sysCompanyPo;
    }

    @Override
    public List<SysCompanyPo> mapperPo(Collection<SysCompanyDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysCompanyPo> list = new ArrayList<SysCompanyPo>( arg0.size() );
        for ( SysCompanyDto sysCompanyDto : arg0 ) {
            list.add( mapperPo( sysCompanyDto ) );
        }

        return list;
    }

    @Override
    public Page<SysCompanyPo> mapperPagePo(Collection<SysCompanyDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysCompanyPo> page = new Page<SysCompanyPo>();
        for ( SysCompanyDto sysCompanyDto : arg0 ) {
            page.add( mapperPo( sysCompanyDto ) );
        }

        return page;
    }
}
