package com.heju.system.declaration.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.declaration.domain.dto.SysBusinessAnnualReportDto;
import com.heju.system.declaration.domain.po.SysBusinessAnnualReportPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-21T14:09:20+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysBusinessAnnualReportConverterImpl implements SysBusinessAnnualReportConverter {

    @Override
    public SysBusinessAnnualReportDto mapperDto(SysBusinessAnnualReportPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysBusinessAnnualReportDto sysBusinessAnnualReportDto = new SysBusinessAnnualReportDto();

        sysBusinessAnnualReportDto.setId( arg0.getId() );
        sysBusinessAnnualReportDto.setSourceName( arg0.getSourceName() );
        sysBusinessAnnualReportDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysBusinessAnnualReportDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysBusinessAnnualReportDto.setName( arg0.getName() );
        sysBusinessAnnualReportDto.setStatus( arg0.getStatus() );
        sysBusinessAnnualReportDto.setSort( arg0.getSort() );
        sysBusinessAnnualReportDto.setCreateBy( arg0.getCreateBy() );
        sysBusinessAnnualReportDto.setCreateTime( arg0.getCreateTime() );
        sysBusinessAnnualReportDto.setUpdateBy( arg0.getUpdateBy() );
        sysBusinessAnnualReportDto.setUpdateTime( arg0.getUpdateTime() );
        sysBusinessAnnualReportDto.setDelFlag( arg0.getDelFlag() );
        sysBusinessAnnualReportDto.setCreateName( arg0.getCreateName() );
        sysBusinessAnnualReportDto.setUpdateName( arg0.getUpdateName() );
        sysBusinessAnnualReportDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysBusinessAnnualReportDto.setEntityId( arg0.getEntityId() );
        sysBusinessAnnualReportDto.setCode( arg0.getCode() );
        sysBusinessAnnualReportDto.setReporttypeType( arg0.getReporttypeType() );
        sysBusinessAnnualReportDto.setYear( arg0.getYear() );
        sysBusinessAnnualReportDto.setReportAddress( arg0.getReportAddress() );
        sysBusinessAnnualReportDto.setRemark( arg0.getRemark() );
        sysBusinessAnnualReportDto.setDeclareStatus( arg0.getDeclareStatus() );
        sysBusinessAnnualReportDto.setDeclareRemark( arg0.getDeclareRemark() );
        sysBusinessAnnualReportDto.setReason( arg0.getReason() );

        return sysBusinessAnnualReportDto;
    }

    @Override
    public List<SysBusinessAnnualReportDto> mapperDto(Collection<SysBusinessAnnualReportPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysBusinessAnnualReportDto> list = new ArrayList<SysBusinessAnnualReportDto>( arg0.size() );
        for ( SysBusinessAnnualReportPo sysBusinessAnnualReportPo : arg0 ) {
            list.add( mapperDto( sysBusinessAnnualReportPo ) );
        }

        return list;
    }

    @Override
    public Page<SysBusinessAnnualReportDto> mapperPageDto(Collection<SysBusinessAnnualReportPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysBusinessAnnualReportDto> page = new Page<SysBusinessAnnualReportDto>();
        for ( SysBusinessAnnualReportPo sysBusinessAnnualReportPo : arg0 ) {
            page.add( mapperDto( sysBusinessAnnualReportPo ) );
        }

        return page;
    }

    @Override
    public SysBusinessAnnualReportPo mapperPo(SysBusinessAnnualReportDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysBusinessAnnualReportPo sysBusinessAnnualReportPo = new SysBusinessAnnualReportPo();

        sysBusinessAnnualReportPo.setId( arg0.getId() );
        sysBusinessAnnualReportPo.setSourceName( arg0.getSourceName() );
        sysBusinessAnnualReportPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysBusinessAnnualReportPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysBusinessAnnualReportPo.setName( arg0.getName() );
        sysBusinessAnnualReportPo.setStatus( arg0.getStatus() );
        sysBusinessAnnualReportPo.setSort( arg0.getSort() );
        sysBusinessAnnualReportPo.setCreateBy( arg0.getCreateBy() );
        sysBusinessAnnualReportPo.setCreateTime( arg0.getCreateTime() );
        sysBusinessAnnualReportPo.setUpdateBy( arg0.getUpdateBy() );
        sysBusinessAnnualReportPo.setUpdateTime( arg0.getUpdateTime() );
        sysBusinessAnnualReportPo.setDelFlag( arg0.getDelFlag() );
        sysBusinessAnnualReportPo.setCreateName( arg0.getCreateName() );
        sysBusinessAnnualReportPo.setUpdateName( arg0.getUpdateName() );
        sysBusinessAnnualReportPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysBusinessAnnualReportPo.setEntityId( arg0.getEntityId() );
        sysBusinessAnnualReportPo.setCode( arg0.getCode() );
        sysBusinessAnnualReportPo.setReporttypeType( arg0.getReporttypeType() );
        sysBusinessAnnualReportPo.setYear( arg0.getYear() );
        sysBusinessAnnualReportPo.setReportAddress( arg0.getReportAddress() );
        sysBusinessAnnualReportPo.setRemark( arg0.getRemark() );
        sysBusinessAnnualReportPo.setDeclareStatus( arg0.getDeclareStatus() );
        sysBusinessAnnualReportPo.setDeclareRemark( arg0.getDeclareRemark() );
        sysBusinessAnnualReportPo.setReason( arg0.getReason() );

        return sysBusinessAnnualReportPo;
    }

    @Override
    public List<SysBusinessAnnualReportPo> mapperPo(Collection<SysBusinessAnnualReportDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysBusinessAnnualReportPo> list = new ArrayList<SysBusinessAnnualReportPo>( arg0.size() );
        for ( SysBusinessAnnualReportDto sysBusinessAnnualReportDto : arg0 ) {
            list.add( mapperPo( sysBusinessAnnualReportDto ) );
        }

        return list;
    }

    @Override
    public Page<SysBusinessAnnualReportPo> mapperPagePo(Collection<SysBusinessAnnualReportDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysBusinessAnnualReportPo> page = new Page<SysBusinessAnnualReportPo>();
        for ( SysBusinessAnnualReportDto sysBusinessAnnualReportDto : arg0 ) {
            page.add( mapperPo( sysBusinessAnnualReportDto ) );
        }

        return page;
    }
}
