package com.heju.system.report.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.report.domain.dto.SysBillReportDto;
import com.heju.system.report.domain.po.SysBillReportPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-21T14:09:20+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysBillReportConverterImpl implements SysBillReportConverter {

    @Override
    public SysBillReportDto mapperDto(SysBillReportPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysBillReportDto sysBillReportDto = new SysBillReportDto();

        sysBillReportDto.setId( arg0.getId() );
        sysBillReportDto.setSourceName( arg0.getSourceName() );
        sysBillReportDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysBillReportDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysBillReportDto.setName( arg0.getName() );
        sysBillReportDto.setStatus( arg0.getStatus() );
        sysBillReportDto.setSort( arg0.getSort() );
        sysBillReportDto.setCreateBy( arg0.getCreateBy() );
        sysBillReportDto.setCreateTime( arg0.getCreateTime() );
        sysBillReportDto.setUpdateBy( arg0.getUpdateBy() );
        sysBillReportDto.setUpdateTime( arg0.getUpdateTime() );
        sysBillReportDto.setDelFlag( arg0.getDelFlag() );
        sysBillReportDto.setCreateName( arg0.getCreateName() );
        sysBillReportDto.setUpdateName( arg0.getUpdateName() );
        sysBillReportDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysBillReportDto.setEntityId( arg0.getEntityId() );
        sysBillReportDto.setEntityName( arg0.getEntityName() );
        sysBillReportDto.setCode( arg0.getCode() );
        sysBillReportDto.setBillType( arg0.getBillType() );
        sysBillReportDto.setReporttimeType( arg0.getReporttimeType() );
        sysBillReportDto.setYear( arg0.getYear() );
        sysBillReportDto.setMonth( arg0.getMonth() );
        sysBillReportDto.setSeason( arg0.getSeason() );
        sysBillReportDto.setReportAddress( arg0.getReportAddress() );
        sysBillReportDto.setRemark( arg0.getRemark() );

        return sysBillReportDto;
    }

    @Override
    public List<SysBillReportDto> mapperDto(Collection<SysBillReportPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysBillReportDto> list = new ArrayList<SysBillReportDto>( arg0.size() );
        for ( SysBillReportPo sysBillReportPo : arg0 ) {
            list.add( mapperDto( sysBillReportPo ) );
        }

        return list;
    }

    @Override
    public Page<SysBillReportDto> mapperPageDto(Collection<SysBillReportPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysBillReportDto> page = new Page<SysBillReportDto>();
        for ( SysBillReportPo sysBillReportPo : arg0 ) {
            page.add( mapperDto( sysBillReportPo ) );
        }

        return page;
    }

    @Override
    public SysBillReportPo mapperPo(SysBillReportDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysBillReportPo sysBillReportPo = new SysBillReportPo();

        sysBillReportPo.setId( arg0.getId() );
        sysBillReportPo.setSourceName( arg0.getSourceName() );
        sysBillReportPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysBillReportPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysBillReportPo.setName( arg0.getName() );
        sysBillReportPo.setStatus( arg0.getStatus() );
        sysBillReportPo.setSort( arg0.getSort() );
        sysBillReportPo.setCreateBy( arg0.getCreateBy() );
        sysBillReportPo.setCreateTime( arg0.getCreateTime() );
        sysBillReportPo.setUpdateBy( arg0.getUpdateBy() );
        sysBillReportPo.setUpdateTime( arg0.getUpdateTime() );
        sysBillReportPo.setDelFlag( arg0.getDelFlag() );
        sysBillReportPo.setCreateName( arg0.getCreateName() );
        sysBillReportPo.setUpdateName( arg0.getUpdateName() );
        sysBillReportPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysBillReportPo.setEntityId( arg0.getEntityId() );
        sysBillReportPo.setEntityName( arg0.getEntityName() );
        sysBillReportPo.setCode( arg0.getCode() );
        sysBillReportPo.setBillType( arg0.getBillType() );
        sysBillReportPo.setReporttimeType( arg0.getReporttimeType() );
        sysBillReportPo.setYear( arg0.getYear() );
        sysBillReportPo.setMonth( arg0.getMonth() );
        sysBillReportPo.setSeason( arg0.getSeason() );
        sysBillReportPo.setReportAddress( arg0.getReportAddress() );
        sysBillReportPo.setRemark( arg0.getRemark() );

        return sysBillReportPo;
    }

    @Override
    public List<SysBillReportPo> mapperPo(Collection<SysBillReportDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysBillReportPo> list = new ArrayList<SysBillReportPo>( arg0.size() );
        for ( SysBillReportDto sysBillReportDto : arg0 ) {
            list.add( mapperPo( sysBillReportDto ) );
        }

        return list;
    }

    @Override
    public Page<SysBillReportPo> mapperPagePo(Collection<SysBillReportDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysBillReportPo> page = new Page<SysBillReportPo>();
        for ( SysBillReportDto sysBillReportDto : arg0 ) {
            page.add( mapperPo( sysBillReportDto ) );
        }

        return page;
    }
}
