package com.heju.system.approval.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.approval.domain.dto.SysApprovalCustomerinfoDto;
import com.heju.system.approval.domain.po.SysApprovalCustomerinfoPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-21T14:09:19+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysApprovalCustomerinfoConverterImpl implements SysApprovalCustomerinfoConverter {

    @Override
    public SysApprovalCustomerinfoDto mapperDto(SysApprovalCustomerinfoPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysApprovalCustomerinfoDto sysApprovalCustomerinfoDto = new SysApprovalCustomerinfoDto();

        sysApprovalCustomerinfoDto.setId( arg0.getId() );
        sysApprovalCustomerinfoDto.setSourceName( arg0.getSourceName() );
        sysApprovalCustomerinfoDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysApprovalCustomerinfoDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysApprovalCustomerinfoDto.setName( arg0.getName() );
        sysApprovalCustomerinfoDto.setStatus( arg0.getStatus() );
        sysApprovalCustomerinfoDto.setSort( arg0.getSort() );
        sysApprovalCustomerinfoDto.setRemark( arg0.getRemark() );
        sysApprovalCustomerinfoDto.setCreateBy( arg0.getCreateBy() );
        sysApprovalCustomerinfoDto.setCreateTime( arg0.getCreateTime() );
        sysApprovalCustomerinfoDto.setUpdateBy( arg0.getUpdateBy() );
        sysApprovalCustomerinfoDto.setUpdateTime( arg0.getUpdateTime() );
        sysApprovalCustomerinfoDto.setDelFlag( arg0.getDelFlag() );
        sysApprovalCustomerinfoDto.setUpdateName( arg0.getUpdateName() );
        sysApprovalCustomerinfoDto.setFieldId( arg0.getFieldId() );
        sysApprovalCustomerinfoDto.setBusinessId( arg0.getBusinessId() );
        sysApprovalCustomerinfoDto.setBeforeUpdate( arg0.getBeforeUpdate() );
        sysApprovalCustomerinfoDto.setAfterUpdate( arg0.getAfterUpdate() );
        sysApprovalCustomerinfoDto.setSheetId( arg0.getSheetId() );
        sysApprovalCustomerinfoDto.setAddType( arg0.getAddType() );
        sysApprovalCustomerinfoDto.setBeforeUpdateId( arg0.getBeforeUpdateId() );
        sysApprovalCustomerinfoDto.setAfterUpdateId( arg0.getAfterUpdateId() );
        sysApprovalCustomerinfoDto.setAssBusinessId( arg0.getAssBusinessId() );
        sysApprovalCustomerinfoDto.setAssSheetName( arg0.getAssSheetName() );
        sysApprovalCustomerinfoDto.setCreateName( arg0.getCreateName() );

        return sysApprovalCustomerinfoDto;
    }

    @Override
    public List<SysApprovalCustomerinfoDto> mapperDto(Collection<SysApprovalCustomerinfoPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysApprovalCustomerinfoDto> list = new ArrayList<SysApprovalCustomerinfoDto>( arg0.size() );
        for ( SysApprovalCustomerinfoPo sysApprovalCustomerinfoPo : arg0 ) {
            list.add( mapperDto( sysApprovalCustomerinfoPo ) );
        }

        return list;
    }

    @Override
    public Page<SysApprovalCustomerinfoDto> mapperPageDto(Collection<SysApprovalCustomerinfoPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysApprovalCustomerinfoDto> page = new Page<SysApprovalCustomerinfoDto>();
        for ( SysApprovalCustomerinfoPo sysApprovalCustomerinfoPo : arg0 ) {
            page.add( mapperDto( sysApprovalCustomerinfoPo ) );
        }

        return page;
    }

    @Override
    public SysApprovalCustomerinfoPo mapperPo(SysApprovalCustomerinfoDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysApprovalCustomerinfoPo sysApprovalCustomerinfoPo = new SysApprovalCustomerinfoPo();

        sysApprovalCustomerinfoPo.setId( arg0.getId() );
        sysApprovalCustomerinfoPo.setSourceName( arg0.getSourceName() );
        sysApprovalCustomerinfoPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysApprovalCustomerinfoPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysApprovalCustomerinfoPo.setName( arg0.getName() );
        sysApprovalCustomerinfoPo.setStatus( arg0.getStatus() );
        sysApprovalCustomerinfoPo.setSort( arg0.getSort() );
        sysApprovalCustomerinfoPo.setRemark( arg0.getRemark() );
        sysApprovalCustomerinfoPo.setCreateBy( arg0.getCreateBy() );
        sysApprovalCustomerinfoPo.setCreateTime( arg0.getCreateTime() );
        sysApprovalCustomerinfoPo.setUpdateBy( arg0.getUpdateBy() );
        sysApprovalCustomerinfoPo.setUpdateTime( arg0.getUpdateTime() );
        sysApprovalCustomerinfoPo.setDelFlag( arg0.getDelFlag() );
        sysApprovalCustomerinfoPo.setCreateName( arg0.getCreateName() );
        sysApprovalCustomerinfoPo.setUpdateName( arg0.getUpdateName() );
        sysApprovalCustomerinfoPo.setFieldId( arg0.getFieldId() );
        sysApprovalCustomerinfoPo.setBusinessId( arg0.getBusinessId() );
        sysApprovalCustomerinfoPo.setBeforeUpdate( arg0.getBeforeUpdate() );
        sysApprovalCustomerinfoPo.setAfterUpdate( arg0.getAfterUpdate() );
        sysApprovalCustomerinfoPo.setSheetId( arg0.getSheetId() );
        sysApprovalCustomerinfoPo.setAddType( arg0.getAddType() );
        sysApprovalCustomerinfoPo.setBeforeUpdateId( arg0.getBeforeUpdateId() );
        sysApprovalCustomerinfoPo.setAfterUpdateId( arg0.getAfterUpdateId() );
        sysApprovalCustomerinfoPo.setAssBusinessId( arg0.getAssBusinessId() );
        sysApprovalCustomerinfoPo.setAssSheetName( arg0.getAssSheetName() );

        return sysApprovalCustomerinfoPo;
    }

    @Override
    public List<SysApprovalCustomerinfoPo> mapperPo(Collection<SysApprovalCustomerinfoDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysApprovalCustomerinfoPo> list = new ArrayList<SysApprovalCustomerinfoPo>( arg0.size() );
        for ( SysApprovalCustomerinfoDto sysApprovalCustomerinfoDto : arg0 ) {
            list.add( mapperPo( sysApprovalCustomerinfoDto ) );
        }

        return list;
    }

    @Override
    public Page<SysApprovalCustomerinfoPo> mapperPagePo(Collection<SysApprovalCustomerinfoDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysApprovalCustomerinfoPo> page = new Page<SysApprovalCustomerinfoPo>();
        for ( SysApprovalCustomerinfoDto sysApprovalCustomerinfoDto : arg0 ) {
            page.add( mapperPo( sysApprovalCustomerinfoDto ) );
        }

        return page;
    }
}
