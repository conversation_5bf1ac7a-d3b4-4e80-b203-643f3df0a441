package com.heju.system.forms.field.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.forms.field.domain.dto.SysFieldDto;
import com.heju.system.forms.field.domain.po.SysFieldPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-28T17:23:00+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysFieldConverterImpl implements SysFieldConverter {

    @Override
    public SysFieldDto mapperDto(SysFieldPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysFieldDto sysFieldDto = new SysFieldDto();

        sysFieldDto.setId( arg0.getId() );
        sysFieldDto.setSourceName( arg0.getSourceName() );
        sysFieldDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysFieldDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysFieldDto.setName( arg0.getName() );
        sysFieldDto.setStatus( arg0.getStatus() );
        sysFieldDto.setSort( arg0.getSort() );
        sysFieldDto.setCreateBy( arg0.getCreateBy() );
        sysFieldDto.setCreateTime( arg0.getCreateTime() );
        sysFieldDto.setUpdateBy( arg0.getUpdateBy() );
        sysFieldDto.setUpdateTime( arg0.getUpdateTime() );
        sysFieldDto.setDelFlag( arg0.getDelFlag() );
        sysFieldDto.setCreateName( arg0.getCreateName() );
        sysFieldDto.setUpdateName( arg0.getUpdateName() );
        sysFieldDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysFieldDto.setApiName( arg0.getApiName() );
        sysFieldDto.setFieldSystemType( arg0.getFieldSystemType() );
        sysFieldDto.setFieldType( arg0.getFieldType() );
        sysFieldDto.setIsEdit( arg0.getIsEdit() );
        sysFieldDto.setShowLength( arg0.getShowLength() );
        sysFieldDto.setFieldLength( arg0.getFieldLength() );
        sysFieldDto.setDecimalLength( arg0.getDecimalLength() );
        sysFieldDto.setOptionType( arg0.getOptionType() );
        sysFieldDto.setOptionId( arg0.getOptionId() );
        sysFieldDto.setDateType( arg0.getDateType() );
        sysFieldDto.setFileType( arg0.getFileType() );
        sysFieldDto.setQuoteSheetId( arg0.getQuoteSheetId() );
        sysFieldDto.setQuoteSheetFieldId( arg0.getQuoteSheetFieldId() );
        sysFieldDto.setReferencedFieldId( arg0.getReferencedFieldId() );
        sysFieldDto.setReferencingFieldId( arg0.getReferencingFieldId() );
        sysFieldDto.setRelationSheetId( arg0.getRelationSheetId() );
        sysFieldDto.setCurrentFieldId( arg0.getCurrentFieldId() );
        sysFieldDto.setRelationSheetFieldId( arg0.getRelationSheetFieldId() );
        sysFieldDto.setRelationType( arg0.getRelationType() );
        sysFieldDto.setCascadeId( arg0.getCascadeId() );
        sysFieldDto.setSheetId( arg0.getSheetId() );
        sysFieldDto.setRemark( arg0.getRemark() );
        sysFieldDto.setApplyUpt( arg0.getApplyUpt() );
        sysFieldDto.setIsUpdate( arg0.getIsUpdate() );

        return sysFieldDto;
    }

    @Override
    public List<SysFieldDto> mapperDto(Collection<SysFieldPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysFieldDto> list = new ArrayList<SysFieldDto>( arg0.size() );
        for ( SysFieldPo sysFieldPo : arg0 ) {
            list.add( mapperDto( sysFieldPo ) );
        }

        return list;
    }

    @Override
    public Page<SysFieldDto> mapperPageDto(Collection<SysFieldPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysFieldDto> page = new Page<SysFieldDto>();
        for ( SysFieldPo sysFieldPo : arg0 ) {
            page.add( mapperDto( sysFieldPo ) );
        }

        return page;
    }

    @Override
    public SysFieldPo mapperPo(SysFieldDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysFieldPo sysFieldPo = new SysFieldPo();

        sysFieldPo.setId( arg0.getId() );
        sysFieldPo.setSourceName( arg0.getSourceName() );
        sysFieldPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysFieldPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysFieldPo.setName( arg0.getName() );
        sysFieldPo.setStatus( arg0.getStatus() );
        sysFieldPo.setSort( arg0.getSort() );
        sysFieldPo.setCreateBy( arg0.getCreateBy() );
        sysFieldPo.setCreateTime( arg0.getCreateTime() );
        sysFieldPo.setUpdateBy( arg0.getUpdateBy() );
        sysFieldPo.setUpdateTime( arg0.getUpdateTime() );
        sysFieldPo.setDelFlag( arg0.getDelFlag() );
        sysFieldPo.setCreateName( arg0.getCreateName() );
        sysFieldPo.setUpdateName( arg0.getUpdateName() );
        sysFieldPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysFieldPo.setApiName( arg0.getApiName() );
        sysFieldPo.setFieldSystemType( arg0.getFieldSystemType() );
        sysFieldPo.setFieldType( arg0.getFieldType() );
        sysFieldPo.setIsEdit( arg0.getIsEdit() );
        sysFieldPo.setShowLength( arg0.getShowLength() );
        sysFieldPo.setFieldLength( arg0.getFieldLength() );
        sysFieldPo.setDecimalLength( arg0.getDecimalLength() );
        sysFieldPo.setOptionType( arg0.getOptionType() );
        sysFieldPo.setOptionId( arg0.getOptionId() );
        sysFieldPo.setDateType( arg0.getDateType() );
        sysFieldPo.setFileType( arg0.getFileType() );
        sysFieldPo.setQuoteSheetId( arg0.getQuoteSheetId() );
        sysFieldPo.setQuoteSheetFieldId( arg0.getQuoteSheetFieldId() );
        sysFieldPo.setReferencedFieldId( arg0.getReferencedFieldId() );
        sysFieldPo.setReferencingFieldId( arg0.getReferencingFieldId() );
        sysFieldPo.setRelationSheetId( arg0.getRelationSheetId() );
        sysFieldPo.setCurrentFieldId( arg0.getCurrentFieldId() );
        sysFieldPo.setRelationSheetFieldId( arg0.getRelationSheetFieldId() );
        sysFieldPo.setRelationType( arg0.getRelationType() );
        sysFieldPo.setCascadeId( arg0.getCascadeId() );
        sysFieldPo.setSheetId( arg0.getSheetId() );
        sysFieldPo.setRemark( arg0.getRemark() );
        sysFieldPo.setApplyUpt( arg0.getApplyUpt() );
        sysFieldPo.setIsUpdate( arg0.getIsUpdate() );

        return sysFieldPo;
    }

    @Override
    public List<SysFieldPo> mapperPo(Collection<SysFieldDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysFieldPo> list = new ArrayList<SysFieldPo>( arg0.size() );
        for ( SysFieldDto sysFieldDto : arg0 ) {
            list.add( mapperPo( sysFieldDto ) );
        }

        return list;
    }

    @Override
    public Page<SysFieldPo> mapperPagePo(Collection<SysFieldDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysFieldPo> page = new Page<SysFieldPo>();
        for ( SysFieldDto sysFieldDto : arg0 ) {
            page.add( mapperPo( sysFieldDto ) );
        }

        return page;
    }
}
