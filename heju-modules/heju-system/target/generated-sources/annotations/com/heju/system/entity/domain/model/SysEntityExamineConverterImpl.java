package com.heju.system.entity.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.entity.domain.dto.SysEntityExamineDto;
import com.heju.system.entity.domain.po.SysEntityExaminePo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-28T17:23:00+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysEntityExamineConverterImpl implements SysEntityExamineConverter {

    @Override
    public SysEntityExamineDto mapperDto(SysEntityExaminePo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysEntityExamineDto sysEntityExamineDto = new SysEntityExamineDto();

        sysEntityExamineDto.setId( arg0.getId() );
        sysEntityExamineDto.setSourceName( arg0.getSourceName() );
        sysEntityExamineDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysEntityExamineDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysEntityExamineDto.setName( arg0.getName() );
        sysEntityExamineDto.setStatus( arg0.getStatus() );
        sysEntityExamineDto.setSort( arg0.getSort() );
        sysEntityExamineDto.setCreateBy( arg0.getCreateBy() );
        sysEntityExamineDto.setCreateTime( arg0.getCreateTime() );
        sysEntityExamineDto.setUpdateBy( arg0.getUpdateBy() );
        sysEntityExamineDto.setUpdateTime( arg0.getUpdateTime() );
        sysEntityExamineDto.setDelFlag( arg0.getDelFlag() );
        sysEntityExamineDto.setUpdateName( arg0.getUpdateName() );
        sysEntityExamineDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysEntityExamineDto.setFieldComment( arg0.getFieldComment() );
        sysEntityExamineDto.setFieldName( arg0.getFieldName() );
        sysEntityExamineDto.setFieldBelong( arg0.getFieldBelong() );
        sysEntityExamineDto.setBeforeText( arg0.getBeforeText() );
        sysEntityExamineDto.setAfterText( arg0.getAfterText() );
        sysEntityExamineDto.setEntityId( arg0.getEntityId() );
        sysEntityExamineDto.setEntityName( arg0.getEntityName() );
        sysEntityExamineDto.setListId( arg0.getListId() );
        sysEntityExamineDto.setExamineStatus( arg0.getExamineStatus() );
        sysEntityExamineDto.setRemark( arg0.getRemark() );
        sysEntityExamineDto.setCreateName( arg0.getCreateName() );

        return sysEntityExamineDto;
    }

    @Override
    public List<SysEntityExamineDto> mapperDto(Collection<SysEntityExaminePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysEntityExamineDto> list = new ArrayList<SysEntityExamineDto>( arg0.size() );
        for ( SysEntityExaminePo sysEntityExaminePo : arg0 ) {
            list.add( mapperDto( sysEntityExaminePo ) );
        }

        return list;
    }

    @Override
    public Page<SysEntityExamineDto> mapperPageDto(Collection<SysEntityExaminePo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysEntityExamineDto> page = new Page<SysEntityExamineDto>();
        for ( SysEntityExaminePo sysEntityExaminePo : arg0 ) {
            page.add( mapperDto( sysEntityExaminePo ) );
        }

        return page;
    }

    @Override
    public SysEntityExaminePo mapperPo(SysEntityExamineDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysEntityExaminePo sysEntityExaminePo = new SysEntityExaminePo();

        sysEntityExaminePo.setId( arg0.getId() );
        sysEntityExaminePo.setSourceName( arg0.getSourceName() );
        sysEntityExaminePo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysEntityExaminePo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysEntityExaminePo.setName( arg0.getName() );
        sysEntityExaminePo.setStatus( arg0.getStatus() );
        sysEntityExaminePo.setSort( arg0.getSort() );
        sysEntityExaminePo.setCreateBy( arg0.getCreateBy() );
        sysEntityExaminePo.setCreateTime( arg0.getCreateTime() );
        sysEntityExaminePo.setUpdateBy( arg0.getUpdateBy() );
        sysEntityExaminePo.setUpdateTime( arg0.getUpdateTime() );
        sysEntityExaminePo.setDelFlag( arg0.getDelFlag() );
        sysEntityExaminePo.setCreateName( arg0.getCreateName() );
        sysEntityExaminePo.setUpdateName( arg0.getUpdateName() );
        sysEntityExaminePo.setEnterpriseId( arg0.getEnterpriseId() );
        sysEntityExaminePo.setFieldComment( arg0.getFieldComment() );
        sysEntityExaminePo.setFieldName( arg0.getFieldName() );
        sysEntityExaminePo.setFieldBelong( arg0.getFieldBelong() );
        sysEntityExaminePo.setBeforeText( arg0.getBeforeText() );
        sysEntityExaminePo.setAfterText( arg0.getAfterText() );
        sysEntityExaminePo.setEntityId( arg0.getEntityId() );
        sysEntityExaminePo.setEntityName( arg0.getEntityName() );
        sysEntityExaminePo.setListId( arg0.getListId() );
        sysEntityExaminePo.setExamineStatus( arg0.getExamineStatus() );
        sysEntityExaminePo.setRemark( arg0.getRemark() );

        return sysEntityExaminePo;
    }

    @Override
    public List<SysEntityExaminePo> mapperPo(Collection<SysEntityExamineDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysEntityExaminePo> list = new ArrayList<SysEntityExaminePo>( arg0.size() );
        for ( SysEntityExamineDto sysEntityExamineDto : arg0 ) {
            list.add( mapperPo( sysEntityExamineDto ) );
        }

        return list;
    }

    @Override
    public Page<SysEntityExaminePo> mapperPagePo(Collection<SysEntityExamineDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysEntityExaminePo> page = new Page<SysEntityExaminePo>();
        for ( SysEntityExamineDto sysEntityExamineDto : arg0 ) {
            page.add( mapperPo( sysEntityExamineDto ) );
        }

        return page;
    }
}
