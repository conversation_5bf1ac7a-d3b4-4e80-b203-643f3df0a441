package com.heju.system.file.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.file.domain.dto.SysFileClassifyDto;
import com.heju.system.file.domain.po.SysFileClassifyPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-21T14:09:20+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysFileClassifyConverterImpl implements SysFileClassifyConverter {

    @Override
    public Page<SysFileClassifyDto> mapperPageDto(Collection<SysFileClassifyPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysFileClassifyDto> page = new Page<SysFileClassifyDto>();
        for ( SysFileClassifyPo sysFileClassifyPo : arg0 ) {
            page.add( mapperDto( sysFileClassifyPo ) );
        }

        return page;
    }

    @Override
    public Page<SysFileClassifyPo> mapperPagePo(Collection<SysFileClassifyDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysFileClassifyPo> page = new Page<SysFileClassifyPo>();
        for ( SysFileClassifyDto sysFileClassifyDto : arg0 ) {
            page.add( mapperPo( sysFileClassifyDto ) );
        }

        return page;
    }

    @Override
    public SysFileClassifyDto mapperDto(SysFileClassifyPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysFileClassifyDto sysFileClassifyDto = new SysFileClassifyDto();

        sysFileClassifyDto.setId( arg0.getId() );
        sysFileClassifyDto.setSourceName( arg0.getSourceName() );
        sysFileClassifyDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysFileClassifyDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysFileClassifyDto.setName( arg0.getName() );
        sysFileClassifyDto.setStatus( arg0.getStatus() );
        sysFileClassifyDto.setSort( arg0.getSort() );
        sysFileClassifyDto.setCreateBy( arg0.getCreateBy() );
        sysFileClassifyDto.setCreateTime( arg0.getCreateTime() );
        sysFileClassifyDto.setUpdateBy( arg0.getUpdateBy() );
        sysFileClassifyDto.setUpdateTime( arg0.getUpdateTime() );
        sysFileClassifyDto.setDelFlag( arg0.getDelFlag() );
        sysFileClassifyDto.setCreateName( arg0.getCreateName() );
        sysFileClassifyDto.setUpdateName( arg0.getUpdateName() );
        sysFileClassifyDto.setParentId( arg0.getParentId() );
        sysFileClassifyDto.setParentName( arg0.getParentName() );
        sysFileClassifyDto.setAncestors( arg0.getAncestors() );
        sysFileClassifyDto.setLevel( arg0.getLevel() );
        sysFileClassifyDto.setDefaultNode( arg0.getDefaultNode() );
        sysFileClassifyDto.setOldAncestors( arg0.getOldAncestors() );
        sysFileClassifyDto.setOldLevel( arg0.getOldLevel() );
        sysFileClassifyDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysFileClassifyDto.setCode( arg0.getCode() );
        sysFileClassifyDto.setRemark( arg0.getRemark() );

        return sysFileClassifyDto;
    }

    @Override
    public List<SysFileClassifyDto> mapperDto(Collection<SysFileClassifyPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysFileClassifyDto> list = new ArrayList<SysFileClassifyDto>( arg0.size() );
        for ( SysFileClassifyPo sysFileClassifyPo : arg0 ) {
            list.add( mapperDto( sysFileClassifyPo ) );
        }

        return list;
    }

    @Override
    public SysFileClassifyPo mapperPo(SysFileClassifyDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysFileClassifyPo sysFileClassifyPo = new SysFileClassifyPo();

        sysFileClassifyPo.setId( arg0.getId() );
        sysFileClassifyPo.setSourceName( arg0.getSourceName() );
        sysFileClassifyPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysFileClassifyPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysFileClassifyPo.setName( arg0.getName() );
        sysFileClassifyPo.setStatus( arg0.getStatus() );
        sysFileClassifyPo.setSort( arg0.getSort() );
        sysFileClassifyPo.setCreateBy( arg0.getCreateBy() );
        sysFileClassifyPo.setCreateTime( arg0.getCreateTime() );
        sysFileClassifyPo.setUpdateBy( arg0.getUpdateBy() );
        sysFileClassifyPo.setUpdateTime( arg0.getUpdateTime() );
        sysFileClassifyPo.setDelFlag( arg0.getDelFlag() );
        sysFileClassifyPo.setCreateName( arg0.getCreateName() );
        sysFileClassifyPo.setUpdateName( arg0.getUpdateName() );
        sysFileClassifyPo.setParentId( arg0.getParentId() );
        sysFileClassifyPo.setParentName( arg0.getParentName() );
        sysFileClassifyPo.setAncestors( arg0.getAncestors() );
        sysFileClassifyPo.setLevel( arg0.getLevel() );
        sysFileClassifyPo.setDefaultNode( arg0.getDefaultNode() );
        sysFileClassifyPo.setOldAncestors( arg0.getOldAncestors() );
        sysFileClassifyPo.setOldLevel( arg0.getOldLevel() );
        sysFileClassifyPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysFileClassifyPo.setCode( arg0.getCode() );
        sysFileClassifyPo.setRemark( arg0.getRemark() );

        return sysFileClassifyPo;
    }

    @Override
    public List<SysFileClassifyPo> mapperPo(Collection<SysFileClassifyDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysFileClassifyPo> list = new ArrayList<SysFileClassifyPo>( arg0.size() );
        for ( SysFileClassifyDto sysFileClassifyDto : arg0 ) {
            list.add( mapperPo( sysFileClassifyDto ) );
        }

        return list;
    }
}
