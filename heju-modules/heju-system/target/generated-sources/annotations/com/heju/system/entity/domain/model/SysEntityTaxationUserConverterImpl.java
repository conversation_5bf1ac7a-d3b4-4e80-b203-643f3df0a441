package com.heju.system.entity.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.entity.domain.dto.SysEntityTaxationUserDto;
import com.heju.system.entity.domain.po.SysEntityTaxationUserPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-21T14:09:20+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysEntityTaxationUserConverterImpl implements SysEntityTaxationUserConverter {

    @Override
    public SysEntityTaxationUserDto mapperDto(SysEntityTaxationUserPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysEntityTaxationUserDto sysEntityTaxationUserDto = new SysEntityTaxationUserDto();

        sysEntityTaxationUserDto.setId( arg0.getId() );
        sysEntityTaxationUserDto.setSourceName( arg0.getSourceName() );
        sysEntityTaxationUserDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysEntityTaxationUserDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysEntityTaxationUserDto.setName( arg0.getName() );
        sysEntityTaxationUserDto.setStatus( arg0.getStatus() );
        sysEntityTaxationUserDto.setSort( arg0.getSort() );
        sysEntityTaxationUserDto.setRemark( arg0.getRemark() );
        sysEntityTaxationUserDto.setCreateBy( arg0.getCreateBy() );
        sysEntityTaxationUserDto.setCreateTime( arg0.getCreateTime() );
        sysEntityTaxationUserDto.setUpdateBy( arg0.getUpdateBy() );
        sysEntityTaxationUserDto.setUpdateTime( arg0.getUpdateTime() );
        sysEntityTaxationUserDto.setDelFlag( arg0.getDelFlag() );
        sysEntityTaxationUserDto.setCreateName( arg0.getCreateName() );
        sysEntityTaxationUserDto.setUpdateName( arg0.getUpdateName() );
        sysEntityTaxationUserDto.setUserName( arg0.getUserName() );
        sysEntityTaxationUserDto.setUserType( arg0.getUserType() );
        sysEntityTaxationUserDto.setIdentityType( arg0.getIdentityType() );
        sysEntityTaxationUserDto.setIdentityNumber( arg0.getIdentityNumber() );
        sysEntityTaxationUserDto.setPhone( arg0.getPhone() );
        sysEntityTaxationUserDto.setTelephone( arg0.getTelephone() );
        sysEntityTaxationUserDto.setEMail( arg0.getEMail() );
        sysEntityTaxationUserDto.setEntityId( arg0.getEntityId() );

        return sysEntityTaxationUserDto;
    }

    @Override
    public List<SysEntityTaxationUserDto> mapperDto(Collection<SysEntityTaxationUserPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysEntityTaxationUserDto> list = new ArrayList<SysEntityTaxationUserDto>( arg0.size() );
        for ( SysEntityTaxationUserPo sysEntityTaxationUserPo : arg0 ) {
            list.add( mapperDto( sysEntityTaxationUserPo ) );
        }

        return list;
    }

    @Override
    public Page<SysEntityTaxationUserDto> mapperPageDto(Collection<SysEntityTaxationUserPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysEntityTaxationUserDto> page = new Page<SysEntityTaxationUserDto>();
        for ( SysEntityTaxationUserPo sysEntityTaxationUserPo : arg0 ) {
            page.add( mapperDto( sysEntityTaxationUserPo ) );
        }

        return page;
    }

    @Override
    public SysEntityTaxationUserPo mapperPo(SysEntityTaxationUserDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysEntityTaxationUserPo sysEntityTaxationUserPo = new SysEntityTaxationUserPo();

        sysEntityTaxationUserPo.setId( arg0.getId() );
        sysEntityTaxationUserPo.setSourceName( arg0.getSourceName() );
        sysEntityTaxationUserPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysEntityTaxationUserPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysEntityTaxationUserPo.setName( arg0.getName() );
        sysEntityTaxationUserPo.setStatus( arg0.getStatus() );
        sysEntityTaxationUserPo.setSort( arg0.getSort() );
        sysEntityTaxationUserPo.setRemark( arg0.getRemark() );
        sysEntityTaxationUserPo.setCreateBy( arg0.getCreateBy() );
        sysEntityTaxationUserPo.setCreateTime( arg0.getCreateTime() );
        sysEntityTaxationUserPo.setUpdateBy( arg0.getUpdateBy() );
        sysEntityTaxationUserPo.setUpdateTime( arg0.getUpdateTime() );
        sysEntityTaxationUserPo.setDelFlag( arg0.getDelFlag() );
        sysEntityTaxationUserPo.setCreateName( arg0.getCreateName() );
        sysEntityTaxationUserPo.setUpdateName( arg0.getUpdateName() );
        sysEntityTaxationUserPo.setUserName( arg0.getUserName() );
        sysEntityTaxationUserPo.setUserType( arg0.getUserType() );
        sysEntityTaxationUserPo.setIdentityType( arg0.getIdentityType() );
        sysEntityTaxationUserPo.setIdentityNumber( arg0.getIdentityNumber() );
        sysEntityTaxationUserPo.setPhone( arg0.getPhone() );
        sysEntityTaxationUserPo.setTelephone( arg0.getTelephone() );
        sysEntityTaxationUserPo.setEMail( arg0.getEMail() );
        sysEntityTaxationUserPo.setEntityId( arg0.getEntityId() );

        return sysEntityTaxationUserPo;
    }

    @Override
    public List<SysEntityTaxationUserPo> mapperPo(Collection<SysEntityTaxationUserDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysEntityTaxationUserPo> list = new ArrayList<SysEntityTaxationUserPo>( arg0.size() );
        for ( SysEntityTaxationUserDto sysEntityTaxationUserDto : arg0 ) {
            list.add( mapperPo( sysEntityTaxationUserDto ) );
        }

        return list;
    }

    @Override
    public Page<SysEntityTaxationUserPo> mapperPagePo(Collection<SysEntityTaxationUserDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysEntityTaxationUserPo> page = new Page<SysEntityTaxationUserPo>();
        for ( SysEntityTaxationUserDto sysEntityTaxationUserDto : arg0 ) {
            page.add( mapperPo( sysEntityTaxationUserDto ) );
        }

        return page;
    }
}
