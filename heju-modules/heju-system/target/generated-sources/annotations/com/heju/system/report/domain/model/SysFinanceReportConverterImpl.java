package com.heju.system.report.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.report.domain.dto.SysFinanceReportDto;
import com.heju.system.report.domain.po.SysFinanceReportPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-21T14:09:19+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysFinanceReportConverterImpl implements SysFinanceReportConverter {

    @Override
    public SysFinanceReportDto mapperDto(SysFinanceReportPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysFinanceReportDto sysFinanceReportDto = new SysFinanceReportDto();

        sysFinanceReportDto.setId( arg0.getId() );
        sysFinanceReportDto.setSourceName( arg0.getSourceName() );
        sysFinanceReportDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysFinanceReportDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysFinanceReportDto.setName( arg0.getName() );
        sysFinanceReportDto.setStatus( arg0.getStatus() );
        sysFinanceReportDto.setSort( arg0.getSort() );
        sysFinanceReportDto.setCreateBy( arg0.getCreateBy() );
        sysFinanceReportDto.setCreateTime( arg0.getCreateTime() );
        sysFinanceReportDto.setUpdateBy( arg0.getUpdateBy() );
        sysFinanceReportDto.setUpdateTime( arg0.getUpdateTime() );
        sysFinanceReportDto.setDelFlag( arg0.getDelFlag() );
        sysFinanceReportDto.setCreateName( arg0.getCreateName() );
        sysFinanceReportDto.setUpdateName( arg0.getUpdateName() );
        sysFinanceReportDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysFinanceReportDto.setEntityId( arg0.getEntityId() );
        sysFinanceReportDto.setEntityName( arg0.getEntityName() );
        sysFinanceReportDto.setCode( arg0.getCode() );
        sysFinanceReportDto.setFinanceType( arg0.getFinanceType() );
        sysFinanceReportDto.setReporttimeType( arg0.getReporttimeType() );
        sysFinanceReportDto.setYear( arg0.getYear() );
        sysFinanceReportDto.setMonth( arg0.getMonth() );
        sysFinanceReportDto.setSeason( arg0.getSeason() );
        sysFinanceReportDto.setReportAddress( arg0.getReportAddress() );
        sysFinanceReportDto.setRemark( arg0.getRemark() );

        return sysFinanceReportDto;
    }

    @Override
    public List<SysFinanceReportDto> mapperDto(Collection<SysFinanceReportPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysFinanceReportDto> list = new ArrayList<SysFinanceReportDto>( arg0.size() );
        for ( SysFinanceReportPo sysFinanceReportPo : arg0 ) {
            list.add( mapperDto( sysFinanceReportPo ) );
        }

        return list;
    }

    @Override
    public Page<SysFinanceReportDto> mapperPageDto(Collection<SysFinanceReportPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysFinanceReportDto> page = new Page<SysFinanceReportDto>();
        for ( SysFinanceReportPo sysFinanceReportPo : arg0 ) {
            page.add( mapperDto( sysFinanceReportPo ) );
        }

        return page;
    }

    @Override
    public SysFinanceReportPo mapperPo(SysFinanceReportDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysFinanceReportPo sysFinanceReportPo = new SysFinanceReportPo();

        sysFinanceReportPo.setId( arg0.getId() );
        sysFinanceReportPo.setSourceName( arg0.getSourceName() );
        sysFinanceReportPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysFinanceReportPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysFinanceReportPo.setName( arg0.getName() );
        sysFinanceReportPo.setStatus( arg0.getStatus() );
        sysFinanceReportPo.setSort( arg0.getSort() );
        sysFinanceReportPo.setCreateBy( arg0.getCreateBy() );
        sysFinanceReportPo.setCreateTime( arg0.getCreateTime() );
        sysFinanceReportPo.setUpdateBy( arg0.getUpdateBy() );
        sysFinanceReportPo.setUpdateTime( arg0.getUpdateTime() );
        sysFinanceReportPo.setDelFlag( arg0.getDelFlag() );
        sysFinanceReportPo.setCreateName( arg0.getCreateName() );
        sysFinanceReportPo.setUpdateName( arg0.getUpdateName() );
        sysFinanceReportPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysFinanceReportPo.setEntityId( arg0.getEntityId() );
        sysFinanceReportPo.setEntityName( arg0.getEntityName() );
        sysFinanceReportPo.setCode( arg0.getCode() );
        sysFinanceReportPo.setFinanceType( arg0.getFinanceType() );
        sysFinanceReportPo.setReporttimeType( arg0.getReporttimeType() );
        sysFinanceReportPo.setYear( arg0.getYear() );
        sysFinanceReportPo.setMonth( arg0.getMonth() );
        sysFinanceReportPo.setSeason( arg0.getSeason() );
        sysFinanceReportPo.setReportAddress( arg0.getReportAddress() );
        sysFinanceReportPo.setRemark( arg0.getRemark() );

        return sysFinanceReportPo;
    }

    @Override
    public List<SysFinanceReportPo> mapperPo(Collection<SysFinanceReportDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysFinanceReportPo> list = new ArrayList<SysFinanceReportPo>( arg0.size() );
        for ( SysFinanceReportDto sysFinanceReportDto : arg0 ) {
            list.add( mapperPo( sysFinanceReportDto ) );
        }

        return list;
    }

    @Override
    public Page<SysFinanceReportPo> mapperPagePo(Collection<SysFinanceReportDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysFinanceReportPo> page = new Page<SysFinanceReportPo>();
        for ( SysFinanceReportDto sysFinanceReportDto : arg0 ) {
            page.add( mapperPo( sysFinanceReportDto ) );
        }

        return page;
    }
}
