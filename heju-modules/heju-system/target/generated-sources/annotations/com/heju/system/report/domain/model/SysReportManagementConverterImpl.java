package com.heju.system.report.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.report.domain.dto.SysReportManagementDto;
import com.heju.system.report.domain.po.SysReportManagementPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-21T14:09:20+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysReportManagementConverterImpl implements SysReportManagementConverter {

    @Override
    public SysReportManagementDto mapperDto(SysReportManagementPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysReportManagementDto sysReportManagementDto = new SysReportManagementDto();

        sysReportManagementDto.setId( arg0.getId() );
        sysReportManagementDto.setSourceName( arg0.getSourceName() );
        sysReportManagementDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysReportManagementDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysReportManagementDto.setName( arg0.getName() );
        sysReportManagementDto.setStatus( arg0.getStatus() );
        sysReportManagementDto.setSort( arg0.getSort() );
        sysReportManagementDto.setRemark( arg0.getRemark() );
        sysReportManagementDto.setCreateBy( arg0.getCreateBy() );
        sysReportManagementDto.setCreateTime( arg0.getCreateTime() );
        sysReportManagementDto.setUpdateBy( arg0.getUpdateBy() );
        sysReportManagementDto.setUpdateTime( arg0.getUpdateTime() );
        sysReportManagementDto.setDelFlag( arg0.getDelFlag() );
        sysReportManagementDto.setCreateName( arg0.getCreateName() );
        sysReportManagementDto.setUpdateName( arg0.getUpdateName() );
        sysReportManagementDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysReportManagementDto.setEntityId( arg0.getEntityId() );
        sysReportManagementDto.setEntityName( arg0.getEntityName() );
        sysReportManagementDto.setYear( arg0.getYear() );
        sysReportManagementDto.setMonth( arg0.getMonth() );
        sysReportManagementDto.setSeason( arg0.getSeason() );
        Map<String, Boolean> map1 = arg0.getBankReportTypes();
        if ( map1 != null ) {
            sysReportManagementDto.setBankReportTypes( new LinkedHashMap<String, Boolean>( map1 ) );
        }
        Map<String, Boolean> map2 = arg0.getBillReportsTypes();
        if ( map2 != null ) {
            sysReportManagementDto.setBillReportsTypes( new LinkedHashMap<String, Boolean>( map2 ) );
        }
        Map<String, Boolean> map3 = arg0.getFinanceReportTypes();
        if ( map3 != null ) {
            sysReportManagementDto.setFinanceReportTypes( new LinkedHashMap<String, Boolean>( map3 ) );
        }
        Map<String, Boolean> map4 = arg0.getTaxReportTypes();
        if ( map4 != null ) {
            sysReportManagementDto.setTaxReportTypes( new LinkedHashMap<String, Boolean>( map4 ) );
        }

        return sysReportManagementDto;
    }

    @Override
    public List<SysReportManagementDto> mapperDto(Collection<SysReportManagementPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysReportManagementDto> list = new ArrayList<SysReportManagementDto>( arg0.size() );
        for ( SysReportManagementPo sysReportManagementPo : arg0 ) {
            list.add( mapperDto( sysReportManagementPo ) );
        }

        return list;
    }

    @Override
    public Page<SysReportManagementDto> mapperPageDto(Collection<SysReportManagementPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysReportManagementDto> page = new Page<SysReportManagementDto>();
        for ( SysReportManagementPo sysReportManagementPo : arg0 ) {
            page.add( mapperDto( sysReportManagementPo ) );
        }

        return page;
    }

    @Override
    public SysReportManagementPo mapperPo(SysReportManagementDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysReportManagementPo sysReportManagementPo = new SysReportManagementPo();

        sysReportManagementPo.setId( arg0.getId() );
        sysReportManagementPo.setSourceName( arg0.getSourceName() );
        sysReportManagementPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysReportManagementPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysReportManagementPo.setName( arg0.getName() );
        sysReportManagementPo.setStatus( arg0.getStatus() );
        sysReportManagementPo.setSort( arg0.getSort() );
        sysReportManagementPo.setRemark( arg0.getRemark() );
        sysReportManagementPo.setCreateBy( arg0.getCreateBy() );
        sysReportManagementPo.setCreateTime( arg0.getCreateTime() );
        sysReportManagementPo.setUpdateBy( arg0.getUpdateBy() );
        sysReportManagementPo.setUpdateTime( arg0.getUpdateTime() );
        sysReportManagementPo.setDelFlag( arg0.getDelFlag() );
        sysReportManagementPo.setCreateName( arg0.getCreateName() );
        sysReportManagementPo.setUpdateName( arg0.getUpdateName() );
        sysReportManagementPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysReportManagementPo.setEntityId( arg0.getEntityId() );
        sysReportManagementPo.setEntityName( arg0.getEntityName() );
        sysReportManagementPo.setYear( arg0.getYear() );
        sysReportManagementPo.setMonth( arg0.getMonth() );
        sysReportManagementPo.setSeason( arg0.getSeason() );
        Map<String, Boolean> map1 = arg0.getBankReportTypes();
        if ( map1 != null ) {
            sysReportManagementPo.setBankReportTypes( new LinkedHashMap<String, Boolean>( map1 ) );
        }
        Map<String, Boolean> map2 = arg0.getBillReportsTypes();
        if ( map2 != null ) {
            sysReportManagementPo.setBillReportsTypes( new LinkedHashMap<String, Boolean>( map2 ) );
        }
        Map<String, Boolean> map3 = arg0.getFinanceReportTypes();
        if ( map3 != null ) {
            sysReportManagementPo.setFinanceReportTypes( new LinkedHashMap<String, Boolean>( map3 ) );
        }
        Map<String, Boolean> map4 = arg0.getTaxReportTypes();
        if ( map4 != null ) {
            sysReportManagementPo.setTaxReportTypes( new LinkedHashMap<String, Boolean>( map4 ) );
        }

        return sysReportManagementPo;
    }

    @Override
    public List<SysReportManagementPo> mapperPo(Collection<SysReportManagementDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysReportManagementPo> list = new ArrayList<SysReportManagementPo>( arg0.size() );
        for ( SysReportManagementDto sysReportManagementDto : arg0 ) {
            list.add( mapperPo( sysReportManagementDto ) );
        }

        return list;
    }

    @Override
    public Page<SysReportManagementPo> mapperPagePo(Collection<SysReportManagementDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysReportManagementPo> page = new Page<SysReportManagementPo>();
        for ( SysReportManagementDto sysReportManagementDto : arg0 ) {
            page.add( mapperPo( sysReportManagementDto ) );
        }

        return page;
    }
}
