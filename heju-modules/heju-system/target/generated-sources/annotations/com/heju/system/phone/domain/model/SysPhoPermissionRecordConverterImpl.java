package com.heju.system.phone.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.phone.domain.dto.SysPhoPermissionRecordDto;
import com.heju.system.phone.domain.po.SysPhoPermissionRecordPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-28T17:23:01+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysPhoPermissionRecordConverterImpl implements SysPhoPermissionRecordConverter {

    @Override
    public SysPhoPermissionRecordDto mapperDto(SysPhoPermissionRecordPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysPhoPermissionRecordDto sysPhoPermissionRecordDto = new SysPhoPermissionRecordDto();

        sysPhoPermissionRecordDto.setId( arg0.getId() );
        sysPhoPermissionRecordDto.setSourceName( arg0.getSourceName() );
        sysPhoPermissionRecordDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysPhoPermissionRecordDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysPhoPermissionRecordDto.setName( arg0.getName() );
        sysPhoPermissionRecordDto.setStatus( arg0.getStatus() );
        sysPhoPermissionRecordDto.setSort( arg0.getSort() );
        sysPhoPermissionRecordDto.setRemark( arg0.getRemark() );
        sysPhoPermissionRecordDto.setCreateBy( arg0.getCreateBy() );
        sysPhoPermissionRecordDto.setCreateTime( arg0.getCreateTime() );
        sysPhoPermissionRecordDto.setUpdateBy( arg0.getUpdateBy() );
        sysPhoPermissionRecordDto.setUpdateTime( arg0.getUpdateTime() );
        sysPhoPermissionRecordDto.setDelFlag( arg0.getDelFlag() );
        sysPhoPermissionRecordDto.setCreateName( arg0.getCreateName() );
        sysPhoPermissionRecordDto.setUpdateName( arg0.getUpdateName() );
        sysPhoPermissionRecordDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysPhoPermissionRecordDto.setPermissionerId( arg0.getPermissionerId() );
        sysPhoPermissionRecordDto.setStartTime( arg0.getStartTime() );
        sysPhoPermissionRecordDto.setEndTime( arg0.getEndTime() );
        sysPhoPermissionRecordDto.setTimes( arg0.getTimes() );
        sysPhoPermissionRecordDto.setViewedTimes( arg0.getViewedTimes() );
        sysPhoPermissionRecordDto.setRemainingTimes( arg0.getRemainingTimes() );
        sysPhoPermissionRecordDto.setPhoneNumber( arg0.getPhoneNumber() );

        return sysPhoPermissionRecordDto;
    }

    @Override
    public List<SysPhoPermissionRecordDto> mapperDto(Collection<SysPhoPermissionRecordPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysPhoPermissionRecordDto> list = new ArrayList<SysPhoPermissionRecordDto>( arg0.size() );
        for ( SysPhoPermissionRecordPo sysPhoPermissionRecordPo : arg0 ) {
            list.add( mapperDto( sysPhoPermissionRecordPo ) );
        }

        return list;
    }

    @Override
    public Page<SysPhoPermissionRecordDto> mapperPageDto(Collection<SysPhoPermissionRecordPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysPhoPermissionRecordDto> page = new Page<SysPhoPermissionRecordDto>();
        for ( SysPhoPermissionRecordPo sysPhoPermissionRecordPo : arg0 ) {
            page.add( mapperDto( sysPhoPermissionRecordPo ) );
        }

        return page;
    }

    @Override
    public SysPhoPermissionRecordPo mapperPo(SysPhoPermissionRecordDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysPhoPermissionRecordPo sysPhoPermissionRecordPo = new SysPhoPermissionRecordPo();

        sysPhoPermissionRecordPo.setId( arg0.getId() );
        sysPhoPermissionRecordPo.setSourceName( arg0.getSourceName() );
        sysPhoPermissionRecordPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysPhoPermissionRecordPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysPhoPermissionRecordPo.setName( arg0.getName() );
        sysPhoPermissionRecordPo.setStatus( arg0.getStatus() );
        sysPhoPermissionRecordPo.setSort( arg0.getSort() );
        sysPhoPermissionRecordPo.setRemark( arg0.getRemark() );
        sysPhoPermissionRecordPo.setCreateBy( arg0.getCreateBy() );
        sysPhoPermissionRecordPo.setCreateTime( arg0.getCreateTime() );
        sysPhoPermissionRecordPo.setUpdateBy( arg0.getUpdateBy() );
        sysPhoPermissionRecordPo.setUpdateTime( arg0.getUpdateTime() );
        sysPhoPermissionRecordPo.setDelFlag( arg0.getDelFlag() );
        sysPhoPermissionRecordPo.setCreateName( arg0.getCreateName() );
        sysPhoPermissionRecordPo.setUpdateName( arg0.getUpdateName() );
        sysPhoPermissionRecordPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysPhoPermissionRecordPo.setPermissionerId( arg0.getPermissionerId() );
        sysPhoPermissionRecordPo.setStartTime( arg0.getStartTime() );
        sysPhoPermissionRecordPo.setEndTime( arg0.getEndTime() );
        sysPhoPermissionRecordPo.setTimes( arg0.getTimes() );
        sysPhoPermissionRecordPo.setViewedTimes( arg0.getViewedTimes() );
        sysPhoPermissionRecordPo.setRemainingTimes( arg0.getRemainingTimes() );
        sysPhoPermissionRecordPo.setPhoneNumber( arg0.getPhoneNumber() );

        return sysPhoPermissionRecordPo;
    }

    @Override
    public List<SysPhoPermissionRecordPo> mapperPo(Collection<SysPhoPermissionRecordDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysPhoPermissionRecordPo> list = new ArrayList<SysPhoPermissionRecordPo>( arg0.size() );
        for ( SysPhoPermissionRecordDto sysPhoPermissionRecordDto : arg0 ) {
            list.add( mapperPo( sysPhoPermissionRecordDto ) );
        }

        return list;
    }

    @Override
    public Page<SysPhoPermissionRecordPo> mapperPagePo(Collection<SysPhoPermissionRecordDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysPhoPermissionRecordPo> page = new Page<SysPhoPermissionRecordPo>();
        for ( SysPhoPermissionRecordDto sysPhoPermissionRecordDto : arg0 ) {
            page.add( mapperPo( sysPhoPermissionRecordDto ) );
        }

        return page;
    }
}
