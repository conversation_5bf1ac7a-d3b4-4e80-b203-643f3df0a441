package com.heju.system.annualReport.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.annualReport.domain.dto.SysAnnualReportDto;
import com.heju.system.annualReport.domain.po.SysAnnualReportPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-28T17:23:00+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysAnnualReportConverterImpl implements SysAnnualReportConverter {

    @Override
    public SysAnnualReportDto mapperDto(SysAnnualReportPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysAnnualReportDto sysAnnualReportDto = new SysAnnualReportDto();

        sysAnnualReportDto.setId( arg0.getId() );
        sysAnnualReportDto.setSourceName( arg0.getSourceName() );
        sysAnnualReportDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysAnnualReportDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysAnnualReportDto.setName( arg0.getName() );
        sysAnnualReportDto.setStatus( arg0.getStatus() );
        sysAnnualReportDto.setSort( arg0.getSort() );
        sysAnnualReportDto.setRemark( arg0.getRemark() );
        sysAnnualReportDto.setCreateBy( arg0.getCreateBy() );
        sysAnnualReportDto.setCreateTime( arg0.getCreateTime() );
        sysAnnualReportDto.setUpdateBy( arg0.getUpdateBy() );
        sysAnnualReportDto.setUpdateTime( arg0.getUpdateTime() );
        sysAnnualReportDto.setDelFlag( arg0.getDelFlag() );
        sysAnnualReportDto.setUpdateName( arg0.getUpdateName() );
        sysAnnualReportDto.setEnterpriseId( arg0.getEnterpriseId() );
        sysAnnualReportDto.setEntityId( arg0.getEntityId() );
        sysAnnualReportDto.setAnnualReportType( arg0.getAnnualReportType() );
        sysAnnualReportDto.setReportYear( arg0.getReportYear() );
        sysAnnualReportDto.setIsReport( arg0.getIsReport() );
        sysAnnualReportDto.setVoucher( arg0.getVoucher() );
        sysAnnualReportDto.setCreateName( arg0.getCreateName() );

        return sysAnnualReportDto;
    }

    @Override
    public List<SysAnnualReportDto> mapperDto(Collection<SysAnnualReportPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysAnnualReportDto> list = new ArrayList<SysAnnualReportDto>( arg0.size() );
        for ( SysAnnualReportPo sysAnnualReportPo : arg0 ) {
            list.add( mapperDto( sysAnnualReportPo ) );
        }

        return list;
    }

    @Override
    public Page<SysAnnualReportDto> mapperPageDto(Collection<SysAnnualReportPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysAnnualReportDto> page = new Page<SysAnnualReportDto>();
        for ( SysAnnualReportPo sysAnnualReportPo : arg0 ) {
            page.add( mapperDto( sysAnnualReportPo ) );
        }

        return page;
    }

    @Override
    public SysAnnualReportPo mapperPo(SysAnnualReportDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysAnnualReportPo sysAnnualReportPo = new SysAnnualReportPo();

        sysAnnualReportPo.setId( arg0.getId() );
        sysAnnualReportPo.setSourceName( arg0.getSourceName() );
        sysAnnualReportPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysAnnualReportPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysAnnualReportPo.setName( arg0.getName() );
        sysAnnualReportPo.setStatus( arg0.getStatus() );
        sysAnnualReportPo.setSort( arg0.getSort() );
        sysAnnualReportPo.setRemark( arg0.getRemark() );
        sysAnnualReportPo.setCreateBy( arg0.getCreateBy() );
        sysAnnualReportPo.setCreateTime( arg0.getCreateTime() );
        sysAnnualReportPo.setUpdateBy( arg0.getUpdateBy() );
        sysAnnualReportPo.setUpdateTime( arg0.getUpdateTime() );
        sysAnnualReportPo.setDelFlag( arg0.getDelFlag() );
        sysAnnualReportPo.setCreateName( arg0.getCreateName() );
        sysAnnualReportPo.setUpdateName( arg0.getUpdateName() );
        sysAnnualReportPo.setEnterpriseId( arg0.getEnterpriseId() );
        sysAnnualReportPo.setEntityId( arg0.getEntityId() );
        sysAnnualReportPo.setAnnualReportType( arg0.getAnnualReportType() );
        sysAnnualReportPo.setReportYear( arg0.getReportYear() );
        sysAnnualReportPo.setIsReport( arg0.getIsReport() );
        sysAnnualReportPo.setVoucher( arg0.getVoucher() );

        return sysAnnualReportPo;
    }

    @Override
    public List<SysAnnualReportPo> mapperPo(Collection<SysAnnualReportDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysAnnualReportPo> list = new ArrayList<SysAnnualReportPo>( arg0.size() );
        for ( SysAnnualReportDto sysAnnualReportDto : arg0 ) {
            list.add( mapperPo( sysAnnualReportDto ) );
        }

        return list;
    }

    @Override
    public Page<SysAnnualReportPo> mapperPagePo(Collection<SysAnnualReportDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysAnnualReportPo> page = new Page<SysAnnualReportPo>();
        for ( SysAnnualReportDto sysAnnualReportDto : arg0 ) {
            page.add( mapperPo( sysAnnualReportDto ) );
        }

        return page;
    }
}
