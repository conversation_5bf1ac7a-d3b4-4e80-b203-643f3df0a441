package com.heju.system.entity.domain.model;

import com.github.pagehelper.Page;
import com.heju.system.entity.domain.dto.SysEntitySaicBranchDto;
import com.heju.system.entity.domain.po.SysEntitySaicBranchPo;
import java.util.ArrayList;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-08-21T14:09:20+0800",
    comments = "version: 1.5.3.Final, compiler: javac, environment: Java 17.0.9 (GraalVM Community)"
)
@Component
public class SysEntitySaicBranchConverterImpl implements SysEntitySaicBranchConverter {

    @Override
    public SysEntitySaicBranchDto mapperDto(SysEntitySaicBranchPo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysEntitySaicBranchDto sysEntitySaicBranchDto = new SysEntitySaicBranchDto();

        sysEntitySaicBranchDto.setId( arg0.getId() );
        sysEntitySaicBranchDto.setSourceName( arg0.getSourceName() );
        sysEntitySaicBranchDto.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysEntitySaicBranchDto.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysEntitySaicBranchDto.setName( arg0.getName() );
        sysEntitySaicBranchDto.setStatus( arg0.getStatus() );
        sysEntitySaicBranchDto.setSort( arg0.getSort() );
        sysEntitySaicBranchDto.setRemark( arg0.getRemark() );
        sysEntitySaicBranchDto.setCreateBy( arg0.getCreateBy() );
        sysEntitySaicBranchDto.setCreateTime( arg0.getCreateTime() );
        sysEntitySaicBranchDto.setUpdateBy( arg0.getUpdateBy() );
        sysEntitySaicBranchDto.setUpdateTime( arg0.getUpdateTime() );
        sysEntitySaicBranchDto.setDelFlag( arg0.getDelFlag() );
        sysEntitySaicBranchDto.setCreateName( arg0.getCreateName() );
        sysEntitySaicBranchDto.setUpdateName( arg0.getUpdateName() );
        sysEntitySaicBranchDto.setEntityId( arg0.getEntityId() );

        return sysEntitySaicBranchDto;
    }

    @Override
    public List<SysEntitySaicBranchDto> mapperDto(Collection<SysEntitySaicBranchPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysEntitySaicBranchDto> list = new ArrayList<SysEntitySaicBranchDto>( arg0.size() );
        for ( SysEntitySaicBranchPo sysEntitySaicBranchPo : arg0 ) {
            list.add( mapperDto( sysEntitySaicBranchPo ) );
        }

        return list;
    }

    @Override
    public Page<SysEntitySaicBranchDto> mapperPageDto(Collection<SysEntitySaicBranchPo> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysEntitySaicBranchDto> page = new Page<SysEntitySaicBranchDto>();
        for ( SysEntitySaicBranchPo sysEntitySaicBranchPo : arg0 ) {
            page.add( mapperDto( sysEntitySaicBranchPo ) );
        }

        return page;
    }

    @Override
    public SysEntitySaicBranchPo mapperPo(SysEntitySaicBranchDto arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysEntitySaicBranchPo sysEntitySaicBranchPo = new SysEntitySaicBranchPo();

        sysEntitySaicBranchPo.setId( arg0.getId() );
        sysEntitySaicBranchPo.setSourceName( arg0.getSourceName() );
        sysEntitySaicBranchPo.setOperate( arg0.getOperate() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysEntitySaicBranchPo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysEntitySaicBranchPo.setName( arg0.getName() );
        sysEntitySaicBranchPo.setStatus( arg0.getStatus() );
        sysEntitySaicBranchPo.setSort( arg0.getSort() );
        sysEntitySaicBranchPo.setRemark( arg0.getRemark() );
        sysEntitySaicBranchPo.setCreateBy( arg0.getCreateBy() );
        sysEntitySaicBranchPo.setCreateTime( arg0.getCreateTime() );
        sysEntitySaicBranchPo.setUpdateBy( arg0.getUpdateBy() );
        sysEntitySaicBranchPo.setUpdateTime( arg0.getUpdateTime() );
        sysEntitySaicBranchPo.setDelFlag( arg0.getDelFlag() );
        sysEntitySaicBranchPo.setCreateName( arg0.getCreateName() );
        sysEntitySaicBranchPo.setUpdateName( arg0.getUpdateName() );
        sysEntitySaicBranchPo.setEntityId( arg0.getEntityId() );

        return sysEntitySaicBranchPo;
    }

    @Override
    public List<SysEntitySaicBranchPo> mapperPo(Collection<SysEntitySaicBranchDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        List<SysEntitySaicBranchPo> list = new ArrayList<SysEntitySaicBranchPo>( arg0.size() );
        for ( SysEntitySaicBranchDto sysEntitySaicBranchDto : arg0 ) {
            list.add( mapperPo( sysEntitySaicBranchDto ) );
        }

        return list;
    }

    @Override
    public Page<SysEntitySaicBranchPo> mapperPagePo(Collection<SysEntitySaicBranchDto> arg0) {
        if ( arg0 == null ) {
            return null;
        }

        Page<SysEntitySaicBranchPo> page = new Page<SysEntitySaicBranchPo>();
        for ( SysEntitySaicBranchDto sysEntitySaicBranchDto : arg0 ) {
            page.add( mapperPo( sysEntitySaicBranchDto ) );
        }

        return page;
    }
}
