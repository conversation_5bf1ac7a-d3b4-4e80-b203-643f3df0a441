package com.heju.job.service.impl;

import com.heju.common.web.entity.service.impl.BaseServiceImpl;
import com.heju.job.api.domain.dto.SysJobLogDto;
import com.heju.job.api.domain.query.SysJobLogQuery;
import com.heju.job.manager.impl.SysJobLogManagerImpl;
import com.heju.job.service.ISysJobLogService;
import org.springframework.stereotype.Service;

/**
 * 调度日志管理 服务层处理
 *
 * <AUTHOR>
 */
@Service
public class SysJobLogServiceImpl extends BaseServiceImpl<SysJobLogQuery, SysJobLogDto, SysJobLogManagerImpl> implements ISysJobLogService {

    /**
     * 清空任务日志
     */
    @Override
    public void cleanLog() {
        baseManager.cleanLog();
    }
}
