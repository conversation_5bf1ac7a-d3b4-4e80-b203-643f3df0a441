package com.heju.job.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.heju.common.core.constant.basic.OperateConstants;
import com.heju.common.web.annotation.TenantIgnore;
import com.heju.common.web.entity.domain.SlaveRelation;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.job.api.domain.dto.SysJobDto;
import com.heju.job.api.domain.model.SysJobConverter;
import com.heju.job.api.domain.po.SysJobPo;
import com.heju.job.api.domain.query.SysJobQuery;
import com.heju.job.manager.ISysJobManager;
import com.heju.job.mapper.SysJobMapper;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static com.heju.job.api.constant.MergeConstants.JOB_LOG_GROUP;

/**
 * 调度任务管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysJobManagerImpl extends BaseManagerImpl<SysJobQuery, SysJobDto, SysJobPo, SysJobMapper, SysJobConverter> implements ISysJobManager {

    /**
     * 初始化从属关联关系
     *
     * @return 关系对象集合
     */
    protected List<SlaveRelation> subRelationInit() {
        return new ArrayList<>(){{
            add(new SlaveRelation(JOB_LOG_GROUP, SysJobLogManagerImpl.class, OperateConstants.SubOperateLimit.ONLY_DEL));
        }};
    }

    /**
     * 项目启动时
     */
    @Override
    @TenantIgnore(tenantLine = true)
    public List<SysJobDto> initScheduler() {
        List<SysJobPo> jobList = baseMapper.selectList(Wrappers.query());
        return baseConverter.mapperDto(jobList);
    }

}
