package com.heju.job.mapper;

import com.heju.common.datasource.annotation.Isolate;
import com.heju.common.web.entity.mapper.BaseMapper;
import com.heju.job.api.domain.dto.SysJobLogDto;
import com.heju.job.api.domain.po.SysJobLogPo;
import com.heju.job.api.domain.query.SysJobLogQuery;
import org.apache.ibatis.annotations.Delete;


/**
 * 调度日志管理 数据层
 *
 * <AUTHOR>
 */
@Isolate
public interface SysJobLogMapper extends BaseMapper<SysJobLogQuery, SysJobLogDto, SysJobLogPo> {

}
