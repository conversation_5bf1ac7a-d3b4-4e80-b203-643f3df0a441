package com.heju.job.task;

import com.heju.common.core.constant.basic.SecurityConstants;
import com.heju.common.core.utils.core.StrUtil;
import com.heju.common.core.web.result.R;
import com.heju.common.security.utils.SecurityUtils;
import com.heju.system.api.annualReport.feign.RemoteAnnualReportService;
import com.heju.system.api.fileInfo.feign.RemoteFileInfoService;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 定时任务调度测试
 *
 * <AUTHOR>
 */
@Component("ryTask")
public class RyTask {

    @Resource
    private RemoteFileInfoService remoteFileInfoService;

    @Resource
    private RemoteAnnualReportService remoteAnnualReportService;

    public void ryMultipleParams(String s, Boolean b, Long l, Double d, Integer i) {
        System.out.println(StrUtil.format("执行多参方法：字符串类型{}，布尔类型{}，长整型{}，浮点型{}，整形{}", s, b, l, d, i));
    }

    public void ryParams(String params) {
        System.out.println(StrUtil.format("执行有参方法：参数{}", params));
    }

    public void ryNoParams() {
        System.out.println("执行无参方法");
    }

    public void cleanExpiredFiles() {
        R<Integer> result = remoteFileInfoService.deleteExpiredFiles(SecurityUtils.getEnterpriseId(),SecurityUtils.getSourceName(), SecurityConstants.INNER);
        if (result.isOk()) {
            System.out.println("清理过期文件成功，删除数量：" + result.getData());
        } else {
            System.err.println("清理过期文件失败：" + result.getMsg());
        }
    }

    public void checkAnnualReport() {
        R<Integer> result = remoteAnnualReportService.check(SecurityUtils.getEnterpriseId(), SecurityUtils.getSourceName(), SecurityConstants.INNER);
        if (result.isOk()) {
            System.out.println("检查工商年报成功，新增数量：" + result.getData());
        } else {
            System.err.println("检查工商年报失败：" + result.getMsg());
        }
    }
}
