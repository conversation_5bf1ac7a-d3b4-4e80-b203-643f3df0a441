package com.heju.job.manager.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.heju.common.web.entity.manager.impl.BaseManagerImpl;
import com.heju.job.api.domain.dto.SysJobLogDto;
import com.heju.job.api.domain.model.SysJobLogConverter;
import com.heju.job.api.domain.po.SysJobLogPo;
import com.heju.job.api.domain.query.SysJobLogQuery;
import com.heju.job.manager.ISysJobLogManager;
import com.heju.job.mapper.SysJobLogMapper;
import org.springframework.stereotype.Component;

/**
 * 调度任务日志管理 数据封装层处理
 *
 * <AUTHOR>
 */
@Component
public class SysJobLogManagerImpl extends BaseManagerImpl<SysJobLogQuery, SysJobLogDto, SysJobLogPo, SysJobLogMapper, SysJobLogConverter> implements ISysJobLogManager {

    /**
     * 清空任务日志
     */
    @Override
    public void cleanLog() {
        baseMapper.delete(Wrappers.update());
    }
}
