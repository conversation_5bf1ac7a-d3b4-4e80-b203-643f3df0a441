package com.heju.auth.controller;

import com.heju.auth.config.WechatConstant;
import com.heju.auth.service.ISysWechatService;
import com.heju.common.core.utils.core.NumberUtil;
import com.heju.common.core.web.result.AjaxResult;
import com.heju.common.security.utils.SecurityUtils;
import com.heju.system.api.organize.domain.dto.SysUserDto;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.weaver.patterns.IToken;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Slf4j
@RestController
@RequestMapping("/wechat")
public class WechatController {

    @Resource
    private ISysWechatService wechatService;

    /**
     * 微信托管时进行的验签验证
     *
     * @param signature 微信加密签名，signature结合了开发者填写的token参数和请求中的timestamp参数、nonce参数
     * @param timestamp 时间戳
     * @param nonce     随机数
     * @param echostr   随机字符串
     * @return 返回验证消息
     */
    @GetMapping("/index")
    @ResponseBody
    public String checkReceiveUrl(@RequestParam String signature, @RequestParam String timestamp, @RequestParam String nonce, @RequestParam String echostr) {
        return wechatService.checkReceiveMessageUrl(signature, timestamp, nonce, echostr);
    }

    @RequestMapping("/login-ajax")
    @ResponseBody
    public AjaxResult weixinAjax(@RequestParam String oper, @RequestParam(defaultValue = "", value = "login_s") String loginStr, HttpServletRequest request, HttpServletResponse response) {
        try {
            if (WechatConstant.LOGIN_TICKET.equals(oper)) {
                return AjaxResult.success(wechatService.getLoginTicket(NumberUtil.One));
            } else if (WechatConstant.LOGIN_VERIFY.equals(oper)) {
                return AjaxResult.success(wechatService.loginVerify(loginStr));
            } else if (WechatConstant.BIND_TICKET.equals(oper)) {
                return AjaxResult.success(wechatService.getLoginTicket(NumberUtil.Two));
            } else if (WechatConstant.BIND_VERIFY.equals(oper)) {
                return AjaxResult.success(wechatService.bindVerify(loginStr));
            } else if (WechatConstant.WECHAT_BIND_TICKET.equals(oper)){
                return AjaxResult.success(wechatService.getLoginTicket(NumberUtil.Three));
            } else if (WechatConstant.WECHAT_BIND_VERIFY.equals(oper)){
                return AjaxResult.success(wechatService.bindVerify(oper));
            }
            else {
                return AjaxResult.error("未知操作");
            }
        } catch (Exception ex) {
            return AjaxResult.error("未知操作");
        }
    }

    @RequestMapping("/useradd-ajax")
    @ResponseBody
    public AjaxResult useraddAjax(@RequestParam String oper,String sourceName,String loginStr) {
        try {
            if (WechatConstant.USER_TICKET.equals(oper)) {
                return AjaxResult.success(wechatService.getUserAddTicket(NumberUtil.Three,sourceName));
            }
            else {
                return AjaxResult.error("未知操作");
            }
        } catch (Exception ex) {
            return AjaxResult.error("未知操作");
        }
    }


    /**
     * 微信事件推送
     *
     * @param request 推送内容
     * @return 成功
     */
    @PostMapping("/index")
    @ResponseBody
    public String checkReceiveUrl(HttpServletRequest request) throws IOException {
        return wechatService.handleReceiveMessage(request);
    }

}
